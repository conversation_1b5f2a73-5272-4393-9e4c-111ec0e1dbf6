const Joi = require("joi/lib");
const antiFraudService = require("../services/anti-fraud");
const BaseAF1Model = require("./base-af1-model");
const { convertBody } = require("../utils/converter/convert");
const { STATUS } = require("../const/caseStatus");
const { TASK_FLOW } = require("../const/definition");
const { REQUEST_TYPE, PARTNER_CODE, RESPONSE_CODE, BizziHmStep } = require("../const/definition");
const loanContractRepo = require("../repositories/loan-contract-repo");
const moment = require("moment");

const af1schema = Joi.object({
  request_id: Joi.string().required(),
  partner_code: Joi.string().required(),
  tax_id: Joi.string().required(),
  company_name: Joi.string().required(),
  platform_usage_time: Joi.number().required(),
  representations: Joi.array()
    .items(
      Joi.object({
        full_name: Joi.string().required(),
        id: Joi.string().required(),
      })
        .unknown(true)
        .required()
    )
    .required(),
  contract_creator_name: Joi.string().required(),
  contract_creator_phone_number: Joi.string().required(),
  // anchor_transaction_time: Joi.number().required(),
  // anchor_tax_number: Joi.string().required(),
}).unknown(true);

class AF1BizziLimit extends BaseAF1Model {
  constructor(req, res) {
    super(af1schema, req, res);
    this.partner_code = PARTNER_CODE.BZHM;
  }

  convertToPayload() {
    this.payload = convertBody(this.body, REQUEST_TYPE.BZHM_AF1, global.convertCache);
  }

  async checkEligibility() {
    this.updateLoanStatus(STATUS.RECEIVEDA1);

    let resultCheck = {
      isPassed: true,
      message: "Contract is eligible for AF1.",
    };

    //1) Status check
    const statusResult = await antiFraudService.checkContractStatus(
      {
        request_id: this.request_id,
        partner_code: this.partner_code,
        tax_id: this.payload.tax_id,
        contract_number: this.contract_number,
        tax_code: this.payload.tax_id,
        registration_number: this.payload.tax_id,
        company_name: this.payload.sme_name,
        company_type: "E",
        contract_type: "LIMIT",
        check_list: ["IN_ACTIVATED", "IN_PROGRESS"],
      },
      TASK_FLOW.CHECK_ELIGIBLE_STATUS
    );

    let isCheckStatusPassed = await processStatusActiveCheckResult(statusResult, this.contract_number);
    if (!isCheckStatusPassed) {
      resultCheck.isPassed = false;
      resultCheck.message = "Contract is not eligible for activated status.";
      return resultCheck;
    }

    // 2) Renew date check
    const renewResult = await antiFraudService.checkRenewDate(this.request_id, this.partner_code, this.payload.tax_id, this.contract_number, TASK_FLOW.CHECK_CONTRACT_RENEW_DATE_AF1);
    let isRenewDatePassed = await processRenewDateCheckResult(renewResult, this.contract_number);
    if (!isRenewDatePassed) {
      resultCheck.isPassed = false;
      resultCheck.message = "Contract is not eligible for renewal.";
      return resultCheck;
    }

    //3) Whitelist AF1 check
    const whitelistAf1Result = await antiFraudService.checkWhitelistAf1({
      request_id: this.request_id,
      partner_code: this.partner_code,
      tax_code: this.payload.tax_id,
      contract_number: this.contract_number,
      contract_type: "LIMIT",
      platform_usage_time: this.payload.platform_usage_time,
      anchor_transaction_time: null,
    });

    let isWhitelistPassed = await processWhitelistAf1Result(whitelistAf1Result, this.contract_number);
    if (!isWhitelistPassed) {
      resultCheck.isPassed = false;
      resultCheck.message = "Contract is not eligible for whitelist.";
      return resultCheck;
    }

    // 4) Blacklistcheck
    const blacklistResult = await antiFraudService.checkBlacklist({
      request_id: this.request_id,
      partner_code: this.partner_code,
      tax_code: this.payload.tax_id,
      contract_number: this.contract_number,
      id_number: this.payload.representations?.[0]?.id,
    });

    let isBlacklistPassed = await processBlacklistCheckResult(blacklistResult, this.contract_number);
    if (!isBlacklistPassed) {
      resultCheck.isPassed = false;
      resultCheck.message = "Contract is not eligible for blacklist.";
      return resultCheck;
    }

    //5) CIC B11T check
    const cicB11SmeResult = await antiFraudService.checkCicB11Sme({
      request_id: this.request_id,
      partner_code: this.partner_code,
      contract_number: this.contract_number,
      tax_code: this.payload.tax_id,
      persons: this.payload?.representations?.map((item) => ({
        full_name: item.full_name,
        id_number: item.id,
        address: "",
      })),
    });

    let isB11TPassed = await processB11TCheckResult(cicB11SmeResult, this.contract_number);
    if (!isB11TPassed) {
      resultCheck.isPassed = false;
      resultCheck.message = "Contract is not eligible for CIC B11T.";
      return resultCheck;
    }

    await this.updateLoanApprovedStatus(STATUS.PASSED_REVIEW_A1);
    return resultCheck;
  }
}

const processStatusActiveCheckResult = async (checkResult, contractNumber) => {
  try {
    if (!checkResult?.success) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processStatusActiveCheckResult error | cannot get response`);
      return false;
    }

    const { data } = checkResult || {};
    if (!["ELIGIBLE", "NOT_ELIGIBLE"].includes(data?.data?.decision)) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processStatusActiveCheckResult error | decision code not valid`);
      return false;
    }

    if (data.data?.decision == "NOT_ELIGIBLE") {
      await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE, contractNumber);
      //handle callback 3P here

      return false;
    }

    return true;
  } catch (error) {
    console.log(`[BZHM_AF1][processStatusActiveCheckResult] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const processRenewDateCheckResult = async (checkResult, contractNumber) => {
  try {
    if (!checkResult?.success) {
      console.log(`${contractNumber}| ${new Date()} | processRenewDateCheckResult error | cannot get response`);
      return false;
    }

    const { data } = checkResult || {};

    if (data?.data?.is_locking) {
      await loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber);
      //handle callback 3P here

      return false;
    }

    const contractRenewDate = data?.data?.contract_renew_date;
    const now = moment();
    if (contractRenewDate && moment(contractRenewDate, "YYYY-MM-DD HH:mm:ss").isAfter(now)) {
      await loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber);
      //handle callback 3P here

      return false;
    }

    // contract_renew_date is null or date is in the past
    return true;
  } catch (error) {
    console.log(`[BZHM_AF1][processRenewDateCheckResult] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const processBlacklistCheckResult = async (checkResult, contractNumber) => {
  try {
    if (!checkResult?.success) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processBlacklistCheckResult error | cannot get response`);
      return false;
    }

    const { data } = checkResult || {};
    if (!["ELIGIBLE", "NOT_ELIGIBLE"].includes(data?.code)) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processBlacklistCheckResult error | decision code not valid`);
      return false;
    }

    if (data?.code == "NOT_ELIGIBLE") {
      await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE, contractNumber);
      //handle callback 3P here

      return false;
    }

    return true;
  } catch (error) {
    console.log(`[BZHM_AF1][processBlacklistCheckResult] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const processB11TCheckResult = async (checkResult, contractNumber) => {
  try {
    if (!checkResult?.success) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processB11TCheckResult error | cannot get response`);
      return false;
    }

    const { data } = checkResult || {};
    const { data: b11tData } = data || {};
    if (!["ELIGIBLE", "NOT_ELIGIBLE"].includes(b11tData?.decision)) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processBlacklistCheckResult error | decision code not valid`);
      return false;
    }

    if (b11tData.decision == "NOT_ELIGIBLE") {
      await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE, contractNumber);
      //handle callback 3P here

      return false;
    }

    return true;
  } catch (error) {
    console.log(`[BZHM_AF1][processB11TCheckResult] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const processWhitelistAf1Result = async (checkResult, contractNumber) => {
  try {
    if (!checkResult?.success) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processWhitelistAf1Result error | cannot get response`);
      return false;
    }

    const { data } = checkResult || {};
    if (!["ELIGIBLE", "NOT_ELIGIBLE"].includes(data?.data?.decision)) {
      console.log(`${contractNumber}| ${new Date()} | BZHM_AF1 | processWhitelistAf1Result error | decision code not valid`);
      return false;
    }

    if (data?.data?.decision == "NOT_ELIGIBLE") {
      await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE, contractNumber);
      //handle callback 3P here

      return false;
    }

    return true;
  } catch (error) {
    console.log(`[BZHM_AF1][processWhitelistAf1Result] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

module.exports = AF1BizziLimit;
