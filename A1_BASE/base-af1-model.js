const { BadRequestResponse, SuccessResponse, ServerErrorResponse } = require("../base/response");
const inputValidator = require("../a1_application/a1-input-validator");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loggingService = require("../utils/loggingService");
const { convertBody } = require("../utils/converter/convert");
const { STATUS } = require("../const/caseStatus");
const { REQUEST_TYPE, PARTNER_CODE, RESPONSE_CODE, BizziHmStep } = require("../const/definition");
const loanCustomerRepo = require('../repositories/loan-customer-repo')
const sqlHelper = require("../utils/sqlHelper")
const loggingRepo = require("../repositories/logging-repo")
const helper = require("../utils/helper")
const loansAf1Repo = require('../repositories/loans-af1-repo')
const _ = require("lodash");
const moment = require("moment");

class BaseAF1Model {
  constructor(schema, req, res) {
    this.schema = schema;
    this.body = req.body;
    this.req = req;
    this.res = res;
    this.payload = {};
    this.request_id = this.body?.request_id;
    this.partner_code = PARTNER_CODE.BIZZ;
  }
  convertToPayload() {
    this.payload = convertBody(
      this.body,
      REQUEST_TYPE.BIZZ_AF1,
      global.convertCache
    );
  }

  async validate() {
    const { error } = this.schema.validate(this.body);
    if (error) {
      const err = {
        code: error.details[0].context.key,
        message: error.message,
      };
      this.logging(err);
      throw new BadRequestResponse(err);
    }
    const processingCase = await this.getProcessingCase({ taxId: this.body.tax_id });
    if (processingCase > 0) {
      const errors = [{
        errorCode: `status`,
        errorMessage: `Quý khách đang có hồ sơ chưa hoàn thành, không thể submit`
      }]
      this.logging(errors);
      throw new BadRequestResponse(errors);
    }

    let isValid = true;
    let errorDetail = {};
    const [
      loanByRequestId
    ] = await Promise.all([
      sqlHelper.findOne({
        table:'loan_contract',
        whereCondition: {
          request_id: this.body.request_id
        }
      })
    ]);

    if (loanByRequestId?.id) {
      isValid = false;
      errorDetail = {
        code: 'request_id',
        message: `Hồ sơ đã tồn tại với request_id ${this.body.request_id}`
      };
    }

    if (!isValid) {
      const errors = [{
        errorCode: errorDetail.code,
        errorMessage: errorDetail.message
      }]
      this.logging(errors);
      throw new BadRequestResponse(errors);
    }
  }

  logging(body) {
    loggingService.saveRequestV2(
      this.req.poolWrite,
      this.payload,
      body,
      this.contract_number,
      this.request_id,
      this.partner_code
    );
  }

  updateLoanStatus(status) {
    return loanContractRepo.updateContractStatus(status, this.contract_number);
  }

  updateLoanApprovedStatus(status) {
    return loanContractRepo.updateLoanApprovalStatus({
      status: status,
      contractNumber: this.contract_number,
      approvalDate: moment().tz("Asia/Ho_Chi_Minh").format("YYYY-MM-DD HH:mm:ss")
    });
  }

  async createLoanContract() {
    this.payload.contract_number = this.contract_number;
    const [representative] = this.body.representations;
    this.payload.sme_representation_id = representative.id;
    this.payload.sme_representation_name = representative.full_name;
    this.payload.sme_tax_id = this.body.tax_id;
    this.contract = await loanContractRepo.insertLoanContract(this.payload);
    if (!this.contract) {
      const error = {
        code: RESPONSE_CODE.SERVER_ERROR,
        message: "Internal Server Error",
      };
      this.logging(error);
      throw new BadRequestResponse(error);
    }
    await this.updateLoanStatus(STATUS.RECEIVED);
  }

  async checkEligibility() { }

  async process() {
    try {
      await this.validate();
      this.convertToPayload();
      this.contract_number = await inputValidator.generateContractNumber_v2(
        this.req
      );
      await Promise.all([
        this.createLoanContract(),
        this.insertLoanCustomer(),
        this.insertLoanAf1()  
      ]);
      const resultCheck = await this.checkEligibility();
      this.contract = await loanContractRepo.findByContractNumber({
        contractNumber: this.contract_number,
        partnerCode: this.partner_code,
      });
      const responseData = {
        contract_number: this.contract_number,
        status: resultCheck?.isPassed ? this.contract.status : 'NOT_ELIGIBLE',
        reason: resultCheck.message ?? null,
      };
      return new SuccessResponse(responseData);
    } catch (e) {
      console.error(`ERROR | BaseAF1Model | process | ${this.contract_number}`, e);
      if (e instanceof BadRequestResponse) {
        throw e;
      }
      throw new ServerErrorResponse();
    }
  }

  async insertLoanCustomer() {
    try {
      let body = this.body;

      let loanCustomerData = { ...body, contract_number: this.contract_number };
      await Promise.all([
        sqlHelper.insertData(
          `loan_customer`,
          loanCustomerRepo.columns,
          sqlHelper.generateValues(loanCustomerData, loanCustomerRepo.columns)
        ),
        loggingRepo.saveWorkflow(BizziHmStep.AF1, STATUS.RECEIVEDA1, this.contract_number, 'system')
      ])
    } catch (e) {
      console.error(`ERROR | BaseAF1Model | insertLoanCustomer | ${this.contract_number}`, e);
      throw new BadRequestResponse(e);
    }
  }

  async getProcessingCase({ taxId }) {
    const poolWrite = global.poolWrite;
    let rowCount = 0;
    let sql = `
            select 
                * 
            from 
                loan_contract lc 
            where 
                sme_tax_id = $1
                and status not in ($2, $3, $4, $5, $6, $7, $8, $9)
            `;
    let params = [taxId, STATUS.ACTIVATED, STATUS.CANCELLED, STATUS.REFUSED, STATUS.NOT_ELIGIBLE, STATUS.TERMINATED, STATUS.BAD_DEBT, STATUS.EXPIRED, STATUS.INACTIVATED];
    rowCount = (await poolWrite.query(sql, params)).rowCount || 0;
    return rowCount;
  }

  async insertLoanAf1() {
    try {
      let body = {
        ..._.cloneDeep(this.body),
        contract_number: this.contract_number,
        representation_full_name: this.body.representations[0].full_name,
        representation_id: this.body.representations[0].id,
        is_deleted: 0
      };

      await Promise.all([
        sqlHelper.insertData(
          `loans_af1`,
          loansAf1Repo.columns,
          sqlHelper.generateValues(body, loansAf1Repo.columns)
        ),
      ])
    } catch (e) {
      console.error(`ERROR | BaseAF1Model | insertLoanAf1 | ${this.contract_number}`, e);
      throw new BadRequestResponse(e);
    }
  }
}

module.exports = BaseAF1Model;
