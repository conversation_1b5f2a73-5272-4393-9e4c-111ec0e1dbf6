const BaseAF2Model = require("./base-af2-model");
const { af2Schema } = require("../a2_application/af2-bizzi-limit-schema");
const { REQUEST_TYPE, PARTNER_CODE, RESPONSE_CODE, TASK_FLOW, BZHMStep, PARTNER_TYPE, CONTRACT_TYPE, BizziHmStep } = require("../const/definition");
const { STATUS } = require("../const/caseStatus");
const utils = require("../utils/helper");
const sqlHelper = require("../utils/sqlHelper");
const { BadRequestResponse, ServerErrorResponse, SuccessResponse } = require("../base/response");
const { getValueCode_v3, getValueCodeByCodeType } = require("../utils/masterdataService");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loggingRepo = require("../repositories/logging-repo");
const productService = require("../utils/productService");
const documentRepo = require("../repositories/document");
const loggingService = require("../utils/loggingService");
const loanBusinessOwnerRepo = require("../repositories/loan-business-owner-repo");
const loanCustomerRepo = require("../repositories/loan-customer-repo");
const loanContractDocumentRepo = require("../repositories/document");
const cicReportService = require("../services/cic-report-service");
const { convertBody, convertBodyVer2 } = require("../utils/converter/convert");
const loanRepsRepo = require("../repositories/loan-customer-representations-repo");
const loanShareholdersRepo = require("../repositories/loan-customer-shareholders-repo");
const loanManagerRepo = require("../repositories/loan-customer-manager-repo");
const loanRevenuesRepo = require("../repositories/loan-revenues-repo");
const loanVatFormsRepo = require("../repositories/loan-vat-forms-repo");
const loanCustomerWarehousesRepo = require("../repositories/loan-customer-warehouses-repo");
const loanBranchAddressRepo = require("../repositories/loan-branch-address-repo");
const { goNextStep } = require("../services/workflow-continue");
const _ = require("lodash");

const initBusinessOwnerData = (body) => {
  let businessOwnerWrap = convertBodyVer2(_.cloneDeep(body.business_owner || {}), REQUEST_TYPE.BZHM_AF2_OWNER, global.convertCache);
  // let businessOwnerWrap = utils.snakeToCamel(_.cloneDeep(body.business_owner || {}))


  businessOwnerWrap.married_status = body?.business_owner?.married_status ? (body?.business_owner?.married_status === "SINGLE" ? "C" : "M") : null;

  return businessOwnerWrap;
};

const initLoanCustomerData = (payload, body) => {
  let loanCustomerData = {
    ...payload,
    phone_number: body.company_phone_number,
    email: body.company_email,
    address_on_license: null,
  };

  return loanCustomerData;
};

const initLoanManagersData = (body) => {
  let loanManagers = _.cloneDeep(body.managers || []);
  for (let i = 0; i < loanManagers.length; i++) {
    loanManagers[i] = {
      ...loanManagers[i],
      per_province_code: loanManagers[i].per_address.province_code,
      per_ward_code: loanManagers[i].per_address.ward_code,
      per_detail_address: loanManagers[i].per_address.detail,
      cur_province_code: loanManagers[i]?.cur_address?.province_code,
      cur_ward_code: loanManagers[i]?.cur_address?.ward_code,
      cur_detail_address: loanManagers[i]?.cur_address?.detail,
    };
  }

  return loanManagers;
};

const initLoanWareHouseData = (body) => {
  const warehousse = _.cloneDeep(body.warehouses)
  for (let i = 0; i < warehousse.length; i++) {
    warehousse[i] = {
      ...warehousse[i],
      detail_address: warehousse[i]?.detail,
      new_province_code: warehousse[i]?.province_code,
      new_ward_code: warehousse[i]?.ward_code,
    };
  }

  return utils.convertSnakeToCamel(warehousse);
};

const initBrancheData = (body) => {
  const branches = _.cloneDeep(body.branches)
  for (let i = 0; i < branches.length; i++) {
    branches[i] = {
      ...branches[i],
      detail_address: branches[i]?.detail,
      new_province_code: branches[i]?.province_code,
      new_ward_code: branches[i]?.ward_code,
    };
  }

  return utils.convertSnakeToCamel(branches);
};

class AF2BizziLimitModel extends BaseAF2Model {
  constructor(req, res) {
    super(af2Schema, req, res);
    this.partner_code = PARTNER_CODE.BZHM;
  }

  convertToPayload() {
    this.payload = convertBodyVer2(this.body, REQUEST_TYPE.BZHM_AF2, global.convertCache);
  }

  async validate() {
    let isValid = true;
    let errorDetail = {};
    const { error } = this.schema.validate(this.body);

    if (error) {
      isValid = false;
      errorDetail = {
        code: error.details[0].context.key,
        message: error.message,
      };
    }

    const [loanContract, loanByRequestId] = await Promise.all([
      sqlHelper.findOne({
        table: "loan_contract",
        whereCondition: {
          contract_number: this.body.contract_number,
        },
      }),
      sqlHelper.findOne({
        table: "loan_contract",
        whereCondition: {
          request_id: this.body.request_id,
        },
      }),
    ]);

    if (loanByRequestId?.id) {
      isValid = false;
      errorDetail = {
        code: "request_id",
        message: `Hồ sơ đã tồn tại với request_id ${this.body.request_id}`,
      };
    }
    if (!loanContract?.id) {
      isValid = false;
      errorDetail = {
        code: "contract_number",
        message: `Hợp đồng vay với số hợp đồng ${this.body.contract_number} không tồn tại`,
      };
    }

    if (!loanContract) {
      isValid = false;
      errorDetail = {
        code: RESPONSE_CODE.INVALID_REQUEST,
        message: `Loan contract with number ${this.body.contract_number} not found`,
      };
    }

    if (loanContract?.status !== STATUS.PASSED_REVIEW_A1) {
      isValid = false;
      errorDetail = {
        code: RESPONSE_CODE.INVALID_REQUEST,
        message: `Loan contract with number ${this.body.contract_number} invalid status`,
      };
      this.logging(errorDetail);
    }

    let isMissingFinancialReport = false;
    //check bao cao tai chinh
    if (this.body?.financial_information?.revenues?.length === 0) {
      isMissingFinancialReport = true;
    }
    const latestRevenue = this.body.financial_information.revenues.reduce((latest, current) => {
      return parseInt(current.year) > parseInt(latest.year) ? current : latest;
    }, this.body.financial_information.revenues[0]);
    if (latestRevenue?.financial_report_docs?.length === 0 || !latestRevenue.financial_report_docs.find((doc) => doc.doc_type === "SFSTD")) {
      isMissingFinancialReport = true;
    }
    let financialReportDoc = latestRevenue.financial_report_docs.find((doc) => doc.doc_type === "SFSTD");
    let isExistedFinancialReportDoc = await loanContractDocumentRepo.validDocId(financialReportDoc.doc_id);
    if (!isExistedFinancialReportDoc) {
      isMissingFinancialReport = true;
    }

    if (isMissingFinancialReport) {
      isValid = false;
      errorDetail = {
        code: `financial_information`,
        message: `Thiếu file báo cáo tài chính`,
      };
    }

    return {
      ...errorDetail,
      isValid,
    };
  }

  async process() {
    try {
      const validateresult = await this.validate();

      if (!validateresult.isValid) {
        const errors = [{ code: validateresult.code, message: validateresult.message }];
        this.logging(errors);
        return new BadRequestResponse(errors);
      }

      this.convertToPayload();

      //set default value
      this.payload.status = STATUS.RECEIVEDA2;
      this.payload.channel = PARTNER_CODE.BZHM;
      this.payload.contract_type = CONTRACT_TYPE.CREDIT_LINE;
      this.payload.product_code = ""; //update lại product_code ở bước check model thẩm định

      this.payload.approval_tenor = this.payload.request_tenor;
      this.payload = await this.setManager();
      this.payload.current_task = TASK_FLOW.START;
      this.setRepresentation();
      const updateFullLoanRs = await Promise.all([
        loanContractRepo.updateLoanContract(this.payload),
        loggingRepo.saveWorkflow(BizziHmStep.AF2, this.payload.status, this.contract_number, "system")
      ]);
      if (!updateFullLoanRs[0]) {
        return new ServerErrorResponse([], "Error processing AF2 model");
      }
      let body = this.body;

      let businessOwnerWrap = initBusinessOwnerData(body);
      let loanCustomerData = initLoanCustomerData(this.payload, body);
      let loanManagers = initLoanManagersData(body);
      let loanWareHouseData = initLoanWareHouseData(body);
      let branchData = initBrancheData(body);

      const preparingShareholders = await this.prepareInsertShareholders(body?.shareholders?.members);
      const masterdataDocuments = getValueCodeByCodeType("DOCUMENT");

      await Promise.all([
        loanBranchAddressRepo.insert(
          this.contract_number,
          branchData,
          masterdataDocuments
        ),
        loanManagerRepo.insert(
          this.contract_number, 
          utils.convertSnakeToCamel(loanManagers), 
          masterdataDocuments
        ),
        sqlHelper.patchUpdate({
          table: 'loan_customer',
          columns: loanCustomerRepo.columns,
          values: sqlHelper.generateValues(loanCustomerData, loanCustomerRepo.columns),
          conditions: {
            contract_number: this.contract_number
          }
        }),
        loanRepsRepo.insert(
          this.contract_number,
          this.payload.registration_number,
          utils.convertSnakeToCamel(_.cloneDeep(this.payload.representations)),
          masterdataDocuments
        ),
        loanCustomerWarehousesRepo.insert(
          this.contract_number,
          loanWareHouseData,
          masterdataDocuments
        ),
        loanBusinessOwnerRepo.insertV2(
          this.contract_number,
          businessOwnerWrap,
          masterdataDocuments
        ),
        loanShareholdersRepo.insert(
          this.contract_number,
          preparingShareholders,
          masterdataDocuments
        ),
        loanRevenuesRepo.insert(
          this.contract_number,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.revenues)),
          null,
          masterdataDocuments
        ),
        loanVatFormsRepo.insert(
          this.contract_number,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.vat_forms)),
          masterdataDocuments
        ),
        loanContractRepo.insertLoanCustomerPartners(
          this.contract_number,
          PARTNER_TYPE.INT,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.input_partners))
        ),
        loanContractRepo.insertLoanCustomerPartners(
          this.contract_number,
          PARTNER_TYPE.OUT,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.output_partners))
        )
      ])

      await this.saveDocuments();

      goNextStep(this.contract_number);
      this.contract = await loanContractRepo.findByContractNumber({
        contractNumber: this.contract_number,
        partnerCode: this.partner_code,
      });

      const responseData = {
        contract_number: this.contract_number,
        status: this.contract.status,
      };
      return new SuccessResponse(responseData);
    } catch (error) {
      console.error("Error processing AF2 model:", error);
      if (error instanceof BadRequestResponse) {
        throw error;
      }
      throw new ServerErrorResponse();
    }
  }
}

module.exports = AF2BizziLimitModel;
