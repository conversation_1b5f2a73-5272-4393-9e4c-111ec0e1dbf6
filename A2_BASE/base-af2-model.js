const { BadRequestResponse, ServerErrorResponse, SuccessResponse } = require("../base/response");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loggingService = require("../utils/loggingService");
const { convertBody, convertBodyVer2 } = require("../utils/converter/convert");
const productService = require("../utils/productService");
const documentRepo = require("../repositories/document");
const { STATUS } = require("../const/caseStatus");
const {
  REQUEST_TYPE,
  PARTNER_CODE,
  RESPONSE_CODE,
  TASK_FLOW,
  BizziHmStep,
  PARTNER_TYPE,
  CONTRACT_TYPE,
} = require("../const/definition");
const { getValueCode_v3, getValueCodeByCodeType } = require("../utils/masterdataService");
const loggingRepo = require("../repositories/logging-repo")

const utils = require("../utils/helper")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const sqlHelper = require("../utils/sqlHelper")
const loanBusinessOwnerRepo = require('../repositories/loan-business-owner-repo')
const loanCustomerRepo = require('../repositories/loan-customer-repo')
const _ = require('lodash');
const loanContractDocumentRepo = require('../repositories/document');
const cicReportService = require("../services/cic-report-service");
const loanRepsRepo = require('../repositories/loan-customer-representations-repo');
const loanShareholdersRepo = require('../repositories/loan-customer-shareholders-repo');
const loanManagerRepo = require('../repositories/loan-customer-manager-repo');
const loanRevenuesRepo = require('../repositories/loan-revenues-repo');
const loanVatFormsRepo = require('../repositories/loan-vat-forms-repo');
const { goNextStep } = require("../services/workflow-continue");
const loanCustomerWarehousesRepo = require('../repositories/loan-customer-warehouses-repo');
const loanBranchAddressRepo = require('../repositories/loan-branch-address-repo');

class BaseAF2Model {
  constructor(schema, req, res) {
    this.schema = schema;
    this.body = req.body;
    this.req = req;
    this.res = res;
    this.payload = {};
    this.request_id = this.body?.request_id;
    this.partner_code = PARTNER_CODE.BIZZ;
    this.contract_number = this.body?.contract_number;
  }

  convertToPayload() {
    this.payload = convertBodyVer2(
      this.body,
      REQUEST_TYPE.BIZZ_AF2,
      global.convertCache
    );
  }

  async validate() {
    const { error } = this.schema.validate(this.body);
    if (error) {
      const err = {
        code: error.details[0].context.key,
        message: error.message,
      };
      this.logging(err);
      throw new BadRequestResponse(err);
    }

    let isValid = true;
    let errorDetail = {};
    const [
      loanContract,
      loanByRequestId
    ] = await Promise.all([
      sqlHelper.findOne({
        table: 'loan_contract',
        whereCondition: {
          contract_number: this.body.contract_number
        }
      }),
      sqlHelper.findOne({
        table: 'loan_contract',
        whereCondition: {
          request_id: this.body.request_id
        }
      })
    ]);

    if (loanByRequestId?.id) {
      isValid = false;
      errorDetail = {
        code: 'request_id',
        message: `Hồ sơ đã tồn tại với request_id ${this.body.request_id}`
      };
    }
    if (!loanContract?.id) {
      isValid = false;
      errorDetail = {
        code: 'contract_number',
        message: `Hợp đồng vay với số hợp đồng ${this.body.contract_number} không tồn tại`,
      };
    }

    if (!isValid) {
      const errors = [{
        code: errorDetail.code,
        message: errorDetail.message
      }]
      this.logging(errors);
      throw new BadRequestResponse(errors);
    }
    
    if (!loanContract) {
      const err = {
        code: RESPONSE_CODE.INVALID_REQUEST,
        message: `Loan contract with number ${this.body.contract_number} not found`,
      };
      this.logging(err);
      throw new BadRequestResponse(err);
    }
    if (loanContract?.status !== STATUS.PASSED_REVIEW_A1) {
      const err = {
        code: RESPONSE_CODE.INVALID_REQUEST,
        message: `Loan contract with number ${this.body.contract_number} invalid status`,
      };
      this.logging(err);
      throw new BadRequestResponse(err);
    }

    let isMissingFinancialReport = false;
    //check bao cao tai chinh
    if (this.body?.financial_information?.revenues?.length === 0) {
      isMissingFinancialReport = true;
    }
    const latestRevenue = this.body.financial_information.revenues.reduce((latest, current) => {
      return parseInt(current.year) > parseInt(latest.year) ? current : latest;
    }, this.body.financial_information.revenues[0]);
    if (latestRevenue?.financial_report_docs?.length === 0 ||
      !latestRevenue.financial_report_docs.find(doc => doc.doc_type === 'SFSTD')) {
      isMissingFinancialReport = true;
    }
    let financialReportDoc = latestRevenue.financial_report_docs.find(doc => doc.doc_type === 'SFSTD');
    let isExistedFinancialReportDoc = await loanContractDocumentRepo.validDocId(financialReportDoc.doc_id);
    if (!isExistedFinancialReportDoc) {
      isMissingFinancialReport = true;
    }

    if (isMissingFinancialReport) {
      const err = {
        code: `financial_information`,
        message: `Thiếu file báo cáo tài chính`,
      };
      this.logging(err);
      throw new BadRequestResponse(err);
    }
  }

  logging(body) {
    loggingService.saveRequestV2(
      this.req.poolWrite,
      this.payload,
      body,
      this.contract_number,
      this.request_id,
      this.partner_code
    );
  }

  updateLoanStatus(status) {
    return loanContractRepo.updateContractStatus(status, this.contract_number);
  }

  async saveLoanContract() {
    this.contract_number = this.payload.contract_number;
    await loanContractRepo.updateLoanContract(this.payload);
    await this.updateLoanStatus(STATUS.RECEIVEDA2);
  }

  async saveDocuments() {
    let body = this.body;
    // const bundleInfoRes = await productService.getBundle(
    //   global.config,
    //   this.payload.product_code
    // );
    let documents = [];
    if (Array.isArray(body?.other_docs) && body.other_docs.length > 0) {
      documents.push(...body.other_docs);
    }
    // if (Array.isArray(body?.branches) && body.branches.length > 0) {
    //   for (const branch of body?.branches) {
    //     if (Array.isArray(branch.docs) && branch.docs.length > 0) {
    //       documents.push(...branch.docs);
    //     }
    //   }
    // }
    // if (Array.isArray(body?.warehouses?.docs) && body.warehouses.docs.length > 0) {
    //   documents.push(...body.warehouses.docs);
    // }
    // if (Array.isArray(body?.representations) && body.representations.length > 0) {
    //   for (const rep of body?.representations) {
    //     if (Array.isArray(rep.docs) && rep.docs.length > 0) {
    //       documents.push(...rep.docs);
    //     }
    //   }
    // }
    // if (Array.isArray(body?.managers) && body.managers.length > 0) {
    //   for (const rep of body?.managers) {
    //     if (Array.isArray(rep.docs) && rep.docs.length > 0) {
    //       documents.push(...rep.docs);
    //     }
    //   }
    // }
    // if (Array.isArray(body?.business_owner?.docs) && body.business_owner.docs.length > 0) {
    //   documents.push(...body.business_owner.docs);
    // }
    // if (Array.isArray(body?.business_owner?.partner?.docs) && body?.business_owner?.partner?.docs?.length > 0) {
    //   documents.push(...body.business_owner.partner.docs);
    // }
    // if (Array.isArray(body?.shareholders?.docs) && body.shareholders.docs.length > 0) {
    //   documents.push(...body.shareholders.docs);
    // }
    // if (Array.isArray(body?.financial_information?.revenues) && body?.financial_information?.revenues?.length > 0) {
    //   for (const revenue of body.financial_information.revenues) {
    //     if (Array.isArray(revenue.financial_report_docs) && revenue.financial_report_docs.length > 0) {
    //       documents.push(...revenue.financial_report_docs);
    //     }
    //   }
    // }
    // if (Array.isArray(body?.financial_information?.vat_forms) && body?.financial_information?.vat_forms?.length > 0) {
    //   for (const vat of body.financial_information.vat_forms) {
    //     if (Array.isArray(vat.docs) && vat.docs.length > 0) {
    //       documents.push(...vat.docs);
    //     }
    //   }
    // }
    // documents = productService.mapBundleGroup(documents, bundleInfoRes.data);

    await documentRepo.saveRequestDocuments({
      contractNumber: body.contract_number,
      docList: utils.snakeToCamel(documents)
    });
  }

  async process() {
    try {
      await this.validate();
      this.convertToPayload();

      //set default value
      this.payload.status = STATUS.RECEIVEDA2
      this.payload.channel = PARTNER_CODE.BIZZ;
      this.payload.contract_type = CONTRACT_TYPE.CREDIT_LINE;
      this.payload.product_code = 'BIZZ_BTT_01';
      this.payload.approval_tenor = this.payload.request_tenor;
      this.payload = await this.setManager();
      this.payload.current_task = TASK_FLOW.START;
      const updateFullLoanRs = await Promise.all([
        loanContractRepo.updateLoanContract(this.payload),
        loggingRepo.saveWorkflow(BizziHmStep.AF2, this.payload.status, this.contract_number, 'system')
      ])
      if (!updateFullLoanRs[0]) {
        return new ServerErrorResponse();
      }
      let body = this.body;
      let businessOwnerWrap = convertBodyVer2(
        (_.cloneDeep(body.business_owner || {})),
        REQUEST_TYPE.BIZZ_AF2_OWNER,
        global.convertCache
      );
      businessOwnerWrap.married_status = body?.business_owner?.married_status
        ? (body?.business_owner?.married_status === 'SINGLE' ? 'C' : 'M') : null;

      let loanCustomerData = {
        ...this.payload,
        phone_number: body.company_phone_number,
        email: body.company_email,
        address_on_license: null
      };

      let loanManagers = _.cloneDeep(body.managers || []);
      for (let i = 0; i < loanManagers.length; i++) {
        loanManagers[i] = {
          ...loanManagers[i],
          per_province_code: loanManagers[i].per_address.province_code,
          per_ward_code: loanManagers[i].per_address.ward_code,
          per_detail_address: loanManagers[i].per_address.detail,
          cur_province_code: loanManagers[i]?.cur_address?.province_code,
          cur_ward_code: loanManagers[i]?.cur_address?.ward_code,
          cur_detail_address: loanManagers[i]?.cur_address?.detail
        };
      }

      const preparingShareholders = await this.prepareInsertShareholders(body?.shareholders?.members);
      const [
        bundleInfoRes,
        masterdataDocuments
      ] = await Promise.all([
        productService.getBundle(
          global.config,
          this.payload.product_code
        ),
        getValueCodeByCodeType("DOCUMENT")
      ]);

      await Promise.all([
        loanBranchAddressRepo.insert(
          this.contract_number,
          utils.convertSnakeToCamel(_.cloneDeep(body.branches)),
          masterdataDocuments
        ),
        loanManagerRepo.insert(
          this.contract_number, 
          utils.convertSnakeToCamel(loanManagers), 
          masterdataDocuments
        ),
        sqlHelper.patchUpdate({
          table: 'loan_customer',
          columns: loanCustomerRepo.columns,
          values: sqlHelper.generateValues(loanCustomerData, loanCustomerRepo.columns),
          conditions: {
            contract_number: this.contract_number
          }
        }),
        loanRepsRepo.insert(
          this.contract_number,
          this.payload.registration_number,
          utils.convertSnakeToCamel(_.cloneDeep(this.payload.representations)),
          masterdataDocuments
        ),
        loanCustomerWarehousesRepo.insert(
          this.contract_number,
          utils.convertSnakeToCamel(_.cloneDeep(this.payload.warehouses)),
          masterdataDocuments
        ),
        loanBusinessOwnerRepo.insertV2(
          this.contract_number,
          businessOwnerWrap,
          masterdataDocuments
        ),
        loanShareholdersRepo.insert(
          this.contract_number,
          preparingShareholders,
          masterdataDocuments
        ),
        loanRevenuesRepo.insert(
          this.contract_number,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.revenues)),
          null,
          masterdataDocuments
        ),
        loanVatFormsRepo.insert(
          this.contract_number,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.vat_forms)),
          masterdataDocuments
        ),
        loanContractRepo.insertLoanCustomerPartners(
          this.contract_number,
          PARTNER_TYPE.INT,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.input_partners))
        ),
        loanContractRepo.insertLoanCustomerPartners(
          this.contract_number,
          PARTNER_TYPE.OUT,
          utils.convertSnakeToCamel(_.cloneDeep(body.financial_information.output_partners))
        )
      ])
      await this.saveDocuments();
      const allDocument = await sqlHelper.find({
        table: 'loan_contract_document',
        whereCondition: {
          contract_number: this.contract_number,
          is_deleted: 0
        }
      })
      await documentRepo.updateBundleGroup({
        docList: utils.snakeToCamel(allDocument),
        bundleInfo: bundleInfoRes?.data ?? [],
      });

      goNextStep(this.contract_number);
      this.contract = await loanContractRepo.findByContractNumber({
        contractNumber: this.contract_number,
        partnerCode: this.partner_code,
      });

      const responseData = {
        contract_number: this.contract_number,
        status: this.contract.status,
      };
      return new SuccessResponse(responseData);
    } catch (error) {
      console.error("Error processing AF2 model:", error);
      if (error instanceof BadRequestResponse) {
        throw error;
      }
      throw new ServerErrorResponse();
    }
  }

  setRepresentation = () => {
    if (!this.body?.representations?.[0]) {
      return this.payload;
    }
    let body = this.body;
    this.payload = {
      ...this.payload,
      sme_representation_name: body.representations[0].full_name,
      // sme_representation_gender,
      sme_representation_dob: body.representations[0].dob,
      sme_representation_id: body.representations[0].id,
      sme_representation_address_cur: body.representations[0].per_address.detail, //không có cur
      sme_representation_issue_date: body.representations[0].issue_date,
      sme_representation_position: body.representations[0].position,
      sme_representation_issue_place: body.representations[0].issue_place,
      sme_representation_email: body.representations[0].email || body.business_owner?.[0]?.email,
      sme_representation_address_per: body.representations[0].per_address.detail,
      sme_representation_ward_per: body.representations[0].per_address.ward_code,
      sme_representation_province_per: body.representations[0].per_address.province_code,
      sme_representation_ward_cur: body.representations[0].cur_address?.ward_code,
      sme_representation_province_cur: body.representations[0].cur_address?.province_code,
      sme_representation_phone_number: body.representations[0].phone_number
    }
    return this.payload;
  }

  setManager = async () => {
    if (!this.body?.managers?.[0]) {
      return this.payload;
    }
    let body = this.body;

    let addressPromises = [];

    if (
      body.managers[0].per_address &&
      Object.keys(body.managers[0].per_address).length > 0
    ) {
      addressPromises.push(getValueCode_v3(body.managers[0].per_address.ward_code, "WARD"));
      addressPromises.push(getValueCode_v3(body.managers[0].per_address.province_code, 'PROVINCE'));
    }
    if (
      body.managers[0].cur_address &&
      Object.keys(body.managers[0].cur_address).length > 0
    ) {
      addressPromises.push(getValueCode_v3(body.managers[0].cur_address.ward_code, "WARD"));
      addressPromises.push(getValueCode_v3(body.managers[0].cur_address.province_code, 'PROVINCE'));
    }

    const masterData = await Promise.all(addressPromises);

    this.payload = {
      ...this.payload,
      sme_manager_address_per: (
      body.managers[0].per_address &&
      Object.keys(body.managers[0].per_address).length > 0
    ) ? [
        body.managers[0].per_address.detail,
        masterData[0],
        masterData[1],
      ].filter(element => element)
        .join(", ") : null,
      sme_manager_address_cur: (
        body.managers[0].cur_address &&
        Object.keys(body.managers[0].cur_address).length > 0
      ) ? [
        body.managers[0].cur_address.detail,
        masterData[2],
        masterData[3],
      ].filter(element => element)
        .join(", ") : null,
    }
    return this.payload;
  }

  prepareInsertShareholders = async (shareholders) => {
    if (!shareholders || shareholders?.length === 0) {
      return shareholders
    }
    let newShareholders = []
    for (const shareholder of shareholders) {
      const isIndividual = shareholder.subject === `INDIVIDUAL` ? true : false;
      const s = {
        ...utils.snakeToCamel(_.cloneDeep(shareholder)),
        contractNumber: this.contract_number,
        fullName: isIndividual ? shareholder.full_name : shareholder.representations?.[0]?.full_name,
        identityType: isIndividual ? shareholder.identity_type : shareholder.representations?.[0]?.identity_type,
        id: isIndividual ? shareholder.id : shareholder.representations?.[0]?.id,
        issueDate: isIndividual ? shareholder.issue_date : shareholder.representations?.[0]?.issue_date,
        issuePlace: isIndividual ? shareholder.issue_place : shareholder.representations?.[0]?.issue_place,
        perProvinceCode: isIndividual ? shareholder.per_address.province_code : shareholder.representations?.[0]?.per_address?.province_code,
        perWardCode: isIndividual ? shareholder.per_address.ward_code : shareholder.representations?.[0]?.per_address?.ward_code,
        perDetailAddress: isIndividual ? shareholder?.per_address?.detail : shareholder.representations?.[0]?.per_address?.detail,
        // curProvinceCode: isIndividual ? shareholder.cur_address.province_code : shareholder.representations?.[0]?.cur_address?.province_code,
        // curWardCode: isIndividual ? shareholder.cur_address.ward_code : shareholder.representations?.[0]?.cur_address?.ward_code,
        // curDetailAddress: isIndividual ? shareholder.cur_address.detail : shareholder.representations?.[0]?.cur_address?.detail,
        businessProvinceCode: shareholder.business_address?.province_code,
        businessWardCode: shareholder.business_address?.ward_code,
        businessDetailAddress: shareholder.business_address?.detail,
        dob: isIndividual ? shareholder.dob : shareholder.representations?.[0]?.dob
      }
      newShareholders.push(s);
    }
    return newShareholders
  }
}

module.exports = BaseAF2Model;
