const { af3Schema } = require("../a3_application/af3-bizzi-limit-schema");
const BaseAF3Model = require("./base-af3-model");
const { REQUEST_TYPE, PARTNER_CODE, RESPONSE_CODE } = require("../const/definition");

class AF3BizziLimitModel extends BaseAF3Model {
  constructor(req, res) {
    super(af3Schema, req, res);
    this.partner_code = PARTNER_CODE.BZHM;
  }

  convertToPayload() {
    // this.payload = convertBody(this.body, REQUEST_TYPE.BZHM_AF3, global.convertCache);
    this.payload = {};
  }
}

module.exports = AF3BizziLimitModel;
