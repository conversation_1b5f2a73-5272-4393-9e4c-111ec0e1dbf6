const { BadRequestResponse, SuccessResponse } = require("../base/response");
const { LENDER_REQUEST_TYPE, } = require("../const/variables-const");
const BizziLoanLender = require("../services/bizzi-loan-lender");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loggingService = require("../utils/loggingService");
const { convertBody } = require("../utils/converter/convert");
const productService = require("../utils/productService");
const documentRepo = require("../repositories/document");
const { STATUS } = require("../const/caseStatus");
const { REQUEST_TYPE, PARTNER_CODE, RESPONSE_CODE } = require("../const/definition");
const { goNextStep } = require("../services/workflow-continue");
const sqlHelper = require("../utils/sqlHelper")
const actionAuditService = require("../services/action-audit")
const { CASE_STATUS } = require("../const/code-const")

class BaseAF3Model {
  constructor(schema, req, res) {
    this.schema = schema;
    this.body = req.body;
    this.is_af3_re_submit = req.body.is_af3_re_submit || false;
    this.req = req;
    this.res = res;
    this.payload = {};
    this.request_id = this.body?.request_id;
    this.partner_code = PARTNER_CODE.BIZZ;
    this.product_code = "";
    this.loan_status = "";
    this.contract_number = this.body.contract_number;
  }

  convertToPayload() {
    this.payload = convertBody(this.body, REQUEST_TYPE.BIZZ_AF3, global.convertCache);
  }

  async validate() {
    const { error } = this.schema.validate(this.body);
    if (error) {
      const err = {
        code: RESPONSE_CODE.INVALID_REQUEST,
        message: error.message,
      };
      this.logging(err);
      throw new BadRequestResponse(err);
    }

    const [loanContract, loanByRequestId] = await Promise.all([
      sqlHelper.findOne({
        table: "loan_contract",
        whereCondition: {
          contract_number: this.body.contract_number,
        },
      }),
      sqlHelper.findOne({
        table: "loan_contract",
        whereCondition: {
          request_id: this.body.request_id,
        },
      }),
    ]);

    if (!loanContract) {
      throw new BadRequestResponse([], "contract_number not found");
    }

    if (loanByRequestId?.id) {
      throw new BadRequestResponse([], `request_id đã tồn tại ${this.body.request_id}`);
    }

    if (loanContract?.status !== STATUS.WAITING_CUSTOMER_SIGNATURE && loanContract?.status !== STATUS.RESUBMIT_A3) {
      const err = {
        code: RESPONSE_CODE.INVALID_REQUEST,
        message: `Loan contract with number ${this.body.contract_number} invalid status`,
      };
      this.logging(err);
      throw new BadRequestResponse([], `Loan contract with number ${this.body?.contract_number} invalid status`);
    }

    for (const item of this.body?.documents ?? []) {
      const isValid = await documentRepo.validDocIdAndType(item?.doc_id, item?.doc_type);
      if (!isValid) {
        throw new BadRequestResponse([], `doc_id is invalid or has already been used: ${item.doc_id}`);
      }
    }

    this.product_code = loanContract?.product_code;
    this.loan_status = loanContract?.status;

    return true;
  }

  logging(body) {
    loggingService.saveRequestV2(this.req.poolWrite, this.payload, body, this.contract_number, this.request_id, this.partner_code);
  }

  updateLoanStatus(status) {
    return loanContractRepo.updateAndGetContractStatus(status, this.contract_number);
  }

  async saveLoanContract() {
    this.contract_number = this.body.contract_number;

    if (this.is_af3_re_submit) {
      //duyệt change request khi khách hàng resubmit
      const loan = await loanContractRepo.getLoanContract(this.contract_number);
      if (loan?.status === STATUS.RESUBMIT_A3) {
        const loanLenderModel = await BizziLoanLender.init({
          referenceNumber: loan.contract_number,
          data: loan,
          partnerCode: loan.partner_code,
          table: "loan_contract",
        });
        // await updateLenderChangeRequest(changeRequest.id, {
        //   status: LENDER_CHANGE_REQUEST_STATUS.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ,
        // });

        await loanLenderModel.commitAF3ChangeRequest({ comment: "auto approved change-request by af3-resubmit", createdBy: loan?.sme_name }, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ);
      }
    }
  }

  async saveDocuments() {
    const bundleInfoRes = await productService.getBundle(global.config, this.product_code);
    const docListrequesr = this.body.documents;
    const docList = productService.mapBundleGroupBizz(docListrequesr, bundleInfoRes?.data);
    const history_tag = this.loan_status;
    return await documentRepo.saveUploadedDocumentBIZZ(this.req.poolWrite, this.contract_number, history_tag, docList);
  }

  async process() {
    await this.validate();
    this.convertToPayload();
    await this.saveLoanContract();

    const successed = await this.saveDocuments();

    if (successed) {
      actionAuditService.saveCaseHistoryActionAudit(this.contract_number, CASE_STATUS.SIGNING_IN_PROGRESS.STEP_CODE, CASE_STATUS.SIGNING_IN_PROGRESS.ACTION.COMPLETED_SIGN, this.contract_number);
      const responsedata = {
        contract_number: this.body.contract_number,
        error_code: RESPONSE_CODE.SUCCESS,
      };

      goNextStep(this.body.contract_number);

      //thành công
      return new SuccessResponse(responsedata);
    } else {
      //xử lý nếu lưu file lỗi hoặc bất kì lỗi gì liên quan đến file thì trả bad request hoặc internal server error
      throw new BadRequestResponse(null, "Failed to save documents");
    }
  }
}

module.exports = BaseAF3Model;
