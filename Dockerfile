#FROM 446567516155.dkr.ecr.ap-southeast-1.amazonaws.com/docker-image-common:debian-v3
FROM els-registry.evnfc.vn/common/docker-image-common:debian-v3

#FROM debian:buster-backports
#RUN apt update && apt install -y curl dirmngr apt-transport-https lsb-release ca-certificates
#RUN curl -sL https://deb.nodesource.com/setup_14.x | bash -
#RUN apt install -y nodejs
#RUN apt update && apt install -t buster-backports -y libreoffice

WORKDIR /usr/src/app
       
COPY package*.json ./

# RUN npm install knex -g
RUN npm install
##RUN npm ci --only=production

# Bundle app source
COPY . .

RUN chmod 700 ./entrypoint.sh
#RUN node --max-old-space-size=4096 app.js

# EXPOSE 1000
#ENTRYPOINT ["bash", "entrypoint.sh"]

RUN echo fs.inotify.max_user_watches=524288 >> /etc/sysctl.conf

ENV NODE_ENV=dev
CMD [ "/bin/sh", "-c", "./entrypoint.sh && npm run start" ]
 
