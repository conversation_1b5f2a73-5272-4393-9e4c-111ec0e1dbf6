const { MAPPING_STATUS_CLIENT_NAME, MAPPING_STATUS_CLIENT_CODE } = require("../const/caseStatus");
const { getLoanContractBySmeInfo, getLoanContract } = require("../repositories/loan-contract-repo")
const utils = require("../utils/helper")
const {CONTRACT_TYPE} = require("../const/definition")

async function getLoanBySmeInfo(req,res) {
    try {
        let response = {
            code : 1,
            statusCode : 200,
            msg: 'get data success'
        }
        const poolWrite = global.poolWrite;
        const payLoad = req.query;
        if(utils.isNullOrEmpty(payLoad.fromDate)||utils.isNullOrEmpty(payLoad.toDate)||utils.isNullOrEmpty(payLoad.smeTaxId)||utils.isNullOrEmpty(payLoad.contractType)||![CONTRACT_TYPE.CREDIT_LINE,CONTRACT_TYPE.CASH_LOAN].includes(payLoad.contractType)){
            response.code = 0;
            response.statusCode = 400;
            response.msg = 'Invalid input params';
        }else{
            const loanData = await getLoanContractBySmeInfo(poolWrite,payLoad);
            const datas = loanData?.rows;
            let dataFinal = [];
            datas.map(x=>{
                let data = { 
                    contractNumber : x.contract_number,
                    contractType : x.contract_type,
                    smeTaxId : x.sme_tax_id,
                    smePhoneNumber : x.sme_phone_number,
                    custId : x.cust_id,
                    createDate : x.created_date
                };
                dataFinal.push(data);
            })
            response.data = dataFinal||[];
            if(loanData.rowCount == 0){
                response.code = 0;
                response.statusCode = 400;
                response.msg = 'not found data';
            }
        }
        return res.status(response.statusCode).json(response);
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : -1,
            statusCode : 500,
            msg : 'service error'
        })
    }
}

async function getLoanDetailByContractNumber(req,res) {
    try {
        let response = {
            code : 1,
            statusCode : 200,
            msg: 'get data success'
        }
        const {contractNumber} = req.query;
        if(utils.isNullOrEmpty(contractNumber)){
            response.code = 0;
            response.statusCode = 400;
            response.msg = 'Invalid input params';
        }else{
            const poolWrite = global.poolWrite;
            let sqlGetLoanContract = `select * from loan_contract lc where contract_number = $1`;
            let sqlGetKunn = `select * from kunn where contract_number = $1 and status not in ('ACTIVATED','TERMINATED','SIGNED_TO_BE_DISBURED','ACTIVATED_WITH_DIS_DOCS')`;
            const promiseData = await Promise.all([
                poolWrite.query(sqlGetLoanContract,[contractNumber]),
                poolWrite.query(sqlGetKunn,[contractNumber])
            ])
            const loanData = promiseData[0];
            const kunnDatas = promiseData[1]?.rows;
            const data = loanData?.rows[0];
            let dataFinal = {
                contractNumber: data?.contract_number,
                smeTaxId: data?.sme_tax_id,
                smePhoneNumber: data?.sme_phone_number,
                approvalDate: data?.approval_date,
                approvalAmount: data?.approval_amt,
                tenor: data?.approval_tenor,
                rate: data?.approval_int_rate,
                loanAmount: null,
                availableAmount: null
            };
            let dataKunnFinal = []; 
            kunnDatas.map(kd=>{
                let dataKunn = {
                    kunnNumber: kd?.kunn_id,
                    status: MAPPING_STATUS_CLIENT_NAME[MAPPING_STATUS_CLIENT_CODE[kd?.status]] || '',
                    loanAmount: kd?.with_draw_amount
                };
                dataKunnFinal.push(dataKunn);
            })
            response.data = dataFinal||{};
            response.data.listDebtAckContract = dataKunnFinal||[];
            
            if(loanData.rowCount == 0){
                response.code = 0;
                response.statusCode = 400;
                response.msg = 'not found data';
                response.data = [];
            }
        }
        return res.status(response.statusCode).json(response);
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : -1,
            statusCode : 500,
            msg : 'service error'
        })
    }
}

module.exports = {
    getLoanBySmeInfo,
    getLoanDetailByContractNumber
}