const { SuccessResponse, BadRequestResponseV2 } = require("../base/response");
const s3Service = require("../upload_document/s3-service");
const documentRepo = require("../repositories/document");
const kunnRepo = require("../repositories/kunn-repo");
const invoiceRepo = require("../repositories/invoice-repo");
const { KUNN_TEMPLATE_DOCTYPE_SIGNED, LENDER_REFERENCE_TYPE} = require("../const/variables-const");
const { KUNN_STATUS } = require("../const/caseStatus");
const { getLatestLenderChangeRequest, getLenderChangeRequestDetail } = require("../repositories/lender-change-request-repo");
const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");


async function buildDataCallbackKunnResubmit(kunnId) {
    let data = {};
    const changeRequest = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.KUNN, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT });
    if(changeRequest) {
        const changeRequestDetails = await getLenderChangeRequestDetail(changeRequest.id);
        if(changeRequestDetails && changeRequestDetails.length > 0) {
            for (const detail of changeRequestDetails) {
                if (detail.status === false && detail.is_change === false) {
                    data[detail.key] = {
                        value: detail.old_value || '',
                        comment: detail.comment || ''
                    };
                }
            }
        }
    }
    //files
    const files = await documentRepo.getResubmitListKunn(kunnId);
    if (files && files.length > 0) {
        data.files = files.map(file => ({
            doc_id: file.doc_id,
            doc_type: file.doc_type,
            file_key: file.file_key,
            comment: file.comment
        }));
    }

    //invoice_details
    const invoices = await invoiceRepo.getInvoiceWaitingResubmit(kunnId);
    let invoiceDetails = [];
    if(invoices && invoices.length > 0) {
        invoiceDetails = invoices.map(invoice => {
            return {
                invoice_order: invoice.invoice_order,
                invoice_number: invoice.invoice_number,
                invoice_date: invoice.invoice_date,
                invoice_amount: invoice.invoice_amount,
                payment_date: invoice.payment_date,
                note: invoice.note,
                is_invoice_valid: invoice.is_invoice_valid,
                purchaser_tax_code: invoice.purchaser_tax_code,
                comment: invoice.comment || ''
            };
        });
    }
    // invoices change_request_details
    const changeRequestInvoices = await getLatestLenderChangeRequest({ reference_code: kunnId, reference_type: LENDER_REFERENCE_TYPE.INVOICE, status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT });
    if (changeRequestInvoices) {
        const changeRequestInvoiceDetails = await getLenderChangeRequestDetail(changeRequestInvoices.id);
        if (changeRequestInvoiceDetails && changeRequestInvoiceDetails.length > 0) {
            const invoice = await invoiceRepo.getInvoiceById(Number(changeRequestInvoices.reference_code));
            const isExist = invoiceDetails.some(detail => detail.invoice_number === invoice.invoice_number);
            if (invoice && !isExist) {
                invoiceDetails.push({
                    invoice_order: invoice.invoice_order,
                    invoice_number: invoice.invoice_number,
                    invoice_date: invoice.invoice_date,
                    invoice_amount: invoice.invoice_amount,
                    payment_date: invoice.payment_date,
                    note: invoice.note,
                    is_invoice_valid: invoice.is_invoice_valid,
                    purchaser_tax_code: invoice.purchaser_tax_code,
                    comment: invoice.comment || ''
                });
            }
        }
    }
    data.invoice_details = invoiceDetails;
    return data;
}

async function buildDataCallbackKunnSignInProgress(contractNumber, kunnId) {
    const listdoc = await documentRepo.getDocumentsByContractAndKunnAndDocTypes(contractNumber, kunnId, KUNN_TEMPLATE_DOCTYPE_SIGNED);
    if(!listdoc || listdoc.length === 0) {
        return [];
    }

    const result = await s3Service.genMultiplePresignedDownloadUrlForSme(listdoc);
    if (!result) {
        return [];
    }
    return result?.map(item => {
        return {
            doc_type: listdoc.find(doc => doc.doc_id === item.doc_id)?.doc_type || '',
            presigned_url: item?.url,
            doc_id: item?.doc_id,
            file_name: item?.file_name,
        };
    });
}

async function buildDataCallbackKunnResubmitSigned(kunnId) {
    const files = await documentRepo.getResubmitListKunn(kunnId);
    if (files && files.length > 0) {
        return files.map(file => ({
            doc_id: file.doc_id,
            doc_type: file.doc_type,
            file_key: file.file_key,
            comment: file.comment
        }));
    }
    return files;
}

async function getKunnStatus({ contract_number, debt_contract_number }) {
    if (!contract_number && !debt_contract_number) {
        throw new BadRequestResponseV2([], "contract_number or debt_contract_number is required");
    }
    const kunnData = await kunnRepo.getKunnData(debt_contract_number);
    if (!kunnData) {
        throw new BadRequestResponseV2([], "KUNN not found");
    }
    if (kunnData.contract_number !== contract_number) {
        throw new BadRequestResponseV2([], "contract_number does not match with KUNN data");
    }
    let data = {};
    let note = "";
    let status = kunnData.status;
    switch(kunnData.status) {
        case KUNN_STATUS.WAITING_RESUBMIT:
            note = "KUNN is waiting for resubmit";
            data = await buildDataCallbackKunnResubmit(debt_contract_number);
            break;
        case KUNN_STATUS.SIGNING_IN_PROGRESS:
            note = "KUNN is signing in progress";
            data.files = await buildDataCallbackKunnSignInProgress(contract_number, debt_contract_number);
            break;
        case KUNN_STATUS.WAITING_RESUBMIT_SIGNED:
            note = "KUNN is waiting for resubmit signed";
            data.files = await buildDataCallbackKunnResubmitSigned(debt_contract_number);
            break;
        default:
            break;
    }
    //map in_progress
    if(![ KUNN_STATUS.ACTIVATED, KUNN_STATUS.TERMINATED, KUNN_STATUS.NOT_ELIGIBLE, KUNN_STATUS.REFUSED, KUNN_STATUS.CANCELLED, KUNN_STATUS.SIGNING_IN_PROGRESS,
        KUNN_STATUS.WAITING_RESUBMIT, KUNN_STATUS.WAITING_RESUBMIT_SIGNED, KUNN_STATUS.RESUBMITED, KUNN_STATUS.SIGNED
    ].includes(kunnData.status)) {
        status = "IN_PROGRESS";
        note = "KUNN is in progress";
    }
    return new SuccessResponse({
        contract_number: kunnData.contract_number,
        debt_contract_number: kunnData.kunn_id,
        status: status,
        data: data,
        note: note
    });
}

module.exports = {
    getKunnStatus
};