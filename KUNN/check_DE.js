const currentTaskCode = 'MC_CHECK_CREDIT_RULE'
const common = require("../utils/common")

function callDE(req, res) {

	const deResult = {
		"msg": "ELIGIBLE",
		"data": {
			"pcb_score1": 0,
			"pcb_score2": 0
		},
		"code": 1
	}

	// let contractNumber = req.body.data.contractNumber
	// const wf_lb = "http://localhost:1001"
	let workflowUri = req.config.data.workflow.uri;
	let lb = req.config.basic.wfMcCredit[req.config.env]
	const workflowUrl = lb + workflowUri
	req.body.data['deResult'] = deResult

	if (deResult.code === 1) {

		let flowData = {
			"data": req.body.data,
			"current_task_code": currentTaskCode
		}

		// let sql = "update loan_score set pcb_score1=$1,pcb_score2=$2 where contract_number=$3"
		/*req.poolWrite.query(sql, [deResult.data.pcb_score1, deResult.data.pcb_score2, contractNumber])
			.then(rs => console.log("updated pcb score"))
			.catch(error => console.log(error))
		*/
		common.postAPI(workflowUrl, flowData)
			.then()
			.catch(err => console.log(err.message))
	}
	// else {
	// }

	res.status(200).json({
		"msg": "check eligible",
		"code": 1
	})

}

module.exports = {
	callDE
}