let currentTaskCode = 'MC_CHECK_AVAILBLE';

let common = require("../utils/common");
const utils = require("../utils/helper")
const callbackService = require("../utils/callbackService")
const {saveRequest} = require("../utils/loggingService")
const {caseStatus,caseStatusCode, STATUS} = require("../const/caseStatus")
const loggingService = require("../utils/loggingService")
const documentRepo = require("../repositories/document")
const deService = require("../services/de-service")
const loanContractRepo = require("../repositories/loan-contract-repo")
const defind = require("../const/definition")
const productService = require("../utils/productService")
const {pushTaskKU} = require("../utils/aadService")
const moment = require('moment-timezone');
const { PRODUCT_CODE } = require("../const/definition");
moment().tz('Asia/Ho_Chi_Minh').format()

const getDateString = () => {
	const d = new Date()
	const m = d.getMonth() + 1
	const month = m < 10 ? '0' + m : m
	const day = d.getDate() < 10 ? '0' + d.getDate() : d.getDate()
	return d.getFullYear().toString().slice(2, 4) + month + day
}
function delay(time) {
	return new Promise((resolve) => setTimeout(function () {
		//console.log(time);
		resolve();
	}, time))
}



async function createRequest(req, res) {
	const poolWrite = req.poolWrite
	const config = req.config
	// const userName = req.userName

	let response
	try {
		let {
			requestId, partnerCode, customerName, ir, tenor,
			withdrawPurpose, withdrawAmount, bankCode, bankBranchCode,
			bankAccount, contractNumber,billDay,capitalNeed,selfFinancing,otherCapital,fundingFromEc,repaymentSources,fixedAsset,planAsset,numberBusinessAsset
		} = req.body
		
		const beneficiaryName = utils.nonAccentVietnamese(customerName)
		const poolRead = req.poolRead
		const contractStatus = await utils.getKuStatus(poolRead,contractNumber)
		// const docList = req.body.doc_collecting_list
		const statusCur = (await loanContractRepo.getLoanContract(contractNumber))?.status;
		if([STATUS.CREDIT_REVIEW,STATUS.CLOSED].includes(statusCur)){
			response = {
				code: "0",
				message: 'can not create new request'
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(201).json(response)
		}
		if(contractStatus.length != 0) {
			for(let i=0; i< contractStatus.length; i ++) {
				if(!([caseStatusCode.KKH13,caseStatusCode.KCP07,caseStatusCode.KKH14,caseStatusCode.KKH15,STATUS.CANCELLED,STATUS.TERMINATED,STATUS.ACTIVATED].includes(contractStatus[i].status))) {
					response = {
						code: "0",
						message: 'can not create new request'
					}
					saveRequest(poolWrite,req.body,response,contractNumber)
					return res.status(201).json(response)
				}
			}
		}
		const lmsUri = req.config.data.lms.checkAvailable;
		const lmsUrl = config.basic.lmsMc[config.env] + lmsUri
		const workflowUri = req.config.data.workflow.uri;
		const workflowUrl = config.basic.wfMcCredit[config.env] + workflowUri
		const lmsHeader = await getAuthLms(req)
		const body = { contractNumber }

		const result = await common.postAPI(lmsUrl, body, lmsHeader)
		if (result.code === 0 && withdrawAmount <= result.data.avalibleAmount) {
			let kunnNumber = 0
			let isSucesss = false
			let retryCount = 0
			let genKunnRs ;
			await updateLoanContract(poolWrite, contractNumber, billDay)

			while (!isSucesss && retryCount < 3) {
				genKunnRs = await initKunn(requestId, partnerCode, customerName, method, ir, withdrawPurpose, withdrawAmount, tenor, beneficiaryName, bankCode, bankBranchCode, bankAccount, contractNumber, result.data.avalibleAmount,capitalNeed,selfFinancing,otherCapital,fundingFromEc,repaymentSources,fixedAsset,planAsset,numberBusinessAsset, req)
				isSucesss = genKunnRs.result
				if (isSucesss) {
					kunnNumber = genKunnRs.kunnNumber
				}
				await delay(100)
				retryCount++
			}
			
			if (!genKunnRs.result) {
				response = {
					code: "ERROR",
					message: 'Tạo số hợp đồng khế ước lỗi.'
				}
				saveRequest(poolWrite,req.body,response,contractNumber)
				return res.status(200).json(response)
			}

			utils.saveKUStatus(poolWrite,kunnNumber,caseStatusCode.KKH02)
			//await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KKH02,contractNumber,'system',kunnNumber)

			req.body['lmsResult'] = result
			req.body['kunnId'] = kunnNumber
			let flowData = {
				"data": req.body,
				"current_task_code": currentTaskCode
			}
			// console.log('flowData',flowData)
			common.postAPI(workflowUrl, flowData).then()
			response = {
				message: "The request is received",
				code: "RECEIVED",
				data: {
					contractNumber: contractNumber,
					withdrawId: kunnNumber,
					withdrawPurpose: withdrawPurpose
				}
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			res.status(200).json(response)
		}
		else {

			utils.saveKUStatus(poolWrite,kunnNumber,caseStatusCode.KKH03)
			//await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KKH03,contractNumber,'system',kunnNumber)

			const partnerCode = await utils.getPartnerCode(poolRead,contractNumber)
			const currentTimestamp = new Date().getTime()
			const requestID = partnerCode + currentTimestamp
			const callbackBody = {
				"requestId" : requestID,
				"contractNumber" : contractNumber,
				"code" : "REJECTED",
				targetType : "disbursement-request",
				rejectReason : "OVER_CAPACITY",
				"message": "Not meet the payment requirement ",
				
			}
	
			callbackService.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)
			response = {
				code: "FORMAT",
				message: 'Request is invalid',
				error: 'The withdrawAmount is invalid'
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			res.status(200).json(response)
		}
		
	}
	catch (error) {
		console.log(error)
		return res.status(500).json({
			message: "Sever error",
			code: "ERROR"
		})
	}
}


const parseMethod = (method) => {
	switch (method.toLowerCase()) {
		case "month":
			return 1
		case "quarter":
			return 3
		case "biannual":
			return 6
		case "annual":
			return 12
		default:
			return 1;
	}
}

const getCount = async (req) => {
	const sql = "SELECT count(kunn_id) FROM kunn WHERE kunn_id LIKE $1"
	const date = '03' + getDateString() + '____' // ****ddmmyy
	//console.log('date', date);
	const result = await req.poolRead.query(sql, [date])
	if (result.rows) {
		return result.rows[0].count
	}
	else {
		return 0
	}
}

const getNextvalSequense = async (req) => {
	const sql = "SELECT nextval('contract_generate_number')";
	const result = await req.poolWrite.query(sql)
	if (result.rows) {
		return result.rows[0].nextval
	}
	else {
		return 0
	}
}

async function generateKunnId(req) {
	const kunnNumber = await getNextvalSequense(req)
	return kunnNumber;
}

const initKunn = async (
	requestId, partnerCode, customerName, method, ir,
	withdrawPurpose, withdrawAmount, tenor, beneficiaryName, bankCode, bankBranchCode,
	bankAccount, contractNumber, availableAmount,capitalNeed,selfFinancing,otherCapital,fundingFromEc,repaymentSources,fixedAsset,planAsset,numberBusinessAsset, req
) => {
	let INIT_KUNN
	if(partnerCode=="VSK"){
		INIT_KUNN =
		`INSERT INTO kunn( request_id, partner_code, customer_name,kunn_id, method,
			ir, with_draw_amount, tenor, beneficiary_name, bank_code, bank_branch_code,
			bank_account,contract_number, available_amount,kunn_code,capital_need,self_financing,other_capital,funding_from_EC,fixed_asset,plan_asset) 
			VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21)
			ON CONFLICT DO NOTHING`
	}
	else{
		INIT_KUNN =
		`INSERT INTO kunn( request_id, partner_code, customer_name,kunn_id, method,
			ir, with_draw_purpose,with_draw_amount, tenor, beneficiary_name, bank_code, bank_branch_code,
			bank_account,contract_number, available_amount,kunn_code,capital_need,self_financing,other_capital,funding_from_EC,repayment_source,fixed_asset,plan_asset,number_bussiness_asset) 
			VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16,$17,$18,$19,$20,$21,$22,$23,$24)
			ON CONFLICT DO NOTHING`
	}
	/*
	const count = await getCount(req)

	let id
	if (count <= 9999) {
		id = '03' + getDateString() + ('000' + (parseInt(count) + 1)).slice(-4)
	}
	else {
		id = '03' + getDateString() + (parseInt(count) + 1)
	}
	*/
	const kunnNumber = await generateKunnId(req);
	const kunnCode = await getKunnCode(req, contractNumber)
	if (!kunnCode) {
		return {
			id: id,
			result: false
		}
	}
	let params
	let result
	if(partnerCode=="VSK"){
		params = [
			requestId, partnerCode, customerName, kunnNumber,
			method, ir, withdrawAmount, tenor, beneficiaryName, bankCode, bankBranchCode,
			bankAccount, contractNumber, availableAmount, kunnCode,capitalNeed,selfFinancing,otherCapital,fundingFromEc,fixedAsset,planAsset
		]
	}
	else{
		params = [
			requestId, partnerCode, customerName, kunnNumber,
			method, ir, withdrawPurpose, withdrawAmount, tenor, beneficiaryName, bankCode, bankBranchCode,
			bankAccount, contractNumber, availableAmount, kunnCode,capitalNeed,selfFinancing,otherCapital,fundingFromEc,repaymentSources,fixedAsset,planAsset,numberBusinessAsset
		]
	}
	result = await req.poolWrite.query(INIT_KUNN, params)
	if (result.rowCount) {
		return {
			kunnNumber,
			result: true
		}
	}
	else {
		return {
			kunnNumber,
			result: false
		}
	}
}

const getKunnCode = async (req, contractNumber) => {
	const sql = "SELECT product_code FROM loan_contract WHERE contract_number = $1"
	const result = await req.poolWrite.query(sql, [contractNumber])
	const {rows} = result
	if (rows && rows[0]) {
		let { product_code } = rows[0]
		product_code = product_code === 'HMTD_SAPO' ? 'SAPO_CREDIT_LINE' : product_code
		const url = req.config.basic.product[req.config.env] + req.config.data.productService.getKunnCodeList + '?productCode=' + product_code
		const result = await common.getAPI(url)
		// console.log({url,result:result.data});
		if (result && result.code === 'SUCCESS') {
			return result.data[0].kunn_code
		}
		else {
			return null
		}
	}
	return null
}

const getAuthLms = async (req) => {
	const lb = req.config.basic.aaa[req.config.env];
	const url = req.config.data.app.urlAuthentSingIn
	const bodySignIn = { username: global.config.data.lms.lmsUsername, password: global.config.data.lms.lmsPassword }
	const SignInUrl = lb + url
	const lmsUri = req.config.data.lms.checkAvailable;
	const result = await common.postAPI(SignInUrl, bodySignIn)
	const lmsHeader = {
		uiid: result.uiid,
		token: result.token,
		service: lmsUri
	}
	return lmsHeader
}

async function updateLoanContract(poolRead,contractNumber,billDay) {
	const currentDate = new Date()
	const sql = "update loan_contract set bill_day = $1,updated_date=$2 where contract_number=$3"
	poolRead.query(sql,[billDay,currentDate,contractNumber])
	.then()
	.catch(err => console.log(err))
}


module.exports = {
	createRequest,
}