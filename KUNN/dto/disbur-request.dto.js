class DisburRequestDto {
  constructor(
    request_id,
    partner_code,
    bank_account,
    bank_code,
    bank_branch_code,
    contract_number,
    total_receivable_amount,
    factoring_amount,
    supplier_contract_files,
    invoice_files,
    invoice_details
  ) {
    this.request_id = request_id;
    this.partner_code = partner_code;
    this.bank_account = bank_account;
    this.bank_code = bank_code;
    this.bank_branch_code = bank_branch_code;
    this.contract_number = contract_number;
    this.total_receivable_amount = total_receivable_amount;
    this.factoring_amount = factoring_amount;
    this.supplier_contract_files = supplier_contract_files;
    this.invoice_files = invoice_files;
    this.invoice_details = invoice_details;
  }
}

module.exports = DisburRequestDto;
