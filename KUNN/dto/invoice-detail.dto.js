class InvoiceDetailDto {
  constructor(order_number, invoice_number, invoice_date, invoice_amount, payment_date, note, is_invoice_valid, purchaser_tax_code) {
    this.invoice_order = order_number;
    this.invoice_number = invoice_number;
    this.invoice_date = invoice_date;
    this.invoice_amount = invoice_amount;
    this.payment_date = payment_date;
    this.note = note;
    this.is_invoice_valid = is_invoice_valid;
    this.purchaser_tax_code = purchaser_tax_code;
  }
}

module.exports = InvoiceDetailDto;