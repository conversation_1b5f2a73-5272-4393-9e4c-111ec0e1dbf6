const { BadRequestResponse } = require("../base/response");
const documentRepo = require("../repositories/document");
const { checkS3FilesExist } = require("../upload_document/s3-service");
const { PARTNER_CODE, DATE_FORMAT } = require("../const/definition");
const { submitKunnSchema } = require("./schema/finv-scheme");
const { findByContractNumber } = require("../repositories/loan-contract-repo");
const { isBeforeDate, formatDate, now } = require("../utils/dateHelper");
const kunnRepo = require("../repositories/kunn-repo");
const { KUNN_STATUS, STATUS } = require("../const/caseStatus");


async function validateFileExists(body) {
    let docIds = [];
    let docs = [];
    if (body.docs && Array.isArray(body.docs)) {
        docIds = body.docs.map(file => file.docId);
        docs = body.docs.map(file => ({ docId: file.docId, docType: file.docType }));
    }

    if (docIds.length === 0) {
        throw new BadRequestResponse([], "No files found in request body");
    }
    const duplicateDocIds = docIds.filter((id, idx) => docIds.indexOf(id) !== idx);
    if (duplicateDocIds.length > 0) {
        throw new BadRequestResponse([...new Set(duplicateDocIds)], "Duplicate docIds found in request body");
    }

    const documents = await documentRepo.getDocumentsByDocIdsWhereNoContract(docIds);
    console.log(`Found documents: ${JSON.stringify(documents)}`);
    const savedDocIds = documents.map(doc => doc.doc_id);
    // Check if all docIds exist in savedDocIds
    const notFoundDocIds = docIds.filter(id => !savedDocIds.includes(id));
    if (notFoundDocIds.length > 0) {
        throw new BadRequestResponse(notFoundDocIds, "Some files do not exists");
    }
    //check doc_id and doc_type is coupled
    const invalidDocType = docs.filter(doc => {
        const file = documents.find(f => f.doc_id === doc.docId);
        return file && file.doc_type !== doc.docType;
    });
    if (invalidDocType.length > 0) {
        throw new BadRequestResponse(invalidDocType.map(doc => ({ docId: doc.docId, docType: doc.docType })), "Some document types are invalid");
    }

    // Get fileKeys for S3 existence check
    const fileKeys = documents.map(doc => doc.file_key);
    const fileExists = await checkS3FilesExist(fileKeys);
    if (!fileExists || typeof fileExists === 'string') {
        const doc = documents.filter(doc => fileExists.includes(doc.file_key))?.[0];
        throw new BadRequestResponse([{doc_id: doc?.doc_id, doc_type: doc?.doc_type}], "Some files do not upload before");
    }
}

async function validateSubmitKunn(body) {
    const { error, value } = submitKunnSchema.validate(body, { abortEarly: false });
    if (error) {
        const errors = error.details.map((detail) => detail.message.replace(/"/g, ''));
        throw new BadRequestResponse(errors, "Invalid request body");
    }
    await validateContractNumber(value.contractNumber, PARTNER_CODE.FINV);
    await validateFileExists(body);
    return value;
}

async function validateContractNumber(contractNumber, partnerCode) {
    const loanContract = await findByContractNumber({ contractNumber, partnerCode });
    if (!loanContract) {
        throw new BadRequestResponse([`contract_number: ${contractNumber} not found`], `Loan contract not found`);
    }
    if( loanContract.status !== STATUS.ACTIVATED) {
        throw new BadRequestResponse([`contract_number: ${contractNumber} is not activated`], `Loan contract is not activated`);
    }   
    const endDate = formatDate(loanContract.end_date, DATE_FORMAT.DB_FORMAT);
    const currentDate = formatDate(now(), DATE_FORMAT.DB_FORMAT);
    console.log(`[${contractNumber}] endDate: ${endDate}, currentDate: ${currentDate}`);
    if(isBeforeDate(endDate, currentDate)) {
        throw new BadRequestResponse([`${contractNumber}`], `Loan contract is expired`);
    }
}

async function checkProcessingKunn(contractNumber) {
    const data = await kunnRepo.getKunnByContractNumber(contractNumber);
    const listKunn = data?.rows || [];
    const listStatusProcessingNotIn = [KUNN_STATUS.ACTIVATED, KUNN_STATUS.REFUSED, KUNN_STATUS.CANCELLED, KUNN_STATUS.NOT_ELIGIBLE, KUNN_STATUS.TERMINATED];
    if(listKunn && Array.isArray(listKunn) && listKunn.length > 0) {
        for (const kunn of listKunn) {
            if (!listStatusProcessingNotIn.includes(kunn.status)) {
                throw new BadRequestResponse([kunn?.kunn_id], `Has processing KUNN`);
            }
        }
    }
}

async function updateKunnDocument({ files, contractNumber, kunnContractNumber, signedType }) {
    let updatePromises = [];
    for (const file of files) {
        updatePromises.push(documentRepo.updateLoanContractNumberKunn({
            docType: file.docType,
            docId: file.docId,
            contractNumber,
            kunnContractNumber,
            signedType: signedType ?? null
        }));
    }
    const results = await Promise.all(updatePromises);
    return results;
}

module.exports = { validateSubmitKunn, validateContractNumber, checkProcessingKunn, updateKunnDocument }