const helper = require("../utils/helper")
const callbackService = require("../utils/callbackService")
const {caseStatusCode} = require("../const/caseStatus")
const loggingService = require("../utils/loggingService")
const offerRepo = require("../repositories/offer")

function getOfferInput(poolRead, contractNumber) {
	return new Promise(function (resolve, reject) {
		let promiseQuery = []
		let sql1 = "select product_code,request_amt,request_tenor from loan_contract where contract_number = $1"
		promiseQuery.push(poolRead.query(sql1, [contractNumber]))

		let sql2 = "select net_income from loan_main_score where contract_number = $1"
		promiseQuery.push(poolRead.query(sql2, [contractNumber]))

		Promise.all(promiseQuery)
			.then(result => {
				let rs1 = result[0].rows[0]
				let rs2 = result[1].rows[0]
				resolve([rs1.product_code, rs1.request_amt, rs1.request_tenor, rs2.net_income])
			})
			.catch(error => {
				console.log(error)
				reject([])
			})
	})
}

const getTurnOver = async (contractNumber, type, req) => {
	let sql = `SELECT 
            value_of_month AS value FROM loan_turnover_transaction  WHERE contract_number = $1 AND info_type = $2
    ORDER BY month_of_info`
	const data = await req.poolRead.query(sql, [contractNumber, type])
	const arr = data.rows.map(item => item.value)
	return data.rows ? arr : []
}

async function saveOffer(req, poolWrite, contractNumber, kunnId, withdrawAmount, request_tenor, ir, available_amount) {
	const params = await getOfferInput(req.poolRead, contractNumber)
	if (params?.length === 0) {
		return undefined
	}
	const product_code = params[0]
	const di_score = params[3]
	const kunCode = await helper.getKUCode(req.poolRead, kunnId)

	const maxInstallMentSql = "select max_installment from loan_main_score lms where contract_number =$1;"
	const rs = await poolWrite.query(maxInstallMentSql,[contractNumber])
	const maxInstallment = rs.rows[0].max_installment || 0

	const turnOver = await getTurnOver(contractNumber, 'turnover', req)
	const offerInfo = await genOffer(req, kunCode, withdrawAmount, di_score, turnOver, available_amount,maxInstallment)
	const rate = offerInfo.config.rate
	const tenor = offerInfo.config.tenor

	let sql = "insert into loan_offer_selection (contract_number,kunn_id,int_rate,tenor,product_code,offer_amt,request_amt,offer_type) values ($1,$2,$3,$4,$5,$6,$7,$8) returning *"
	let allQuery = []
	offerInfo.offer.forEach(item => {
		allQuery.push(poolWrite.query(sql, [contractNumber, kunnId, rate, tenor, product_code, item.amount, withdrawAmount, item.type]))
	})
	const insertRs = await Promise.all(allQuery)
	return insertRs.map(item => {
		return {
			offerId: item.rows[0].id,
			offerAmount: parseInt(item.rows[0].offer_amt),
			tenor: parseInt(item.rows[0].tenor),
			offerIr: parseFloat(item.rows[0].int_rate),
			method : "Monthly"
		}
	})
}

async function generateOfferList(req, res) {
	const poolWrite = req.poolWrite
	const poolRead =req.poolRead
	const config = req.config
	try {
		const { contractNumber, kunnId, ir, tenor, withdrawAmount } = req.body.data
		const saveOfferRs = await offerRepo.saveKUOffer(contractNumber,kunnId,withdrawAmount,parseFloat(ir),parseFloat(tenor),'STANDARD','',withdrawAmount,tenor)
		
		helper.saveKUStatus(poolWrite,kunnId,caseStatusCode.KKH07)
		//await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KKH07,contractNumber,'system',kunnId)

		const partnerCode = await helper.getPartnerCode(poolRead,contractNumber)
		const currentTimestamp = new Date().getTime()
		const requestID = partnerCode + currentTimestamp
		const callbackBody = {
			"requestId" : requestID,
			"contractNumber" : contractNumber,
			"withdrawId" : kunnId,
			"code" : "OFFER_LISTS",
			targetType : "disbursement-request",
			offer : {
				offerId: saveOfferRs.rows[0].id,
				offerAmount: parseInt(saveOfferRs.rows[0].offer_amt),
				tenor: parseInt(saveOfferRs.rows[0].tenor),
				offerIr: parseFloat(saveOfferRs.rows[0].int_rate),
				method : "Monthly"
			}
			
		}
		callbackService.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)
		return res.status(200).json({
			code : 1,
			msg : "gen offer success."
		})
	}
	catch (error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "gen offer error."
		})
	}
}

module.exports = {
	generateOfferList
}