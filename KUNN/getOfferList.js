const getOfferList = async (req, res) => {
    try {
        const { kunn_id } = req.body
        if (!kunn_id) {
            res.status(200).json({
                code: -1,
                message: 'Missing kunn_id'
            })
        }
        else {
            const sql = "SELECT id,offer_amt,tenor, int_rate FROM loan_offer_selection WHERE kunn_id = $1 AND is_delt=0 "
            const params = [kunn_id]
            const data = await req.poolRead.query(sql, params)
            const sql1 = "SELECT method FROM kunn WHERE kunn_id = $1"
            const data1 = await req.poolRead.query(sql1, params)
            // console.log({data:data,data1:data1})
            if (data.rows.length > 0 && data1.rows.length > 0) {
                return res.status(200).json({
                    code: 1,
                    data: data.rows.map(item => {
                        const offer = {
                            offerId: item.id,
                            offerAmount: parseInt(item.offer_amt),
                            offerTenor: parseInt(item.tenor),
                            offerRate: parseFloat(item.int_rate),
                            offerMethod: parseInt(data1.rows[0].method),
                            monthlyInstallment: 0
                        }
                        return offer
                    }),
                })
            }
            else {
                return res.status(200).json({
                    code: -1,
                    message: "Không tìm thấy khế ước nhận nợ"
                })
            }
        }
    }
    catch {
        res.status(500).json({
            code: -1,
            message: "Service error"
        })
    }

}

module.exports = {
    getOfferList
}