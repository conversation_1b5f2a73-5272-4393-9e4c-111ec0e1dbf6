const common = require("../utils/common")
const {STATUS} = require("../const/caseStatus")

async function getLoanInfo(req,res) {
    try {
        const contractNumber = req.body.contractNumber
        const getHMSql = "select * from loan_contract where contract_number=$1"
        // const getKUsql = "select * from loan_contract lc join kunn k on lc.contract_number = k.contract_number where lc.contract_number =$1 and k.status in ('KKH13','KKH14')"
        const poolRead = req.poolRead
        const rs = await Promise.all([poolRead.query(getHMSql,[contractNumber])])
        // const hmResult = rs[0]
        if(rs[0].rows.length != 0) {
            const limitData = rs[0].rows[0]
            const status = limitData.status
            let loanStatus = 'IN PROCESS'
            if(status == STATUS.ACTIVATED) {
                loanStatus = 'ACTIVE'
            }
            const startDate1 = new Date(limitData.approval_date)
            const startDate = limitData.approval_date
            const endDate = new Date(startDate.setMonth(startDate.getMonth() + 36))

            const url = req.config.basic.lmsMc[req.config.env] + "/lms-mc/v1/merchant-limit/available"
            const body = {
                contractNumber
            }
            const available = await common.postApiV2(url,body)
            const contractLimitData = {
                contractNumber : contractNumber,
                loanStatus : loanStatus,
                creditLimitAmount : parseInt(limitData.approval_amt),
                remainLimitAmount : parseInt(available.data.data.avalibleAmount),
                usedAmount : parseInt(limitData.approval_amt) - parseInt(available.data.data.avalibleAmount),
                redundAmount : 0,
                intRate : limitData.approval_int_rate,
                startDate : startDate1,
                endDate : endDate
            }
            return res.status(200).json({
                code : 1,
                message : "The query contract is successful.",
                loanInfo : [contractLimitData],
                transactionInfo : []
            })
        }
        else {
            return res.status(200).json({
                code : 0,
                message : "Can not find contract info."
            })
        }
    }
    catch(err) {
        console.log(err)
        common.responseErrorPublic(res)
    }
}

module.exports = {
    getLoanInfo
}