
const { getAPI } = require('../../utils/common');
const validator = require('../helpers/validate');
const path = require('path');
const { response } = require('express');

const customMessages = {
    required: 'The :attribute is missing',
}

//getProdSchemeValidate
const getProdSchemeValidate = (req, res, next) => {
    const validationRule = {
        "contractNumber": "required|string",
    }
    validator(req.query, validationRule, customMessages, (err, status) => {
        if (!status) {
            return res.status(200)
                .send({
                    code: 'INVALID REQUEST',
                    message: 'Request is invalid',
                    errors: Object.keys(err.errors).map(item => {
                        const msg = err.errors[item][0]
                        const error = {
                            code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
                            message: msg.search('missing') ? msg : 'Request is invalid',
                        }
                        return error
                    })
                });
        } else {
            next();
        }
    });
}

const getContractInfo = async (contractnumber, req) => {
    const sql = "SELECT id_number,product_code,cust_full_name,request_id,product_code, partner_code,bill_day from loan_contract WHERE contract_number = $1"
    const params = [contractnumber]
    const data = await req.poolRead.query(sql, params)
    if (data.rows) {
        return data.rows[0]
    }
    else {
        return null
    }
}

// createRequestValidate
const createRequestValidate = async (req, res, next) => {
    
    const body = req.body

    const minAmount = 10000000;
    const maxAmount = 500000000;
    if (body.withdrawAmount < minAmount || body.withdrawAmount> maxAmount) {
        res.status(400).json({
            code: "FORMAT",
            message: 'Request is invalid',
            error: 'Invalid withdrawAmount'
        })
        return 
    }
    const contractInfo = await getContractInfo(body.contractNumber, req)
    if (contractInfo) {
        if (body.customerName.toLowerCase() !== contractInfo.cust_full_name.toLowerCase()) {
            res.status(400).json({
                code: "FORMAT",
                message: 'Request is invalid',
                error: 'Invalid customerName'
            })
            return 
        }
        if(!contractInfo.bill_day) {
            if(body.billDay == undefined) {
                res.status(400).json({
                    code: "FORMAT",
                    message: 'Request is invalid',
                    error: 'billDay is missing'
                })
                return 
            }
            else {
                if(![5,10,15,20,25].includes(body.billDay) ) {
                    res.status(400).json({
                        code: "FORMAT",
                        message: 'Request is invalid',
                        error: 'billDay must in 5,10,15,20,25'
                    })
                    return 
                }
            }
        }
        else {
            if(body.billDay != contractInfo.bill_day) {
                res.status(400).json({
                    code: "FORMAT",
                    message: 'Request is invalid',
                    error: 'BillDay must same as previous withdraw Request'
                })
                return 
            }
        }

    }
    else {
        res.status(200).json({
            code: "FORMAT",
            message: 'Request is invalid',
            error: 'Invalid contractNumber'
        })
        return 
    }
    return next()
}

const createRequestVskValidate = async (req, res, next) => {
    
    const body = req.body

    const minAmount = 10000000;
    const maxAmount = 500000000;
    if (body.withdrawAmount < minAmount || body.withdrawAmount> maxAmount) {
        res.status(400).json({
            code: "FORMAT",
            message: 'Request is invalid',
            error: 'Invalid withdrawAmount'
        })
        return 
    }
    const contractInfo = await getContractInfo(body.contractNumber, req)
    if (contractInfo) {
        if (body.customerName.toLowerCase() !== contractInfo.cust_full_name.toLowerCase()) {
            res.status(400).json({
                code: "FORMAT",
                message: 'Request is invalid',
                error: 'Invalid customerName'
            })
            return 
        }
        if(!contractInfo.bill_day) {
            if(body.billDay == undefined) {
                res.status(400).json({
                    code: "FORMAT",
                    message: 'Request is invalid',
                    error: 'billDay is missing'
                })
                return 
            }
            else {
                if(![5,10,15,20,25].includes(body.billDay) ) {
                    res.status(400).json({
                        code: "FORMAT",
                        message: 'Request is invalid',
                        error: 'billDay must in 5,10,15,20,25'
                    })
                    return 
                }
            }
        }
        else {
            return 
        }

    }
    else {
        res.status(200).json({
            code: "FORMAT",
            message: 'Request is invalid',
            error: 'Invalid contractNumber'
        })
        return 
    }
    return next()
}

const getError = (err) => {
    return Object.keys(err.errors).map(item => {
        const msg = err.errors[item][0]
        const error = {
            code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
            message: msg.search('missing') > 0 ? msg : 'Request is invalid',
        }
        return error
    })
}

// selectOfer
const selectOfferValidate = async (req, res, next) => {
    const validationRule = {
        "requestId": "required|string|min:15|max:20",
        "withdrawId": "required|string",
        "selectedOfferId": "required|string",
        "contractNumber": "required|string",
        "listWithdrawDoc": "array",
        "listWithdrawDoc.name": "string",
        "listWithdrawDoc.docid": "string",
    }
    validator(req.body, validationRule, customMessages, async (err, status) => {
        if (!status) {
            return res.status(200)
                .send({
                    code: 'INVALID REQUEST',
                    message: 'Request is invalid',
                    errors: Object.keys(err.errors).map(item => {
                        const msg = err.errors[item][0]
                        const error = {
                            code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
                            message: msg.search('missing') ? msg : 'Request is invalid',
                        }
                        return error
                    })
                });
        } else {
            if (!(await validateOffer(req.body.withdrawId, req.body.contractNumber, req.body.selectedOfferId, req))) {
                return res.status(200).json({
                    code: "FORMAT",
                    message: 'Request is invalid',
                    error: 'Invalid contractNumber or selectedOfferId or withdrawId'
                })
            } else {
                next()
            }
        }
    });

}


const validateOffer = async (kunnId, contractnumber, offerId, req) => {
    const sql = `SELECT * FROM kunn JOIN loan_offer_selection offer 
    ON kunn.kunn_id = offer.kunn_id 
    WHERE kunn.contract_number = $1 AND kunn.kunn_id = $2 AND offer.id = $3`
    const params = [contractnumber, kunnId, offerId]
    const data = await req.poolRead.query(sql, params)
    if (data.rows.length) {
        return true
    }
    else {
        return false
    }
}
// resubmit 
const resubmitValidate = async (req, res, next) => {
    const validationRule = {
        "withdrawId": "required|string",
        "listWithdrawDoc": "required|array",
        "listWithdrawDoc.name": "required|string",
        "listWithdrawDoc.docid": "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            return res.status(200)
                .send({
                    code: 'INVALID REQUEST',
                    message: 'Request is invalid',
                    errors: Object.keys(err.errors).map(item => {
                        const msg = err.errors[item][0]
                        const error = {
                            code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
                            message: msg.search('missing') ? msg : 'Request is invalid',
                        }
                        return error
                    })
                });
        } else {
            next()
        }
    });
}

// updateStatus 
const updateStatus = async (req, res, next) => {
    const validationRule = {
        "kunnId": "required|string",
        "userName": "required|string",
        "role": "required|string",
        "decision": "required|string",
        "taskId": "required|integer",
        "resubmit": "array",
        "reject": "array",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            return res.status(200)
                .send({
                    code: 'INVALID REQUEST',
                    message: 'Request is invalid',
                    errors: Object.keys(err.errors).map(item => {
                        const msg = err.errors[item][0]
                        const error = {
                            code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
                            message: msg.search('missing') ? msg : 'Request is invalid',
                        }
                        return error
                    })
                });
        } else {
            next()
        }
    });
}

const uploadValidation = async (req, res, next) => {
    const config = await getFileConfig(req)
    if(!config) {
        return res.status(200).json({
            code: "ERROR",
            message: "SERVER ERROR"
        })
    }
    if (!req.files) {
        return res.status(200).json({
            code: "MISSING",
            message: "FILE CAN NOT BE NULL"
        })
    }

    const arr = []
    for (const file of req.files) {
        let isValidFile = await checkValidFile(file, config)

        if (!isValidFile) {
            arr.push({
                code: 'INVALID',
                message: "FILE " + file.fieldname + ' IS INVALID NAME OR SIZE'
            })
        }
    }

    console.log("arr", arr);
    if (arr.length) {
        return res.status(200).json({
            code: "MISSING",
            error: arr
        })
    }

    next()
}
const checkValidFile = async (file, config) => {
    console.log("file");

    const { size, fieldname, originalname } = file
    const fileExtension = getFileExtension(originalname)
    if (!config[fieldname]) return false
    return config[fieldname].some(item => item.fileExtension === fileExtension && size <= item.size)
}

const getFileConfig = async (req) => {
    let lb = req.config.basic.product[req.config.env];
    let uri =  req.config.data.productService.getCongig || '/product/v1/getBundleConfig';
    const reponse = await getAPI(lb + uri)
    if (reponse.code == "SUCCESS") {
        return reponse.data
    }
    else return null
}
const getFileExtension = (filename) => {
    if (!filename) return ''
    return filename.split('.').slice(1).join('.')
}

const validContract = async (contractnumber, kunn_id, req) => {
    const sql = "SELECT * from kunn WHERE contract_number = $1 AND kunn_id = $2"
    const params = [contractnumber, kunn_id]
    const data = await req.poolRead.query(sql, params)
    if (data.rows.length) {
        return true
    }
    else {
        return false
    }
}

const resubmitValidation = async (req, res, next) => {
    const validationRule = {
        "listDocResubmit": "required|array",
        "withdrawId" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            return res.status(200)
                .send({
                    code: 'INVALID REQUEST',
                    message: 'Request is invalid',
                    errors: Object.keys(err.errors).map(item => {
                        const msg = err.errors[item][0]
                        const error = {
                            code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
                            message: msg.search('missing') ? msg : 'Request is invalid',
                        }
                        return error
                    })
                });
        } else {
            next();
        }
    });
    next()
}

module.exports = {
    getProdSchemeValidate,
    createRequestValidate,
    selectOfferValidate,
    resubmitValidate,
    updateStatus,
    uploadValidation,
    resubmitValidation,
    createRequestVskValidate
}

