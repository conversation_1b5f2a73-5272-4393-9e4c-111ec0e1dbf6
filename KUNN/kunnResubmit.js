const common = require("../utils/common");
const utils = require("../utils/helper");
const { saveRequest } = require("../utils/loggingService");
const aadService = require("../utils/aadService")
const {roleCode,CONTRACT_TYPE, PARTNER_CODE, DOC_TYPE, PRODUCT_CODE} = require("../const/definition")
const productService = require("../utils/productService")
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")
const {checkContract} = require("../contract/get-contract-info")
const documentRepo = require("../repositories/document")
const { serviceEndpoint } = require('../const/config');
const s3Service = require("../upload_document/s3-service")
const FormData = require('form-data');
const { STATUS } = require("../const/caseStatus");
const { isValidResubmit } = require("../utils/validator/logic-validator");

async function resubmit(req, res) {
    const poolWrite = req.poolWrite
    const poolRead = req.poolRead
    // const config = req.config
    let response
    let { listDocResubmit, withdrawId } = req.body
    const kunnData = await kunnRepo.getKunnData(withdrawId)
    const requestId = utils.genRequestId(PARTNER_CODE.MISA)
    const partnerCode = kunnData?.partner_code
    const waitingResubmitList = await documentRepo.getResubmitListKunn(withdrawId)
    const validResubmit = isValidResubmit(waitingResubmitList,listDocResubmit)
    if(!waitingResubmitList || !validResubmit) {
        return res.status(400).json({
            code : 'INVALID_REQUEST',
            msg : "Invalid list_doc_resubmit",
            data : {
                kunnNumber: withdrawId,
                requestId
            }
        })
    }
    if([PARTNER_CODE.VSK,PARTNER_CODE.MISA,PARTNER_CODE.KOV,PARTNER_CODE.MCAPP,PARTNER_CODE.SPL,PARTNER_CODE.SMA,PARTNER_CODE.SMASYNC].includes(partnerCode)){
        try {
            const contractNumber = await utils.getContractNumber(poolRead,withdrawId)
            let bundleInfo;
            let contractType='';
            let cusType;
            if(partnerCode==PARTNER_CODE.MISA){
                const LCTDocIdIndex = listDocResubmit.findIndex(item => item.docType == DOC_TYPE.LCTKU)
                if(LCTDocIdIndex > -1) {
                    const fileKey = (await documentRepo.findByDocID(listDocResubmit[LCTDocIdIndex].docId)).file_key
                    const dataCheckSign = new FormData();
                    
                    const checkSignUrl = global.config.basic['bss-esigning-service'][global.config.env] + serviceEndpoint.ESIGN.checkValidDigitalSign;
                    let bufferFileCheckSign = (await s3Service.downloadFile(global.config.data,fileKey)).Body;
                    dataCheckSign.append('file',bufferFileCheckSign)
                    const rsCheck = await common.postApiV2(checkSignUrl, dataCheckSign, dataCheckSign.getHeaders());
                    if (rsCheck.data.code !== 0) {
                        return res.status(400).json({
                            code: 'INVALID_REQUEST',
                            msg: "Chữ ký số không hợp lệ.",
                            data: {
                                contractNumber,
                                requestId
                            }
                        })
                    }
                }

                const loanContractData = await loanContractRepo.getLoanContract(contractNumber);
                let contractTypeBundle;
                let baseKunnCode = 'KU_SME_MISA_';
                contractTypeBundle = loanContractData.contract_type==CONTRACT_TYPE.CREDIT_LINE?'HM':'VM';
                contractType = loanContractData.contract_type;
                cusType = loanContractData.cust_type;
                const kunnCode = baseKunnCode+contractTypeBundle+'_'+cusType;
                bundleInfo = await productService.getBundle(global.config,kunnCode,undefined,true);
            }else if(partnerCode===PARTNER_CODE.VSK){
                bundleInfo = await productService.getBundle(global.config,'MASBAS_KU',undefined,true);
            }else if(partnerCode===PARTNER_CODE.KOV || partnerCode===PARTNER_CODE.SPL){
                bundleInfo = await productService.getBundle(global.config,'KIOT_VIET_KU',undefined,true);
            }else if([PARTNER_CODE.MCAPP,PARTNER_CODE.SMA,PARTNER_CODE.SMASYNC].includes(partnerCode)) {
                bundleInfo = await productService.getBundle(global.config,kunnData.kunn_code,undefined,true);
            }
            const newDocList = productService.mapBundleGroup(listDocResubmit,bundleInfo.data)
            const promiseList = []
            const updateResubmitSql = 'update loan_contract_document set is_resubmit=1,doc_group = $1,doc_type=$2 where doc_id = $3' 
            for(const idx in newDocList) {
                const doc = newDocList[idx]
                if(doc.hasOwnProperty("docId") && doc.hasOwnProperty("docName")) {
                    promiseList.push(poolWrite.query(updateResubmitSql,[doc.docGroup,doc.docName,doc.docId]))
                }
                else if(doc.hasOwnProperty("docId") && doc.hasOwnProperty("docType")) {
                    promiseList.push(poolWrite.query(updateResubmitSql,[doc.docGroup,doc.docType,doc.docId]))
                }
                if(doc.hasOwnProperty("doc_id")) {
                    promiseList.push(poolWrite.query(updateResubmitSql,[doc.docGroup,doc.doc_type,doc.doc_id]))
                }
            }
            await Promise.all(promiseList)
            listDocResubmit.forEach(element => {
                updateUploadVSK(poolWrite, element, withdrawId)
            });
            const resubmitRole = await loanContractRepo.getLastStepResubmit(withdrawId)
            if(partnerCode==PARTNER_CODE.MISA){
                let statusSign = 'sign';
                const signData = await checkContract(req.poolRead,withdrawId);
                if(signData.rowCount!=0) statusSign = signData?.rows[0].status;
                let status = `IN_${resubmitRole}_QUEUE`
                if(('SME PARTNER SIGN IN PROGRESS','RESUBMIT').includes(statusSign)){
                    await aadService.pushTaskMcV2(resubmitRole,withdrawId,contractType,STATUS.IN_CP_BEFORE_SIGN_QUEUE);
                    await loanContractRepo.updateKUStatus(STATUS.IN_CP_BEFORE_SIGN_QUEUE,withdrawId)
                }else{
                    await aadService.pushTaskMcV2(resubmitRole,withdrawId,contractType,status);
                    await loanContractRepo.updateKUStatus(status,withdrawId)
                }
            }
            else{
                const status = `IN_${resubmitRole}_QUEUE`;
                await Promise.all([
                    aadService.pushTaskMcV2(resubmitRole,withdrawId,contractType,status),
                    loanContractRepo.updateKUStatus(status,withdrawId)
                ]);
                
                // await aadService.pushTaskMcV2(roleCode.CP,withdrawId,contractType,STATUS.IN_CP_QUEUE);
                // await loanContractRepo.updateKUStatus(STATUS.IN_CP_QUEUE,withdrawId)

            }
            
            response = {
                "code": "SUCCESS",
                "message": "Documents are uploaded successfully",
                listDocResubmit
            }
            saveRequest(poolWrite,req.body,response,contractNumber)
            return res.status(200).json(response)
        }
        catch (err) {
            console.log(err);
            res.status(500).json({
                code: "ERROR",
                message: "Service Error",
            })
        }
    }
    else{
        try {
            const contractNumber = await utils.getContractNumber(poolRead,withdrawId)
            listDocResubmit.forEach(element => {
                updateUpload(poolWrite, element, withdrawId)
            });
            await aadService.pushTaskMc(roleCode.CP,withdrawId,CONTRACT_TYPE.CREDIT_LINE)
    
            response = {
                "code": "SUCCESS",
                "message": "Documents are uploaded successfully",
                listDocResubmit
            }
            saveRequest(poolWrite,req.body,response,contractNumber)
            return res.status(200).json(response)
        }
        catch (err) {
            console.log(err);
            res.status(500).json({
                code: "ERROR",
                message: "Service Error",
            })
        }
    }
}

async function updateUploadVSK(poolRead, object, id) {
    const { docName, docId } = object
    let params = [id, docId]
    let paramsDEl = [1, docName, id]
    let sql = "UPDATE loan_contract_document SET kunn_contract_number = $1 , is_resubmit=1 where doc_id = $2"
    let sqlDel = "UPDATE loan_contract_document SET is_deleted = $1, waiting_resubmit = 0 where doc_type = $2 and kunn_contract_number = $3"
    await poolRead.query(sqlDel, paramsDEl)
    poolRead.query(sql, params)
        .then(result => {
            
        })
        .catch(error => {
            console.log(error)
            console.log("save upload info failure")
        })
}

async function updateUpload(poolRead, object, id) {
    const { docName, docId } = object
    let params = [id, docId]
    let paramsDEl = [1, docName, id]

    let sql = "UPDATE loan_contract_document SET kunn_contract_number = $1 , is_resubmit=1 where doc_id = $2"
    let sqlDel = "UPDATE loan_contract_document SET is_deleted = $1 where doc_type = $2 and kunn_contract_number = $3"
    await poolRead.query(sqlDel, paramsDEl)
    poolRead.query(sql, params)
        .then(result => {
            
        })
        .catch(error => {
            console.log(error)
            console.log("save upload info failure")
        })

}

async function getResubmit(req, res) {
    try {
        const kunnNumber = req.query.kunnNumber
        const resubmitList = await getListResubmit(kunnNumber)
        const resultObj = {}
        const resultList = []

        const kunnData = await kunnRepo.getKunnData(kunnNumber);
        const kunnCode = kunnData?.kunn_code;
        const bundleList = await productService.getBundleV3(req.config,kunnCode);
        let docList = [];
        let docTypeList = [];
        (bundleList?.data ?? []).map(x => {
            docList.push(...(x?.docList ?? []));
        });
        docList.forEach(x =>  {
            docTypeList.push(x.docType);
        })

        for(let idx in resubmitList) {
            let doc = resubmitList[idx]
            if(!resultObj.hasOwnProperty(doc.doc_group)) {
                resultObj[doc.doc_group] = []
            }
            
            resultObj[doc.doc_group].push({
                docType : doc.doc_type,
                resubmitReason : `${doc.mistake_desc} : ${doc.deviation_cmt}`
            })
        }

        for(let key in resultObj) {
            resultList.push({
                docGroup : key,
                docList : resultObj[key]
            })
        }

        (resultList ?? []).forEach(x => {
                (x.docList ?? []).map(y => {
                    y.nameVN = y.docType == 'VAT' ? 'HOÁ ĐƠN VAT' : y.docType == 'PIC' ? 'HÌNH SELFIE' : (docTypeList.includes(y.docType) ? docList.find(m => m.docType == y.docType)?.docVnName : "")
                })
        })
        
        return res.status(200).json({
            code : 1,
            msg : "get resubmit list successfully.",
            data : resultList
        })
    }
    catch(err) {
        console.log(err)
        common.log(`get bundle resubmit error : ${err.message} `,req.query.contractNumber)
        return res.status(500).json({
            code : 1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
}

async function getListResubmit(kunnNumber) {
    try {
        const poolRead = global.poolRead
        const selectSql = `select * from loan_contract_document lcd 
        left join loan_manual_decision lmd on lcd.contract_number = lmd.contract_number 
        inner join kunn k on k.kunn_id = lcd.kunn_contract_number 
        where waiting_resubmit = 1 and lcd.kunn_contract_number = $1 ;`
        const result = await poolRead.query(selectSql,[kunnNumber])
        // console.log('rs',result)
        if(result.rowCount == 0) {
            return false
        }
        // console.log('rs', result.rows)
        return result.rows
    }
    catch(err) {
        common.log(`get resubmit list error : ${err.message}`,kunnNumber)
        return false
    }
}

module.exports = {
    resubmit,
    getResubmit
}