// validation.js
const Joi = require("joi");

const fileSchema = Joi.object({
  doc_type: Joi.string().required(),
  doc_id: Joi.string().required(),
});

const invoiceDetailSchema = Joi.object({
  invoice_order: Joi.number().required(),
  invoice_number: Joi.string().required(),
  invoice_date: Joi.string().required(),
  invoice_amount: Joi.number().required(),
  payment_date: Joi.string().required(),
  is_invoice_valid: Joi.boolean().required(),
  purchaser_tax_code: Joi.string().required(),
  note: Joi.string().required(),
  files: Joi.array().items(fileSchema).required(),
});

const disburRequestSchema = Joi.object({
  request_id: Joi.string().required(),
  partner_code: Joi.string().required(),
  contract_number: Joi.string().required(),
  beneficiary_name: Joi.string().required(),
  bank_account: Joi.string().required(),
  bank_code: Joi.string().required(),
  bank_branch_code: Joi.string().required(),
  total_receivable_amount: Joi.number().required(),
  with_draw_amount: Joi.number().required(),
  invoice_details: Joi.array().items(invoiceDetailSchema).required(),
  files: Joi.array().items(fileSchema).required(),
  ec_signature: Joi.string().optional() ,
});

module.exports = { disburRequestSchema, invoiceDetailSchema };
