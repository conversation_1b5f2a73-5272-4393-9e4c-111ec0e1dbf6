// validation.js
const {
  PARTNER_CODE,
  DEFAULT_PAYMENT_DATE,
} = require("../../const/definition");
const Joi = require("joi");

const fileSchema = Joi.object({
  doc_type: Joi.string().required(),
  doc_id: Joi.string().required(),
});

const invoiceDetailSchema = Joi.object({
  invoice_number: Joi.string().required(),
  invoice_date: Joi.date().less("now").required(),
  invoice_amount: Joi.number().required(),
  is_invoice_valid: Joi.boolean().required(),
  files: Joi.array().items(fileSchema).required(),
});

const disbursementInfoSchema = Joi.object({
  account_name: Joi.string().required(),
  account_number: Joi.string().required(),
  bank_code: Joi.string().required(),
  branch_code: Joi.string().required(),
  amount: Joi.number().required(),
  transfer_content: Joi.string().required(),
  invoices: Joi.array().items(invoiceDetailSchema).required(),
  documents: Joi.array().items(fileSchema).required(),
});

const disbursementRequestSchema = Joi.object({
  request_id: Joi.string().required(),
  partner_code: Joi.valid(PARTNER_CODE.BZHM).required(),
  contract_number: Joi.string().required(),
  total_withdrawal_amount: Joi.number().required(),
  monthly_payment_date: Joi.string()
    .valid(...DEFAULT_PAYMENT_DATE)
    .required(),
  disbursements: Joi.array().items(disbursementInfoSchema).required(),
});

module.exports = { disbursementRequestSchema, invoiceDetailSchema };
