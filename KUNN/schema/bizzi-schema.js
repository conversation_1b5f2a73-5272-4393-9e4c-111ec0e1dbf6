// validation.js
const Joi = require("joi");
const { LIST_PARTNER_CODE } = require("../../const/definition");
const { request } = require("express");

const masterDataSchema = Joi.object({
  code_type: Joi.string().valid(
    "ISSUE_PLACE_VN",
    "LOAN_PURPOSE",
    "BANK",
    "MARRIED_STATUS",
    "PROVINCE",
    "DISTRICT",
    "WARD",
    "BUSINESS_TYPE",
    "SME_EMPLOYMENT_TYPE_4",
    "PROFESSION",
    "NEW_PROVINCE",
    "NEW_WARD",
    "MANAGEMENT_EXPERIENCE",
    "LOAN_PURPOSE_DETAIL",
    "ENTERPRISE_TYPE",
    "IDENTITY_TYPE"
  ).required(),
});

const presignUploadDocSchema = Joi.object({
  request_id: Joi.string().required(),
  file_name: Joi.string().required(),
  doc_type: Joi.string().required(),
  partner_code: Joi.string()
    .valid(...LIST_PARTNER_CODE)
    .required()
    .messages({ "any.only": "partner_code không hợp lệ" }),
}).unknown(true);

const presignDocSchema = Joi.object({
  doc_type: Joi.array().required(),
  contract_number: Joi.string().required().messages({
    "any.required": "contract_number is missing from parameters",
  }),
  partner_code: Joi.string()
    .valid(...LIST_PARTNER_CODE)
    .required()
    .messages({ "any.only": "partner_code không hợp lệ" }),
}).unknown(true);

const presignKUUNDocSchema = Joi.object({
  doc_type: Joi.array().required(),
  contract_number: Joi.string().required().messages({
    "any.required": "contract_number is missing from parameters",
  }),
  debt_contract_number: Joi.string().required().messages({
    "any.required": "contract_number is missing from parameters",
  }),
  partner_code: Joi.string()
    .valid(...LIST_PARTNER_CODE)
    .required()
    .messages({ "any.only": "partner_code không hợp lệ" }),
});

module.exports = { masterDataSchema, presignDocSchema, presignKUUNDocSchema, presignUploadDocSchema };
