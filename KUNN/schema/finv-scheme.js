const { LIST_PARTNER_CODE } = require("../../const/definition");
const Joi = require("joi/lib");

const fileSchema = Joi.object({
  docType: Joi.string().required(),
  docId: Joi.string().required(),
});

const masterDataFinvSchema = Joi.object({
  codeType: Joi.string().valid(
    "ISSUE_PLACE_VN",
    "LOAN_PURPOSE",
    "BANK",
    "MARRIED_STATUS",
    "PROVINCE",
    "DISTRICT",
    "WARD",
    "BUSINESS_TYPE",
    "SME_EMPLOYMENT_TYPE_4",
    "PROFESSION",
    "NEW_PROVINCE",
    "NEW_WARD",
    "MANAGEMENT_EXPERIENCE",
    "LOAN_PURPOSE_DETAIL",
    "ENTERPRISE_TYPE",
    "IDENTITY_TYPE",
    "BANK_BRANCH"
  ).required(),
  codeParent: Joi.string().optional(),
});

const submitKunnSchema = Joi.object({
  partnerCode: Joi.string().optional(),
  requestId: Joi.string().required(),
  contractNumber: Joi.string().required(),
  beneficiaryName: Joi.string().required(),
  bankAccount: Joi.string().required(),
  bankCode: Joi.string().required(),
  bankBranchCode: Joi.string().optional(),
  orderAmount: Joi.number().required(),
  tenor: Joi.number().required(),
  docs: Joi.array().items(fileSchema).required(),
});

const presignUploadDocFinvSchema = Joi.object({
  requestId: Joi.string().required(),
  fileName: Joi.string().required(),
  docType: Joi.string().required(),
  partnerCode: Joi.string()
    .valid(...LIST_PARTNER_CODE)
    .required()
    .messages({ "any.only": "partnerCode không hợp lệ" }),
}).unknown(true);

module.exports = { submitKunnSchema, masterDataFinvSchema, presignUploadDocFinvSchema };