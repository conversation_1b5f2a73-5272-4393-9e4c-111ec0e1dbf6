const { config } = require("aws-sdk");
const common = require("../utils/common");
const utils = require("../utils/helper")
const callbackService = require("../utils/callbackService");
const { saveRequest } = require("../utils/loggingService");
const { STATUS} = require("../const/caseStatus")
const aadService = require("../utils/aadService")
const {CONTRACT_TYPE, PARTNER_CODE, roleCode} = require("../const/definition")
const documentRepo = require("../repositories/document")
const productService = require("../utils/productService")
const loanContractRepo = require("../repositories/loan-contract-repo")

async function selectOffer(req, res) {
	const poolRead = req.poolRead
	const poolWrite = req.poolWrite
	const config = req.config
	let response
	try {
		const kunn_id = req.body.withdrawId
		const id = req.body.selectedOfferId;

		const { listWithdrawDoc, withdrawId, contractNumber, partnerCode } = req.body
		let sql = `UPDATE loan_offer_selection SET is_selected = 1 
				WHERE id = $1 and kunn_id = $2`
		const resultQ = await poolWrite.query(sql, [id, kunn_id])
		if(!resultQ.rowCount) {
			response = {
				code: "INVALID",
				data: "invalid withdrawId"
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(200).json(response)
		}
		
		const bundleInfo = await productService.getBundle(global.config,'KIOT_VIET_KU',undefined,true)
		let listDocs = productService.mapBundleGroupKOV(listWithdrawDoc,bundleInfo.data)
		await documentRepo.saveUploadedDocumentKunn(poolWrite,withdrawId,listDocs)
		const isValidDocument = await validDocument(listWithdrawDoc,kunn_id,poolRead,config)
		
		if(!isValidDocument) {
			response = {
				"statusCode": "201",
				"body": {
					"code": "MISSING",
					"message": "Not enough document",
				}
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(200).json(response)	
		}

		const KUStatus = await utils.getOneKuStatus(poolRead,kunn_id)
		if(KUStatus !== 'KKH07') {
			return res.status(200).json({
				"statusCode": "501",
				"body": {
				"code": "INVALID",
				"message": "Contract not valid to select offer.",
					}
			})
		}

		const GET_KUNN = "SELECT contract_number FROM kunn WHERE kunn_ID = $1"
		const UPDATE = "UPDATE loan_contract_document set kunn_contract_number = $1 where doc_id = $2"
		const UPDATE_DOC_GROUP = "update loan_contract_document set doc_group = CONCAT('KU', doc_group) where doc_id = $1;"
		
		try {
			poolWrite.query(GET_KUNN, [kunn_id])
			listWithdrawDoc.map(item => {
				poolWrite.query(UPDATE, [kunn_id, item.docId])
				poolWrite.query(UPDATE_DOC_GROUP, [item.docId])
			})
			//await aadService.pushTaskKU(roleCode.CP,kunn_id,CONTRACT_TYPE.CREDIT_LINE)
			const _roleCode = partnerCode == PARTNER_CODE.VSK ? roleCode.CP : roleCode.SS;
			const _status = partnerCode == PARTNER_CODE.VSK ? STATUS.IN_CP_QUEUE : STATUS.IN_SS_QUEUE;
			await aadService.pushTaskMcV2(_roleCode,kunn_id,CONTRACT_TYPE.CREDIT_LINE,_status);
			await loanContractRepo.updateKUStatus(_status,kunn_id);
		} catch (error) {
			console.log(error)
			res.status(error.statusCode || 500).json({ message: error.message })
		}
		callbackService.callbackRecieved(poolWrite,config,contractNumber,'disbursement-request',withdrawId)
		response = {
			code: "RECEIVED",
			message: "The offer selection request is received",
			data: {
				contractNumber,
				withdrawId
			}
		}

		saveRequest(poolWrite,req.body,response,contractNumber)
		return res.status(200).json(response)
	} catch (error) {
		res.status(200).json({
			code: -1,
			msg: "Sever Error"
		})
	}
}

const validData = async (listWithdrawDoc, selectedOfferId,withdrawId, contractNumber, requestId, partnerCode, req) => {
	let data = {
		code: 1,
		message: 'correct '
	}
	const body = { listWithdrawDoc, selectedOfferId, withdrawId, contractNumber, requestId, partnerCode }

	Object.keys(body).forEach((key) => {
		if (!body[key]) {
			data = {
				code: -1,
				message: "Missing " + key
			}
			return data;
		}
	})
	if(data.code === -1) return data
	const sql = `SELECT * FROM kunn JOIN loan_contract ON kunn.contract_number = loan_contract.contract_number and
	kunn.kunn_id = $1 and loan_contract.partner_code = $2`
	const dataQuery = await req.poolRead.query(sql, [withdrawId,partnerCode])
	if (!dataQuery.rows.length) {
		data = {
			code: -1,
			message: "Không tìm thấy thông tin khế ước nhận nợ"
		}
	}
	else {
		data = {
			code: 1,
			message: "Tim thấy thông tin khế ước nhận nợ"
		}
	}
	if (listWithdrawDoc.length <= 0) {
		data = {
			code: -1,
			message: "Invalid listWithdrawDoc property "
		}
	}
	return data
}

async function validDocument(docIdList,KUNumber,poolRead,config) {
	const KUCode = await utils.getKUCode(poolRead,KUNumber)
	const productUrl = config.basic.product[config.env] + config.data.productService.getKUBundleList + '?kunnCode=' + KUCode
	const KUBundleResponse = await common.getAPI(productUrl)
	const KUBundle = KUBundleResponse.data
	const uploadBunldeRs = await getBundleCount(poolRead,docIdList)
	const uploadBunlde = uploadBunldeRs.rows
	return compareTwoBundleList(uploadBunlde,KUBundle)

}

async function getBundleCount(poolRead,docIdList) {
	const preSql = "select doc_group,count(doc_group) from loan_contract_document where doc_id in ("
	const postSql = ")group by doc_group" ;
	let inSql = "";
	docIdList.forEach(id => {
		inSql += "'" + id.docId + "'" + ","
	})
	inSql = inSql.slice(0,-1)
	const sql = preSql + inSql + postSql
	return await poolRead.query(sql)
}

function compareTwoBundleList(uploadedBundle,requriedBundle) {
	if(uploadedBundle.length != Object.keys(requriedBundle).length ) {
		return false
	}
	else {
		let totalDoc1 = 0
		for (let key in requriedBundle) {
			if(requriedBundle.hasOwnProperty(key)) {
				totalDoc1 += requriedBundle[key].minDocs
			}
		}
		const totalDoc2 = uploadedBundle.map(x => parseInt(x.count)).reduce((a, b) => a + b, 0)
		if(totalDoc2 < totalDoc1) {
			return false
		}
	}
	return true
}

module.exports = {
	selectOffer
}