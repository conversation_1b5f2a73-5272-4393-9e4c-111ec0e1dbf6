const common = require("../utils/common")

async function getProdScheme(req, res) {
	const config = req.config
	const { contractNumber } = req.query
	const prodUri = "/product/v1/getProductConfig";
	const productCode = await getProductCode(contractNumber, req.poolRead)
	const productUrl = config.basic.product[config.env] + prodUri + '?productCode=' + productCode
	const getBundleUrl = config.basic.product[config.env] + '/product/v1/getKunnBundle?productcode=' + productCode
	try {
		const config = await common.getAPI(productUrl)
		const bundles = await common.getAPI(getBundleUrl)
		if (!config.data) {
			res.status(500).json({
				message: "The request is invalid",
				code: "INVALID_REQUEST"
			})
		}
		else {
			const data = []
			for (const item of bundles.data) {
				const {bundleCode,bundleId,numOfDoc} = item
				const getDocUrl = config.basic.masterData[config.env] + '/masterdata/v1/getDocumentByBundle?bundlecode=' + bundleCode
				const result = await common.getAPI(getDocUrl)
				data.push({
					bundleName : bundleCode,
					bundleCode: bundleId,
					minDoc: +numOfDoc,
					doclist: result.data
				})
			}
		
			res.status(200).json({
				code: "RECEIVED",
				message: "The request is received",
				data:
				{
					tenor: +config.data.tenor,
					ir: +config.data.rate,
					documentCollecting: data
				}
			})
		}
	}
	catch (error) {
		console.log({error});
		res.status(500).json({
			message: "Server Error",
			code: "INVALID_REQUEST "
		})
	}
}

const getProductCode = async (contractNumber, poolRead) => {
	try {
		const sql = `SELECT product_code FROM loan_contract WHERE contract_number = $1`
		const params = [contractNumber]
		const data = await poolRead.query(sql, params)

		if (!data.rows[0] && !data.rows[0].product_code) {
			return []
		}
		else {
			return data.rows[0].product_code
		}
	}
	catch {
		return []
	}
}

module.exports = {
	getProdScheme
}