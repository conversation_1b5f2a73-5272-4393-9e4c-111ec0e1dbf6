const genContract = require("../contract/gen_contract");
const common = require("../utils/common");
const moment = require("moment-timezone");
moment().tz("Asia/Ho_Chi_Minh").format();
const libre = require("libreoffice-convert");
const { getPlace, getBankName, getValueCode, getValueCode_v3 } = require("../utils/masterdataService");
const fs = require("fs");
const helper = require("../utils/helper");
const smsService = require("../utils/smsService");
const callbackService = require("../utils/callbackService");
const callbackServiceV2 = require("../services/callback-service");
const { resubmit } = require("./kunnResubmit");
const { caseStatusCode, STATUS, KUNN_STATUS, CALLBACK_STAUS, MISA_CALLBACK_STATUS } = require("../const/caseStatus");
const loggingService = require("../utils/loggingService");
const { PARTNER_CODE, roleCode, CONTRACT_TYPE, STATUS_KUNN, MANUAL_DECISION, SMA_PAYMENT_METHOD } = require("../const/definition");
const aadService = require("../utils/aadService");
const documentRepo = require("../repositories/document");
const kunnRepo = require("../repositories/kunn-repo");
const utils = require("../utils/helper");
const loanContractRepo = require("../repositories/loan-contract-repo");
const { serviceEndpoint } = require("../const/config");
const { sendNotification } = require("../services/notification-service");
const { getDataByContractNumber } = require("../repositories/kunn-prepare-attribute-repo");
const kunnPrepareAttributeRepo = require("../repositories/kunn-prepare-attribute-repo");

async function updateStatus(req, res) {
  try {
    const poolRead = req.poolRead;
    const poolWrite = req.poolWrite;
    const config = req.config;
    const kunnNumber = req.body.kunnId; // kunn id
    const contractNumber = await helper.getContractNumber(poolRead, kunnNumber); // contract number
    const userName = req.body.userName;
    const userRole = req.body.role;
    const step = userRole;
    const decision = req.body.decision;
    const taskId = req.body.taskId;
    const resubmitDoc = req.body.resubmit;
    const listRejectCode = req.body.reject;
    const cancel = req.body.cancel;
    // const aadUri = req.config.data.aadService.pushTask;
    const aadCompletedUri = req.config.data.aadService.completed;
    // const aadUrl = config.basic.aad[config.env] + aadUri;
    const aadCompletedUrl = config.basic.aad[config.env] + aadCompletedUri;
    // const partner_code = await helper.getPartnerCode(poolRead,contractNumber)
    const partner_code = (await kunnRepo.getKunnData(kunnNumber))?.partner_code;
    const checked = req.body.checkedDoc;
    const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber);
    const kunnDatas = await kunnRepo.getKunnData(kunnNumber);
    await updateKunnStatus(req, kunnNumber, decision);
    // if(partner_code != PARTNER_CODE.VSK && partner_code != PARTNER_CODE.MISA && partner_code != PARTNER_CODE.MCAPP){
    //     if (decision === 'APPROVE' && userRole == roleCode.SS && [PARTNER_CODE.KOV,PARTNER_CODE.SPL].includes(partner_code)) {
    //         await common.postAPI(aadCompletedUrl, { taskId }).then(rs => console.log("complete SS task")).catch(error => console.log(error));
    //         await Promise.all([
    //             aadService.pushTaskMcV2(roleCode.CP,kunnNumber,CONTRACT_TYPE.CREDIT_LINE,STATUS.IN_CP_QUEUE),
    //             loanContractRepo.updateKUStatus(STATUS.IN_CP_QUEUE,kunnNumber)
    //         ]);
    //     } else {
    //         if (decision === 'APPROVE' && userRole == 'CP') {
    //             saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, [])
    //             await callbackService.callbackAprroved(poolWrite,config,contractNumber,'disbursement-request',kunnNumber)
    //             await helper.saveKUStatus(poolWrite,kunnNumber,caseStatusCode.KCP05)

    //             //await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KCP05,contractNumber,userName,kunnNumber)
    //             await genKunn(req,contractNumber)
    //         } else
    //             if (resubmitDoc !== undefined && decision === 'RESUBMIT') {
    //                 helper.saveKUStatus(poolWrite,kunnNumber,caseStatusCode.KCP06)
    //                 //await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KCP06,contractNumber,userName,kunnNumber)
    //                 saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc)
    //                 const partnerCode = await helper.getPartnerCode(poolRead,contractNumber)
    //                 let callbackBody = {
    //                     contractNumber : contractNumber,
    //                     withdrawId : kunnNumber,
    //                     code : "RESUBMIT",
    //                     targetType : "disbursement-request",
    //                     message : "The document is required to reupload for further check.",
    //                     resubmit : {}
    //                 }

    //                 let resubmitBody = []
    //                 for(let i =0; i< resubmitDoc.length; i++) {
    //                     const element = resubmitDoc[i]
    //                     const docName = await helper.getDocName(poolRead,element.docId)
    //                     resubmitBody.push({
    //                         docName : docName,
    //                         mistakeCode : element.mistakeCode,
    //                         mistakeDes : element.mistakeDes
    //                     })
    //                 }

    //                 callbackBody.resubmit.listDocResubmit = resubmitBody
    //                 await callbackService.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)

    //             } else
    //                 if (listRejectCode !== undefined && decision === 'REJECT') {
    //                     await helper.saveKUStatus(poolWrite,kunnNumber,caseStatusCode.KCP04)
    //                     //await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KCP04,contractNumber,userName,kunnNumber)
    //                     saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode)
    //                     callbackService.callbackReject(poolWrite,config,contractNumber,"disbursement-request",kunnNumber)
    //                 }
    //                 else if (cancel !== undefined && decision === 'CANCEL') {
    //                     await helper.saveKUStatus(poolWrite,kunnNumber,caseStatusCode.KCP07)
    //                     //await loggingService.saveCaseStatus(poolWrite,caseStatusCode.KCP07,contractNumber,userName,kunnNumber)
    //                     saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel)
    //                     callbackService.callbackCancel(poolWrite,config,contractNumber,"disbursement-request",kunnNumber)
    //                 }
    //                 else {
    //                     res.status(200).json({
    //                         code: "INVALID",
    //                         message: "Invalid parameter"
    //                     })
    //                 }
    //                 await common.postAPI(aadCompletedUrl, { taskId }).then(rs => console.log("complete CE task")).catch(error => console.log(error));
    //     }
    // }
    if (partner_code == PARTNER_CODE.MISA) {
      const contractNumber = await kunnRepo.getContractByKU(kunnNumber);
      const contractData = await kunnRepo.getKunnData(kunnNumber);
      const loanContractData = await loanContractRepo.getLoanContract(contractNumber);
      const kunnStatus = contractData?.status;
      let contractType = CONTRACT_TYPE.CREDIT_LINE;
      let msg;
      let phoneNumberRs;
      const smsUrl = config.data.smsService.sendSMS;
      const isEnable = config.data.smsService.useSMS;

      if (loanContractData?.contract_type == CONTRACT_TYPE.CASH_LOAN) contractType = CONTRACT_TYPE.CASH_LOAN;
      if (userRole == roleCode.SS) {
        switch (decision) {
          case MANUAL_DECISION.APPROVE:
            saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, []);
            await common
              .postAPI(aadCompletedUrl, { taskId })
              .then(async () => {
                await aadService.pushTaskMcV2(roleCode.CP, kunnNumber, contractType, STATUS.IN_CP_QUEUE);
              })
              .catch((error) => console.log(error));
            await loanContractRepo.updateKUStatus(STATUS.IN_CP_QUEUE, kunnNumber);
            break;
          case MANUAL_DECISION.RESUBMIT:
            msg = config.data.smsService.resubmitSmeKunnMsg;
            msg = msg.replace("kunn", kunnNumber);
            phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
            try {
              if (phoneNumberRs !== undefined) {
                if (isEnable) {
                  await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
                }
              }
            } catch (error) {
              console.log(error);
            }
            helper.saveKUStatus(poolWrite, kunnNumber, STATUS.SS_RESUBMIT);
            saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc);
            callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.RESUBMIT_KU, resubmitDoc, undefined, kunnNumber);
            documentRepo.saveCheckedDocV2(resubmitDoc, userRole);
            await common
              .postAPI(aadCompletedUrl, { taskId })
              .then()
              .catch((error) => console.log(error));
            break;
          case MANUAL_DECISION.REJECT:
            msg = config.data.smsService.cancelSmeKunnMsg;
            msg = msg.replace("kunn", kunnNumber);
            phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
            try {
              if (phoneNumberRs !== undefined) {
                if (isEnable) {
                  await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
                }
              }
            } catch (error) {
              console.log(error);
            }
            await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
            await callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.REJECT_KUNN, undefined, undefined, kunnNumber);
            saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
            await common
              .postAPI(aadCompletedUrl, { taskId })
              .then()
              .catch((error) => console.log(error));
            break;
          case MANUAL_DECISION.CANCEL:
            if (cancel !== undefined) {
              msg = config.data.smsService.cancelSmeKunnMsg;
              msg = msg.replace("kunn", kunnNumber);
              phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
              try {
                if (phoneNumberRs !== undefined) {
                  if (isEnable) {
                    await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
                  }
                }
              } catch (error) {
                console.log(error);
              }
              await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
              await callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.CANCEL_KUNN, undefined, undefined, kunnNumber);
              saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
              await common
                .postAPI(aadCompletedUrl, { taskId })
                .then()
                .catch((error) => console.log(error));
            }
            break;
          default:
            res.status(200).json({
              code: "INVALID",
              message: "Invalid parameter",
            });
            break;
        }
      }
      if (userRole == "CP") {
        if (decision == "APPROVE") {
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, []);
          if (kunnStatus === STATUS.IN_CP_DIS_QUEUE) {
            common
              .postAPI(aadCompletedUrl, { taskId })
              .then()
              .catch((error) => console.log(error));
            await kunnRepo.updateKUStatus(kunnNumber, KUNN_STATUS.ACTIVATED_WITH_DIS_DOCS);
          } else if (kunnStatus === STATUS.IN_CP_BEFORE_SIGN_QUEUE) {
            const signUri = "/esigning/internal/misa/sign";
            const signUrl = config.basic["bss-esigning-service"][config.env] + signUri;
            const contractData = await documentRepo.getAllLctDocument(kunnNumber, true);
            const fileKey = contractData?.file_key;
            const body = {
              contractNumber: kunnNumber,
              filePath: fileKey,
            };
            common
              .postAPI(aadCompletedUrl, { taskId })
              .then()
              .catch((error) => console.log(error));
            const rsSign = await common.postApiV2(signUrl, body);
            if (rsSign.data.code == 0) {
              msg = config.data.smsService.activeSmeKunnMsg;
              msg = msg.replace("kunn", kunnNumber);
              phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
              try {
                if (phoneNumberRs !== undefined) {
                  if (isEnable) {
                    await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
                  }
                }
              } catch (error) {
                console.log(error);
              }

              return res.status(200).json({
                code: 0,
                message: "ACTIVE CONTRACT SUCCESS",
              });
            } else {
              return res.status(200).json({
                code: -1,
                message: "ACTIVE CONTRACT FAILD",
              });
            }
          } else {
            // await common.postAPI(aadCompletedUrl, { taskId }).then(async () => {
            // await aadService.pushTaskMcV2(roleCode.CE,kunnNumber,contractType,STATUS.IN_CE_QUEUE)
            // }).catch(error => console.log(error))
            // await loanContractRepo.updateKUStatus(STATUS.IN_CE_QUEUE,kunnNumber)

            callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.APPROVE_KUNN_FOR_SIGN, undefined, undefined, kunnNumber);
            await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.WAITING_TO_BE_SIGNED);
            await genKunn(req, contractNumber);
            await common
              .postAPI(aadCompletedUrl, { taskId })
              .then((rs) => console.log("complete CP task"))
              .catch((error) => console.log(error));
          }
        } else if (decision === "RESUBMIT") {
          msg = config.data.smsService.resubmitSmeKunnMsg;
          msg = msg.replace("kunn", kunnNumber);
          phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
              }
            }
          } catch (error) {
            console.log(error);
          }
          helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CP_RESUBMIT);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc);
          callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.RESUBMIT_KU, resubmitDoc, undefined, kunnNumber);
          await documentRepo.saveCheckedDocV2(resubmitDoc, userRole);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
        } else if (decision === "REJECT") {
          msg = config.data.smsService.cancelSmeKunnMsg;
          msg = msg.replace("kunn", kunnNumber);
          phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
              }
            }
          } catch (error) {
            console.log(error);
          }
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
          await callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.REJECT_KUNN,  undefined, undefined, kunnNumber);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
        } else if (cancel !== undefined && decision === "CANCEL") {
          msg = config.data.smsService.cancelSmeKunnMsg;
          msg = msg.replace("kunn", kunnNumber);
          phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
              }
            }
          } catch (error) {
            console.log(error);
          }
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
          await callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.CANCEL_KUNN, undefined, undefined, kunnNumber);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
        } else {
          res.status(200).json({
            code: "INVALID",
            message: "Invalid parameter",
          });
        }
      } else if (userRole == "CE") {
        if (decision === "APPROVE") {
          callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.APPROVE_KUNN_FOR_SIGN,undefined, undefined, kunnNumber);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, []);
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.WAITING_TO_BE_SIGNED);
          await genKunn(req, contractNumber);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else if (decision === "SAVE") {
          if (checked !== undefined) {
            await documentRepo.saveCheckedDoc(checked, userRole);
          }
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, checked);
        } else if (decision === "REJECT") {
          msg = config.data.smsService.cancelSmeKunnMsg;
          msg = msg.replace("kunn", kunnNumber);
          phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
              }
            }
          } catch (error) {
            console.log(error);
          }
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
          callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.REJECT_KUNN,  undefined, undefined, kunnNumber);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else if (cancel !== undefined && decision === "CANCEL") {
          msg = config.data.smsService.cancelSmeKunnMsg;
          msg = msg.replace("kunn", kunnNumber);
          phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
              }
            }
          } catch (error) {
            console.log(error);
          }
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
          callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.CANCEL_KUNN,  undefined, undefined, kunnNumber);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else if (decision === "RESUBMIT" && resubmitDoc !== undefined) {
          msg = config.data.smsService.resubmitSmeKunnMsg;
          msg = msg.replace("kunn", kunnNumber);
          phoneNumberRs = await utils.getPhoneNumberSme(contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true);
              }
            }
          } catch (error) {
            console.log(error);
          }
          helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CE_RESUBMIT);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc);
          callbackServiceV2.callbackPartner(contractNumber, partner_code, CALLBACK_STAUS.RESUBMIT_KU,  resubmitDoc, undefined, kunnNumber);
          await documentRepo.saveCheckedDocV2(resubmitDoc, userRole);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else {
          res.status(200).json({
            code: "INVALID",
            message: "Invalid parameter",
          });
        }
      }
    } else if (partner_code == PARTNER_CODE.VSK) {
      //vsk
      if (userRole == "CP") {
        if (decision == "APPROVE") {
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, []);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
          await aadService.pushTaskMcV2(roleCode.CE, kunnNumber, CONTRACT_TYPE.CREDIT_LINE, STATUS.IN_CE_QUEUE);
          await loanContractRepo.updateKUStatus(STATUS.IN_CE_QUEUE, kunnNumber);
        } else if (decision === "RESUBMIT") {
          const smsUrl = config.data.smsService.sendSMS;
          const isEnable = config.data.smsService.useSMS;
          let msg = config.data.smsService.resubmitKunnMsg;
          msg = msg.replace("kunnNumber", kunnNumber);
          const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs);
              }
            }
          } catch (error) {
            console.log(error);
          }
          helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CP_RESUBMIT);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc);
          await documentRepo.saveCheckedDocV2(resubmitDoc, userRole);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
        } else if (decision === "REJECT") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
        } else if (cancel !== undefined && decision === "CANCEL") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
        } else {
          res.status(200).json({
            code: "INVALID",
            message: "Invalid parameter",
          });
        }
      } else if (userRole == "CE") {
        if (decision === "APPROVE") {
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, []);
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.WAITING_TO_BE_SIGNED);
          await genKunn(req, contractNumber);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else if (decision === "SAVE") {
          if (checked !== undefined) {
            await documentRepo.saveCheckedDoc(checked, userRole);
          }
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, checked);
        } else if (decision === "REJECT") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else if (cancel !== undefined && decision === "CANCEL") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CE task"))
            .catch((error) => console.log(error));
        } else {
          res.status(200).json({
            code: "INVALID",
            message: "Invalid parameter",
          });
        }
      }
    } else if ([PARTNER_CODE.MCAPP, PARTNER_CODE.KOV, PARTNER_CODE.SPL, PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(partner_code)) {
      //MCA
      const requestId = kunnDatas?.request_id;
      const isMcaKunn = helper.isMcaKunn(requestId);
      if (userRole == roleCode.CP) {
        if (decision == MANUAL_DECISION.APPROVE) {
          // const isMcaKunn = helper.isMcaKunn(requestId);
          if (isMcaKunn) {
            //call api ký mới
            const signUrl = global.config.basic["bss-esigning-service"][global.config.env] + serviceEndpoint.ESIGN.mcaKunnSign;
            const formData = {
              contractNumber: kunnNumber,
            };
            await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.WAITING_TO_BE_SIGNED);
            const rsSign = await common.postApiV2(signUrl, formData);
            if (rsSign?.data?.code != 0) {
              helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
              callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.CANCELLED);
            }
          } else {
            await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.WAITING_TO_BE_SIGNED);
            await genKunn(req, contractNumber);
          }
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, []);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete CP task"))
            .catch((error) => console.log(error));
        } else if (decision === "RESUBMIT") {
          helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CP_RESUBMIT);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc);
          if (isMcaKunn) callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.RESUBMIT, "", "");
          await Promise.all([
            documentRepo.saveCheckedDoc(checked, userRole),
            documentRepo.saveCheckedDocV2(resubmitDoc, userRole),
            common
              .postAPI(aadCompletedUrl, { taskId })
              .then()
              .catch((error) => console.log(error)),
          ]);
          const smsUrl = config.data.smsService.sendSMS;
          const isEnable = config.data.smsService.useSMS;
          let msg = config.data.smsService.resubmitKunnMsg;
          if (partner_code == PARTNER_CODE.SMA || partner_code == PARTNER_CODE.SMASYNC) msg = config.data.smsService.resubmitKunnMcaMsg;
          const isSendNotiAppMcFlag = await helper.isSendNotiAppMc(kunnNumber);
          if (isSendNotiAppMcFlag) {
            msg = config.data.smsService.resubmitKunnMcaMsg;
            //send noti mc app
            try {
              const bodyNoti = {
                phoneNumber: phoneNumberRs,
                title: "Vui lòng bổ sung lại chứng từ!",
                message: `Vui lòng bổ sung lại chứng từ của hợp đồng khế ước ${kunnNumber}`,
                value: {
                  contractNumber: contractNumber,
                  kuNumber: kunnNumber,
                },
              };
              const endPoint = serviceEndpoint.NOTIFICATION.RESUBMIT;
              await sendNotification(bodyNoti, endPoint, config);
            } catch (error) {
              common.log("send noti mc app error", kunnNumber);
              console.log(error?.message);
            }
          }
          const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs);
              }
            }
          } catch (error) {
            console.log(error);
          }
        } else if (decision === "REJECT") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
          if (isMcaKunn) callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.REJECTED, "", "");
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
          //send noti mc app
          const bodyNoti = {
            phoneNumber: phoneNumberRs,
            title: "Khế ước đã bị bị từ chối",
            message: `Khế ước ${kunnNumber} của bạn đã bị từ chối. Vui lòng thực hiện lại thao tác rút tiền trên app.`,
            value: {
              contractNumber: contractNumber,
              kuNumber: kunnNumber,
            },
          };
          const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
          sendNotification(bodyNoti, endPoint, config);
        } else if (cancel !== undefined && decision === "CANCEL") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
          if (isMcaKunn) callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.CANCELLED, "", "");
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));
          //send noti mc app
          const bodyNoti = {
            phoneNumber: phoneNumberRs,
            title: "Khế ước đã bị bị từ chối",
            message: `Khế ước ${kunnNumber} của bạn đã bị huỷ. Vui lòng thực hiện lại thao tác rút tiền trên app.`,
            value: {
              contractNumber: contractNumber,
              kuNumber: kunnNumber,
            },
          };
          const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
          sendNotification(bodyNoti, endPoint, config);
        } else {
          res.status(200).json({
            code: "INVALID",
            message: "Invalid parameter",
          });
        }
      } else if (userRole == roleCode.SS) {
        if (decision === "APPROVE") {
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete SS task"))
            .catch((error) => console.log(error));
          await Promise.all([aadService.pushTaskMcV2(roleCode.CP, kunnNumber, CONTRACT_TYPE.CREDIT_LINE, STATUS.IN_CP_QUEUE), loanContractRepo.updateKUStatus(STATUS.IN_CP_QUEUE, kunnNumber)]);
        } else if (decision === "SAVE") {
          if (checked !== undefined) {
            await documentRepo.saveCheckedDoc(checked, userRole);
          }
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, checked);
        } else if (decision === "RESUBMIT") {
          if (checked !== undefined) documentRepo.saveCheckedDoc(checked, userRole);
          helper.saveKUStatus(poolWrite, kunnNumber, STATUS.SS_RESUBMIT);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, resubmitDoc);
          if (isMcaKunn) callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.RESUBMIT, "", "");
          if (!helper.isNullOrEmpty(resubmitDoc)) documentRepo.saveCheckedDocV2(resubmitDoc, userRole);
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then()
            .catch((error) => console.log(error));

          const smsUrl = config.data.smsService.sendSMS;
          const isEnable = config.data.smsService.useSMS;
          let msg = config.data.smsService.resubmitKunnMsg;
          msg = msg.replace("kunnNumber", kunnNumber);
          const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber);
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs);
              }
            }
          } catch (error) {
            console.log(error);
          }
          const isSendNotiAppMcFlag = await helper.isSendNotiAppMc(kunnNumber);
          if (isSendNotiAppMcFlag) {
            //send noti mc app
            try {
              const bodyNoti = {
                phoneNumber: phoneNumberRs,
                title: "Vui lòng bổ sung lại chứng từ!",
                message: `Vui lòng bổ sung lại chứng từ của hợp đồng khế ước ${kunnNumber}`,
                value: {
                  contractNumber: contractNumber,
                  kuNumber: kunnNumber,
                },
              };
              const endPoint = serviceEndpoint.NOTIFICATION.RESUBMIT;
              await sendNotification(bodyNoti, endPoint, config);
            } catch (error) {
              common.log("send noti mc app error", kunnNumber);
              console.log(error?.message);
            }
          }
        } else if (decision === "REJECT") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.REFUSED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, listRejectCode);
          if (isMcaKunn) callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.REJECTED, "", "");
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete SS task"))
            .catch((error) => console.log(error));
          //send noti mc app
          const bodyNoti = {
            phoneNumber: phoneNumberRs,
            title: "Khế ước đã bị bị từ chối",
            message: `Khế ước ${kunnNumber} của bạn đã bị từ chối. Vui lòng thực hiện lại thao tác rút tiền trên app.`,
            value: {
              contractNumber: contractNumber,
              kuNumber: kunnNumber,
            },
          };
          const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
          sendNotification(bodyNoti, endPoint, config);
        } else if (cancel !== undefined && decision === "CANCEL") {
          await helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED);
          saveCase(poolWrite, kunnNumber, userName, userRole, step, decision, cancel);
          if (isMcaKunn) callbackServiceV2.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.CANCELLED, "", "");
          await common
            .postAPI(aadCompletedUrl, { taskId })
            .then((rs) => console.log("complete SS task"))
            .catch((error) => console.log(error));
          //send noti mc app
          const bodyNoti = {
            phoneNumber: phoneNumberRs,
            title: "Khế ước đã bị bị từ chối",
            message: `Khế ước ${kunnNumber} của bạn đã bị huỷ. Vui lòng thực hiện lại thao tác rút tiền trên app.`,
            value: {
              contractNumber: contractNumber,
              kuNumber: kunnNumber,
            },
          };
          const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
          sendNotification(bodyNoti, endPoint, config);
        } else {
          res.status(200).json({
            code: "INVALID",
            message: "Invalid parameter",
          });
        }
      }
    }

    res.status(200).json({
      code: decision,
      message: "The contract status is updated",
    });
  } catch (e) {
    console.log("e", e);
    res.status(200).json({
      msg: "SERVER ERROR",
      code: "ERROR",
    });
  }
}

const updateKunnStatus = async (req, contractNumber, decision) => {
  const sql = `UPDATE loan_esigning SET status=$1 WHERE contract_number = $2`;
  await req.poolWrite.query(sql, [decision, contractNumber]);
};

const genKunn = async (req, contractNumber) => {
  const loanData = await loanContractRepo.getLoanContract(contractNumber);
  const contractType = loanData.contract_type;
  const partnerCode = loanData?.partner_code;
  const kunn_id = req.body.kunnId;
  // const poolRead = req.poolRead;
  const poolWrite = req.poolWrite;
  const unsigned_folder = "/mc-credit/unsigned-kunn-contract";
  const config = req.config;
  const sql = "SELECT * FROM kunn WHERE kunn_id = $1";
  let kunn = await req.poolRead.query(sql, [kunn_id]);
  kunn = kunn.rows[0];
  let filePath;
  let contractData;
  if (partnerCode === PARTNER_CODE.MISA) {
    const addMonths = (dateString, months) => {
      return moment(dateString).add(months, "months");
    };
    let startDate = new Date();
    let startDate2 = new Date();
    let endDate = addMonths(startDate2, kunn.tenor);
    await kunnRepo.updateEffectiveDate(kunn.kunn_id, startDate, endDate);
  }
  filePath = "./static_file/kunn_contract.docx";
  try {
    contractData = await getContractData(req, kunn);
    if (partnerCode === PARTNER_CODE.MISA && contractType === CONTRACT_TYPE.CREDIT_LINE) {
      filePath = "./static_file/kunn_sme_contract.docx";
      contractData = await getContractDataSme(req, kunn);
    }
    if (partnerCode === PARTNER_CODE.MISA && contractType === CONTRACT_TYPE.CASH_LOAN) {
      filePath = "./static_file/kunn_sme_contract_cash.docx";
      contractData = await getContractDataSme(req, kunn);
    }
    if (partnerCode === PARTNER_CODE.SMA || partnerCode === PARTNER_CODE.SMASYNC) {
      const kunnAttributeData = await kunnPrepareAttributeRepo.getDataByContractNumber(kunn_id);
      const paymentMethod = kunnAttributeData?.paymentMethod;
      filePath = "./static_file/kunn_contract_vm_sma.docx";
      if (paymentMethod == SMA_PAYMENT_METHOD[0]) filePath = "./static_file/kunn_contract_hm_sma.docx";
    }
  } catch (err) {
    console.error(err);
    console.log(`error when getContractData ${kunn_id}: ${err?.message}`);
  }

  const smsUrl = config.data.smsService.sendSMS;
  const isEnable = config.data.smsService.useSMS;
  if (!kunn) {
    return false;
  }
  try {
    genContract
      .convert2push(filePath, contractData, unsigned_folder, config, kunn.kunn_id)
      .then(async (fileLocation) => {
        helper.saveKUStatus(poolWrite, kunn_id, STATUS.WAITING_TO_BE_SIGNED);
        const sql = "insert into loan_esigning(contract_number,status,unsigned_contract) values($1,$2,$3)";
        poolWrite
          .query(sql, [kunn_id, "not signed yet", fileLocation])
          .then()
          .catch((err) => console.log(err));

        const sql1 = "UPDATE kunn SET date_approval= $1, esign_contract = $2 WHERE kunn_id = $3";
        poolWrite.query(sql1, [new Date(), fileLocation, kunn_id]);

        const isSendNotiAppMcFlag = await helper.isSendNotiAppMc(kunn_id);
        if (isSendNotiAppMcFlag) {
          try {
            const bodyNoti = {
              phoneNumber: loanData?.phone_number1,
              title: "Khế ước đã được duyệt, mời bạn vào ký!",
              message: `Mời quý khách vào ký, Hợp đồng khế ước ${kunn_id} của bạn đã được phê duyệt`,
              value: {
                contractNumber: contractNumber,
                kuNumber: kunn_id,
              },
            };

            const endPoint = serviceEndpoint.NOTIFICATION.KUNN_APPROVE;
            await sendNotification(bodyNoti, endPoint, config);
          } catch (err) {
            common.log("send notification to mc app error", kunn_id);
            console.log(err?.message);
          }
        }

        let phoneNumberRs;
        let msg;
        let isSme = false;
        if (partnerCode === PARTNER_CODE.MISA) {
          isSme = true;
          phoneNumberRs = await helper.getPhoneNumberSme(contractNumber);
          msg = config.data.smsService.esigningSmeKunnMsg;
          msg = msg.replace("kunn", kunn_id);
        } else {
          phoneNumberRs = await helper.getPhoneNumber(poolWrite, contractNumber);
          msg = config.data.smsService.esigningKUMsg;
          if (isSendNotiAppMcFlag) msg = config.data.smsService.esigningKUMcaMsg;
          msg = msg.replace("KUNNNumber", kunn_id);
        }
        if (partnerCode != PARTNER_CODE.SMA && partnerCode != PARTNER_CODE.SMASYNC) {
          try {
            if (phoneNumberRs !== undefined) {
              if (isEnable) {
                await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, isSme);
              }
            }
          } catch (error) {
            console.log(error);
          }
        }
      })
      .catch((e) => {
        console.log(e);
        return false;
      });
    return true;
  } catch {
    return false;
  }
};

function saveCase(poolWrite, contractNumber, userName, userRole, step, decision, listDecision) {
  let promiseQuery = [];
  const sql = "insert into loan_manual_decision (contract_number,assignee,role,step,result_chk,doc_id,case_code,deviation_cmt) values($1,$2,$3,$4,$5,$6,$7,$8)";
  listDecision.forEach((element) => {
    const docId = element.docId;
    const mistakeCode = element.mistakeCode;
    const mistakeDes = element.mistakeDes;
    promiseQuery.push(poolWrite.query(sql, [contractNumber, userName, userRole, step, decision, docId, mistakeCode, mistakeDes]));
  });
  Promise.all(promiseQuery)
    .then((result) => console.log("insert thanh cong"))
    .catch((err) => console.log(err));
}

const getContract = async (req, contract_number) => {
  const sql = "select * from loan_contract where contract_number = $1";
  const result = await req.poolRead.query(sql, [contract_number]);
  if (result.rows) return result.rows[0];
  else return null;
};

const getContractOffer = async (req, contract_number) => {
  const sql = "select * from loan_offer_selection where contract_number = $1 and is_selected = 1 AND kunn_id IS NULL ";
  const result = await req.poolRead.query(sql, [contract_number]);
  if (result.rows) return result.rows[0];
  else return null;
};

const getKunnOffer = async (req, contract_number) => {
  const sql = "select * from loan_offer_selection where kunn_id = $1 and is_selected = 1";
  const result = await req.poolRead.query(sql, [contract_number]);
  if (result.rows) return result.rows[0];
  else return null;
};

const getConfig = async (productCode, req) => {
  const lb = req.config.basic.product[req.config.env];
  const uri = req.config.data.productService.productConfig || "/product/v1/getProductConfig";
  const url = lb + uri + "?productCode=" + productCode;
  const response = await common.getAPI(url);
  if (response.code === 1) {
    return response.data;
  } else {
    return null;
  }
};

Number.prototype.format = function (n, x) {
  let re = "\\d(?=(\\d{" + (x || 3) + "})+" + (n > 0 ? "\\." : "$") + ")";
  return this.toFixed(Math.max(0, ~~n)).replace(new RegExp(re, "g"), "$&,");
};

async function getContractData(req, kunn) {
  const contract = await getContract(req, kunn.contract_number);
  const kunnInfo = await kunnRepo.getKunnData(kunn.kunn_id);
  const kunnAttributeData = await getDataByContractNumber(kunn.kunn_id);
  let paymentMethod = null;
  if (kunnAttributeData) {
    paymentMethod = kunnAttributeData?.paymentMethod;
  }
  let bankName = await getBankName(req, kunn.bank_branch_code);
  if (kunnInfo?.partner_code == PARTNER_CODE.SMA || kunnInfo?.partner_code == PARTNER_CODE.SMASYNC) bankName = await getValueCode(req, kunnInfo?.bank_code, "BANK");
  const contractOffer = await getContractOffer(req, contract.contract_number);
  const kunnOffer = await getKunnOffer(req, kunn.kunn_id);
  //const config = await getConfig(contract.product_code, req)
  const poolRead = req.poolRead;
  const poolWrite = req.poolWrite;
  const config = await getKunnFeeRate(poolRead, kunn.kunn_id);
  const sql = `select sum(with_draw_amount) as received_amount from kunn k where 1=1 and contract_number = $1 and status like '%ACTIVATED%';`;
  let received_amount = (await poolWrite.query(sql, [kunn.contract_number])).rows[0]?.received_amount;
  if (utils.isNullOrEmpty(received_amount)) received_amount = 0;
  //console.log({config})
  const currentDate = new Date();
  const dataFulFill = {
    date: new Date().getDate(),
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    kunn_id: kunn.kunn_id || "",
    contract_number: kunn.contract_number || "",
    contract_approval_date: moment(currentDate).format("DD/MM/YYYY") || "",
    customer_name: contract.cust_full_name || "",
    date_of_birth: moment(contract.birth_date).format("DD/MM/YYYY") || "",
    identify_id: contract.id_number || "",
    ///identify_date: contract.id_issue_dt.toLocaleDateString('en-GB') || '',
    loan_purpose: (await getValueCode(req, contract.loan_purpose, "LOAN_PURPOSE")) || "",
    identify_date: moment(contract.id_issue_dt).format("DD/MM/YYYY") || "",
    identify_address: (await getValueCode(req, contract.id_issue_place, "ISSUE_PLACE_VN")) || "",
    current_address: contract.address_cur || "",
    current_ward: (await getPlace(req, contract.ward_cur, "wards")) || "",
    current_district: (await getPlace(req, contract.district_cur, "districts")) || "",
    current_provice: (await getPlace(req, contract.province_cur, "provinces")) || "",
    per_address: contract.address_per || "",
    per_ward: (await getPlace(req, contract.ward_per, "wards")) || "",
    per_district: (await getPlace(req, contract.district_per, "districts")) || "",
    per_province: (await getPlace(req, contract.province_per, "provinces")) || "",
    phone_number: contract.phone_number1 || "",
    email: contract.email || "",
    approval_amount: helper.numberWithCommas(parseInt(contractOffer?.offer_amt)) || "",
    received_amount: helper.numberWithCommas(parseInt(received_amount)) || 0,
    amount: helper.numberWithCommas(parseInt(kunnOffer.offer_amt)) || "",
    tenor: config.tenor || 6,
    rate: (parseFloat(config.int_rate) * 100).toFixed(2) || "",
    draw_date: moment(currentDate).format("DD/MM/YYYY") || "",
    pay_date: moment().add(+config.tenor, "months").format("DD/MM/YYYY") || "",
    pay_annual_date: 1,
    pay: kunnInfo ? kunnInfo.bill_day : "",
    beneficiary_name: kunn.beneficiary_name || "",
    bank_account: kunn.bank_account || "",
    bank_name: bankName,
    rate_amount: 0,
    total: 0,
    payment_method: paymentMethod == SMA_PAYMENT_METHOD[0] ? "Cuối kỳ" : "Hàng tháng",
  };
  // console.log({ dataFulFill });
  return dataFulFill;
}

async function getContractDataSme(req, kunn) {
  const contract = await getContract(req, kunn.contract_number);
  const kunnInfo = await kunnRepo.getKunnData(kunn.kunn_id);
  const contractOffer = await getContractOffer(req, contract.contract_number);
  const kunnOffer = await getKunnOffer(req, kunn.kunn_id);
  //const config = await getConfig(contract.product_code, req)
  const poolRead = req.poolRead;
  const poolWrite = req.poolWrite;
  const config = await getKunnFeeRate(poolRead, kunn.kunn_id);
  const kunnDocs = await documentRepo.getKunnDoc(kunn.kunn_id);
  let kunnDocTypes = [];
  // const kunnInfo2 = await kunnRepo.getKunnByContractNumber(kunn.contract_number);
  // let countKunn = 0;
  // countKunn = kunnInfo2?.rowCount;
  // const paydate2 = kunnInfo2?.rows[0]?.end_date;
  const sql = `select sum(with_draw_amount) as received_amount from kunn k where 1=1 and contract_number = $1 and status like '%ACTIVATED%';`;
  let received_amount = (await poolWrite.query(sql, [kunn.contract_number])).rows[0]?.received_amount;
  if (utils.isNullOrEmpty(received_amount)) received_amount = 0;

  //kunn hoan han muc
  const checkAvailableUrl = global.config.basic.lmsMc[global.config.env] + global.config.data.lms.checkAvailable;
  const availableRs = await common.postApiV2(checkAvailableUrl, { contractNumber: kunn.contract_number });
  const availableAmount = availableRs?.data?.data?.avalibleAmount;
  const remainPrincipalAmount = availableRs?.data?.data?.remainPrincipalAmount ?? 0;
  if (!availableAmount) {
    throw new Error(`${kunn.contract_number} | checkAvailableUrl: ${checkAvailableUrl} error | response: ${JSON.stringify(availableRs)}`);
  }

  for await (const kd of kunnDocs) {
    if (kd.doc_type === "ESPE") {
      kunnDocTypes.push("Bảng thanh toán tiền lương nhân viên");
    }
    if (kd.doc_type === "VAT" || kd.doc_type === "VATKU") {
      kunnDocTypes.push("Hoá đơn GTGT");
    }
    if (kd.doc_type === "CWS") {
      kunnDocTypes.push("Hợp đồng kinh tế với nhà cung cấp");
    }
  }
  if (kunnDocTypes.length) {
    kunnDocTypes = kunnDocTypes.join(", ").toString();
  } else {
    kunnDocTypes = "";
  }

  //console.log({config})
  const currentDate = new Date();
  const masterData = await Promise.all([getValueCode_v3(contract.sme_headquarters_ward, "WARD"), getValueCode_v3(contract.sme_headquarters_district, "DISTRICT"), getValueCode_v3(contract.sme_headquarters_province, "PROVINCE"), getValueCode_v3(contract.sme_representation_position, "PROFESSION"), getValueCode_v3(contract.authorized_position, "PROFESSION"), getValueCode_v3(kunnInfo.with_draw_purpose, "LOAN_PURPOSE"), getValueCode_v3(contract.bank_code, "BANK"), getValueCode_v3(kunnInfo.bank_branch_code, "BANK_BRANCH"), getValueCode_v3(kunnInfo.bank_code, "BANK")]);
  let fullAddress = contract.sme_headquarters_address + ", " + masterData[0] + ", " + masterData[1] + ", " + masterData[2];
  let cusName = utils.isNullOrEmpty(contract.authorized_name) ? contract.sme_representation_name : contract.authorized_name;
  let position = utils.isNullOrEmpty(contract.authorized_position) ? masterData[3] : masterData[4];
  let approval_amount = contractOffer?.offer_amt ? helper.numberWithCommas(parseInt(contractOffer?.offer_amt)) : helper.numberWithCommas(parseInt(contract?.approval_amt)) ?? "";
  const dataFulFill = {
    date: new Date().getDate(),
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    kunn_id: kunn.kunn_id || "",
    contract_number: kunn.contract_number || "",
    contract_approval_date: moment(contract.approval_date).format("DD/MM/YYYY") || "",
    regist_number: contract.registration_number || "",
    regist_date: moment(contract.first_registration_date).format("DD-MM-YYYY") || "",
    sme_name: contract?.sme_name || "",
    sme_name_upper: contract?.sme_name.toUpperCase() || "",
    sme_address: fullAddress || "",
    sme_phone: contract.sme_phone_number || "",
    sme_representation_name: cusName || "",
    position: position || "",
    author_number: contract.authorization_letter_number || "",
    author_date: contract.authorization_letter_singed_date ? moment(contract.authorization_letter_singed_date).format("DD-MM-YYYY") : "",
    approval_amount: approval_amount,
    received_amount: helper.numberWithCommas(parseInt(received_amount)) || 0,
    amount: helper.numberWithCommas(parseInt(kunnOffer.offer_amt)) || "",
    loan_purpose: masterData[5] || "",
    tenor: config.tenor || "",
    rate: (parseFloat(config.int_rate) * 100).toFixed(2) || "",
    draw_date: moment(currentDate).format("DD/MM/YYYY") || "",
    pay_date: moment(kunnInfo?.end_date).format("DD/MM/YYYY") || "",
    pay: kunnInfo ? kunnInfo.bill_day : "",
    beneficiary_name: kunnInfo.beneficiary_name || "",
    bank_account: kunnInfo.bank_account || "",
    bank_name: masterData[6] || "",
    bank_branch: kunnInfo.bank_code === "204" ? masterData[7] : "",
    bank_number: contract.bank_account || "",
    customer_name: cusName.toUpperCase() || "",
    docs: kunnDocTypes || "",
    bank_name_kunn: masterData[8] || "",
    available_amount: availableAmount ? helper.numberWithCommas(parseInt(availableAmount)) : availableAmount ?? "",
    remain_principal_amount: remainPrincipalAmount ? helper.numberWithCommas(parseInt(remainPrincipalAmount)) : remainPrincipalAmount ?? "",
  };
  console.log(`contractNumber: `, kunn?.kunn_id, `dataFulFill:`, JSON.stringify(dataFulFill));
  return dataFulFill;
}

function getKunnFeeRate(poolRead, kunnID) {
  return new Promise(function (resolve, reject) {
    const sql = "select int_rate,tenor from loan_offer_selection where kunn_id=$1 and is_selected=1";
    poolRead
      .query(sql, [kunnID])
      .then((rs) => {
        resolve(rs.rows[0]);
      })
      .catch((error) => {
        console.log(error);
        reject(error.message);
      });
  });
}

Date.isLeapYear = function (year) {
  return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
};

Date.getDaysInMonth = function (year, month) {
  return [31, Date.isLeapYear(year) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month];
};

Date.prototype.isLeapYear = function () {
  return Date.isLeapYear(this.getFullYear());
};

Date.prototype.getDaysInMonth = function () {
  return Date.getDaysInMonth(this.getFullYear(), this.getMonth());
};

Date.prototype.addMonths = function (value) {
  let n = this.getDate();
  this.setDate(1);
  this.setMonth(this.getMonth() + value);
  this.setDate(Math.min(n, this.getDaysInMonth()));
  return this;
};

function base64_encode(file) {
  // read binary data
  let bitmap = fs.readFileSync(file);
  // convert binary data to base64 encoded string
  return new Buffer(bitmap).toString("base64");
}

module.exports = {
  updateStatus,
  getContractData,
  genKunn,
};
