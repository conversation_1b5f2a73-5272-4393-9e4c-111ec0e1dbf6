const s3Service = require("../upload_document/s3-service")
const uuid = require('uuid')
const documentModel = require('../upload_document/document-model');
const { getAPI } = require("../utils/common");
const common = require('../utils/common.js');
let storageDocumentStoragePath = "document/"
const {FILE_STORAGE} = require("../const/definition")

function uploadDoc(req, res) {
    try {
        let files = req.files;
        let config = req.config.data;
        let { idLoanContractDocument } = req.body;

        if (files === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'File must be not null.',
            })
        }
        const fields = Object.keys(req.files)
        const mapResult = req.files.map(async (item) => {
            const fieldName = item.fieldname
            const originalFileName = item.originalname
            const bundleId = await getBundleId(req, fieldName)
            if (!bundleId) {

                return res.status(200).json({
                    code: 0,
                    message: "Invalid fieldname"
                })
            }

            const fileName = Date.now() + item.originalname.replace(/\s/g, "")
            return s3Service.upload(config, fileName, item.buffer, storageDocumentStoragePath)
            .then(data => {
                return {
                    type: item.fieldname,
                    url: data.Location,
                    key: data.Key,
                    docId: uuid.v4(),
                    bundleId,
                    originalFileName
                }
            })
        })

        Promise.all(mapResult).then((result) => {
            const docList = result.map(object => {
                saveUpload(
                    req.poolWrite,
                    object.docId,
                    object.url,
                    object.type,
                    object.bundleId,
                    object.key,
                    object.originalFileName,
                    idLoanContractDocument
                );
                return {
                    doc_id: object.docId,
                    type: object.type,
                    url: object.url,
                    key: object.key,
                    type_id: object.docId
                }
            })

            return result.length < fields.length ?
                res.status(200).send({ code: 'NOT_UPLOADED', message: 'file upload fail.' })
                : res.status(200).send({
                    code: 'SUCCESS', message: 'Documents are uploaded successfully', data: {
                    "listDocCollecting": docList.map(doc => {
                        return {
                            'docName': doc.type,
                            'docId': doc.type_id
                            }
                        })
                    }
                })
        }).catch((err) => {
            console.log(err)
            return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
        })
    }
    catch (error) {
        console.log(error)
        return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
    }
}

function uploadDocV2(req, res) {
    try {
        let files = req.files;
        let config = req.config.data;
        let { idLoanContractDocument } = req.body;

        if (files === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'File must be not null.',
            })
        }
        const fields = Object.keys(req.files)
        const mapResult = req.files.map(async (item) => {
            // const fieldName = item.fieldname
            const originalFileName = item.originalname
            
            const fileName = Date.now() + item.originalname.replace(/\s/g, "")
            return s3Service.uploadV2(config, fileName, item.buffer, FILE_STORAGE.storageCustomerDocs)
            .then(data => {
                return {
                    type: item.fieldname,
                    url: data.Location,
                    key: data.Key,
                    docId: uuid.v4(),
                    bundleId : "",
                    originalFileName
                }
            })
        })

        Promise.all(mapResult).then((result) => {
            const docList = result.map(object => {
                saveUpload(
                    req.poolWrite,
                    object.docId,
                    object.url,
                    object.type,
                    object.bundleId,
                    object.key,
                    object.originalFileName,
                    idLoanContractDocument
                );
                return {
                    doc_id: object.docId,
                    type: object.type,
                    url: object.url,
                    key: object.key,
                    type_id: object.docId
                }
            })

            return result.length < fields.length ?
                res.status(200).send({ code: 'NOT_UPLOADED', message: 'file upload fail.' })
                : res.status(200).send({
                    code: 'SUCCESS', message: 'Documents are uploaded successfully', data: {
                    "listDocCollecting": docList.map(doc => {
                        return {
                            'docName': doc.type,
                            'docId': doc.type_id
                            }
                        })
                    }
                })
        }).catch((err) => {
            console.log(err)
            return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
        })
    }
    catch (error) {
        console.log(error)
        return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
    }
}

function uploadDocV3(req, res) {
    try {
        let files = req.files;
        let config = req.config.data;
        let { idLoanContractDocument, docGroup } = req.body;

        if (files === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'File must be not null.',
            })
        }
        const fields = Object.keys(req.files)
        const mapResult = req.files.map(async (item) => {
            const fieldName = item.fieldname
            const originalFileName = item.originalname

            let bundleId = docGroup
            if (!bundleId) bundleId = await getBundleId(req, fieldName)
            if (!bundleId) {

                return res.status(200).json({
                    code: 0,
                    message: "Invalid fieldname"
                })
            }

            const fileName = Date.now() + item.originalname.replace(/\s/g, "")
            return s3Service.upload(config, fileName, item.buffer, storageDocumentStoragePath)
                .then(data => {
                    return {
                        type: item.fieldname,
                        url: data.Location,
                        key: data.Key,
                        docId: uuid.v4(),
                        bundleId,
                        originalFileName
                    }
                })
        })

        Promise.all(mapResult).then((result) => {
            const docList = result.map(object => {
                saveUpload(
                    req.poolWrite,
                    object.docId,
                    object.url,
                    object.type,
                    object.bundleId,
                    object.key,
                    object.originalFileName,
                    idLoanContractDocument
                );
                return {
                    doc_id: object.docId,
                    type: object.type,
                    url: object.url,
                    key: object.key,
                    type_id: object.docId
                }
            })

            return result.length < fields.length ?
                res.status(200).send({ code: 'NOT_UPLOADED', message: 'file upload fail.' })
                : res.status(200).send({
                    code: 'SUCCESS', message: 'Documents are uploaded successfully', data: {
                        "listDocCollecting": docList.map(doc => {
                            return {
                                'docName': doc.type,
                                'docId': doc.type_id
                            }
                        })
                    }
                })
        }).catch((err) => {
            console.log(err)
            return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
        })
    }
    catch (error) {
        console.log(error)
        return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
    }
}


const getBundleId = async (req, fieldName) => {
    const lb = req.config.basic.masterData[req.config.env];
    const uri = req.config.data.masterDataService.getBundle
    const url = lb + uri + '?documentId=' + fieldName
    const result = await getAPI(url)
    if (result && result.code === 1) {
        return result.bundleName
    }
    else return null
}

function saveUpload(poolWrite, docId, docUrl, docType, bundleId,fileKey,fileName, idLoanContractDocument) {

    let sql = "";

    if (!idLoanContractDocument) {
        sql = `insert into 
            loan_contract_document(doc_id,doc_type,url, doc_group,file_key,file_name) values ($1,$2,$3,$4,$5,$6)`;

        poolWrite
            .query(sql, [docId, docType, docUrl, bundleId, fileKey, fileName])
            .then()
            .catch((error) => {
                common.log(error.message);
            });
    } else {
        sql = `UPDATE loan_contract_document SET
            doc_id = $1,
            doc_type = $2,
            url = $3,
            doc_group = $4,
            file_key = $5,
            file_name = $6
            WHERE id = $7`;
        poolWrite
            .query(sql, [
                docId,
                docType,
                docUrl,
                bundleId,
                fileKey,
                fileName,
                idLoanContractDocument,
            ])
            .then()
            .catch((error) => {
                common.log(error.message);
            });
    }
}

module.exports = {
    uploadDoc,
    uploadDocV2,
    uploadDocV3,
}