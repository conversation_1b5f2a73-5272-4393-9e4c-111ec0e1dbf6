const {REQUEST_TYPE,PARTNER_CODE, CONTRACT_TYPE, SERVICE_NAME, roleCode} = require("../const/definition")
const {KUNN_STATUS, MISA_CALLBACK_STATUS, CALLBACK_STAUS, caseStatusCode} = require("../const/caseStatus")
const kunnRepo = require("../repositories/kunn-repo")
const {routing} = require("../services/workflow-service")
const common = require("../utils/common")
const productService = require("../utils/productService")
const documentRepo = require("../repositories/document")
const {convertBody} = require("../utils/converter/convert")
const offerRepo = require("../repositories/offer")
const {STATUS} = require("../const/caseStatus")
const utils = require("../utils/helper")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const callbackService = require("../services/callback-service")
const { getLoanContract } = require("../repositories/loan-contract-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const { saveStepLog } = require("../repositories/logging-repo")
const { genKunn } = require("../KUNN/update_status")
const { save } = require("../repositories/kunn-prepare-attribute-repo")
const helper = require("../utils/helper");
const loanRepo = require("../repositories/loan-contract-repo");
const {findOne} = require("../utils/sqlHelper");
const {ADDRESS_CODE_TYPE} = require("../const/variables-const");
const {getValueCode_v2, parseValueCode} = require("../utils/masterdataService");
const {getFullyAddress} = require("../utils/common");

class baseKUNN {
    constructor(req,res,kunnCode) {
        const endDate = moment().add(6,'months').format('yyyy-MM-DD')
        req.body.endDate = endDate
        this.body = req.body
        this.contractNumber = req.body.contractNumber
        this.req = req
        this.res = res 
        this.kunnCode = kunnCode
        this.message = 'CREATE_KUNN'
    }

    async checkValidAmount() {
        await this.#convertDibursementBody()  
        return await this.#checkAvailableAmount()
    }

    async createDiburRequest() {
        await this.#genKunnNumber()
        await this.#insertKunn()
        await this.#createOffer()
        //chèn tạo offer

    }  

    async #convertDibursementBody() {
        const newBody = convertBody(this.body,REQUEST_TYPE.DIBUR,global.convertCache)
        this.convertedBody = newBody
        this.convertedBody.kunn_code = this.kunnCode
    }

    async checkCreateKunn(isSme=false){
        let count = 0
        const poolRead = global.poolRead
        // const rootContractStatus = await loanContractRepo.getContractStaus(this.convertedBody.contract_number)
        // if([STATUS.CREDIT_REVIEW,STATUS.CLOSED].includes(rootContractStatus)===STATUS.CREDIT_REVIEW){
        //     count+=1
        //     console.log(`rootContractStatus: ${rootContractStatus}`)
        // } 
        if(isSme){
            const minDrawAmt = parseInt(50000000)
            const ruleDayKunn = 30
            if(parseInt(this.convertedBody.with_draw_amount)<minDrawAmt||utils.isNullOrEmpty(this.convertedBody.tenor)) count+=1
            const loanData = await getLoanContract(this.convertedBody.contract_number)
            if(loanData?.contract_type===CONTRACT_TYPE.CASH_LOAN){
                const diffDay = utils.getDifferencesDays(loanData?.approval_date,new Date())
                console.log(`diffDayKunn: ${diffDay}`)
                if(diffDay>ruleDayKunn) {
                    this.message = `diffDayKunn: ${diffDay}`;
                    count+=1;
                } 
            }
        }
        // console.log({count})
        const contractStatus = await utils.getKuStatus(poolRead,this.convertedBody.contract_number)
        if(contractStatus?.length != 0) {
            for(let i=0; i< contractStatus.length; i ++) {
                if(!isSme){
                    if(!([STATUS.CANCELLED,STATUS.TERMINATED,STATUS.ACTIVATED,STATUS.NOT_ELIGIBLE,caseStatusCode.KKH13,caseStatusCode.KCP07,caseStatusCode.KKH14,caseStatusCode.KKH15,STATUS.SIGNED_TO_BE_DISBURED].includes(contractStatus[i].status))) {
                        this.message = `kunn contract status: ${contractStatus[i].status}`;
                        count++;
                    }
                }else{
                    if(!([KUNN_STATUS.ACTIVATED_WITH_DIS_DOCS,KUNN_STATUS.CANCELLED,KUNN_STATUS.TERMINATED,KUNN_STATUS.ACTIVATED,caseStatusCode.KKH13,caseStatusCode.KCP07,caseStatusCode.KKH14,caseStatusCode.KKH15].includes(contractStatus[i].status))) {
                        this.message = `kunn contract status: ${contractStatus[i].status}`;
                        count++;
                    }
                }
            }
        }
        return count
    }

    async #checkAvailableAmount() {
        try {
            const body = { 
                "contractNumber" : this.contractNumber
            }
            const checkAvailableUrl = global.config.basic.lmsMc[config.env] + global.config.data.lms.checkAvailable
            const availableRs = await common.postApiV2(checkAvailableUrl,body)
            const availableAmount = availableRs?.data?.data?.avalibleAmount
            if (availableRs?.data?.code == 0 && parseFloat(this.convertedBody.with_draw_amount) <= parseFloat(availableAmount)) {
                this.convertedBody.available_amount = availableAmount
                return true
            }
            else {
                this.message = `avalibleAmount = ${parseFloat(availableAmount)}`;
                return false
            }
        }
        catch(err) {
            return false
        }
    }

    async #genKunnNumber() {
        const kunnNumber = await kunnRepo.genKunnNumber()
        this.kunnNumber = kunnNumber
        this.convertedBody.kunn_id = kunnNumber
    } 

    async #insertKunn() {
        return await kunnRepo.insertKunn(this.convertedBody)
    }

    async #createOffer() {
        const body = this.convertedBody
        let data = {}
        data.contractNumber= body.contract_number
        data.kunnNumber = body.kunn_id
        data.offerAmt = body.with_draw_amount
        data.offerRate = body.ir
        data.offerTenor = body.tenor
        data.offerType = 'STANDARD'
        data.productCode = this.kunnCode
        data.requestAmt = body.with_draw_amount
        data.requestTenor = body.tenor
        return await offerRepo.saveKUOfferV2(data)
    }

    async saveDocument() {
        try {
            const poolWrite = global.poolWrite
            let documentList = this.convertedBody.listDocCollecting
            const bundleInfo = await productService.getBundle(global.config,this.kunnCode,undefined,true)
            documentList = productService.mapBundleGroup(documentList,bundleInfo.data)
            await documentRepo.saveUploadedDocumentKunn(poolWrite,this.kunnNumber,documentList)
        }
        catch(err) {
            console.log(err)
            return false
        }
    }

    async callWorkflow() {
        this.convertedBody.currentTask = 'DIBUR_REQUEST'
        routing(this.convertedBody)
    }

    responseCreateRequestSuccess() {
        return this.res.status(200).json({
            message: "The request is received",
            code: "RECEIVED",
            data: {
                contractNumber: this.contractNumber,
                withdrawId: this.kunnNumber
            }
        })
    }

    responseNotAvailableAmount() {
        return this.res.status(400).json({
            code : 0,
            msg : 'Khách hàng chưa khởi tạo hạn mức hoặc hạn mức không khả dụng.'
        })
    }

    responseCannotCreateKunn() {
        return this.res.status(400).json({
            code : 0,
            msg : 'Không thể tạo KUNN.'
        })
    }

    responseCreateRequestError() {
        return common.responseErrorPublic(this.res)
    }

}

class vskKUNN extends baseKUNN {
    constructor(req,res,kunnCode) {
        req.body.partner_code = PARTNER_CODE.VSK
        super(req,res,kunnCode)
    }

    async createDiburRequest() {
        try {
            const isValidAmount = await super.checkValidAmount()
            const canCreateKunn = await super.checkCreateKunn()
            if(isValidAmount && canCreateKunn==0) {
                await super.createDiburRequest()
                await Promise.all([
                    super.saveDocument(),
                    kunnRepo.updateKUStatus(this.kunnNumber,KUNN_STATUS.RECIEVE)
                ])
                super.callWorkflow()
                super.responseCreateRequestSuccess()
            }
            else if(canCreateKunn!=0){
                super.responseCannotCreateKunn()
            }
            else {
                super.responseNotAvailableAmount()
            }
        }
        catch(err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }

}

class misaKUNN extends baseKUNN {
    constructor(req,res,kunnCode) {
        req.body.partner_code = PARTNER_CODE.MISA
        if(!utils.isNullOrEmpty(req.body.content)) req.body.content = utils.nonAccentVietnamese(req.body.content).toLowerCase();
        if(!utils.isNullOrEmpty(req.body.content2)) req.body.content2 = utils.nonAccentVietnamese(req.body.content2).toLowerCase();
        if(!utils.isNullOrEmpty(req.body.content3)) req.body.content3 = utils.nonAccentVietnamese(req.body.content3).toLowerCase();
        if(!utils.isNullOrEmpty(req.body.content4)) req.body.content4 = utils.nonAccentVietnamese(req.body.content4).toLowerCase();
        const currentTimestamp = new Date().getTime()
	    const requestId = PARTNER_CODE.MISA + currentTimestamp
        req.body.requestId = requestId
        super(req,res,kunnCode)
        this.partnerCode = PARTNER_CODE.MISA
    }

    async createDiburRequest() {
        try {
            this.req.body.endDate = moment().add(12,'months').format('yyyy-MM-DD')
            const isValidAmount = await super.checkValidAmount()
            const canCreateKunn = await super.checkCreateKunn(true)
            if(isValidAmount && canCreateKunn==0) {
                await super.createDiburRequest()
                await Promise.all([
                    super.saveDocument(),
                    kunnRepo.updateKUStatus(this.kunnNumber,KUNN_STATUS.RECIEVE)
                ])
                callbackService.callbackPartner(this.contractNumber,this.partnerCode,CALLBACK_STAUS.CREATED_KUNN,'','',this.kunnNumber)
                super.callWorkflow()
                super.responseCreateRequestSuccess()
            }
            else if(canCreateKunn!=0){
                super.responseCannotCreateKunn()
            }
            else {
                super.responseNotAvailableAmount()
            }
        }
        catch(err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }

}

class MCAppKUNN extends baseKUNN {
    constructor(req,res,kunnCode) {
        // const requestId = utils.genRequestId(req.body.partner_code);
        // req.body.requestId = requestId;
        // req.body.partner_code = PARTNER_CODE.MCAPP;
        super(req,res,kunnCode);
    }
    async #updateBillDay(contractNumber, billDay) {
        console.log({contractNumber, billDay})
        loanContractRepo.updateBillDay(contractNumber, billDay)
    }

    // #initBillDay(){
    //     let billDay;
    //     const nowDate = new Date(moment()).getDate();
    //     if(nowDate >= 1 && nowDate <= 15){
    //         billDay = "25";
    //     } else {
    //         billDay = "5";
    //     }

    //     return billDay;
    // }

    async createDiburRequest() {
        try {
            const isValidAmount = await super.checkValidAmount()
            const canCreateKunn = await super.checkCreateKunn()
            const formData = this.convertedBody
            saveStepLog(formData.contract_number,SERVICE_NAME.CREATE_KUNN,SERVICE_NAME.CREATE_KUNN,this.convertedBody,this.message)
            if(isValidAmount && canCreateKunn==0) {
                // this.convertedBody.bill_day = this.#initBillDay();
                //await super.checkEligible();
                this.#updateBillDay(formData?.contract_number, formData?.bill_day)
                await super.createDiburRequest();
                await Promise.all([
                    super.saveDocument(),
                    kunnRepo.updateKUStatus(this.kunnNumber,KUNN_STATUS.RECIEVE)
                ]);
                // super.callWorkflow();
                await utils.saveKUStatus(this.req.poolWrite,this.kunnNumber,STATUS.WAITING_TO_BE_SIGNED);
                this.req.body.kunnId = this.kunnNumber;
                await genKunn(this.req,formData?.contract_number);
                super.responseCreateRequestSuccess();
            }
            else if(canCreateKunn!=0){
                super.responseCannotCreateKunn();
            }
            else {
                super.responseNotAvailableAmount();
            }
        }
        catch(err) {
            console.log(err);
            super.responseCreateRequestError();
        }
    }
}

class  SuperAppKUNN extends baseKUNN {
    constructor(req,res,kunnCode) {
        let requestId = utils.genRequestId(`${PARTNER_CODE.SMA}_${PARTNER_CODE.SMA}`)
        req.body.requestId = requestId
        super(req,res,kunnCode)
    }
    async #saveKunnAttribute() {
        save({contractNumber: this.kunnNumber, field: "paymentMethod", value: this.req.body.paymentMethod})
    }
    async #updateBillDay(contractNumber, billDay) {
        loanContractRepo.updateBillDay(contractNumber, billDay)
    }
    
    async createDiburRequest() {
        try {
            
            const isValidAmount = await super.checkValidAmount()
            const canCreateKunn = await super.checkCreateKunn()
            const formData = this.convertedBody
            saveStepLog(formData.contract_number,SERVICE_NAME.CREATE_KUNN,SERVICE_NAME.CREATE_KUNN,this.convertedBody,this.message)
            if(isValidAmount && canCreateKunn==0) {
                await super.createDiburRequest();
                this.#saveKunnAttribute()
                this.#updateBillDay(formData?.contract_number, formData?.bill_day)
                await Promise.all([
                    super.saveDocument(),
                    kunnRepo.updateKUStatus(this.kunnNumber,KUNN_STATUS.RECIEVE)
                ]);
                // super.callWorkflow();
                await utils.saveKUStatus(this.req.poolWrite,this.kunnNumber,STATUS.WAITING_TO_BE_SIGNED);
                this.req.body.kunnId = this.kunnNumber;
                // callbackService.callbackPartner(this.kunnNumber,PARTNER_CODE.SMA,CALLBACK_STAUS.SIGNED,'','')
                await genKunn(this.req,formData?.contract_number);
                super.responseCreateRequestSuccess();
            }
            else if(canCreateKunn!=0){
                super.responseCannotCreateKunn();
            }
            else {
                super.responseNotAvailableAmount();
            }
        }
        catch(err) {
            console.log(err);
            super.responseCreateRequestError();
        }
    }
}

const getApplicationForm = async (contractNumber) => {
    try {
        const contract = helper.snakeToCamel(
            await loanRepo.getLoanContract(contractNumber)
        );
        if (!contract) {
            helper.throwBadReqError(`contractNumber`, `Contract number ${contractNumber} not found`);
        }
        const representation = helper.snakeToCamel(
            await findOne({
                table: `loan_customer_representations`,
                whereCondition: {
                    contract_number: contract.contractNumber,
                },
                orderBy: {
                    created_at: "desc",
                },
            })
        );

        const [smeRepresentationWardCur, smeRepresentationDistrictCur, smeRepresentationProvinceCur,
          smeRepresentationNewWardCur, smeRepresentationNewProvinceCur,
          smeRepresentationWardPer, smeRepresentationDistrictPer, smeRepresentationProvincePer,
          smeRepresentationNewWardPer, smeRepresentationNewProvincePer,
          wardCur, districtCur, provinceCur,
          curNewWard, curNewProvince,
          wardPer,districtPer, provincePer,
          perNewWard, perNewProvince
        ] = await Promise.all([
        parseValueCode(contract.smeRepresentationWardCur, ADDRESS_CODE_TYPE.WARD),
        parseValueCode(contract.smeRepresentationDistrictCur, ADDRESS_CODE_TYPE.DISTRICT),
        parseValueCode(contract.smeRepresentationProvinceCur, ADDRESS_CODE_TYPE.PROVINCE),
        parseValueCode(contract.smeRepresentationNewWardCur, ADDRESS_CODE_TYPE.NEW_WARD),
        parseValueCode(contract.smeRepresentationNewProvinceCur, ADDRESS_CODE_TYPE.NEW_PROVINCE),
        parseValueCode(contract.smeRepresentationWardPer, ADDRESS_CODE_TYPE.WARD),
        parseValueCode(contract.smeRepresentationDistrictPer, ADDRESS_CODE_TYPE.DISTRICT),
        parseValueCode(contract.smeRepresentationProvincePer, ADDRESS_CODE_TYPE.PROVINCE),
        parseValueCode(contract.smeRepresentationNewWardPer, ADDRESS_CODE_TYPE.NEW_WARD),
        parseValueCode(contract.smeRepresentationNewProvincePer, ADDRESS_CODE_TYPE.NEW_PROVINCE),
        parseValueCode(contract.wardCur, ADDRESS_CODE_TYPE.WARD),
        parseValueCode(contract.districtCur, ADDRESS_CODE_TYPE.DISTRICT),
        parseValueCode(contract.provinceCur, ADDRESS_CODE_TYPE.PROVINCE),
        parseValueCode(contract.curNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
        parseValueCode(contract.curNewProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
        parseValueCode(contract.wardPer, ADDRESS_CODE_TYPE.WARD),
        parseValueCode(contract.districtPer, ADDRESS_CODE_TYPE.DISTRICT),
        parseValueCode(contract.provincePer, ADDRESS_CODE_TYPE.PROVINCE),
        parseValueCode(contract.perNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
        parseValueCode(contract.perNewProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE)
        ]);

        const personalInfo = {
            name: contract.smeRepresentationName || representation?.fullName || "",
            gender: contract.smeRepresentationGender || representation?.gender || "",
            dob: contract.smeRepresentationDob || representation?.dob || "",
            maritalStatus: "",
            nationality: "VN",
            idCard: contract.smeRepresentationId || representation?.idNumber || "",
            issueDate:
                contract.smeRepresentationIssueDate || representation?.issueDate || "",
            issuePlace:
                contract.smeRepresentationIssuePlace ||
                representation?.issuePlace ||
                "",
            email: contract.smeRepresentationEmail || representation?.email || "",
            phone:
                contract.smeRepresentationPhoneNumber ||
                representation?.phoneNumber ||
                "",
            houseType: "",
            numDependants: "",
            otherIdCard:
                contract.smeRepresentationOtherId ||
                representation?.otherIdNumber ||
                "",
        };
        const address = {
            contactAddress: {},
            curAddress: {
                street: contract.smeRepresentationAddressCur || "",
                ward: smeRepresentationWardCur,
                district: smeRepresentationDistrictCur,
                city: smeRepresentationProvinceCur,
                newWard: smeRepresentationNewWardCur,
                newProvince: smeRepresentationNewProvinceCur,
                country: "Việt Nam",
                address: getFullyAddress(contract.smeRepresentationAddressCur, [smeRepresentationNewWardCur, smeRepresentationNewProvinceCur], [smeRepresentationWardCur, smeRepresentationDistrictCur, smeRepresentationProvinceCur])
            },
            perAddress: {
                street:
                contract.smeRepresentationAddressPer ||
                representation?.perDetailAddress,
                ward: smeRepresentationWardPer,
                district: smeRepresentationDistrictPer,
                city: smeRepresentationProvincePer,
                newWard: smeRepresentationNewWardPer,
                newProvince: smeRepresentationNewProvincePer,
                country: "Việt Nam",
                address: common.getFullyAddress(contract.smeRepresentationAddressPer, [smeRepresentationNewWardPer, smeRepresentationNewProvincePer], [smeRepresentationWardPer, smeRepresentationDistrictPer, smeRepresentationProvincePer])
            },
            };
            const smeInfo = {
            smeName: contract.smeName || "",
            taxCode: contract.smeTaxId || contract.taxId || "",
            registrationNumber: contract.registrationNumber || "",
            status: contract.status,
            address: {
                curAddress: {
                street: contract.addressCur || "",
                ward: wardCur,
                district: districtCur,
                city: provinceCur,
                newWard: curNewWard,
                newProvince: curNewProvince,
                country: "Việt Nam",
                address: getFullyAddress(contract.addressCur, [curNewWard, curNewProvince], [wardCur, districtCur, provinceCur])
                },
                perAddress: {
                street: contract.addressPer || "",
                ward: wardPer ,
                district: districtPer ,
                city: provincePer ,
                newWard: perNewWard ,
                newProvince: perNewProvince ,
                country: "Việt Nam",
                address: getFullyAddress(contract.addressPer, [perNewWard, perNewProvince], [wardPer, districtPer, provincePer])
                },
            },
        };
        const rs = buildResApplicationForm(personalInfo, address, smeInfo);
        return {
            code: 1,
            msg: "success",
            data: rs,
        };
    } catch (error) {
        console.log(
            `[MISA][HM][V2][getApplicationForm] Error contractNumber ${contractNumber}, error: ${error} `
        );
        throw error;
    }
};

const getAddressValue = async (value, type) => {
    if (!value) return "";
    try {
        const config = global.config;
        const mapData = await getValueCode_v2(config, value, type);
        return mapData;
    } catch (error) {
        console.log(`[getAddressValue] error value ${value}`);
        return value;
    }
};

const buildResApplicationForm = (personalInfo, address, smeInfo) => {
    return {
        appDetail: {},
        personalInfo,
        otherInfo: {},
        address,
        disbursement: {},
        offer: {},
        additionalInfor: {},
        voucherInfo: {},
        smeInfo,
    };
};

module.exports = {
    getApplicationForm,
    vskKUNN,
    misaKUNN,
    MCAppKUNN,
    SuperAppKUNN
}