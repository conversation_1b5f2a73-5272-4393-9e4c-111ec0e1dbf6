const {REQUEST_TYPE,PARTNER_CODE, CONTRACT_TYPE, RESPONSE_CODE, roleCode} = require("../const/definition")
const loanContractRepo = require("../repositories/loan-contract-repo")
const common = require("../utils/common")
const productService = require("../utils/productService")
const {convertBody} = require("../utils/converter/convert")
const {STATUS, CALLBACK_STAUS} = require("../const/caseStatus")
const utils = require("../utils/helper")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const inputValidator = require("../a1_application/a1-input-validator")
const loggingService = require("../utils/loggingService")
const {baseCheckEligible,baseCheckEligibleSME, checkEligibleMc} = require("../services/de-service")
const {baseCheckS37,checkPcbSME} = require("../services/de-service")
const aadService = require("../utils/aadService")
const _ = require('underscore')
const offerRepo = require("../repositories/offer")
const documentRepo = require("../repositories/document")
const { validNotEnoughDocument } = require("../utils/validator/base-api-validator")
const dateHelper = require("../utils/dateHelper")

class BaseReview {
    constructor(req,res) {
        this.body = req.body
        this.req = req
        this.res = res 
        this.config = req.config
    }

    async createLoanRequest() {
        await this.genContractNumber()
        await this.convertBasicBody()
        await this.insertLoan()
        await this.checkEligible()
    }  

    async convertBasicBody() {
        let newBody = convertBody(this.body,REQUEST_TYPE.BASIC,global.convertCache)
        this.convertedBody = newBody
        this.convertedBody.contract_number_old = this.body.contractNumber
        this.convertedBody.contract_number = this.contractNumberNew
    }

    async getRootLoanContract(){
        const rootLoan = await loanContractRepo.getLoanContract(this.convertedBody.contract_number_old)
        let newBody = convertBody(rootLoan,REQUEST_TYPE.BASIC,global.convertCache)
        newBody = convertBody(newBody,REQUEST_TYPE.FULL_LOAN,global.convertCache)
        this.convertedBodyOld = newBody
        // this.convertedBodyOld = Object.keys(this.convertedBodyOld).reduce((object, key) => {
        //     if (!["id","province_cur","district_cur","ward_cur","address_cur","other_id_number","other_issue_date","other_issue_place"].includes(key)) {
        //       object[key] = this.convertedBodyOld[key]
        //     }
        //     return object
        // }, {})
        delete this.convertedBodyOld.id
        this.convertedBodyOld.contract_number = this.contractNumberNew
        for (const item of Object.entries(this.convertedBodyOld)) {
            if(utils.isNullOrEmpty(item[1])) delete this.convertedBodyOld[`${item[0]}`]; 
        }
    }

    async checkEligible(isSME=false) {
        try {
            const poolWrite = global.poolWrite
            let responseBody;
            const contractNumber = this.convertedBody.contract_number;
            let finalStatus = STATUS.NOT_ELIGIBLE;
            let code = RESPONSE_CODE.INVALID_REQUEST;
            if(isSME){
                const isEligible = await baseCheckEligibleSME(this.convertedBody);
                if(isEligible) {
                    const isNotBadDebt = await baseCheckS37(this.convertedBody,true);
                    if(isNotBadDebt) {
                        const isPassPcb = await checkPcbSME(this.convertedBody,undefined);
                        if(isPassPcb){
                            finalStatus = STATUS.ELIGIBLE;
                            code = RESPONSE_CODE.RECIEVED;
                        }
                    }
                }

                if(finalStatus != STATUS.ELIGIBLE) {
                    responseBody = {
                        code,
                        message : "NOT ELIGIBLE"
                    }	
                }
                else {
                    responseBody = {
                        code,
                        message : "ELIGIBLE",
                        contractNumber
                    }
                }
            }else{
                const isEligible = await baseCheckEligible(this.convertedBody);
                if(isEligible) {
                    const isNotBadDebt = await baseCheckS37(this.convertedBody);
                    if(isNotBadDebt) {
                        finalStatus = STATUS.ELIGIBLE;
                        code = RESPONSE_CODE.RECIEVED;
                    }
                }

                if(finalStatus != STATUS.ELIGIBLE) {
                    responseBody = {
                        code,
                        message : "NOT ELIGIBLE"
                    }	
                }
                else {
                    responseBody = {
                        code,
                        message : "ELIGIBLE",
                        contractNumber
                    }
                }
            }
            
            await Promise.all([loggingService.saveRequestV2(poolWrite,this.convertedBody,responseBody,this.convertedBody.contract_number,this.convertedBody.request_id,this.convertedBody.partner_code),
                utils.saveStatus(poolWrite,contractNumber,finalStatus),
                offerRepo.createLoanMainScore(contractNumber)])
            return responseBody;
        }
        catch(err) {
            common.log('Check eligible a1 error:' + err.message);
        }
    }

    async genContractNumber() {
        const contractNumber = await inputValidator.generateContractNumber_v2(this.req)
        this.contractNumberNew = contractNumber
    } 

    async insertLoan(partner) {
        if(!utils.isNullOrEmpty(this.convertedBody.branchAddress)){
            loanContractRepo.saveBranchAddress(this.convertedBody.contract_number,this.convertedBody.branchAddress)
        }
        let docList = this.req.body.listDocCollecting;
		let externalDocs = this.req.body.externalDocument;
        const bundleInfo = await productService.getBundle(global.config,this.convertedBodyOld.product_code);
        let allListDoc = [];
        this.req.isEnoughDoc = true;
        let isEnoughDoc;
        if(partner===PARTNER_CODE.KOV){
            allListDoc = [...docList, ...externalDocs];
            isEnoughDoc = await validNotEnoughDocument(allListDoc,this.convertedBodyOld.product_code,undefined,PARTNER_CODE.KOV);
            allListDoc = productService.mapBundleGroupKOV(allListDoc,bundleInfo.data);
        }  
        if(partner===PARTNER_CODE.VSK){
            isEnoughDoc = await validNotEnoughDocument(docList,this.convertedBodyOld.product_code,undefined,PARTNER_CODE.VSK);
            allListDoc = productService.mapBundleGroup(docList,bundleInfo.data);
        } 
        if(!isEnoughDoc) this.req.isEnoughDoc = false;
        const updateLoanRs = await Promise.all([
            loanContractRepo.insertLoanContract(this.convertedBody),
            documentRepo.saveUploadedDocument(global.poolWrite,this.convertedBody.contract_number,allListDoc)
        ])
        let responseBody;
        if(!updateLoanRs[0]||!updateLoanRs[1]) {
			responseBody = {
				code : RESPONSE_CODE.SERVER_ERROR,
				message : "Internal Server Error"
			}
			await loggingService.saveRequestV2(this.req.poolWrite,this.convertedBody,responseBody,this.convertedBody.contract_number,this.convertedBody.request_id,this.convertedBody.partner_code)
            throw new Error('insert Loan error');
		}
    }

    async updateLoan(){
        /**
         * Update các field có thể thay đổi khi reviewHM cho hợp đồng gốc
         * Update các trường cũ(ngoãi các trường có thể thay thế) vào hợp đồng mới
         */
        const oldContractNumber = this.convertedBody.contract_number_old;
        const newContractNumber = this.convertedBody.contract_number;
        // console.log(this.convertedBodyOld)
        this.convertedBodyOld = Object.keys(this.convertedBodyOld).reduce((object, key) => {
            if (!["province_cur","district_cur","ward_cur","address_cur","province_per","district_per","ward_per","address_per",
              "id_number","id_issue_dt","id_issue_place","phone_number1","birth_date"].includes(key)) {
              object[key] = this.convertedBodyOld[key]
            }
            return object
        }, {})
        // console.log(this.convertedBodyOld)
        const updateLoanRs = await Promise.all([
            loanContractRepo.updateFieldLoanContract(newContractNumber,'root_contract_number',oldContractNumber),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'province_cur',this.convertedBody.province_cur),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'district_cur',this.convertedBody.district_cur),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'ward_cur',this.convertedBody.ward_cur),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'address_cur',this.convertedBody.address_cur),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'other_id_number2',this.convertedBody.other_id_number2),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'other_issue_date2',this.convertedBody.other_issue_date2),
            // loanContractRepo.updateFieldLoanContract(oldContractNumber,'other_issue_place2',this.convertedBody.other_issue_place2),
            loanContractRepo.updateLoanContract(this.convertedBodyOld)
        ]) 
        let responseBody;
        if(!updateLoanRs[0]||!updateLoanRs[1]) {
			responseBody = {
				code : RESPONSE_CODE.SERVER_ERROR,
				message : "Internal Server Error"
			}
			await loggingService.saveRequestV2(this.req.poolWrite,this.convertedBody,responseBody,this.convertedBody.contract_number,this.convertedBody.request_id,this.convertedBody.partner_code)
            throw new Error('Update Loan error');
		}
    }

    async checkValidContract(){
        if(this.convertedBodyOld.contract_type===CONTRACT_TYPE.CASH_LOAN||this.convertedBodyOld.status!==STATUS.ACTIVATED){
            return false
        }
        return true
    }

    // async checkMapDataRoot(contractNumber,dataNew){
    //     const dataOld = await loanContractRepo.getLoanContract(contractNumber);
    //     let mess = [];
    //     Object.entries(dataNew).map(x=>{
    //         Object.entries(dataOld).map(y=>{
    //             if(['birth_date','id_issue_dt'].includes(y[0])&&!utils.isNullOrEmpty(y[1])) y[1] = dateHelper.formatDate(y[1], "YYYY-MM-DD")
    //             if(x[0]===y[0]){
    //                 if(x[1]!==y[1]){
    //                     const rs = `not map at attribute: ${x[0]} with value: ${x[1]}`;
    //                     mess.push(rs);
    //                 }
    //             }
    //         })
    //     });
    //     return mess;
    // }

    responseNotValidContract() {
        return this.res.status(400).json({
            code : RESPONSE_CODE.INVALID_REQUEST,
            msg : `${this.convertedBody.contract_number_old}: Hợp đồng gốc không phải là hợp đồng hạn mức hoặc hợp đồng hạn mức khác actived.`
        })
    }

    responseNotEnoughDoc(){
        return this.res.status(200).json({
            "statusCode": 0,
            "body": {
                "code": "INVALID",
                "message": "Chưa đủ document.",
            }
        });
    }

    responseCreateRequestError() {
        return common.responseErrorPublic(this.res)
    }
}

class VskReview extends BaseReview {
    constructor(req,res) {
	    const requestId = utils.genRequestId(PARTNER_CODE.VSK);
        req.body.requestId = requestId;
        super(req,res);
    }

    async reviewLoanContract() {
        try {
            await super.genContractNumber()
            await super.convertBasicBody()
            await super.getRootLoanContract()
            // const newBody = this.convertedBody;
            
            // const dataNew = {
            //     id_number: newBody.id_number,
            //     cust_full_name: newBody.cust_full_name,
            //     birth_date: newBody.birth_date,
            //     id_issue_dt: newBody.id_issue_dt,
            //     id_issue_place: newBody.id_issue_place,
            //     province_per: newBody.permanentProvince,
            //     district_per: newBody.permanentDistrict,
            //     ward_per: newBody.perWard,
            //     address_per: newBody.permanentAddress,
            //     phone_number1: newBody.phone_number1
            // }
            // const rsCheckMapDataRoot = await super.checkMapDataRoot(this.convertedBody.contract_number_old,dataNew);
            // if(Array.isArray(rsCheckMapDataRoot)&&rsCheckMapDataRoot.length){
            //     return this.res.status(200).json({
            //         "code": "INVALID_REQUEST",
            //         "message": rsCheckMapDataRoot,
            //         "contractNumber": this.convertedBody.contract_number
            //     });
            // }
            const isValidContract = await super.checkValidContract()
            if(!isValidContract){
                super.responseNotValidContract()
            }
            else{
                await super.insertLoan(PARTNER_CODE.VSK)
                if(!this.req.isEnoughDoc) return super.responseNotEnoughDoc();
                await super.updateLoan()
                const a1Result = await super.checkEligible()
                if(a1Result.message===STATUS.ELIGIBLE){
                    aadService.pushTaskMcV2(roleCode.CP,this.convertedBody.contract_number,CONTRACT_TYPE.CREDIT_LINE,STATUS.IN_CP_QUEUE);
                    loanContractRepo.updateContractStatus(STATUS.IN_CP_QUEUE,this.convertedBody.contract_number);
                    loanContractRepo.updateContractStatus(STATUS.CREDIT_REVIEW,this.convertedBody.contract_number_old);
                }
                return this.res.status(200).json(a1Result);
            }
        }
        catch(err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

class KovReview extends BaseReview {
    constructor(req,res) {
	    const requestId = utils.genRequestId(PARTNER_CODE.KOV);
        req.body.partnerCode = PARTNER_CODE.KOV;
        req.body.requestId = requestId;
        super(req,res);
    }
    async #insertOtherInfo(){
        const oldContractNumber = this.convertedBody.contract_number_old
        const newContractNumber = this.convertedBody.contract_number
        const poolWrite = this.req.poolWrite
        const sql = `select platform_name as "platformName",store_name as "storeName" from loan_account_trading where contract_number=$1`
        const accountTrading = (await poolWrite.query(sql,[oldContractNumber]))?.rows
        if(!utils.isNullOrEmpty(accountTrading)) loanContractRepo.saveLoanAcountTrading(newContractNumber,accountTrading)
        // loanContractRepo.updateFieldLoanContract(newContractNumber,'root_contract_number',oldContractNumber)
        Promise.all([
            offerRepo.createLoanMainScore(newContractNumber),
            offerRepo.createKOVLoanScore(newContractNumber)
        ])
    }
    async reviewLoanContract() {
        try {
            await super.genContractNumber()
            await super.convertBasicBody()
            await super.getRootLoanContract()
            // const newBody = this.convertedBody;
            // console.log({newBody})
            // const dataNew = {
            //     id_number: newBody.id_number,
            //     cust_full_name: newBody.cust_full_name,
            //     birth_date: newBody.birth_date,
            //     id_issue_dt: newBody.id_issue_dt,
            //     id_issue_place: newBody.id_issue_place,
            //     province_per: newBody.permanentProvince,
            //     district_per: newBody.permanentDistrict,
            //     ward_per: newBody.perWard,
            //     address_per: newBody.permanentAddress,
            //     phone_number1: newBody.phone_number1
            // }
            // const rsCheckMapDataRoot = await super.checkMapDataRoot(this.convertedBody.contract_number_old,dataNew);
            // if(Array.isArray(rsCheckMapDataRoot)&&rsCheckMapDataRoot.length){
            //     return this.res.status(200).json({
            //         "code": "INVALID_REQUEST",
            //         "message": rsCheckMapDataRoot,
            //         "contractNumber": this.convertedBody.contract_number
            //     });
            // }
            const isValidContract = await super.checkValidContract()
            if(!isValidContract){
                super.responseNotValidContract()
            }else{
                await super.insertLoan(PARTNER_CODE.KOV)
                if(!this.req.isEnoughDoc){
                    return super.responseNotEnoughDoc();
                }else{
                    loanContractRepo.updateContractStatus(STATUS.CREDIT_REVIEW,this.convertedBody.contract_number_old)
                    await super.updateLoan()
                    this.#insertOtherInfo()
                    // const a1Result = await super.checkEligible()
                    aadService.pushTaskMcV2(roleCode.DE,this.convertedBody.contract_number,CONTRACT_TYPE.CREDIT_LINE,STATUS.IN_DE_QUEUE);
                    loanContractRepo.updateContractStatus(STATUS.IN_DE_QUEUE,this.convertedBody.contract_number)
                    return this.res.status(200).json({
                        "code": "RECEIVED",
                        "message": "ELIGIBLE",
                        "contractNumber": this.convertedBody.contract_number
                    });
                }
            }
        }
        catch(err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

module.exports = {
    VskReview,
    KovReview
}