const getInfo = async (req, res) => {
    const { id, phone} = req.query;
    try {
        if(!id || !phone)
            return res.status(400).json({code: 1, message: 'Input Invalid'});
        const sql = "select * from loan_contract lc where id_number = $1 and phone_number1 = $2";
        const rs = (await req.poolRead.query(sql,[id,phone])).rows;
        return res.status(200).json({code: 0, message: 'get info success', data: rs});
    } catch (e) {
        console.log(e);
        console.log(`Error at get Info: ${id} | ${phone}`);
    }
}

module.exports = {
    getInfo
}