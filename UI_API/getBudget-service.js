function getBudgetData(req,res) {
	const contractNumber = req.query.contractnumber
	const sql = "select lc.turnover_12m ,ltt.info_type ,ltt.month_of_info ,ltt.value_of_month from loan_contract lc join loan_turnover_transaction ltt on lc.contract_number =ltt.contract_number where lc.contract_number =$1 and ltt.info_type ='turnover';"
	const poolRead = req.poolRead
	poolRead.query(sql,[contractNumber])
	.then(result => {
		if(result.rows === undefined || result.rows.length === 0) {
			res.status(400).json({
				code : -1,
				msg : "can not found contract number"
			})
		}
		else {
			res.status(200).json({
				code : 1,
				msg : "get Buget data successful",
				data : processOutput(result.rows)
			})
		}
	})
}

function processOutput(result) {
	let finalResult = {}
	let total3month = 0
	let month1 = 0
	let month2 = 0
	let month3 = 0
	result.forEach(element => {
		if(element.month_of_info === 1) {
			month1 = parseInt(element.value_of_month)
		}
		if(element.month_of_info === 2) {
			month2 = parseInt(element.value_of_month)
		}
		if(element.month_of_info === 3) {
			month3 = parseInt(element.value_of_month)
		}
	})

	const month12 = parseInt(result[0].turnover_12m)
	const avg3month = (month1 + month2 + month3) / 3
	const avg12month = month12 / 12
	return {
		t1 : month1,
		t2 : month2,
		t3 : month3,
		avg3M : avg3month,
		avg12M : avg12month,
		cardDebtT1 : 0,
		cardDebtT2 : 0,
		cardDebtT3 : 0,
		cardDebtAvg3M : 0,
		cardDebtAvg12M : 0
	}
}

module.exports = {
	getBudgetData	
}