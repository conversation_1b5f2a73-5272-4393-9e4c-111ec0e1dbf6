const camelcaseKeys = require('camelcase-keys');
const utils = require("../utils/helper")
const common = require("../utils/common")
const {detectType} = require("../utils/detectContractType")
const {roleCode,additionDoc,chekedDocByRoleField, CONTRACT_TYPE, PARTNER_CODE, DOC_TYPE} = require("../const/definition");
const { caseStatusCode } = require('../const/caseStatus');
const def = require('../const/definition')
const {getEkycError} = require("../repositories/ekyc-repo")
const documentRepo = require("../repositories/document")
const loanEsignRepo = require("../repositories/loan-esigning")
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")

async function getBundle(req, res) {
	try {
		let { role,contractNumber,isSearchCase, roleCode } = req.query
		let kunnNumber = contractNumber
		const poolRead = req.poolRead
		const poolWrite = global.poolWrite
		const config = req.config
		let productData;
		let partnerCode;
		const isKunn = await detectType(contractNumber, req) === "KU"
		// console.log('isKunn',isKunn)
		if(isKunn) {
			const kunnData = await kunnRepo.getKunnData(kunnNumber);
			const KUCode  = kunnData?.kunn_code;
			contractNumber = await kunnRepo.getContractByKU(kunnNumber)
			const productCode = await utils.getProductCode(poolRead,contractNumber)
			const getKUProductInfoUrl = config.basic.product[config.env] + config.data.productService.getKUBundleList + '?kunnCode=' + KUCode
			const getHMProductInfoUrl = config.basic.product[config.env] + config.data.productService.getBundleList + "?productcode=" + productCode
			const KUProductData =  (await common.getAPI(getKUProductInfoUrl)).data
			const HMProductData = (await common.getAPI(getHMProductInfoUrl)).data
			productData = {...KUProductData,...HMProductData}
			partnerCode = kunnData?.partner_code;
		}
		else {
			const productCode = await utils.getProductCode(poolRead,contractNumber)
			const getProductInfoUrl = config.basic.product[config.env] + config.data.productService.getBundleList + "?productcode=" + productCode
			productData  = (await common.getAPI(getProductInfoUrl)).data
			const contractData = await loanContractRepo.getLoanContract(contractNumber);
			partnerCode = contractData?.partner_code;
		}
		
		let caseData = await Promise.all([
			loanContractRepo.getLoanContract(contractNumber),
			getEkycError(contractNumber),
			documentRepo.isValidCallbackResubmitKOV(contractNumber),
			documentRepo.isValidResubmitSS(contractNumber)])
		if(isKunn){
			caseData = await Promise.all([
			loanContractRepo.getLoanContract(contractNumber),
			getEkycError(kunnNumber),
			documentRepo.isValidCallbackResubmitKOV(contractNumber),
			documentRepo.isValidResubmitSS(contractNumber)])
		}
		// const contractData = caseData[0]
		const caseStatus = caseData[0].status
		const ekycErrorMsgData = caseData[1]
		const isValidForSSUpload = partnerCode!=PARTNER_CODE.MISA?caseData[2]:caseData[3]
		let ekycMsgRes = {
			serviceError : "",
			msgError : ""
		}
		if(ekycErrorMsgData) {
			ekycMsgRes.serviceError = ekycErrorMsgData.service_error
			ekycMsgRes.msgError = ekycErrorMsgData.msg_error
		}
		let sql1 = `
			SELECT kunn_contract_number,foo.waiting_resubmit,foo.is_deleted,foo.doc_id,foo.is_checked,foo.is_ce_checked,foo.is_cp_checked,foo.is_ss_checked,foo.doc_type,foo.is_resubmit,lmd.case_code,lmd.deviation_cmt,foo.type_collection,foo.doc_group,foo.created_time, foo.doc_name_vn, foo.doc_name_vn_detail, foo.period FROM 
			(SELECT kunn_contract_number,waiting_resubmit,is_deleted,type_collection,is_resubmit,is_checked,is_ce_checked,is_cp_checked,is_ss_checked,doc_type,creation_time,doc_id,doc_group,
			lcd.creation_time AS created_time, lcd.doc_name_vn, lcd.doc_name_vn_detail, period FROM loan_contract_document lcd WHERE 1=1
			AND contract_number=$1 or kunn_contract_number=$2 GROUP BY kunn_contract_number,waiting_resubmit,doc_group,doc_type,creation_time,doc_id,is_checked,is_ce_checked,is_cp_checked,is_ss_checked,type_collection,is_resubmit,is_deleted,creation_time,doc_name_vn,doc_name_vn_detail, period
			ORDER BY lcd.creation_time ASC) AS foo LEFT JOIN loan_manual_decision lmd ON foo.doc_id=lmd.doc_id ORDER BY foo.created_time DESC
		`;
		
		// const isSigned = await loanContractRepo.validSigned(contractNumber)
		// if(isSigned && contractData.contract_type != CONTRACT_TYPE.CREDIT_LINE) {
		// 	const sql2 = `
		// 	SELECT foo.waiting_resubmit,foo.is_deleted,foo.doc_id,foo.is_checked,foo.is_ce_checked,foo.is_cp_checked,foo.doc_type,foo.is_resubmit,lmd.case_code,lmd.deviation_cmt,foo.type_collection,foo.doc_group,foo.created_time FROM 
		// 	(SELECT waiting_resubmit,is_deleted,type_collection,is_resubmit,is_checked,is_ce_checked,is_cp_checked,doc_type,creation_time,doc_id,doc_group,
		// 	lcd.creation_time AS created_time FROM loan_contract_document lcd WHERE doc_group !='OTHER' and type_collection ='DIBURSEMENT'
		// 	AND contract_number=$1 or kunn_contract_number=$1 GROUP BY waiting_resubmit,doc_group,doc_type,creation_time,doc_id,is_checked,is_ce_checked,is_cp_checked,type_collection,is_resubmit,is_deleted,creation_time 
		// 	ORDER BY lcd.creation_time ASC) AS foo LEFT JOIN loan_manual_decision lmd ON foo.doc_id=lmd.doc_id
		// `
		// 	result = await poolWrite.query(sql2, [contractNumber])
		// }
		// else {
		// }
		const result = await poolWrite.query(sql1, [contractNumber,kunnNumber])
		
		result.rows.map(x => {
			if(utils.isNullOrEmpty(x.kunn_contract_number) && utils.isNullOrEmpty(x.type_collection) || (!utils.isNullOrEmpty(isSearchCase) && isSearchCase==='true' && (DOC_TYPE.LCT,DOC_TYPE.LCTKU).includes(x.doc_type))) {
				x.type_collection = 'HM'
				if(isKunn && ['SPID','SNIDLR','SPIDLR','SNIDAR','SPIDAR'].includes(x.doc_type)) x.type_collection = 'KU'
			}
			else if (utils.isNullOrEmpty(x.type_collection)){
				x.type_collection = 'KU'
			}
		})
		// return res.status(200).json({
		// 	"msg": "get bundle list sucess",
		// 	"code": 1,
		// 	"recieved": 1,
		// 	"data": result.rows
		// })
		const rows = isKunn && isSearchCase ? result.rows?.filter(e=> !e.doc_type?.endsWith('_ENC') && (e.kunn_contract_number == kunnNumber || !e.kunn_contract_number)) : result.rows
		const data = processData(rows,productData,role,caseStatus,isValidForSSUpload,roleCode,partnerCode)
		res.status(200).json({
			"msg": "get bundle list sucess",
			"code": 1,
			"recieved": 1,
			"data": camelcaseKeys(data, { deep: true }),
			ekycMsg : ekycMsgRes
		})
		
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

function processData(dataList,productData, role,caseStatus,isValidForSSUpload,_roleCode,partnerCode) {

	let result = {}
	let resultList = []
	const mistakeList = {}
	dataList.forEach(element => {
		const condition = (![roleCode.CE,roleCode.SS, roleCode.RP].includes(role)  || (role == roleCode.SS && caseStatus == caseStatusCode.CP03)) && element.doc_group == additionDoc.docGroup;
		if(!condition)  {
			if(element.is_deleted == 0) {
				let isChecked;
				if([roleCode.CP,roleCode.CE,roleCode.SS].includes(role)) {
					isChecked = element[chekedDocByRoleField[role]]
				}
				else  {
					isChecked = 1
				}
				const minmax = getMinMax(element.doc_group,productData)
			
				if (!(element.doc_group in result)) {
					result[element.doc_group] = {}
				}
				result[element.doc_group].recivedDate = element.created_time
				result[element.doc_group].bundleName = element.doc_group
				result[element.doc_group].max = minmax.maxDoc
				result[element.doc_group].min = minmax.minDoc
				result[element.doc_group].bundleIsChecked = 0
				result[element.doc_group].haveResubmit = 0
				result[element.doc_group].docNameVn = element.doc_name_vn || ""
				if (!('docList' in result[element.doc_group])) {
					result[element.doc_group].docList = []
				}
				result[element.doc_group].docList.push({
					isChecked ,
					docName: element.doc_type,
					period: element.period,
					typeCollection: ['HM','KU','DIBURSEMENT'].includes( element.type_collection) ?  element.type_collection : 'HM',
					docId: element.doc_id,
					isResubmit : element.is_resubmit,
					requiredResubmit : isValidForSSUpload  ? 0 : (element.type_collection == def.TYPE_COLLECTION.DOC_EXTERNAL && element.waiting_resubmit == 1 ? 2 : element.waiting_resubmit),
					index : 1,
					docDesc : "",
					mistakeCode : "",
					mistakeDes : "",
					comment : "",
					docNameVn: element.doc_name_vn_detail || ""
				})
			}
		}
		if(element.is_deleted == 1) {
			let mistakeKey = element.doc_type

			if (element.period) {
				mistakeKey += '_' + element.period
			}

			if (!mistakeList.hasOwnProperty(mistakeKey)) {
				mistakeList[mistakeKey] = []
			}
			mistakeList[mistakeKey].push({
				isChecked : element[chekedDocByRoleField[role]] || 1,
				docName: element.doc_type,
				docId: element.doc_id,
				isResubmit : element.is_resubmit,
				index : mistakeList[mistakeKey].length + 1,
				docDesc : "",
				mistakeCode : element.case_code,
				mistakeDes : "",
				comment : element.deviation_cmt,
			})
		}
	})
	if (![roleCode.CE, roleCode.RP, roleCode.CP, undefined].includes(_roleCode)) delete result.OTHER
	for (let key in result) {
		const bundleElement = result[key]

		bundleElement.docList.forEach(document => {
			let mistakeKey = document.docName

			if (document.period) {
				mistakeKey += '_' + document.period
			}

			if(mistakeList.hasOwnProperty(mistakeKey)) {
				document.mistakeList = mistakeList[mistakeKey]
				document.index = mistakeList[mistakeKey].length + 1
			}
			if(document.isChecked == 1) {
				result[key].bundleIsChecked = 1
			}
			if(partnerCode != PARTNER_CODE.SMA) {
				if(role == roleCode.SS) {
					if(document.requiredResubmit == 1) {
						result[key].haveResubmit = 1
					}
				}
				else {
					if(document.isResubmit == 1) {
						result[key].haveResubmit = 1
					}
				}
			}

			result[key].bundleType = document.typeCollection

		})
		resultList.push(result[key])
	}

	resultList.map(x =>  {
		if(x.haveResubmit == 1) {
			x.bundleIsChecked =0
		}
		return x
	})

	resultList.push(
        resultList.splice(
            resultList.findIndex(
                (element) => element.bundleName === "KD_EXTERNAL_DOCUMENT"
            ),
            1
        )[0]
    );
	if(resultList[0]===undefined) resultList = null
	return resultList
}

function getMinMax(bundleCode,data) {
	// console.log('data',data)
	if(data.hasOwnProperty(bundleCode)) {
		let minDoc = data[bundleCode].minDocs
		let maxDoc = data[bundleCode].maxDocs
		return {minDoc,maxDoc}
	}
	else {
		return {
			"minDoc" : additionDoc.minDoc,
			"maxDoc" : additionDoc.maxDoc
		}
	}
}

async function getDocumentWithoutCheckDocs(req, res) {
    try {
        let input = req.query
        common.log('req query getDocumentWithoutCheckDocs: ' + JSON.stringify(input))
        let loanDocList = await documentRepo.getDocumentByContractNumberV2(input.contractNumber)
        let resultList = [];
        for(let value of loanDocList){
            resultList.push({
                url: value.url,
                docType: value.doc_type,
                docName: value.doc_type,
                docId: value.doc_id,
                docDesc: await getDocTypeName(req, value.doc_type),
                fileName: value.file_name,
                docSource: ""
            })
        }
        return res.status(200).json({
            code: 0,
            message: 'Success',
            data: resultList
        })
    } catch (e) {
        console.log(e)
        console.log('Error at getDocumentWithoutCheckDocs: ' + e.message)
        res.status(500).json({ status: -1, message: 'System error' })
    }
}

async function getDocumentWithoutCheckDocsV3(req, res) {
    try {
        let input = req.query
        common.log('req query getDocumentWithoutCheckDocsV3: ' + JSON.stringify(input))

        let loanDocList = [], lctDoc, lctKuDoc;
        const isKunn = await detectType(input.contractNumber, req) === "KU"
        if (isKunn) {
            let kunnNumber = input.contractNumber
            let contractNumber = await kunnRepo.getContractByKU(kunnNumber)

            const docRs = await Promise.all([
                documentRepo.getDocumentByContractNumberV2(kunnNumber),
                loanEsignRepo.getSingedContract(contractNumber),
                loanEsignRepo.getSingedContract(kunnNumber),
            ])

            loanDocList = docRs[0]
            lctDoc = docRs[1]
            lctKuDoc = docRs[2]
        } else {
            const docRs = await Promise.all([
                documentRepo.getDocumentByContractNumberV2(input.contractNumber),
                loanEsignRepo.getSingedContract(input.contractNumber),
            ])
            loanDocList = docRs[0]
            lctDoc = docRs[1]
        }

        let resultList = [];
        for (let value of loanDocList) {
        	if (value.doc_type === def.DOC_TYPE.LCT) {
                lctDoc = null
			}

            if (value.doc_type === def.DOC_TYPE.LCTKU) {
                lctKuDoc = null
            }

            resultList.push({
                url: value.url,
                docType: value.doc_type,
                docName: value.doc_type,
                docId: value.doc_id,
                docDesc: await getDocTypeName(req, value.doc_type),
                fileName: value.file_name,
                docSource: ""
            })
        }

        if (lctDoc) {
            let docType = def.DOC_TYPE.LCT
            resultList.push({
                url: lctDoc,
                docType: docType,
                docName: docType,
                docId: '',
                docDesc: await getDocTypeName(req, docType),
                fileName: utils.getFileNameFromUrl(lctDoc),
                docSource: ""
            })
        }
        if (lctKuDoc) {
            let docType = def.DOC_TYPE.LCTKU
            resultList.push({
                url: lctKuDoc,
                docType: docType,
                docName: docType,
                docId: '',
                docDesc: await getDocTypeName(req, docType),
                fileName: utils.getFileNameFromUrl(lctKuDoc),
                docSource: ""
            })
        }
        return res.status(200).json({
            code: 0,
            message: 'Success',
            data: resultList
        })
    } catch (e) {
        console.log(e)
        console.log('Error at getDocumentWithoutCheckDocsV3: ' + e.message)
        res.status(500).json({ status: -1, message: 'System error' })
    }
}

async function getDocumentWithoutCheckDocsV2(req, res) {
    try {
        let input = req.query
        common.log('req query getDocumentWithoutCheckDocsV2: ' + JSON.stringify(input))
        let loanDocList = await documentRepo.getDocumentByContractNumber(input.contractNumber)
        let resultList = [];
        for(let value of loanDocList){
            resultList.push({
                url: value.url,
                docType: value.doc_type,
                docName: value.doc_type,
                docId: value.doc_id,
                docDesc: await getDocTypeName(req, value.doc_type),
                fileName: value.file_name,
                docSource: "",
				is_deleted: value.is_deleted,
				is_resubmit: value.is_resubmit,
				created_on: value.creation_time
            })
        }
        return res.status(200).json({
            code: 0,
            message: 'Success',
            data: resultList
        })
    } catch (e) {
        console.log(e)
        console.log('Error at getDocumentWithoutCheckDocsV2: ' + e.message)
        res.status(500).json({ status: -1, message: 'System error' })
    }
}

const getDocTypeName = async function(req, docType){
	try{
        const config = req.config;
		const lb = config.basic.masterData[req.config.env];
		const uri = '/masterdata/v1/document?code='
        let url = lb+ uri;
        url += docType;
        let result = await common.getAPI(url);
        if(result.error === 0){
            return result.data.valueNameVn || "";
        }
        return "";
	}catch(err){
		console.log(`getDocTypeName error: ${err.message}`);
		console.log(err);
        return "";
	}
}

module.exports = {
	getBundle,
	getDocumentWithoutCheckDocs,
	getDocumentWithoutCheckDocsV2,
	getDocumentWithoutCheckDocsV3,
}