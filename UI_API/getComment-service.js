const {roleCode} = require("../const/definition")
const common = require("../utils/common")
const documentRepo = require("../repositories/document")
const {getValueCode_v3} = require("../utils/masterdataService")

function getCmt(req,res) {
	const {contractNumber,role} = req.query
	let sql = `select lmd.*,lcd.doc_id,lcd.doc_type,lcd.file_name from loan_manual_decision lmd left join loan_contract_document lcd on lmd.doc_id  = lcd.doc_id where lmd.contract_number = $1 and role != $2 order by created_date desc`
	let params = [contractNumber,roleCode.SS]
	if(role !== undefined && role !== '') {
		sql = `select lmd.*,lcd.doc_id,lcd.doc_type,lcd.file_name from loan_manual_decision lmd left join loan_contract_document lcd on lmd.doc_id  = lcd.doc_id where lmd.contract_number = $1 and lmd.role = $2 and role != $3 order by created_date desc `
		params = [contractNumber,role,roleCode.SS]
	}
	req.poolRead.query(sql,params)
	.then(async result => {
		
		const data = await processOutput3(result.rows)
		res.status(200).json({
			"msg" : "get Cmt list sucess",
			"code" : 1,
			"data" : data
		})
	})
	.catch(error => {
		console.log(error)
		res.status(500).json({
			"msg" : "get Cmt list error",
			"code" : -1
		})	
	})

}

function getCECmt(req,res) {
	const contractNumber = req.query.contractnumber

	const sql = "select * from loan_manual_decision where contract_number = $1 and role = 'CE' order by id desc limit 1"
	req.poolRead.query(sql,[contractNumber])
	.then(async result => {
		const data = await processOutput(req.poolRead,result.rows)
		res.status(200).json({
			"msg" : "get Cmt list sucess",
			"code" : 1,
			"data" : data
		})
	})
	.catch(error => {
		res.status(500).json({
			"msg" : "get Cmt list error",
			"code" : -1
		})	
	})

}

async function getMCcmt(req,res) {
	try {
		const poolWrite = global.poolWrite
		const contractNumber = req.query.contractNumber
		const sql = "select * from loan_manual_decision where contract_number = $1 and role = 'MC'"
		const rs = await poolWrite.query(sql,[contractNumber])
		const data = await processOutput(poolWrite,rs.rows)
		return res.status(200).json({
			"msg" : "Lấy thông tin comment thành công.",
			"code" : 1,
			"data" : data
		})
	}
	catch(err) {
		console.log(err)
		return common.responseErrorInternal(res,err)
	}

}

async function processOutput(poolRead,cmtList) {
	let resultList = []
	let count = 0
	for(let i=0;i<cmtList.length;i++) {
		const element = cmtList[i]
		count += 1
		let cmtObj = {}
		cmtObj.role = element.role
		cmtObj.summary = "Summary" + count
		cmtObj.document = {
			docName : await getDocName(poolRead,element.doc_id) || '',
			docId : element.doc_id || '',
			docType : element.doc_id? await documentRepo.getDocType(element.doc_id):''
		}
		cmtObj.decision = element.result_chk
		cmtObj.date = element.created_date
		cmtObj.taskId = element.task_id
		cmtObj.commentDetail = {
			case : element.task_id,
			code : element.mistake_desc,
			comment : element.deviation_cmt,
			status : element.result_chk
		}
		resultList.push(cmtObj)
	}
	return resultList
}


async function processOutput3(data) {
	const resultList = []
	const resultDict = {}

	for(const row of data){
		let docName = await getValueCode_v3(row.doc_type,'DOCUMENT')
		const taskId = row.task_id
		if(!resultDict.hasOwnProperty(taskId)) {
			resultDict[taskId] = {}
			resultDict[taskId].role = row.role
			resultDict[taskId].decision = row.result_chk
			resultDict[taskId].created_date = row.created_date
			resultDict[taskId].commentDetail = []
		}
		resultDict[taskId].commentDetail.push({
			case : row.id,
			docName : docName,
			type : row.result_chk,
			docId : row.doc_id,
			fileName : row.file_name,
			decisionCode : row.case_code,
			comment : row.deviation_cmt,
			user : row.assigne
		})
	}
	
	for(let key in resultDict) {
		resultList.push(resultDict[key])
	}
	return resultList
}

async function getDocName(poolRead,docId) {
	const sql = "select doc_type from loan_contract_document where doc_id=$1"
	let rs = await poolRead.query(sql,[docId])
	if(rs === undefined || rs.rows.length === 0) {
		return ''
	}
	else {
		return rs.rows[0].doc_type
	}
}

function getDocumentLOV(req,res) {
	const role = req.query.role
	const contractNumber = req.query.contractnumber
	const poolRead = req.poolRead

	if(role == 'MC') {
		const sql = "select lcd.doc_type from loan_manual_decision lmd join loan_contract_document lcd on lmd.doc_id = lcd.doc_id where lmd.contract_number =$1 and role='CE' and result_chk in ('RESUBMIT','REJECT')"
		poolRead.query(sql,[contractNumber]) 
		.then(result => {
			const data  = result.rows.map(x => x.doc_type)
			data.push('OTHER')
			return res.status(200).json({
				code : 1,
				msg : "get document needed to upload success",
				data : data
			})
		})	
		.catch(error => {
			return res.status(400).json({
				code : -1,
				msg : "invalid contract number"
			})
		})
	}
	else if(role == 'RP') {
		return res.status(200).json({
			code : 1,
			msg : "get document needed to upload success",
			data : ['OTHER']
		})
	}
	else {
		return res.status(400).json({
			code : 0,
			msg : "invalid role"
		})
	}

}


module.exports = {
	getCmt,
	getCECmt,
	getDocumentLOV,
	getMCcmt
}