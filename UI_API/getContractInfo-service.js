const utils = require("../utils/helper")
const {caseStatus} = require("../const/caseStatus")
const productService = require("../utils/productService")

async function getCurCaseCode(req,res){
	const {contractNumber} = req.query
	const poolRead = req.poolRead
	const statusCode = await utils.getStatus(poolRead,contractNumber)
	let statusDesc = statusCode
	if(caseStatus.hasOwnProperty(statusCode)) {
		statusDesc = caseStatus[statusCode]
	}
	return res.status(200).json({
		code : 1,
		msg : "get msg successfully.",
		data : statusDesc
	})
}

const getContractByTaxIdAndRegistId = async (req, res) => {
    try {
        const payLoad = req.query
        if (!payLoad || !payLoad.taxId || !payLoad.registId) {
            return res.status(200).json({ code: 1, message: "<PERSON><PERSON><PERSON><PERSON><PERSON> thiếu thông tin!" })
        }
        const poolRead = global.poolRead
        const sql = "select contract_number, approval_date, status, product_code, rejection_reason from loan_contract where sme_tax_id = $1 and registration_number = $2"
        const result = await poolRead.query(sql, [payLoad.taxId, payLoad.registId])
        if (result.rowCount == 0) {
            return res.status(200).json({ code: 0, message: "Không tìm thấy thông tin hợp đồng nào!" })
        }
        for(let contract of result.rows){
            const productData = await productService.getProductInfoV2(contract.product_code)
            contract.product_name = productData ? productData.prdctName : null
            contract.highest_loan = null
        } 
        return res.status(200).json({ code: 0, message: "Lấy thông tin thành công", data: result.rows })
    }
    catch (error) {
        console.log(error)
        return res.status(500).json({ code: 'ERROR', message: error.message })
    }
}

module.exports = {
	getCurCaseCode,
	getContractByTaxIdAndRegistId
}