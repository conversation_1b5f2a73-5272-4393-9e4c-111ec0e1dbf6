const { detectType } = require("../utils/detectContractType");

const getOffers = async (req, res) => {
    try {
        const { contractnumber  } = req.query
        if (!contractnumber) {
            res.status(200).json({
                code: -1,
                message: 'Missing contractnumber'
            })
        }
        else {
            if (await detectType(contractnumber, req) === "KU" ) {
                const sql = "SELECT id,offer_amt,tenor, int_rate, is_delt FROM loan_offer_selection WHERE kunn_id = $1 "
                const params = [contractnumber]
                const data = await req.poolRead.query(sql, params)
                // const sql1 = "SELECT method FROM kunn WHERE kunn_id = $1"
                // const data1 = await req.poolRead.query(sql1, params)
                if (data.rows.length > 0) {
                    return res.status(200).json({
                        code: 1,
                        data: data.rows.map(item => {
                            const offer = {
                                offerId: item.id,
                                creditAmount: parseInt(item.offer_amt),
                                periods: parseInt(item.tenor),
                                monthlyInterestRate: parseFloat(item.int_rate)*100 + "%",
                                docList: ["DL","CMND"]
                            }
                            return offer
                        }),
                    })
                }
                else {
                    return res.status(200).json({
                        code: -1,
                        message: "Không tìm thấy hợp đồng"
                    })
                }
            }
            else {
                const sql = "SELECT id,offer_amt,tenor, int_rate,is_delt FROM loan_offer_selection WHERE contract_number = $1 and kunn_id IS NULL and is_delt=0"
                const params = [contractnumber]
                const data = await req.poolRead.query(sql, params)
                if (data.rows.length > 0) {
                    return res.status(200).json({
                        code: 1,
                        type: await detectType(contractnumber, req) === "KU"? "KHẾ ƯỚC" : "HẠN MỨC"  ,
                        data: data.rows
                    })
                }
                else {
                    return res.status(200).json({
                        code: -1,
                        message: "Không tìm thấy hợp đồng"
                    })
                }
            }
        }
    }
    catch(e) {
        res.status(500).json({
            code: -1,
            message: "Service error"
        })
    }

}

module.exports = {
    getOffers
}