const common = require("../utils/common")
const camelcaseKeys = require('camelcase-keys');

async function getVariable(req,res) {
	try {
		const poolRead = req.poolRead

		const contractNumber = req.query.contractnumber
		const scoreType = "CHECKLIST"
		const config = req.config

		const productUri = req.config.data.productService.getVariableTable
		const productUrl = config.basic.product[config.env] + productUri
		let productSheme = await common.getAPI(productUrl)
		productSheme = productSheme.data
		let checkedList = await poolRead.query("select input_param_name,input_param_value from loan_score_detail lsd where contract_number = $1 and param_group =$2",[contractNumber,scoreType])
		checkedList = checkedList.rows
		productSheme.forEach(productElement => {
			const code = parseInt(productElement.code)
			if(checkedList.length == 0) {
				productElement.isChecked = 0
			}
			else {
				checkedList.forEach(checkingElement => {
					if(code == parseInt(checkingElement.input_param_name)) {
						productElement.isChecked = parseInt(checkingElement.input_param_value)
					}
				})
			}
		})
		return res.status(200).json({
			code : 1,
			msg : "get variable data success",
			data : camelcaseKeys(productSheme,{deep:true})
		})
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : 1,
			msg : "get variable data error",
			data : []
		})
	}
}

function getChecklistData(req,res) {
	
	const productUri = req.config.data.productService.getCheckListTable
	const productUrl = req.config.basic.product[req.config.env] + productUri
	common.getAPI(productUrl)
	.then(rs => {
		res.status(200).json({
			"code" : 1,
			"msg" : "get checklist data success",
			"data" : camelcaseKeys(rs,{deep: true})
		})
	})
	.catch(err => {
		console.log(err)
		res.status(500).json({
			"code" : -1,
			"msg" : "service error"
		})
	})
}

function getBudgetData(req,res) {
	const productUri = req.config.data.productService.getBudgetTable
	const productUrl = req.config.basic.product[req.config.env] + productUri
	common.getAPI(productUrl)
	.then(rs => {
		res.status(200).json({
			"code" : 1,
			"msg" : "get budge analysys data success",
			"data" : camelcaseKeys(rs,{deep: true})
		})
	})
	.catch(err => {
		console.log(err)
		res.status(500).json({
			"code" : -1,
			"msg" : "service error"
		})
	})
}

function getCICData(req,res) {
	const productUri = req.config.data.productService.getCICTable
	const productUrl = req.config.basic.product[req.config.env] + productUri
	common.getAPI(productUrl)
	.then(rs => {
		res.status(200).json({
			"code" : 1,
			"msg" : "get CIC data success",
			"data" : camelcaseKeys(rs,{deep: true})
		})
	})
	.catch(err => {
		console.log(err)
		res.status(500).json({
			"code" : -1,
			"msg" : "service error"
		})
	})
}

module.exports = {
	getVariable,
	getChecklistData,
	getBudgetData,
	getCICData
}