const s3Service = require("../upload_document/s3-service")
const url = require("url");

const getDoc = (req, res) => {
    try {
        //console.log('Start downloading file...')
        const contractNumber = req.query.contractnumber

        //console.log(contractNumber)
        if (contractNumber === undefined || contractNumber === '') {
            return res.status(400).send({code: 'INVALID_REQUEST', message: 'contractNumber param invalid.'})
        } else {
            req.poolRead.query("select unsigned_contract from loan_esigning where contract_number=$1",[contractNumber])
            .then(result => {
                if (result.rows === undefined) {
                    return res.status(400).send({code: 'INVALID_REQUEST', message: 'contractNumber param invalid.'})
                }
                else {
                    let unsignedFileUrl = result.rows[0].unsigned_contract
                    let unsignedFileKey = url.parse(unsignedFileUrl).path.slice(1)
                    //console.log(unsignedFileKey)
                    s3Service.downloadFile(req.config.data,unsignedFileKey)
                    .then(buffer => {
                        //console.log(buffer)
                        updateSigningStatus(req.poolWrite,contractNumber)
                        return res.status(200).send(Buffer.from(buffer.Body).toString('base64'))
                    })
                    .catch(err => {
                        console.log(err)
                        return res.status(500).json({code: 'ERROR', message: 'service error.'})
                    })
                }

            })
            .catch(error => {
                console.log(error)
            })
        }
    }
    catch (error) {
        console.log(error)
        return res.status(500).json({code: 'ERROR', message: 'service error.'})
    }
}

function updateSigningStatus(poolWrite,contractNumber) {
    let sql = "update loan_esigning set status=$1 where contract_number=$2"
    poolWrite.query(sql,['SIGN IN PROGRESS',contractNumber])
}

module.exports = {
    getDoc
}