const s3Service = require("../upload_document/s3-service");
const url = require("url");
const mime = require("mime-types");

const getDocument = (req, res) => {
  try {
    // console.log('Start downloading file...')
    const documentid = req.query.documentid;
    //console.log(documentid)
    if (documentid === undefined || documentid === "") {
      return res.status(400).send({ code: "INVALID_REQUEST", message: "documentid param invalid." });
    } else {
      req.poolRead
        .query("select * from loan_contract_document where doc_id=$1", [documentid])
        .then((result) => {
          if (result.rows.length <= 0 || result.rows === undefined) {
            return res.status(400).send({ code: "INVALID_REQUEST", message: "documentid param invalid." });
          } else {
            const fileName = result.rows[0].file_name;
            const fileKey = result.rows[0].file_key;
            // console.log({fileExtend})
            if (!fileKey) {
              return res.status(200).json({
                code: -1,
                message: "Not found documentid",
              });
            } else {
              const fileExtend = fileKey.split(".").pop().toLowerCase();
              s3Service
                .downloadFile(req.config.data, fileKey)
                .then((buffer) => {
                  if (["jpg", "jpeg", "png"].includes(fileExtend)) {
                    res.set("Content-Type", "image/" + fileExtend);
                  } else if (fileExtend == "pdf") {
                    res.set("Content-Type", "application/pdf");
                  } else if (fileExtend == "xlsx") {
                    res.set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                  } else if (fileExtend == "docx") {
                    res.set("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                  } else if (fileExtend == "doc") {
                    res.set("Content-Type", "application/msword");
                  } else if (fileExtend == "mp4") {
                    res.set("Content-Type", "video/mp4");
                  } else if (fileExtend == "csv") {
                    res.set("Content-Type", "text/csv");
                  } else if (fileExtend == "xlsm") {
                    res.set("Content-Type", "application/vnd.ms-excel.sheet.macroEnabled.12");
                  } else if (fileExtend == "mp3") {
                    res.set("Content-Type", "audio/mp3");
                  } else {
                    const contentType = mime.lookup(fileName);
                    res.set("Content-Type", contentType);
                  }
                  // else {
                  //     res.set("Content-Type","text/xml")
                  // }
                  // res.attachment(fileName)
                  return res.status(200).send(Buffer.from(buffer.Body));
                })
                .catch((err) => {
                  console.log(err);
                  return res.status(500).json({ code: "ERROR", message: "service error." });
                });
            }
          }
        })
        .catch((error) => {
          console.log(error);
        });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({ code: -1, message: "service error." });
  }
};
const AWS = require("aws-sdk");
const getDocPreSigned = async (req, res) => {
  // let configS3 = req.config.data.s3
  // console.log(req.config)
  const awsConfig = req.config.data.aws;
  const s3Config = req.config.data.awsS3;
  // console.log({awsConfig})
  // console.log({s3Config})

  const s3 = new AWS.S3({
    endpoint: "s3.ap-southeast-1.amazonaws.com", // Put you region
    accessKeyId: awsConfig.accessKeyId, // Put you accessKeyId
    secretAccessKey: awsConfig.secretAccessKey, // Put you accessKeyId
    Bucket: s3Config.bucketName, // Put your bucket name
    signatureVersion: "v4",
    region: s3Config.region, // Put you region
  });
  const params = {
    Bucket: s3Config.bucketName,
    Key: req.query.file_path,
    Expires: 60 * 10,
  };
  try {
    const url = await new Promise((resolve, reject) => {
      s3.getSignedUrl("getObject", params, (err, url) => {
        err ? reject(err) : resolve(url);
      });
    });
    // console.log(url)
    res.send(url);
  } catch (err) {
    if (err) {
      console.log(err);
    }
  }
};

module.exports = {
  getDocument,
  getDocPreSigned,
};
