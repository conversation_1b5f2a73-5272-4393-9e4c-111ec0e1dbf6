const loanTarget = require('../repositories/loan-target-repo')
const loanContractRepo = require("../repositories/loan-contract-repo")
const common = require("../utils/common")
const camelcaseKeys = require("camelcase-keys");
const helpers = require('../utils/helper');
const { CONTRACT_TYPE } = require('../const/definition');

const getFI = async function (req, res) {
    try {
        let payLoad = req.query
        let resultData = []
        let resultOption = await loanTarget.getFIByName();
        for (let item of resultOption) {
            let nameKey = item.name_fi_code
            let listOptionFI = await loanTarget.getOptionsFI(item.name_fi_code)
            item.listOption = camelcaseKeys(listOptionFI)
            resultData.push({ nameKey: nameKey, dataKey: camelcaseKeys(listOptionFI) })
        }
        let searchResult = await loanTarget.getFIByContractNumber(payLoad.contractNumber)
        if (!helpers.isNullOrEmpty(searchResult)) {
            for (let data of resultData) {
                for (let item of searchResult) {
                    if (data.nameKey == item.name_fi_code) {
                        data.currentRecipe = item.recipe
                        data.currentCriterionScore = item.criterion_score
                        data.currentPercentage = item.percentage
                        data.currentScoreCustomer = Number(item.score_customer)
                        data.currentOptionCode = item.option_fi_code
                    }
                }
            }
        }
        return res.status(200).json({ code: 0, message: "Lấy thông tin thành công", data: resultData })
    } catch (error) {
        common.log(`getAllFinancialIndicators fail: ${error.message}`, 'error')
        return res.status(500).json({ code: -1, message: `getAllFinancialIndicators fail: ${error.message}` })
    }
}

const selectFI = async function (req, res) {
    try {
        let payLoad = req.query
        if (!payLoad || !payLoad.nameFiCode || !payLoad.optionFiCode) {
            return res.status(200).json({ code: 1, message: "Params is missing" })
        }
        let result = await loanTarget.selectFI(payLoad);
        result.score_customer = (Number(result[0].criterion_score) * Number(result[0].percentage)) / 100
        let selectResult = {
            criterion_score: result[0].criterion_score,
            percentage: result[0].percentage,
            score_customer: result.score_customer
        }
        return res.status(200).json({ code: 0, data: camelcaseKeys(selectResult) })
    } catch (error) {
        common.log(`selectOptionFI fail: ${error.message}`, 'error')
        return res.status(500).json({ code: -1, message: `selectOptionFI fail: ${error.message}` })
    }
}

const saveFI = async function (req, res) {
    try {
        let bodyRequest = req.body
        const contractNumber = bodyRequest.contractNumber
        const schema = bodyRequest.schema
        const contractData = await loanContractRepo.getLoanContract(contractNumber);
        // const productCode = contractData?.product_code;
        const contractType = contractData?.contract_type;
        let productCodeNew;
        if(contractType==CONTRACT_TYPE.CREDIT_LINE){
            productCodeNew=schema=='STANDARD'?'SME_MISA_HM_STANDARD':schema=='VIP'?'SME_MISA_HM_VIP':schema=='PREMIUM'?'SME_MISA_HM_PREMIUM':schema=='PLATINUM'?'SME_MISA_HM_PLATINUM':'';
        }
        else{
            productCodeNew=schema=='STANDARD'?'SME_MISA_VM_STANDARD':schema=='VIP'?'SME_MISA_VM_VIP':schema=='PREMIUM'?'SME_MISA_VM_PREMIUM':schema=='PLATINUM'?'SME_MISA_VM_PLATINUM':'';
        }
        if (!bodyRequest) {
            return res.status(200).json({ code: 1, message: "Body is invalid" })
        }
        for (let item of bodyRequest.dataContract) {
            item.contractNumber = contractNumber
            item.createdBy = bodyRequest.createdBy
            let resultSearch = await loanTarget.searchFI(item)
            if (helpers.isNullOrEmpty(resultSearch)) {
                await loanTarget.saveFI(item)
            } else {
                await loanTarget.updateFI(item)
            }
        }
        await Promise.all([
            loanContractRepo.updateFieldLoanContract(contractNumber, 'cust_type', schema),
            loanContractRepo.updateFieldLoanContract(contractNumber, 'product_code', productCodeNew)
        ])
        return res.status(200).json({ code: 0, message: "Save data is successfully" })
    } catch (error) {
        common.log(`saveFI fail: ${error.message}`, 'error')
        return res.status(500).json({ code: -1, message: `saveFI fail: ${error.message}` })
    }
}

const getFIByContractNumber = async function (req, res) {
    try {
        let payLoad = req.query
        if (!payLoad || !payLoad.contractNumber) {
            return res.status(200).json({ code: 1, message: "contractNumber is missing" })
        }
        let resultFI = await loanTarget.getFIByContractNumber(payLoad.contractNumber)
        if (helpers.isNullOrEmpty(resultFI)) {
            return res.status(200).json({ code: 1, message: "Not found data with contracNumber: " + payLoad.contractNumber })
        }
        return res.status(200).json({ code: 0, message: "Lấy thông tin thành công", data: camelcaseKeys(resultFI) })
    } catch (error) {
        common.log(`getFIByContractNumber fail: ${error.message}`, 'error')
        return res.status(500).json({ code: -1, message: `getFIByContractNumber fail: ${error.message}` })
    }
}

module.exports = {
    getFI,
    selectFI,
    saveFI,
    getFIByContractNumber
}