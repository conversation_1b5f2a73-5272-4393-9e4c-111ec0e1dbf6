const s3Service = require("../upload_document/s3-service")
const url = require("url");

const getKunnDetail = (req, res) => {
    try {
        // console.log('Start downloading file...')
        const KunnContractNumber = req.query.contract_number
        if (KunnContractNumber === undefined || KunnContractNumber === '') {
            return res.status(400).send({code: 'INVALID_REQUEST', message: 'contract_number param invalid.'})
        } else {
            req.poolRead.query("select * from loan_esigning where contract_number=$1",[KunnContractNumber])
            .then(result => {
                if (result.rows.length <= 0 || result.rows === undefined) {
                    return res.status(400).send({code: 'INVALID_REQUEST', message: 'contract_number param invalid.'})
                }
                else {
                    let fileKey = '';
                    if(result.rows[0].status == 'SIGNED'){
                        fileKey = url.parse(result.rows[0].contract_signed_path).path.slice(1)
                    }else{
                        fileKey = url.parse(result.rows[0].unsigned_contract).path.slice(1)
                    }
                    // const fileName = result.rows[0].file_name
                    // const fileKey = result.rows[0].file_key
                    // console.log({fileExtend})
                    if(!fileKey) {
                        return res.status(200).json({
                            code:-1,
                            message: "Not found kunNumber"
                        })
                    }
                    else {
                        const fileExtend = fileKey.split(".").pop().toLowerCase();
                        s3Service.downloadFile(req.config.data,fileKey)
                        .then(buffer => {
                            if(['jpg','jpeg','png'].includes(fileExtend)) {
                                res.set("Content-Type", "image/"+fileExtend);
                            }
                            else if(fileExtend=='pdf') {
                                res.set("Content-Type", "application/pdf");
                            }
                            else if(fileExtend=='xlsx') {
                                res.set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                            }
                            else if(fileExtend == 'docx') {
                                res.set("Content-Type","application/vnd.openxmlformats-officedocument.wordprocessingml.document");
                            }
                            else if(fileExtend == 'doc') {
                                res.set("Content-Type","application/msword")
                            }
                            else if(fileExtend == 'mp4') {
                                res.set("Content-Type","video/mp4")
                            }
                            else if(fileExtend == 'csv') {
                                res.set("Content-Type","text/csv")
                            }
                            else if(fileExtend == 'xlsm') {
                                res.set("Content-Type","application/vnd.ms-excel.sheet.macroEnabled.12")
                            }
                            else if(fileExtend == 'mp3') {
                                res.set("Content-Type","audio/mp3")
                            }
                            else {
                                res.set("Content-Type","text/xml")
                            }
                            // res.attachment(fileName)
                            let k = buffer.Body.Buffer;
                            return res.status(200).send(Buffer.from(buffer.Body))
                            //return res.status(200).json({path: fileExtend, message: 'upload file thành công'})
                        })
                        .catch(err => {
                            console.log(err)
                            return res.status(500).json({code: 'ERROR', message: 'service error.'})
                        })
                        //return res.status(200).json({path: fileKey, message: 'Lấy thông tin thành công'})
                    }
                }

            })
            .catch(error => {
                console.log(error)
            })
        }
    }
    catch (error) {
        console.log(error)
        return res.status(500).json({code: -1, message: 'service error.'})
    }
}


module.exports = {
    getKunnDetail
}