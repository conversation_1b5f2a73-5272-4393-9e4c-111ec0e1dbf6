const { postAPI } = require('../utils/common')
const getKunnInfo = async (req, res) => {
    try {
        const config = req.config
        const { contractnumber, kunnid } = req.query
        if(contractnumber && kunnid ) {
            const result = await validContract(req,contractnumber, kunnid)
            if(!result) {
              return  res.status(200).json({
                    code: 0,
                    message: 'Invalid params',
                })
            }
        }
        if (!contractnumber && !kunnid) {
            res.status(200).json({
                code: 0,
                message: 'Missing params',
            })
        }
        else {
            const dataKUNN = await getKunnInfomation(req)
            const contractNumber = dataKUNN ? dataKUNN.contract_number : req.query.contractnumber
            
            const data = await getContractInfomation(req,contractNumber)
            const lmsHeader = await getAuthLms(req)
            const lmsUri = req.config.data.lms.checkAvailable;
            const lmsUrl = config.basic.lmsMc[config.env] + lmsUri
            if (!data) {
                return res.status(200).json({
                    code: -1,
                    message: "Invalid parameter"
                })
            }
            const body = { contractNumber: contractNumber }
            const result = await postAPI(lmsUrl, body, lmsHeader)
            res.status(200).json({
                code: 1 ,
                message: "SUCCESS",
                data: {
                    contractInfomation: {
                        contractNumber: data.contract_number || null,
                        dateRequest: data.created_date || null,
                        dateApproval: data.approval_date || null,
                        requestCreditAmount: data.request_amt || null,
                        approvalAmount: data.offer_amt || null,
                        availableAmount: result.data ? result.data.avalibleAmount : 0,
                    },
                    kunnInfomation: dataKUNN && dataKUNN.kunn_id ? {
                        kunn_id: dataKUNN.kunn_id,
                        requestAmt: dataKUNN.with_draw_amount,
                        dateRequest: dataKUNN.created_date || null,
                        availableAmount: dataKUNN.with_draw_amount || null,
                        approvalAmount: dataKUNN.offer_amt || null,
                        disbursement: dataKUNN.offer_amt || null,
                        method: parseMethod(dataKUNN.method) || null,
                    } : "Không tìm thấy thông tin khế ước"
                },
            })
        }
    }
    catch (error) {
        console.log(error);
        res.status(500).json({
            code: 1,
            message: "ERROR"
        })
    }
}

const validContract = async (req,contractnumber, kunn_id) => {
    const sql = "SELECT * from kunn WHERE contract_number = $1 AND kunn_id = $2"
    const params = [contractnumber, kunn_id]
    const data = await req.poolRead.query(sql, params)
    if (data.rows.length) {
        return true
    }
    else {
        return false
    }
}

const parseMethod = (method) => {
    switch (method) {
        case "1":
            return "month"
        case "3":
            return "quarter"
        case "6":
            return "biannual"
        case "12":
            return "annual"
        default:
            return "month";
    }
}

const getAuthLms = async (req) => {
    const url = req.config.data.app.urlAuthentSingIn
    const bodySignIn = { username: global.config.data.lms.lmsUsername, password: global.config.data.lms.lmsPassword }
    const SignInUrl = req.config.basic.aaa[req.config.env] + url
    const lmsUri = req.config.data.lms.checkAvailable;
    const result = await postAPI(SignInUrl, bodySignIn)
    const lmsHeader = {
        uiid: result.uiid,
        token: result.token,
        service: lmsUri
    }
    return lmsHeader
}

const getContractInfomation = async (req,contractnumber) => {
    let sql = `
    SELECT *
    FROM loan_contract 
    INNER JOIN loan_offer_selection offer ON offer.contract_number = loan_contract.contract_number
    AND  offer.kunn_id IS NULL AND offer.is_selected = 1
    AND loan_contract.contract_number = $1
    `
   
    const data = await req.poolRead.query(sql, [contractnumber])
    if (data.rows) {
        return data.rows[0]
    }
    else {
        return null
    }
}
const getKunnInfomation = async (req) => {
    let sql = `
    SELECT * FROM kunn  
    JOIN loan_offer_selection ON loan_offer_selection.kunn_id = kunn.kunn_id
    AND loan_offer_selection.is_selected = 1
    WHERE kunn.kunn_id = $1
    `
    const data = await req.poolRead.query(sql, [req.query.kunnid])
    if (data.rows) {
        return data.rows[0]
    }
    else {
        return null
    }
}


module.exports = {
    getKunnInfo
}

