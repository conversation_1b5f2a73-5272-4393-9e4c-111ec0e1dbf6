const camelcaseKeys = require("camelcase-keys");
const {getPlace,getValueCode} = require('../utils/masterdataService')
const {getLoanContractJoinMainScore} = require("../repositories/loan-contract-repo")
const {getKunnData} = require("../repositories/kunn-repo");
const { PARTNER_CODE } = require("../const/definition");
const { isNullOrEmpty } = require("../utils/helper");
const kunnDisburInfoRepo = require("../repositories/kunn-disbursement-info-repo");
const { getApiTimeoutV2, postAPI } = require("../utils/common");
 
const getKunnOtherInfo = async (req, res) => {
    try {
        const poolWrite = req.poolWrite
        let { contractnumber, kunnid } = req.query
        if (!kunnid && !contractnumber) {
            res.status(200).json({
                code: -1,
                message: 'Missing params',
            })
        }
        else {
            let kunnInfo = {}
            let kunnDisburInfos = [];
            let disbursResp;
            if(kunnid) {
                const contractKu =  await getContractKunn(req, kunnid)
                kunnInfo = await getKunnData(kunnid)

                const lmsUri = `/lms-mc/v1/debt-ack-contract/disbursements`;
                const lmsHeader = await getAuthLms(req)
                const lmsUrl = global.config.basic.lmsMc[config.env] + lmsUri + `?debtAckContractNumber=${kunnid}`
                disbursResp = await getApiTimeoutV2({ url: lmsUrl, headers: lmsHeader})
                kunnDisburInfos = await kunnDisburInfoRepo.findByKunnId(kunnid);
                
                if(contractnumber && contractKu !== contractnumber) {
                    return res.status(200).json({
                        code: 0,
                        message: "INVALID kunnId or contractnumber"
                    })
                }
                else {
                    contractnumber = contractKu
                }
            }
            const data = await getLoanContractJoinMainScore(poolWrite,contractnumber)
            const partnerCode = data?.partner_code
            let dataResponse;
            const masterData = await Promise.all([
                getValueCode(req,data.empl_type,"EMPLOYMENT_TYPE"),
                getPlace(req,data.province_cur, "provinces"),
                getPlace(req,data.district_cur, "districts"),
                getPlace(req,data.ward_cur, "wards"),
                getPlace(req,data.province_per, "provinces"),
                getPlace(req,data.district_per, "districts"),
                getPlace(req,data.ward_per, "wards"),
                getValueCode(req,data.job_type,"JOB_TYPE"),
                getValueCode(req,data.married_status,"MARRIED_STATUS"),
                getValueCode(req,data.house_type,"HABITAT"),
                getValueCode(req,data.income_method,"SALARY_METHOD"),
                getValueCode(req,data.income_frequency,"SALARY_FREQUENCY"),
                getPlace(req,data.empl_province, "provinces"),
                getPlace(req,data.empl_district, "districts"),
                getPlace(req,data.empl_ward, "wards"),
                getValueCode(req,data.empl_ctrct_type,"EMPLOYMENT_TERM"),
                getValueCode(req,data.employment_contract_term,"EMPLOYMENT_CONTRACT_TYPE"),
                getValueCode(req,data.loan_purpose,"LOAN_PURPOSE"),
                getValueCode(req,data.reference_type_1,"FONCTION_INTERLOCUTEUR"),
                getValueCode(req,data.reference_type_2,"FONCTION_INTERLOCUTEUR"),
                getValueCode(req,data.bank_code,"BANK"),
                getValueCode(req,data.bank_name2,"BANK"),
                getValueCode(req,data.bank_branch2,"BANK_BRANCH")
            ])
            const masterDataKunn = await Promise.all([
                getValueCode(req,kunnInfo?.bank_code,"BANK"),
                getValueCode(req,kunnInfo?.bank_branch_code,"BANK_BRANCH"),
                getValueCode(req,kunnInfo?.bank_code2,"BANK"),
                getValueCode(req,kunnInfo?.bank_branch_code2,"BANK_BRANCH"),
                getValueCode(req,kunnInfo?.bank_code3,"BANK"),
                getValueCode(req,kunnInfo?.bank_branch_code3,"BANK_BRANCH"),
                getValueCode(req,kunnInfo?.bank_code4,"BANK"),
                getValueCode(req,kunnInfo?.bank_branch_code4,"BANK_BRANCH"),
            ])
            
            let bankInfo = [
                {
                    bankAccount: kunnInfo?.bank_account || null,
                    beneficiaryName: kunnInfo?.beneficiary_name || null,
                    bankName: masterDataKunn[0] || null,
                    bankBranch: masterDataKunn[1] || null,
                    disbursedAmount: kunnInfo?.disbursed_amount || null,
                    bankCode: kunnInfo?.bank_code ?? null
                },
                {
                    bankAccount: kunnInfo?.bank_account2 || null,
                    beneficiaryName: kunnInfo?.beneficiary_name2 || null,
                    bankName: masterDataKunn[2] || null,
                    bankBranch: masterDataKunn[3] || null,
                    disbursedAmount: kunnInfo?.disbursed_amount2 || null,
                    bankCode: kunnInfo?.bank_code2 ?? null
                },
                {
                    bankAccount: kunnInfo?.bank_account3 || null,
                    beneficiaryName: kunnInfo?.beneficiary_name3 || null,
                    bankName: masterDataKunn[4] || null,
                    bankBranch: masterDataKunn[5] || null,
                    disbursedAmount: kunnInfo?.disbursed_amount3 || null,
                    bankCode: kunnInfo?.bank_code3 ?? null
                },
                {
                    bankAccount: kunnInfo?.bank_account4 || null,
                    beneficiaryName: kunnInfo?.beneficiary_name4 || null,
                    bankName: masterDataKunn[6] || null,
                    bankBranch: masterDataKunn[7] || null,
                    disbursedAmount: kunnInfo?.disbursed_amount4 || null,
                    bankCode: kunnInfo?.bank_code4 ?? null
                }
            ]

            for (const kunnDisburInfo of kunnDisburInfos) {
                bankInfo.push({
                    bankAccount: kunnDisburInfo?.bank_account || null,
                    beneficiaryName: kunnDisburInfo?.account_name || null,
                    bankName: kunnDisburInfo?.bank_name || null,
                    bankBranch: kunnDisburInfo?.bank_branch_code || null,
                    disbursedAmount: kunnDisburInfo?.amount || null,
                    bankCode: kunnDisburInfo.bank_code ?? null
                });
            }

            if(partnerCode===PARTNER_CODE.MISA){
                bankInfo = bankInfo.filter(bi => !isNullOrEmpty(bi.disbursedAmount))
                // for await (const bi of bankInfo) {
                //     if(isNullOrEmpty(bi.disbursedAmount)) bankInfo.pop(bi);
                //     bankInfo = [...bankInfo]
                // }
            }

            bankInfo = bankInfo.filter(item => item.bankAccount !== null);

            //map status disbur
            let disburs = [...bankInfo];
            if (disbursResp?.data?.data && bankInfo && bankInfo?.length > 0) {
                disburs = bankInfo.map(row => {
                    const matchingData = disbursResp.data.data.find(
                        item => item.bank_account === row.bankAccount
                            && item.bank_code === row.bankCode
                    );
                    return {
                        ...row,
                        tranStatus: matchingData.tran_status ?? null,
                        tranDate: matchingData.tran_date ?? null,
                        reDisburseDate: matchingData.re_disburse_date ?? null
                    };
                });
            }

            if (data) {
                let numDependant = data.num_of_dependants
                if(typeof(numDependant) == 'number') {
                    numDependant = data.num_of_dependants.toString()
                }
                else {
                    numDependant = null
                }
                dataResponse = {
                    contracNumber: contractnumber || null,
                    partnerCode: data.partner_code || null,
                    billDay : data.bill_day || null,
                    employmentType: masterData[0] || null,
                    temporaryProvince: masterData[1] || null ,
                    temporaryProvinceCode: data.province_cur,
                    temporaryDistrict: masterData[2] ,
                    temporaryDistrictCode: data.district_cur,
                    temporaryWard: masterData[3],
                    temporaryWardCode: data.ward_cur,
                    temporaryAddress: data.address_cur || null,
                    yearsOfStay: data.current_address_years || null,
                    permanentProvince: masterData[4],
                    permanentProvinceCode: data.province_per,
                    permanentDistrict: masterData[5],
                    permanentDistrictCode: data.district_per,
                    permanentWard: masterData[6],
                    permanentWardCode: data.ward_per,
                    permanentAddress: data.address_per || null,
                    jobType: masterData[7] || null,
                    marriedStatus: masterData[8] || null,
                    marriageMateID: data.marriage_mate_id || null,
                    houseType: masterData[9] || null,
                    numberOfDependants: numDependant || null,
                    monthlyIncome: data.monthly_income || null,
                    otherIncome: data.other_income || null,
                    incomeMethod: masterData[10] || null,
                    incomeFrequency: masterData[11] || null,
                    incomeReceivingDate: data.income_date || null,
                    monthlyExpenses: data.m_household_expenses || null,
                    workplaceName: data.empl_name || data.workplace_name || null,
                    workplaceProvince: masterData[12],
                    workplaceProvinceCode: data.empl_province,
                    workplaceDistric: masterData[13],
                    workplaceDistricCode: data.empl_district,
                    workplaceWard: masterData[14],
                    workplaceWardCode: data.empl_ward,
                    workplaceAddress: data.empl_address || null,
                    workplacePhone: data.company_phone_number || null,
                    emplymentContractType: masterData[15] || null,
                    emplymentContractYearFrom: data.empl_ctrct_from_year || null,
                    emplymentContractYearTo: data.empl_ctrct_to_year || null,
                    emplymentContractTerm: masterData[16] || null,
                    loanPurpose:  masterData[17] || null,
                    otherContact: data.other_contact_type || null,
                    relationship1: masterData[18] || null,
                    name1: data.reference_name_1 || null,
                    phoneNumber1: data.reference_phone_1 || null,
                    relationship2: masterData[19] || null,
                    name2: data.reference_name_2 || null,
                    phoneNumber2: data.reference_phone_2 || null,
                    latestTaxPayment : data.latest_tax_payment || null,
                    disbursementChanel : data.disbursement_method == null ? 'Chuyển khoản' : (data.disbursement_method == 'TRANSFER' || data.disbursement_method == 'TRS' || data.disbursement_method == 'Transfer' ? 'Chuyển khoản' : 'Tiền mặt'),
                    bankAccount : data.bank_account,
                    bankName : masterData[20],
                    accountOwner : data.bank_account_owner,
                    accountOwner2 : data.bank_account_owner2 || null,
                    bankAccount2 : data.bank_account2 || null,
                    bankName2 : masterData[21] || null,
                    bankBranch2 : masterData[22] || null,
                    kunnBankInfo : kunnid ? disburs : null
                }
            }

            res.status(200).json({
                code: data ? 1 : -1,
                message: data ? "SUCCESS" : "NOT FOUND CONTRACT SUITABLE",
                dataResponse: dataResponse,
            })
        }
    }
    catch (error) {
        console.log(error);
        res.status(500).json({
            code: 1,
            message: "ERROR"
        })
    }


}

const getContractKunn = async (req, kunnid) => {
    let sql = `
    SELECT *
    FROM kunn 
    WHERE kunn_id = $1
    `
    const data = await req.poolRead.query(sql, [kunnid])
    if (data.rows && data.rows[0]) {
        return data.rows[0].contract_number
    }
    else {
        return null
    }
}

const getAuthLms = async (req) => {
    const url = req.config.data.app.urlAuthentSingIn
    const bodySignIn = { username: global.config.data.lms.lmsUsername, password: global.config.data.lms.lmsPassword }
    const SignInUrl = req.config.basic.aaa[req.config.env] + url
    const lmsUri = req.config.data.lms.checkAvailable;
    const result = await postAPI(SignInUrl, bodySignIn)
    const lmsHeader = {
        uiid: result.uiid,
        token: result.token,
        service: lmsUri
    }
    return lmsHeader
}

module.exports = {
    getKunnOtherInfo
}

