const common = require('../utils/common');
const {getPlace,getValueCode,getValueCode_v3, parseValueCode} = require('../utils/masterdataService')
const {getProductInfo} = require("../utils/productService")
const {getLoanContractJoinMainScore,getLoanContract,getRemainInfo,getRemainInfoHanMuc} = require("../repositories/loan-contract-repo")
const loanBusinessOwnerRepo = require('../repositories/loan-business-owner-repo')
const loanCustomerManagerRepo = require('../repositories/loan-customer-manager-repo')
const loanCustomerShareholderRepo = require('../repositories/loan-customer-shareholders-repo')
const turnOverRepo = require('../repositories/turnover-repo');
const { CONTRACT_TYPE } = require("../const/definition");
const { isNullOrEmpty } = require("../utils/helper");
const loanAttributeRepo = require("../repositories/loan-atribute-repo");
const {ADDRESS_CODE_TYPE} = require("../const/variables-const");
const getTaskInfo = async (req, res) => {
    try {
        const { contractnumber, kunnid } = req.query
        const poolRead = req.poolRead
        const poolWrite = req.poolWrite
        const partnerCode = await getLoanContract(contractnumber)
        if (contractnumber && kunnid) {
            const result = await validContract(contractnumber, kunnid, req)
            if (!result) {
                return res.status(200).json({
                    code: 0,
                    message: 'Invalid params',
                })
            }
        }
        if (!kunnid && !contractnumber) {
            res.status(200).json({
                code: -1,
                message: 'Missing params',
            })
        }
        else {
            const dataKUNN = await getKunnInfomation(req)
            const c = dataKUNN ? dataKUNN.contract_number : req.query.contractnumber
            const data = await getLoanContractJoinMainScore(poolWrite, c)
            const loanAttributeData = await loanAttributeRepo.getDataByContractNumber(contractnumber)
            const productInfo = await getProductInfo(config,data.product_code)
            let dataResponse
            const d = data
            const mainScore = await getMainScore(contractnumber)
            const rs = await Promise.all([getValueCode(req,d.id_issue_place,"ISSUE_PLACE_VN"),
                                getValueCode(req,d.other_issue_place,"ISSUE_PLACE_VN"),
                                getValueCode(req,d.married_status,"MARRIED_STATUS"),
                                getValueCode(req,d.reference_type_1,"FONCTION_INTERLOCUTEUR"),
                                getLoanAccountTrading(poolRead,c),
                                getTransAndTurn(d.contract_number,"transaction",req),
                                getTransAndTurn(d.contract_number,"turnover",req),
                                getBranchAddress(poolRead,contractnumber,req),
                                getTransAndTurn(d.contract_number,"custurnover",req),
                                getTransAndTurn(d.contract_number, "profit", req),
                                getValueCode(req,d.sme_representation_issue_place, 'ISSUE_PLACE_VN'),
                                getValueCode(req,d.authorized_issue_place, 'ISSUE_PLACE_VN'),
                                getValueCode(req,d.enterprise_type, 'ENTERPRISE_TYPE'),
                                getValueCode(req,d.sme_employment_type1_code, 'SME_EMPLOYMENT_TYPE'),
                                getValueCode(req,d.sme_employment_type4_code, 'SME_EMPLOYMENT_TYPE_4'),
                                getValueCode(req,d.business_type, 'BUSINESS_TYPE'),
                                loanCustomerManagerRepo.findByContractNumber(contractnumber),
                                loanBusinessOwnerRepo.findByContractNumber(contractnumber),
                                loanCustomerShareholderRepo.findByContractNumber(contractnumber),
                            ])
            const dataPlace = await Promise.all(
                [
                    getPlace(req, d.sme_headquarters_province, 'provinces'),//0
                    getPlace(req, d.sme_headquarters_district, 'districts'),//1
                    getPlace(req, d.sme_headquarters_ward, 'wards'),//2
                    getPlace(req, d.sme_representation_province_cur, 'provinces'),//3
                    getPlace(req, d.sme_representation_district_cur, 'districts'),//4
                    getPlace(req, d.sme_representation_ward_cur, 'wards'),//5
                    getPlace(req, d.sme_representation_province_per, 'provinces'),//6
                    getPlace(req, d.sme_representation_district_per, 'districts'),//7
                    getPlace(req, d.sme_representation_ward_per, 'wards'),//8
                    getPlace(req, d.authorized_province_cur, 'provinces'),//9
                    getPlace(req, d.authorized_district_cur, 'districts'),//10
                    getPlace(req, d.authorized_ward_cur, 'wards'),//11
                    getPlace(req, d.authorized_province_per, 'provinces'),//12
                    getPlace(req, d.authorized_district_per, 'districts'),//13
                    getPlace(req, d.authorized_ward_per, 'wards'),//14
                    getPlace(req, d.contract_creator_province, 'provinces'),//15
                    getPlace(req, d.contract_creator_district, 'districts'),//16
                    getPlace(req, d.contract_creator_ward, 'wards'),//17
                    getPlace(req, d.ware_house_province, 'provinces'),//18
                    getPlace(req, d.ware_house_district, 'districts'),//19
                    getPlace(req, d.ware_house_ward, 'wards'),//20
                    getPlace(req, d.sme_headquarters_new_province, 'provinces'),//21
                    getPlace(req, d.sme_headquarters_new_ward, 'wards'),//22
                    getPlace(req, d.ware_house_new_province, 'provinces'),//23
                    getPlace(req, d.ware_house_new_ward, 'wards'),//24
                    getPlace(req, d.sme_representation_new_province_per, 'provinces'),//25
                    getPlace(req, d.sme_representation_new_ward_per, 'wards'),//26
                    getPlace(req, d.sme_representation_new_province_cur, 'provinces'),//27
                    getPlace(req, d.sme_representation_new_ward_cur, 'wards'),//28
                    getPlace(req, d.authorized_new_province_cur, 'provinces'),//29
                    getPlace(req, d.authorized_new_ward_cur, 'wards'),//30
                    getPlace(req, d.authorized_new_province_per, 'provinces'),//31
                    getPlace(req, d.authorized_new_ward_per, 'wards'),//32      
                ]
            )
            // const [smeHeadquartersNewProvince, smeHeadquartersNewWard, smeRepresentationNewProvincePer, smeRepresentationNewWardPer, smeRepresentationNewProvinceCur] = await Promise.all([
            //     parseValueCode(d.sme_headquarters_new_province, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            //     parseValueCode(d.sme_headquarters_new_ward, ADDRESS_CODE_TYPE.NEW_WARD),
            //     parseValueCode(d.sme_representation_new_province_per, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            //     parseValueCode(d.sme_representation_new_ward_per, ADDRESS_CODE_TYPE.NEW_WARD),
            //     parseValueCode(d.sme_representation_new_province_cur, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            //     parseValueCode(d.sme_representation_new_ward_cur, ADDRESS_CODE_TYPE.NEW_WARD),
            //     parseValueCode(d.authorized_new_province_cur, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            //     parseValueCode(d.authorized_new_ward_cur, ADDRESS_CODE_TYPE.NEW_WARD),
            //     parseValueCode(d.authorized_new_province_per, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            //     parseValueCode(d.authorized_new_ward_per, ADDRESS_CODE_TYPE.NEW_WARD),
            //     parseValueCode(d.ware_house_new_province, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            //     parseValueCode(d.ware_house_new_ward, ADDRESS_CODE_TYPE.NEW_WARD),
                
            // ]);
            //console.log({rs})
            const remainInfo = await getRemainInfoHanMuc(contractnumber)
            // console.log('productInfo',remainInfo)
            const issuePlaceCode = d.id_issue_place
            const otherIssuePlaceCode = d.other_issue_place
            const issuePlace = rs[0]
            const otherIssuePlace = rs[1]
            const marriedStatus = rs[2]
            const refer1 = rs[3]
            const loanAccountTrading = rs [4]
            const transaction = rs[5]
            if(partnerCode.partner_code =='VSK') rs[6].push({month: 0, value: ""})
            const turnOver = rs[6]
            const branchAddress = rs[7]
            const cusTurnOver = rs[8]
            const profit = rs[9]
            const enterPriseType = rs[12]
            const smeEmploymentType1 = rs[13]
            const smeEmploymentType4 = rs[14]
            const businessType = rs[15];

            const managers = rs[16];
            const businessOwners = rs[17];
            const shareholders = rs[18];

            // console.log('dataContract',d)
            // console.log('dataKUNN',dataKUNN)
            const loanContractData = await getLoanContract(contractnumber);
            if (data) {
                let managerInformation = null;
                if (managers.length > 0) {
                    const item = managers[0];
                    const dataManagerPlace = await Promise.all([
                        getPlace(req, item.perProvinceCode, 'provinces'),
                        getPlace(req, item.perDistrictCode, 'districts'),
                        getPlace(req, item.perWardCode, 'wards'),
                        getPlace(req, item.curProvinceCode, 'provinces'),
                        getPlace(req, item.curDistrictCode, 'districts'),
                        getPlace(req, item.curWardCode, 'wards'),
                        getValueCode(req, item.issuePlace, 'ISSUE_PLACE_VN')
                    ]);

                    // const [newProvince, newWard] = await Promise.all([
                    //     parseValueCode(item.newProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
                    //     parseValueCode(item.newWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    //     parseValueCode(item.aTempNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    //     parseValueCode(item.aPerNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    // ]);

                    managerInformation = {
                        fullName: item.fullName,
                        position: item.position,
                        dateOfBirth: item.dob,
                        idNumber: item.idNumber,
                        issueDate: item.issueDate,
                        issuePlace: dataManagerPlace[6],
                        phoneNumber: item.phoneNumber,
                        email: item.email,
                        gender: item.gender,
                        perAddress: [
                            item.perDetailAddress,
                            dataManagerPlace[0],
                            dataManagerPlace[1],
                            dataManagerPlace[2],
                        ].filter(e => e).join(', '),
                        curAddress: [
                            item.curDetailAddress,
                            dataManagerPlace[3],
                            dataManagerPlace[4],
                            dataManagerPlace[5],
                        ].filter(e => e).join(', ')
                    };
                }

                let businessOwnerInformation = null;
                if (businessOwners.length > 0) {
                    const item = businessOwners[0];
                    const dataBusinessOwnerPlace = await Promise.all([
                        getPlace(req, item.perProvinceCode, 'provinces'),
                        getPlace(req, item.perDistrictCode, 'districts'),
                        getPlace(req, item.perWardCode, 'wards'),
                        getPlace(req, item.curProvinceCode, 'provinces'),
                        getPlace(req, item.curDistrictCode, 'districts'),
                        getPlace(req, item.curWardCode, 'wards'),
                        getValueCode(req, item.issuePlace, 'ISSUE_PLACE_VN'),
                        getPlace(req, item.businessProvinceCode, 'provinces'),
                        getPlace(req, item.businessDistrictCode, 'districts'),
                        getPlace(req, item.businessWardCode, 'wards'),
                    ]);
                    // const [newProvince, newWard, aTempNewWard, aPerNewWard] = await Promise.all([
                    //     parseValueCode(item.newProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
                    //     parseValueCode(item.newWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    //     parseValueCode(item.aTempNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    //     parseValueCode(item.aPerNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    // ]);

                    businessOwnerInformation = {
                        subject: item.subject,
                        taxId: item.taxId,
                        companyName: item.companyName,
                        fullName: item.fullName,
                        position: item.position,
                        dateOfBirth: item.dob,
                        idNumber: item.idNumber,
                        issueDate: item.issueDate,
                        issuePlace: dataBusinessOwnerPlace[6],
                        phoneNumber: item.phoneNumber,
                        email: item.email,
                        entityType: item.entityType,
                        authorizationDocNo: item.authorizationDocNo,
                        businessPhoneNumber: item.businessPhoneNumber,
                        businessEmail: item.businessEmail,
                        perAddress: [
                            item.perDetailAddress,
                            dataBusinessOwnerPlace[0],
                            dataBusinessOwnerPlace[1],
                            dataBusinessOwnerPlace[2],
                        ].filter(e => e).join(', '),
                        curAddress: [
                            item.curDetailAddress,
                            dataBusinessOwnerPlace[3],
                            dataBusinessOwnerPlace[4],
                            dataBusinessOwnerPlace[5],
                        ].filter(e => e).join(', '),
                        businessAddress: [
                            item.businessDetailAddress,
                            dataBusinessOwnerPlace[7],
                            dataBusinessOwnerPlace[8],
                            dataBusinessOwnerPlace[9],
                        ].filter(e => e).join(', ')
                    };
                }

                let shareholderInformation = await Promise.all(shareholders.map(async (item) => {
                    let data = {
                        subject: item.subject
                    }

                    const dataShareholderPlace = await Promise.all([
                        getPlace(req, item.perProvinceCode, 'provinces'),
                        getPlace(req, item.perDistrictCode, 'districts'),
                        getPlace(req, item.perWardCode, 'wards'),
                        getPlace(req, item.curProvinceCode, 'provinces'),
                        getPlace(req, item.curDistrictCode, 'districts'),
                        getPlace(req, item.curWardCode, 'wards'),
                        getValueCode(req, item.issuePlace, 'ISSUE_PLACE_VN'),
                        getPlace(req, item.businessProvinceCode, 'provinces'),
                        getPlace(req, item.businessDistrictCode, 'districts'),
                        getPlace(req, item.businessWardCode, 'wards'),
                    ]);

                    // const [dataShareholderNewProvince, dataShareholderNewWard, dataShareholderATempNewWard, dataShareholderAPerNewWard] = await Promise.all([
                    //     parseValueCode(item.newProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
                    //     parseValueCode(item.newWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    //     parseValueCode(item.aTempNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    //     parseValueCode(item.aPerNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
                    // ]);
                    if (item.subject === 'INDIVIDUAL') {
                        data = {
                            ...data,
                            fullName: item.fullName,
                            idNumber: item.idNumber,
                            capitalContributionRatio: item.capitalContributionRatio,
                            phoneNumber: item.phoneNumber,
                            email: item.email,
                            dateOfBirth: null,
                            issueDate: item.issueDate,
                            issuePlace: dataShareholderPlace[6],
                            perAddress: [
                                item.perDetailAddress,
                                dataShareholderPlace[0],
                                dataShareholderPlace[1],
                                dataShareholderPlace[2],
                            ].filter(e => e).join(', '),
                            curAddress: [
                                item.curDetailAddress,
                                dataShareholderPlace[3],
                                dataShareholderPlace[4],
                                dataShareholderPlace[5],
                            ].filter(e => e).join(', '),
                            representations: [],
                        }
                    } else {
                        data = {
                            ...data,
                            fullName: item.companyName,
                            idNumber: item.taxId,
                            capitalContributionRatio: item.capitalContributionRatio,
                            phoneNumber: item.businessPhoneNumber,
                            email: item.businessEmail,
                            dateOfBirth: null,
                            issueDate: null,
                            issuePlace: null,
                            perAddress: [
                                item.businessDetailAddress,
                                dataShareholderPlace[7],
                                dataShareholderPlace[8],
                                dataShareholderPlace[9],
                            ].filter(e => e).join(', '),
                            curAddress: [
                                item.businessDetailAddress,
                                dataShareholderPlace[7],
                                dataShareholderPlace[8],
                                dataShareholderPlace[9],
                            ].filter(e => e).join(', '),
                            representations: [
                                {
                                    fullName: item.fullName,
                                    entityType: item.entityType,
                                    authorizationDocNo: item.authorizationDocNo,
                                    dateOfBirth: null,
                                    phoneNumber: item.phoneNumber,
                                    email: item.email,
                                    idNumber: item.idNumber,
                                    issueDate: item.issueDate,
                                    issuePlace: dataShareholderPlace[6],
                                    perAddress: [
                                        item.perDetailAddress,
                                        dataShareholderPlace[0],
                                        dataShareholderPlace[1],
                                        dataShareholderPlace[2],
                                    ].filter(e => e).join(', '),
                                    curAddress: [
                                        item.curDetailAddress,
                                        dataShareholderPlace[3],
                                        dataShareholderPlace[4],
                                        dataShareholderPlace[5],
                                    ].filter(e => e).join(', '),
                                }
                            ],
                        }
                    }

                    return data;
                }))

                dataResponse = {
                    contractNumber: contractnumber || null,
                    kunnNumber: kunnid || null,
                    review: isNullOrEmpty(loanContractData?.root_contract_number)?'false':'true',
                    clientDetails: {
                        fullName: d.cust_full_name || null,
                        role: null,
                        gender: d.gender === "F" ? "FEMALE" : "MALE",
                        id: d.id_number || null,
                        custId: d.cust_id || null,
                        issueDate: d.id_issue_dt || null,
                        issuePlace: issuePlace,
                        issuePlaceCode,
                        otherId: d.other_id_number || null,
                        otherIssueDate: d.other_issue_date || null,
                        otherIssuePlace: otherIssuePlace,
                        otherIssuePlaceCode,
                        dateOfBirth: d.birth_date || null,
                        email: d.email || "",
                        phone: d.phone_number1,
                        currentAddress: d.address_cur || null,
                        permannentAddress: d.address_per || null,
                        marriedStatus: marriedStatus,
                        marriageMateID: d.marriage_mate_id || null,
                        relationship1: refer1,
                        name1: d.reference_name_1 || null,
                        phoneNumber1: d.reference_phone_1 || null,
                        merchantContractNumber: d.merchant_contract_number,
                        representatiMerchantContract:
                        d.representati_merchant_contract,
                        startDateOnMerchant: d.start_date_on_merchant,
                        endDateOnMerchant: d.end_date_on_merchant,
                        latestTaxPayment: d.latest_tax_payment,
                        businessDuration : d.bussiness_duration || '',
                        taxId: d.tax_id || null
                    },
                    HanMuc: {
                        productName: d.product_code || null,
                        requestAmount: d.request_amt || null,
                        requestTenor: d.request_tenor || (productInfo?.productVar?.[0]?.defTenor || null),
                        requestRate: productInfo?.length > 0 ? parseFloat(productInfo?.productVar?.[0]?.intRate) || null : null
                    },
                    Kunn: kunnid
                        ? {
                              productName: d.product_code || null,
                              requestAmount: dataKUNN.with_draw_amount || null,
                              requestRate: dataKUNN.ir*1000/10+`%/năm` || null,
                              requestTenor: dataKUNN.tenor || null,
                              contractNumber: dataKUNN.kunn_id || null,
                              remainAmount: kunnid&&remainInfo!=false?remainInfo.remain_amt:null
                          }
                        : null,
                    merchantInformation: {
                        typeTrading: d.type_trading || null,
                        accountTrading: loanAccountTrading,
                        businessLegal: d.business_legal || null,
                        transaction12m: d.transaction12m || null,
                        transaction6m: d.transaction12m || null,
                        turnoverLast12Months: d.turnover_12m || null,
                        turnoverLast6Months: d.turnover_12m || null,
                        transaction : transaction || [],
                        turnOVer: turnOver || [],
                        cusTurnOver: cusTurnOver || [],
                        timeDuration: d.time_duration || null,
                        activeMonth: d.active_month || null,
                        sectorIndustry: d.sector_industry || null,
                        businessType: businessType ?? d.business_type ?? null,
                        firstRegistrationDate : d.first_registration_date || null,
                        merchantAccount : d.merchant_account || null,
                        saleManagementSoftwar : d.name_of_app || null,
                        timeUsingSaleManagementSoftware: d.name_of_app_duration || null,
                        applicationDriver : d.name_of_tool || null,
                        timeUsingApplicationDeliver: d.name_of_tool_duration || null,
                        revenueOnBankStatement : null,
                        capitalNeed: d.capital_need || null,
                        selfFinancing: d.self_financing || null,
                        otherCapital: d.other_capital || null,
                        fundingFromEC: d.funding_from_ec || null,
                        repaymentSources: d.repayment_sources || null,
                        fixedAsset: d.fixed_asset || null,
                        planAsset: d.plan_asset || null,
                        numberBusinessAsset: d.number_business_asset || null,
                        timeUsingAsset: d.time_using_asset || null,
                        branchAddress,
                        profit: profit || [],
                        totalProfit: d.total_profit || null,
                        averageDepreciation: d.average_depreciation || null,
                        totalTurnoverLastYear: Number(d.total_turnover_next_year) || 0,
                        totalCapitalLastYear: Number(d.total_capital_last_year) || 0,
                        profitAfterTax: Number(d.profit_after_tax) || 0,
                        businessDuration: Number(d.bussiness_duration) || 0,
                        registrationNumber: d.registration_number || null,
                        saleCode: loanAttributeData?.saleCode || ""
                    },
                    disbursementRequest: {
                        billDay: dataKUNN?dataKUNN.bill_day : null,
                        channel:  dataKUNN?dataKUNN.disbursement_channel : null,
                        bankAccount: d.bank_account || null,
                        bankName:  dataKUNN?dataKUNN.bank_name : null,
                        BankBranch: d.bank_branch || null,
                        capitalNeed: dataKUNN?.capital_need || null,
                        selfFinancing: dataKUNN?.self_financing || null,
                        otherCapital: dataKUNN?.other_capital || null,
                        fundingFromEC: dataKUNN?.funding_from_ec || null,
                        repaymentSources: dataKUNN?.repayment_source || null,
                        fixedAsset: d.fixed_asset || null,
                        planAsset: d.plan_asset || null
                    },
                    clientSmeDetails: {
                        registrationNumber: d.registration_number || null,
                        taxId: d.sme_tax_id || null,
                        smeName: d.sme_name || null,
                        phoneNumber: d.sme_phone_number || null,
                        smeRepresentationName: d.sme_representation_name || null,
                        smeRepresentationId: d.sme_representation_id || null,
                        timeDuration: d.time_duration || null,
                        socialInsuranceEmployee: d.socal_insurance_staff_number || 0,
                        totalCapitalLastYear: Number(d.total_capital_last_year) || 0,
                        totalTurnoverNextYear: Number(d.total_turnover_next_year) || 0,
                        profitAfterTax: Number(d.profit_after_tax) || 0,
                        smeEmail: d.sme_email || null,
                        headquartersAddress: d.sme_headquarters_address + ', ' + dataPlace[2] + ', ' + dataPlace[1] + ', ' + dataPlace[0],
                        representationAddressCur: d.sme_representation_address_cur + ', ' + 
                        (dataPlace[5] ?? dataPlace[8]) + ', ' + 
                        (dataPlace[4] ?? dataPlace[7]) + ', ' + 
                        (dataPlace[3] ?? dataPlace[6]), // nếu không có địa chỉ tạm trú thì lấy địa chỉ thường trú
                        representationAddressPer: d.sme_representation_address_per + ', ' + dataPlace[8] + ', ' + dataPlace[7] + ', ' + dataPlace[6],
                        // new address
                        headquartersNewAddress: d.sme_headquarters_address + ', ' + dataPlace[22] + ', ' + dataPlace[21],
                        representationNewAddressCur: d.sme_representation_address_cur + ', ' + (dataPlace[26] ?? dataPlace[28]) + ',  ' + (dataPlace[25] ?? dataPlace[27]),
                        representationNewAddressPer: d.sme_representation_address_per + ', ' + dataPlace[26] + ', ' + dataPlace[25] ,
                        smeRepresentationIssueDate: d.sme_representation_issue_date || null,
                        smeRepresentationIssuePlace: rs[10] || null,
                        smeRepresentationPhoneNumber: d.sme_representation_phone_number || null,
                        requestAmount: Number(d.request_amt) || null,
                        requestTenor: d.contract_type===CONTRACT_TYPE.CREDIT_LINE?d.request_tenor_kunn:d.request_tenor || null,
                        numberOfStaff: d.socal_insurance_staff_number || null
                    },
                    smeInfomation: {
                        enterpriseType: enterPriseType ?? businessType ?? null,
                        applicationUsed: d.name_of_app || null,
                        sectorIndustry: d.sector_industry || null,
                        sectorIndustry1: d.sector_industry1 || null,
                        sectorIndustry2: d.sector_industry2 || null,
                        sectorIndustry3: d.sector_industry3 || null,
                        branchTaxId: d.branch_tax_id || null,
                        socalInsuranceStaffNumber: d.socal_insurance_staff_number || null,
                        headquartersAddress: d.sme_headquarters_address || null,
                        headquartersProvince: dataPlace[0] || null,
                        headquartersDistrict: dataPlace[1] || null,
                        headquartersWard: dataPlace[2] || null,
                        headquartersProvinceCode: d.sme_headquarters_province || null,
                        headquartersDistrictCode: d.sme_headquarters_district || null,
                        headquartersWardCode: d.sme_headquarters_ward || null,
                        //new headquarters address
                        headquartersNewProvince: dataPlace[21] || null,
                        headquartersNewWard: dataPlace[22] || null,
                        headquartersNewProvinceCode: d.sme_headquarters_new_province || null,
                        headquartersNewWardCode: d.sme_headquarters_new_ward || null,

                        AccountantStatus: d.accountant_status || null,
                        AccountantName: d.accountant_name || null,
                        AccountantGender: d.accountant_gender === "F" ? "FEMALE" : "MALE",
                        AccountantPhone: d.accountant_phone_number || null,
                        AccountantEmail: d.accountant_email || null,
                        contractCreatorName: d.contract_creator_name || null,
                        contractCreatorPosition: await getValueCode_v3(d.contract_creator_position,'PROFESSION')|| null,
                        contractCreatorPhone: d.contract_creator_phone_number || null,
                        AccountantProvince: dataPlace[15] || null,
                        contractCreatorDistrict: dataPlace[16] || null,
                        AccountantWard: dataPlace[17] || null,
                        contractCreatorAddress: d.contract_creator_address || null,
                        warehoseAddress: [
                            {
                                branchName: d.brand_name || null,
                                warehouseAddress: d.ware_house_address || null,
                                warehouseWard: dataPlace[20] || null,
                                warehouseDistrict: dataPlace[19] || null,
                                warehouseProvince: dataPlace[18] || null,
                                warehouseProvinceCode: d.ware_house_province || null,
                                warehouseDistrictCode: d.ware_house_district || null,
                                warehouseWardCode: d.ware_house_ward || null,
                                warehouseNewProvince: dataPlace[21] || null,
                                warehouseNewWard: dataPlace[22] || null,
                                warehouseNewProvinceCode: d.ware_house_new_province || null,
                                warehouseNewWardCode: d.ware_house_new_ward || null
                            }
                        ],
                        smeEmploymentType1: smeEmploymentType1 || null,
                        smeEmploymentType4: smeEmploymentType4 || null
                    },
                    representationInfomation:{
                        signedContract: d.is_authorization_sign == 'Y' ? d.authorized_name : d.sme_representation_name,
                        representationName: d.sme_representation_name || null,
                        representationDOB: d.sme_representation_dob || null,
                        representationGender: d.sme_representation_gender === "F" ? "FEMALE" : "MALE",
                        representationPosition: await getValueCode_v3(d.sme_representation_position,'PROFESSION') || null,
                        representationId: d.sme_representation_id || null,
                        representationIssueDate: d.sme_representation_issue_date || null,
                        representationIssuePlace: rs[10] || null,
                        representationIssuePlaceCode: d.sme_representation_issue_place || null,
                        representationOtherId: d.sme_representation_other_id || null,
                        representationPhoneNumber: d.sme_representation_phone_number || null,
                        representationEmail: d.sme_representation_email || null,
                        rPerProvince: dataPlace[6] || null,
                        rPerDistrict: dataPlace[7] || null,
                        rPerWard: dataPlace[8] || null,
                        rPerProvinceCode: d.sme_representation_province_per || null,
                        rPerDistrictCode: d.sme_representation_district_per || null,
                        rPerWardCode: d.sme_representation_ward_per || null,
                        rPerAddress: d.sme_representation_address_per || null,
                        //new permanent address
                        rePerNewProvince: dataPlace[25] || null,
                        rePerNewWard: dataPlace[26] || null,
                        rePerNewProvinceCode: d.sme_representation_new_province_per || null,
                        rePerNewWardCode: d.sme_representation_new_ward_per || null,
                        //End new permanent address
                        rTempProvince: dataPlace[3] || null,
                        rTempDistrict: dataPlace[4] || null,
                        rTempWard: dataPlace[5] || null,
                        rTempProvinceCode: d.sme_representation_province_cur || null,
                        rTempDistrictCode: d.sme_representation_district_cur || null,
                        rTempWardCode: d.sme_representation_ward_cur || null,
                        rTempAddress: d.sme_representation_address_cur || null,
                        //new retemporary address
                        reTempNewProvince: dataPlace[27] || null,
                        reTempNewWard: dataPlace[28] || null,
                        reTempNewProvinceCode: d.sme_representation_new_province_cur || null,
                        reTempNewWardCode: d.sme_representation_new_ward_cur || null,
                        //End new retemporary address
                        authorizedName: d.authorized_name || null,
                        authorizedDOB: d.authorized_dob || null,
                        authorizedGender: d.authorized_gender === "F" ? "FEMALE" : d.authorized_gender === "M" ? "MALE":null,
                        authorizedPosition: await getValueCode_v3(d.authorized_position,'PROFESSION') || null,
                        authorizedId: d.authorized_id || null,
                        authorizedIssueDate: d.authorized_issue_date || null,
                        authorizedIssuePlace: rs[11] || null,
                        authorizedIssuePlaceCode: d.authorized_issue_place || null,
                        authorizedOtherId: d.authorized_other_id || null,
                        authorizedPhoneNumber: d.authorized_phone_number || null,
                        aPerProvince: dataPlace[9] || null,
                        aPerDistrict: dataPlace[10] || null,
                        aPerWard: dataPlace[11] || null,
                        aPerProvinceCode: d.authorized_province_cur || null,
                        aPerDistrictCode: d.authorized_district_cur || null,
                        aPerWardCode: d.authorized_ward_cur || null,
                        aPerAddress: d.authorized_address_per || null,
                        //new aPer address
                        aPerNewProvince: dataPlace[29] || null,
                        aPerNewWard: dataPlace[30] || null,
                        aPerNewProvinceCode: d.authorized_new_province_cur || null,
                        aPerNewWardCode: d.authorized_new_ward_cur || null,
                        //end aPer address
                        aTempProvince: dataPlace[12] || null,
                        aTempDistrict: dataPlace[13] || null,
                        aTempWard: dataPlace[14] || null,
                        aTempProvinceCode:  d.authorized_province_per || null,
                        aTempDistrictCode: d.authorized_district_per || null,
                        aTempWardCode: d.authorized_ward_per || null,
                        aTempAddress: d.authorized_address_cur || null,
                        //new aTemp adress
                        aTempNewProvince: dataPlace[31] || null,
                        aTempNewWard: dataPlace[32] || null,
                        aTempNewProvinceCode:  d.authorized_new_province_per|| null,
                        aTempNewWardCode: d.authorized_new_ward_per || null,

                        authorizationLetterNumber: d.authorization_letter_number || null,
                        AuthorizationLetterSignedDate: d.authorization_letter_singed_date || null
                    },
                    managerInformation,
                    businessOwnerInformation,
                    shareholderInformation,
                    loanInfo: {
                        // revenueLoan: loanInfo.length > 0 ? Number(loanInfo.filter(item => {
                        //     return item.month_of_info == 1
                        // })[0].value_of_month) : null,
                        revenueLoan: Number(d.total_turnover_next_year) || 0,
                        profitAfterTax: Number(d.profit_after_tax) || 0,
                        averageDepreciation: Number(d.average_depreciation) || 0,
                        totalShortTermLoanLimit: Number(d.total_short_term_loan_limit) || 0,
                        percentComparedLastYear: Number(mainScore.percent_compared_last_year) || 0,
                        workingCapitalTurnover: Number(mainScore.working_capital_turnover) || 0,
                        loanSector: mainScore.loan_sector || 0,
                        approvalAmt: Number(d.approval_amt) || 0,
                        tenorLoan: Number(d.approval_tenor) || 0,
                        uMediumTermDebtAmount: Number(d.medium_term_debt_amount_u) || 0,
                        uLongTermDebtAmount: Number(d.long_term_debt_amount_u) || 0,
                        mMediumTermDebtAmount: Number(d.medium_term_debt_amount_m) || 0,
                        mLongTermDebtAmount: Number(d.long_term_debt_amount_m) || 0,
                        loanAmount: Number(d.loan_amount) || 0,
                        tenorMaxLoan: Number(d.tenor_max_loan) || 0,
                        rateMaxLoan: Number(d.rate_max_loan) || 0,
                        pcbInstallment: d.net_income||null,
                        cardPcb: d.card_balance||null,
                        riskGroupPcb: d.pcb_debt_group||null,
                        riskGroupDate: d.risk_date||null,
                        warning: 0,
                        riskGroupCic: d.cic_debt_group||null
                    }
                };
            }

            res.status(200).json({
                code: 1,
                message: "SUCCESS",
                dataResponse,
            })
        }
    }
    catch (error) {
        console.log(error);
        res.status(500).json({
            code: 1,
            message: "ERROR"
        })
    }


}

const validContract = async (contractnumber, kunn_id, req) => {
    const sql = "SELECT * from kunn WHERE contract_number = $1 AND kunn_id = $2"
    const params = [contractnumber, kunn_id]
    const data = await req.poolRead.query(sql, params)
    return !!data.rows.length
}

const getTransAndTurn = async (contractNumber, type, req) => {
    let sql = `SELECT month_of_info AS month,
            value_of_month AS value FROM loan_turnover_transaction  WHERE contract_number = $1 AND info_type = $2
    ORDER BY month_of_info`
    const data = await req.poolRead.query(sql, [contractNumber, type])
    //console.log('data.rows[0]', data.rows[0]);
    return data.rows ? data.rows : []
}

async function getLoanAccountTrading(poolRead,contractNumber) {
    const sql = "select platform_name,store_name from loan_account_trading where contract_number=$1"
    const result = await poolRead.query(sql,[contractNumber])
    return result.rows
}

const getKunnInfomation = async (req) => {
    let sql = `
    SELECT *
    FROM loan_contract 
    INNER JOIN kunn ON loan_contract.contract_number = kunn.contract_number 
    AND 
    `
    let count = 1
    let params = []
    if (req.query.contractnumber) {
        sql += "loan_contract.contract_number = $" + count + ' AND '
        count++
        params.push(req.query.contractnumber)
    }
    if (req.query.kunnid) {
        sql += "kunn.kunn_id = $" + count + ' AND '
        count++
        params.push(req.query.kunnid)
    }

    sql = sql.slice(0, -4)
    //console.log("sql", sql);
    //console.log("params", params);
    const data = await req.poolRead.query(sql, params)
    if (data.rows) {
        return data.rows[0]
    }
    else {
        return null
    }
}

async function getBranchAddress(poolRead,contractNumber,req) {
    const sql = "select * from loan_branch_address where contract_number = $1"
    const result = await poolRead.query(sql,[contractNumber])
    const branchResult = []
    for (let i in result.rows) {
        const element = result.rows[i]
        const data = await Promise.all([getPlace(req,element.province,'provinces'),getPlace(req,element.district, 'districts'),getPlace(req,element.ward,"wards"),
            getPlace(req,element.new_province,'provinces'),getPlace(req,element.new_district, 'districts'),getPlace(req,element.new_ward,"wards")
        ])
        const temp = {}
        temp.id = element.id
        temp.branchName = element.branch_name
        temp.street = element.address
        temp.provinceCode = element.province
        temp.districtCode = element.district
        temp.wardCode = element.ward
        temp.province = data[0]
        temp.district = data[1]
        temp.ward = data[2]
        //new branch address
        temp.newProvinceCode = element.new_province
        temp.newWardCode = element.new_ward
        temp.newProvince = data[3]
        temp.newWard = data[5]
        branchResult.push(temp)
    }   
    return branchResult
}

async function getWorkflowStatus(req,res) {
    const poolRead = req.poolRead
    const {contractNumber,kunnNumber} = req.query
    let sql = "select * from case_status_log csl where contract_number = $1 and kunn_number is null order by created_date asc"
    let params = [contractNumber]
    //console.log({kunnNumber})
    if(kunnNumber) {
        sql = "select * from case_status_log csl where contract_number = $1 or kunn_number = $2 order by created_date asc"
        params = [contractNumber,kunnNumber]
    }
    //console.log({sql})
    const rs = await poolRead.query(sql,params)
    let count = 0;
    return res.status(200).json({
        code : 1,
        msg : "get workflow successfully",
        data : rs.rows.map(row => {
            count += 1
            return {
                id : count,
                createdDate : row.created_date,
                stage : row.stage,
                status : row.status_code,
                user : row.created_user,
                updated_date : row.created_date
            }
        })
    })
}

async function getMainScore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const getMainScore = 'select * from loan_main_score where contract_number = $1'
        const rs = await poolWrite.query(getMainScore,[contractNumber])
        if(rs.rowCount == 0) {
            common.log(`get loan main score error `,contractNumber)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`get loan main score error : ${err.message}`,contractNumber)
        return false
    }
}

module.exports = {
    getTaskInfo,
    getWorkflowStatus
}

