const utils = require("../utils/helper")
const {requestStatus} = require("../const/updateRequest")
const loanContractUpdateRequestRepo = require("../repositories/loan-contract-update-request-repo")
const loanContractRepo = require("../repositories/loan-contract-repo");
const {getValueCode_v3} = require("../utils/masterdataService");

async function getLoanContractNotUpdate(req, res) {
	try {
		let { page, size, search } = req.query

		const { limit, offset } = utils.getPagination(page, size);

		let result = await loanContractUpdateRequestRepo.getNotUpdateContractAndCount({limit, offset, search})

		const {totalItems, dataList} = result;
		const totalPages = Math.ceil(totalItems / limit);
		res.status(200).json({
			"msg": "get loan contract not update success",
			"code": 1,
			"data": {
				totalItems,
				dataList,
				totalPages,
				currentPage: page
			}
		})
		
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

async function getLoanContractUpdateRequest(req, res) {
	try {
		let { page, size, status, search } = req.query
		status = status ? status : 'all'

		const { limit, offset } = utils.getPagination(page, size);

		let result = await loanContractUpdateRequestRepo.getUpdateRequestAndCount({
			status,
			search,
			limit,
			offset
		})
		const {totalItems, dataList} = result

		const totalPages = Math.ceil(totalItems / limit);
		res.status(200).json({
			"msg": "get loan contract update request success",
			"code": 1,
			"data": {
				totalItems,
				dataList,
				totalPages,
				currentPage: page
			}
		})

	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

async function createLoanContractUpdateRequest(req, res) {
	try {
		let data = req.body

		if (!data.contract_number)  {
			return res.status(400).json({
				code : -1,
				msg : "Thiếu tham số"
			})
		}

		if (data.status === requestStatus.WAITING_APPROVE) {
			if (!data.username)  {
				return res.status(400).json({
					code : -1,
					msg : "Thiếu tham số"
				})
			}

			data.requested_by = data.username
		} else {
			data.status = requestStatus.DRAFT
		}

		let pendingUpdateRequest = await loanContractUpdateRequestRepo.getPendingUpdateRequestByContractNumber({contract_number: data.contract_number})
		if (pendingUpdateRequest) {
			return res.status(400).json({
				code : -1,
				msg : " Có yêu cầu đang xử lý"
			})
		}

		let updateRequest = await loanContractUpdateRequestRepo.createUpdateRequest({data})

		if (!updateRequest) {
			return res.status(400).json({
				code : -1,
				msg : " Tạo yêu cầu thất bại"
			})
		}

		await Promise.all([
			loanContractUpdateRequestRepo.createUpdateRequestRepresentations({
				updateRequestId: updateRequest.id,
				data: data.loan_customer_representations
			}),
			loanContractUpdateRequestRepo.createUpdateRequestManagers({
				updateRequestId: updateRequest.id,
				data: data.loan_customer_managers
			})
		])

		res.status(200).json({
			"msg": "create loan contract update request success",
			"code": 1,
			"data": updateRequest
		})

	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

async function updateLoanContractUpdateRequest(req, res) {
	try {
		let data = req.body

		if (!data.id || !data.username)  {
			return res.status(400).json({
				code : -1,
				msg : "Thiếu tham số"
			})
		}

		let updateRequest = await loanContractUpdateRequestRepo.findUpdateRequestById({id: data.id})
		if (!updateRequest || updateRequest.is_deleted == 1) {
			return res.status(400).json({
				code : -1,
				msg : "Yêu cầu không tồn tại"
			})
		}

		if (![requestStatus.DRAFT, requestStatus.REJECTED].includes(updateRequest.status)) {
			return res.status(400).json({
				code : -1,
				msg : "Trạng thái yêu cầu không hợp lệ"
			})
		}

		let updateResult = await loanContractUpdateRequestRepo.updateUpdateRequestById({id: data.id, data})

		if (updateResult) {
			await Promise.all([
				loanContractUpdateRequestRepo.deleteUpdateRequestRepresentationsByRequestId({update_request_id: data.id, username: data.username}),
				loanContractUpdateRequestRepo.deleteUpdateRequestManagersByRequestId({update_request_id: data.id, username: data.username}),
				loanContractUpdateRequestRepo.createUpdateRequestRepresentations({
					updateRequestId: data.id,
					data: data.loan_customer_representations
				}),
				loanContractUpdateRequestRepo.createUpdateRequestManagers({
					updateRequestId: data.id,
					data: data.loan_customer_managers
				})
			])

			res.status(200).json({
				"msg": "update loan contract update request success",
				"code": 1,
				"data": null
			})
		} else {
			res.status(500).json({
				"msg": "update loan contract update request failed",
				"code": 0,
				"data": null
			})
		}
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

async function deleteLoanContractUpdateRequest(req, res) {
	try {
		let {id, username} = req.body

		if (!id || !username)  {
			return res.status(400).json({
				code : -1,
				msg : "Thiếu tham số"
			})
		}

		let updateRequest = await loanContractUpdateRequestRepo.findUpdateRequestById({id})
		if (!updateRequest || updateRequest.is_deleted == 1) {
			return res.status(400).json({
				code : -1,
				msg : "Yêu cầu không tồn tại"
			})
		}

		if (![requestStatus.DRAFT].includes(updateRequest.status)) {
			return res.status(400).json({
				code : -1,
				msg : "Trạng thái yêu cầu không hợp lệ"
			})
		}

		let deleteResult = await loanContractUpdateRequestRepo.deleteUpdateRequestById({id, username})

		if (deleteResult) {
			res.status(200).json({
				"msg": "delete loan contract update request success",
				"code": 1,
				"data": null
			})
		} else {
			res.status(500).json({
				"msg": "delete loan contract update request failed",
				"code": 0,
				"data": null
			})
		}
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

async function requestLoanContractUpdateRequest(req, res) {
	try {
		let {id, username} = req.body

		if (!id || !username)  {
			return res.status(400).json({
				code : -1,
				msg : "Thiếu tham số"
			})
		}

		let updateRequest = await loanContractUpdateRequestRepo.findUpdateRequestById({id})
		if (!updateRequest || updateRequest.is_deleted == 1) {
			return res.status(400).json({
				code : -1,
				msg : "Yêu cầu không tồn tại"
			})
		}

		if (![requestStatus.DRAFT, requestStatus.REJECTED].includes(updateRequest.status)) {
			return res.status(400).json({
				code : -1,
				msg : "Trạng thái yêu cầu không hợp lệ"
			})
		}

		let updateResult = await loanContractUpdateRequestRepo.requestUpdateRequestById({id, username})

		if (updateResult) {
			res.status(200).json({
				"msg": "request loan contract update request success",
				"code": 1,
				"data": null
			})
		} else {
			res.status(500).json({
				"msg": "request loan contract update request failed",
				"code": 0,
				"data": null
			})
		}
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

async function approveLoanContractUpdateRequest(req, res) {
	try {
		let {id, username} = req.body

		if (!id || !username)  {
			return res.status(400).json({
				code : -1,
				msg : "Thiếu tham số"
			})
		}

		let updateRequest = await loanContractUpdateRequestRepo.findUpdateRequestById({id})
		if (!updateRequest || updateRequest.is_deleted == 1) {
			return res.status(400).json({
				code : -1,
				msg : "Yêu cầu không tồn tại"
			})
		}

		let updateRequestDetail = await loanContractUpdateRequestRepo.findUpdateRequestDetailById({id})

		if (![requestStatus.WAITING_APPROVE].includes(updateRequest.status)) {
			return res.status(400).json({
				code : -1,
				msg : "Trạng thái yêu cầu không hợp lệ"
			})
		}

		let loanContract = await loanContractRepo.findOneMisaLoanContract(updateRequestDetail.contract_number);
		if (!loanContract) {
			return res.status(400).json({
				code : -1,
				msg : "Không tìm thấy hợp đồng"
			})
		}

		let updateResult = await loanContractUpdateRequestRepo.approveUpdateRequestById({id, username})

		if (updateResult) {
			await Promise.all([
				loanContractRepo.deleteLoanCustomerManagerByContractNumber({contract_number: updateRequestDetail.contract_number}),
				loanContractRepo.deleteLoanCustomerRepresentationByContractNumber({contract_number: updateRequestDetail.contract_number}),
			]);

			if (updateRequestDetail?.loan_customer_representations?.[0]) {
				// const masterData = await Promise.all([
				// 	getValueCode_v3(updateRequestDetail?.loan_customer_representations?.[0].per_ward_code,"WARD"),
				// 	getValueCode_v3(updateRequestDetail?.loan_customer_representations?.[0].per_district_code,"DISTRICT"),
				// 	getValueCode_v3(updateRequestDetail?.loan_customer_representations?.[0].per_province_code,"PROVINCE"),
				// ])

				updateRequestDetail = {
					...updateRequestDetail,
					sme_representation_name: updateRequestDetail?.loan_customer_representations?.[0].full_name,
					sme_representation_id: updateRequestDetail?.loan_customer_representations?.[0].id_number,
					sme_representation_address_per: updateRequestDetail?.loan_customer_representations?.[0].per_detail_address,
					sme_representation_ward_per: updateRequestDetail?.loan_customer_representations?.[0].per_ward_code,
					sme_representation_district_per: updateRequestDetail?.loan_customer_representations?.[0].per_district_code,
					sme_representation_province_per: updateRequestDetail?.loan_customer_representations?.[0].per_province_code,
				}
			}

			if (updateRequestDetail?.loan_customer_managers?.[0]) {
				const masterData = await Promise.all([
					getValueCode_v3(updateRequestDetail?.loan_customer_managers?.[0].per_ward_code,"WARD"),
					getValueCode_v3(updateRequestDetail?.loan_customer_managers?.[0].per_district_code,"DISTRICT"),
					getValueCode_v3(updateRequestDetail?.loan_customer_managers?.[0].per_province_code,"PROVINCE"),
				])

				updateRequestDetail = {
					...updateRequestDetail,
					sme_manager_name: updateRequestDetail?.loan_customer_managers?.[0].full_name,
					sme_manager_id: updateRequestDetail?.loan_customer_managers?.[0].id_number,
					sme_manager_address_per: [
						updateRequestDetail?.loan_customer_managers?.[0].per_detail_address,
						masterData[0],
						masterData[1],
						masterData[2],
					].filter(element => element)
						.join(", "),
				}
			}

			await Promise.all([
				loanContractRepo.updateLoanContract({
					...updateRequestDetail,
					first_registration_date: updateRequestDetail.registration_date
				}),
				loanContractRepo.insertLoanCustomerRepresentations(updateRequestDetail.contract_number, loanContract.registration_number, updateRequestDetail.loan_customer_representations || []),
				loanContractRepo.insertLoanCustomerManagers(updateRequestDetail.contract_number, updateRequestDetail.loan_customer_managers || []),
			]);

			res.status(200).json({
				"msg": "approve loan contract update request success",
				"code": 1,
				"data": null
			})
		} else {
			res.status(500).json({
				"msg": "approve loan contract update request failed",
				"code": 0,
				"data": null
			})
		}
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}
async function rejectLoanContractUpdateRequest(req, res) {
	try {
		let {id, username, reject_reason} = req.body

		if (!id || !username || !reject_reason)  {
			return res.status(400).json({
				code : -1,
				msg : "Thiếu tham số"
			})
		}

		let updateRequest = await loanContractUpdateRequestRepo.findUpdateRequestById({id})
		if (!updateRequest || updateRequest.is_deleted == 1) {
			return res.status(400).json({
				code : -1,
				msg : "Yêu cầu không tồn tại"
			})
		}

		if (![requestStatus.WAITING_APPROVE].includes(updateRequest.status)) {
			return res.status(400).json({
				code : -1,
				msg : "Trạng thái yêu cầu không hợp lệ"
			})
		}

		let updateResult = await loanContractUpdateRequestRepo.rejectUpdateRequestById({id, username, reject_reason})

		if (updateResult) {
			res.status(200).json({
				"msg": "reject loan contract update request success",
				"code": 1,
				"data": null
			})
		} else {
			res.status(500).json({
				"msg": "reject loan contract update request failed",
				"code": 0,
				"data": null
			})
		}
	}
	catch(error) {
		console.log(error)
		return res.status(500).json({
			code : -1,
			msg : "service error"
		})
	}
}

module.exports = {
	getLoanContractNotUpdate,
	getLoanContractUpdateRequest,
	createLoanContractUpdateRequest,
	updateLoanContractUpdateRequest,
	deleteLoanContractUpdateRequest,
	approveLoanContractUpdateRequest,
	requestLoanContractUpdateRequest,
	rejectLoanContractUpdateRequest,
}