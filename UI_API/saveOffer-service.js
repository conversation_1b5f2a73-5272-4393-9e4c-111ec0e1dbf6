const offerRepo = require("../repositories/offer")
const productService = require("../utils/productService")

async function saveOffer(req,res) {
	try {
		const poolWrite = req.poolWrite
		// const poolRead = req.poolRead
		const contractNumber = req.body.contractNumber
		const update = req.body.updateOffer
		const insert = req.body.addOffer
		const userName = req.body.userName
		const role = req.body.role
		const isSme = req.body.isSme
		const selectedOffer = req.body.selectedOffer

		if(selectedOffer !== undefined ) {
			const offerId = selectedOffer.id
			await selectOffer(poolWrite,contractNumber,offerId,userName,role)		
			return res.status(200).json({
				"code" : 1,
				"msg" : "Select offer successful"
			})
		}
		const currentDate = new Date()

		let promiseQuery = []

		const updateSql = "update loan_offer_selection set is_delt=$1,user_acted=$2,updated_date=$3 where contract_number=$4 and id=$5 returning id"
		if(update !== undefined) {
			update.forEach(offer => {
				let isDelt = 0
				if(offer.decision === 'REMOVE') {	
					isDelt = 1
				}
				promiseQuery.push(poolWrite.query(updateSql,[isDelt,userName,currentDate,contractNumber,offer.offerId]))
			})
		}
		Promise.all(promiseQuery).then(result => console.log("update offer successs")).catch(error => console.log(error))

		const addedList = await addOffer(insert,contractNumber,userName,role,isSme)
		res.status(200).json({
			"code" : 1,
			"msg" : "save offer successful",
			"addedOffer" : addedList
		})
	}
	catch(error	) {
		console.log(error)
		res.status(500).json({
			"msg" : "service error",
			"code" : -1
		})
	}
} 

async function selectOffer(poolWrite,contractNumber,offerId,userName,userRole) {
	const currentDate = new Date()
	const sql = "update loan_offer_selection set is_delt=1,updated_date=$1,user_acted=$2,role_acted=$3 where id =$4"
	return await Promise.all([poolWrite.query(sql,[currentDate,userName,userRole,offerId])/*,poolWrite.query(sql2,[currentDate,userName,userRole,offerId,contractNumber])*/])
}

async function addOffer(offerList,contractNumber,userName,userRole,isSme) {
	let promiseQuery = []
	if(offerList !== undefined) {
		offerList.forEach(offer => {
			if(isSme){
				promiseQuery.push(offerRepo.saveOfferSme(contractNumber,offer.amout,offer.rate,offer.tenor,0,userName,userRole))
			}else {
				promiseQuery.push(offerRepo.saveOfferV4(contractNumber,offer.amout,offer.tenor,0,userName,userRole))
			}
		})
	}	
	const insertRs = await Promise.all(promiseQuery)
	let idList = []
	insertRs.forEach(element => {
		const data = element.rows[0]
		idList.push({
			id : data.id,
			offerAmt : data.offer_amt,
			intRate : data.int_rate,
			tenor : data.tenor
		})
	})
	return idList
}

module.exports = {
	saveOffer
}