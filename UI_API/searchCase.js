const { caseStatus,MAPPING_STATUS_CLIENT_CODE,MAPPING_STATUS_CLIENT_NAME, caseStatusCode, KUNN_STATUS } = require("../const/caseStatus")
const utils = require("../utils/helper")
const loanContractRepo = require('../repositories/loan-contract-repo');
const { serviceEndpoint } = require('../const/config');
const common = require('../utils/common.js');
const { query } = require("express");
const {STATUS} = require("../const/caseStatus");
const { ReqError, BadReqExp } = require("../exception/exception");
const kunnFromService = require('../KUNN_V2/misa-kunn-form-service');
const appFromService = require('../KUNN_V2/app-form-service');
const { BadRequestResponse } = require("../base/response");
const helper = require("../utils/helper");
const kunnRepo = require("../repositories/kunn-repo");

async function searchCaseEasy(req,res) {
    try{
        const poolRead = req.poolRead
        let { 
            contractNumber,
            kunnNumber,  
            customerId, 
            clientName,
            idNumber,
            dob,
            phone,
            // caseCategory,
            startDate,
            endDate,
            phoneReference, 
            contractType, 
            smeTaxId,
            // page,
            // size,
            thirdPartyCode
        } = req.query
        if (utils.isNullOrEmpty(contractNumber) && utils.isNullOrEmpty(customerId) && utils.isNullOrEmpty(clientName) && utils.isNullOrEmpty(idNumber) && utils.isNullOrEmpty(dob)
            && utils.isNullOrEmpty(phone) && utils.isNullOrEmpty(startDate) && utils.isNullOrEmpty(kunnNumber) && utils.isNullOrEmpty(phoneReference) && utils.isNullOrEmpty(smeTaxId)
            && utils.isNullOrEmpty(thirdPartyCode)) {
            return res.status(200).json({
                code: -1,
                message: 'Missing params',
                data : []
            })
        }

        let sql = `select lc.product_code,lc.contract_type,lc.reference_phone_1, lc.reference_phone_2, lc.contract_number ,ku.kunn_id ,
        lc.status ,lc.cust_id ,lc.cust_full_name ,lc.request_amt,birth_date,lc.email,lc.id_number ,phone_number1,lc.partner_code,
        lc.created_date as contract_created_date,lc.updated_date as contract_updated_date,ku.created_date as ku_created_date,
        ku.updated_date as ku_updated_date,ku.status as drstatus,lc.lms_type,lc.sme_tax_id,lc.authorized_name,lc.approval_amt,
        lc.request_tenor,lc.request_int_rate,lc.approval_int_rate,lc.old_contract_number
        from loan_contract lc left join kunn ku on lc.contract_number = ku.contract_number where 1 = 1`
        const params = []
        let count = 1

        if(!utils.isNullOrEmpty(contractNumber)) {
            sql += ` and lc.contract_number like $${count}`
            count += 1
            params.push('%' + contractNumber + '%')
        }

        if(!utils.isNullOrEmpty(kunnNumber)) {
            sql += ` and ku.kunn_id like $${count}`
            count += 1
            params.push('%' + kunnNumber + '%')
        }

        if(!utils.isNullOrEmpty(customerId)) {
            sql += ` and lc.cust_id = $${count}`
            count += 1
            params.push(customerId)
        }
        if(!utils.isNullOrEmpty(clientName)) {
            sql += ` and (lc.cust_full_name like $${count} or lc.sme_name like $${count} or lc.authorized_name like $${count})`
            count += 1
            params.push('%' + clientName + '%')
        }
        
        if(!utils.isNullOrEmpty(idNumber)) {
            sql += ` and lc.id_number = $${count}`
            count += 1
            params.push(idNumber)
        }
        if(!utils.isNullOrEmpty(smeTaxId)) {
            sql += ` and lc.sme_tax_id = $${count}`
            count += 1
            params.push(smeTaxId)
        }
        if(!utils.isNullOrEmpty(thirdPartyCode)) {
            sql += ` and lc.misa_loan_code = $${count}`
            count += 1
            params.push(thirdPartyCode)
        }
        if(!utils.isNullOrEmpty(dob)) {
                if(!isValidDate(dob)) {
                return res.status(500).json({
                    code : -1,
                    msg : "Ngày sinh không hợp lệ.format YYYY-MM-DD"
                })
            }
            else {
                sql += ` and lc.birth_date = $${count}`
                count += 1
                params.push(dob)
            }
        }
        if(!utils.isNullOrEmpty(phone)) {
            sql += ` and lc.phone_number1=$${count}`
            count += 1
            params.push(phone)
        }
        if(!utils.isNullOrEmpty(phoneReference)) {
            sql += ` and (lc.reference_phone_1=$${count} or lc.reference_phone_2=$${count})`
            count += 1
            params.push(phoneReference)
        }
        if(contractType){
            sql += ` and lc.contract_type =$`+count
            count += 1
            params.push(contractType)
        }

        if(!utils.isNullOrEmpty(startDate)) {
            if(utils.isNullOrEmpty(endDate)) {
                endDate = new Date()
                endDate = utils.convertYYYYMMDD(endDate)
            }
            endDate = utils.addOneDayToDate(endDate)
            sql += ` and lc.created_date::date between $${count} and $${count +1}`
            params.push(startDate)
            params.push(endDate)
        }
        sql += ` order by ku.kunn_id desc limit 20`;
        const queryRs = await poolRead.query(sql,params)

        if(queryRs.rows.length == 0) {
            return res.status(201).json({
                code : 0,
                msg : "Không tìm thấy thông tin hợp lệ."
            })
        }
        else {
            const finalRs = [];
            for await (const row of queryRs.rows) {
                const rs = {
                    contract_number: row.contract_number || '',
                    productName: row.product_code=='MCBAS_HMTD'?'HMTD':row.product_code=='MCBAS_VIP'?'VIP':row.product_code=='MCBAS_STANDARD'?'STANDARD':row.product_code=='MCBAS_PREMIUM'?'PREMIUM':'',
                    finRequest : row.contract_number || '',
                    oldContractNumber: row.old_contract_number || '',
                    debt_ack_contract_number: row.kunn_id,
                    status: row.status || '',
                    statusName: row.status ? caseStatus[row.status] : '',
                    drStatus : row.drstatus,
                    status_client_code: MAPPING_STATUS_CLIENT_CODE[row.status] || row.status || '',
                    status_client_name: MAPPING_STATUS_CLIENT_NAME[MAPPING_STATUS_CLIENT_CODE[row.status]] || '',
                    cust_id:row.cust_id || '',
                    full_name: row.cust_full_name || '',
                    apr_limit_amt:row.request_amt || '',
                    birth_date: row.birth_date || '',
                    email: row.email || '',
                    id_number: row.id_number || '',
                    systemType:"MCC",
                    chanel:"MCC",
                    contractType:row.contract_type,
                    phone : row.phone_number1 || '',
                    phone_reference1 : row.reference_phone_1 || '',
                    phone_reference2 : row.reference_phone_2 || '',
                    partner : row.partner_code || '',
                    isManual : 0,
                    createdDate : row.ku_created_date || row.contract_created_date || 0,
                    updatedDate : row.ku_updated_date || row.contract_updated_date || 0,
                    lmsType : row.lms_type || row.contract_type,
                    request_amt: row.request_amt || 0,
                    approve_amt: row.approval_amt || 0,
                    tenor: row.request_tenor || 0,
                    rate: row.request_int_rate || row.approval_int_rate || 0,
                    approval_date: row.approval_date || '',
                    end_date: row.end_date || ''
                }
                finalRs.push(rs);
            }
            return res.status(200).json({
                code : 1,
                msg : "Tìm kiếm thông tin thành công.",
                data : finalRs
            })
        }
    }
    catch(err){
        console.log(`searchCaseEasy error: ${err.message}`);
        return res.status(201).json({
            code : 0,
            msg : "Lỗi tìm kiếm"
        })
    }
}
async function searchCaseEasyApp(req,res) {
    try{
        const poolRead = req.poolRead
        let { contractNumber,kunnNumber,  customerId, clientName,idNumber,dob,phone,startDate,endDate,phoneReference, contractType, smeTaxId,isAppMc} = req.query
        if (utils.isNullOrEmpty(contractNumber) && utils.isNullOrEmpty(customerId) && utils.isNullOrEmpty(clientName) && utils.isNullOrEmpty(idNumber) && utils.isNullOrEmpty(dob) 
            && utils.isNullOrEmpty(phone) && utils.isNullOrEmpty(startDate) && utils.isNullOrEmpty(kunnNumber) && utils.isNullOrEmpty(phoneReference) && utils.isNullOrEmpty(smeTaxId)) {
            return res.status(200).json({
                code: -1,
                message: 'Missing params',
                data : []
            })
        }

        let sql = `select lc.bill_day,lc.product_code,lc.contract_type,lc.reference_phone_1, lc.reference_phone_2, lc.contract_number ,ku.kunn_id ,
        lc.status ,lc.cust_id ,lc.cust_full_name ,lc.request_amt,birth_date,lc.email,lc.id_number ,phone_number1,lc.partner_code,
        lc.created_date as contract_created_date,lc.updated_date as contract_updated_date,ku.created_date as ku_created_date,
        ku.updated_date as ku_updated_date,ku.status as drstatus,lc.lms_type,lc.sme_tax_id,lc.authorized_name,lc.approval_amt,lc.request_tenor,lc.request_int_rate,lc.approval_int_rate,
        lc.bank_account, lc.bank_code, lc.gender, lc.old_contract_number, ku.with_draw_amount 
        from loan_contract lc left join kunn ku on lc.contract_number = ku.contract_number where 1 = 1`
        const params = []
        let count = 1
        let checkKunnStatusCustid = false;

        if(!utils.isNullOrEmpty(contractNumber)) {
            sql += ` and lc.contract_number like $${count}`
            count += 1
            params.push('%' + contractNumber + '%')
        }

        if(!utils.isNullOrEmpty(kunnNumber)) {
            sql += ` and ku.kunn_id like $${count}`
            count += 1
            params.push('%' + kunnNumber + '%')
        }

        if(!utils.isNullOrEmpty(customerId)) {
            sql += ` and lc.cust_id = $${count}`
            count += 1
            params.push(customerId)
        }
        if(!utils.isNullOrEmpty(clientName)) {
            sql += ` and (lc.cust_full_name like $${count} or lc.sme_name like $${count} or lc.authorized_name like $${count})`
            count += 1
            params.push('%' + clientName + '%')
        }
        
        if(!utils.isNullOrEmpty(idNumber)) {
            sql += ` and lc.id_number = $${count}`
            count += 1
            params.push(idNumber)
        }
        if(!utils.isNullOrEmpty(smeTaxId)) {
            sql += ` and lc.sme_tax_id = $${count}`
            count += 1
            params.push(smeTaxId)
        }
        if(!utils.isNullOrEmpty(dob)) {
                if(!isValidDate(dob)) {
                return res.status(500).json({
                    code : -1,
                    msg : "Ngày sinh không hợp lệ.format YYYY-MM-DD"
                })
            }
            else {
                sql += ` and lc.birth_date = $${count}`
                count += 1
                params.push(dob)
            }
        }
        if(!utils.isNullOrEmpty(phone)) {
            sql += ` and lc.phone_number1=$${count}`
            count += 1
            params.push(phone)
        }
        if(!utils.isNullOrEmpty(phoneReference)) {
            sql += ` and (lc.reference_phone_1=$${count} or lc.reference_phone_2=$${count})`
            count += 1
            params.push(phoneReference)
        }
        if(contractType){
            sql += ` and lc.contract_type =$`+count
            count += 1
            params.push(contractType)
        }

        if(!utils.isNullOrEmpty(startDate)) {
            if(utils.isNullOrEmpty(endDate)) {
                endDate = new Date()
                endDate = utils.convertYYYYMMDD(endDate)
            }
            endDate = utils.addOneDayToDate(endDate)
            sql += ` and lc.created_date::date between $${count} and $${count +1}`
            params.push(startDate)
            params.push(endDate)
        }
        if(utils.isNullOrEmpty(isAppMc)) sql += ` limit 20`
        const queryRs = await poolRead.query(sql,params)
        const sqlGetKunn = `select * from loan_contract lc inner join kunn k
                            on lc.contract_number = k.contract_number 
                            where lc.status = 'WAITING_TO_BE_SIGNED' and k.status = 'WAITING_TO_BE_SIGNED' and cust_id = $1 limit 1`
        const sqlQueryRs = await poolRead.query(sqlGetKunn,[customerId])
        const kunnId = sqlQueryRs.rows[0]?.kunn_id
        if(queryRs.rows.length == 0) {
            return res.status(201).json({
                code : 0,
                msg : "Không tìm thấy thông tin hợp lệ."
            })
        }
        else {
            const finalRs = [];
            for await (const row of queryRs.rows) {
                let availableAmount = null;
                const contractStatus = await utils.getKuStatus(poolRead, row.contract_number);
                if(([STATUS.CANCELLED,STATUS.TERMINATED,STATUS.ACTIVATED,STATUS.KH09,'CP02'].includes(row.status))) {
                    const body = { 
                        "contractNumber" : row.contract_number
                    }
                    const checkAvailableUrl = global.config.basic.lmsMc[config.env] + global.config.data.lms.checkAvailable
                    const availableRs = await common.postApiV2(checkAvailableUrl,body)
                    availableAmount = availableRs?.data?.data?.avalibleAmount
                    if(availableRs?.data?.code == 0 && parseFloat(availableAmount) > 0){
                        checkKunnStatusCustid = true;
                        if(contractStatus?.length != 0) {
                            for(let i=0; i< contractStatus.length; i ++) {
                                if(([STATUS.CANCELLED,STATUS.TERMINATED,STATUS.ACTIVATED,STATUS.NOT_ELIGIBLE,caseStatusCode.KKH13,caseStatusCode.KCP07,caseStatusCode.KKH14,caseStatusCode.KKH15].includes(contractStatus[i].status))){
                                    checkKunnStatusCustid = true;
                                } else {
                                    checkKunnStatusCustid = false;
                                }
                            }
                        }
                    }
                }else{
                    checkKunnStatusCustid = false;
                }
                const rs = {
                    contract_number: row.contract_number || '',
                    productName: row.product_code=='MCBAS_HMTD'?'HMTD':row.product_code=='MCBAS_VIP'?'VIP':row.product_code=='MCBAS_STANDARD'?'STANDARD':row.product_code=='MCBAS_PREMIUM'?'PREMIUM':'',
                    finRequest : row.contract_number || '',
                    oldContractNumber: row.old_contract_number || '',
                    debt_ack_contract_number: row.kunn_id,
                    status: row.status || '',
                    statusName: row.status ? caseStatus[row.status] : '',
                    drStatus : row.drstatus,
                    status_client_code: MAPPING_STATUS_CLIENT_CODE[row.status] || row.status || '',
                    status_client_name: MAPPING_STATUS_CLIENT_NAME[MAPPING_STATUS_CLIENT_CODE[row.status]] || '',
                    cust_id:row.cust_id || '',
                    full_name: row.cust_full_name || '',
                    apr_limit_amt:row.request_amt || '',
                    birth_date: row.birth_date || '',
                    email: row.email || '',
                    id_number: row.id_number || '',
                    systemType:"MCC",
                    chanel:"MCC",
                    contractType:row.contract_type,
                    phone : row.phone_number1 || '',
                    phone_reference1 : row.reference_phone_1 || '',
                    phone_reference2 : row.reference_phone_2 || '',
                    partner : row.partner_code || '',
                    isManual : 0,
                    createdDate : row.ku_created_date || row.contract_created_date || 0,
                    updatedDate : row.ku_updated_date || row.contract_updated_date || 0,
                    lmsType : row.lms_type || row.contract_type,
                    request_amt: row.request_amt || 0,
                    approve_amt: row.approval_amt || 0,
                    tenor: row.request_tenor || 0,
                    rate: row.request_int_rate || row.approval_int_rate || 0,
                    approval_date: row.approval_date || '',
                    end_date: row.end_date || '',
                    remain_amt: (await loanContractRepo.getRemainInfo(row.contract_number))?.remain_amt,
                    loaned_amt: (await loanContractRepo.getRemainInfo(row.contract_number))?.loan_amt,
                    checkKunnStatusCustid: checkKunnStatusCustid,
                    availableAmount: utils.isNullOrEmpty(availableAmount) ? row.approval_amt : availableAmount,
                    bank_account: row.bank_account || '',
                    bank_code: row.bank_code || '',
                    gender: row.gender || '',
                    bill_day: row.bill_day || '',
                    kunnId,
                    with_draw_amount: row.with_draw_amount || 0
                }
                finalRs.push(rs);
            }
            return res.status(200).json({
                code : 1,
                msg : "Tìm kiếm thông tin thành công.",
                data : finalRs
            })
        }
    }
    catch(err){
        console.log(`searchCaseEasy error: ${err.message}`);
        return res.status(201).json({
            code : 0,
            msg : "Lỗi tìm kiếm"
        })
    }
}

async function searchKunn(req,res) {
    try {
        let kunnNumber = req.query
        const kunnDetailUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.getKunnDetail}${kunnNumber.contractNumber}`
        const data = await common.getApiV2(kunnDetailUrl)
        return res.status(200).json({
            code : 1,
            msg : "Tìm kiếm thông tin thành công.",
            data: data?.data?.data
        })
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function paymentInfo(req,res) {
    try {
        // const contract_number = {
        //     contractNumber: req.body
        // }
        const contractNumber = req.body
        console.log(global.config.basic.easy_ui_service);
        //const kunnDetailUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.EASY_UI_SERVICE.getPaymentInfo}${contractNumber.contractNumber}`
        const kunnDetailUrl = `${global.config.basic.easy_ui_service[global.config.env]}${serviceEndpoint.EASY_UI_SERVICE.getPaymentInfo}`
        //console.log(global.config.basic.EASY_UI_SERVICE)
        console.log(kunnDetailUrl);
        const data = await common.postApiV2(kunnDetailUrl, contractNumber)
        return res.status(200).json({
            code : 1,
            msg : "Thêm mới thông tin thành công.",
            data: data?.data?.data
        })
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function installment(req,res) {
    try {
        let kunnNumber = req.query
        const kunnDetailUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.getInstallment}${kunnNumber.debtAckContractNumber}`
        const data = await common.getApiV2(kunnDetailUrl)
        return res.status(200).json({
            code : 1,
            msg : "Tìm kiếm thông tin thành công.",
            data: data?.data?.data
        })
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function listContract(req,res) {
    try {
        const kunnNumber = req.query
        console.log(global.config.basic.easy_ui_service);
        const listContractUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.listKunn}${kunnNumber.contractNumber}`
        console.log(listContractUrl);
        const data = await common.getApiV2(listContractUrl)
        return res.status(200).json({
            code : 1,
            msg : "Tìm kiếm thông tin thành công.",
            data: data?.data?.data
        })
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function repayment(req,res) {
    try {
        const kunnNumber = req.query
        console.log(global.config.basic.easy_ui_service);
        const listContractUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.repayment}${kunnNumber.custId}`
        console.log(listContractUrl);
        const data = await common.getApiV2(listContractUrl)
        return res.status(200).json({
            code : 1,
            msg : "Tìm kiếm thông tin thành công.",
            data: data?.data?.data
        })
    }
    catch(err) {
        console.log(err)
        return false
    }
}

function isValidDate(dateString) {
    let regEx = /^\d{4}-\d{2}-\d{2}$/;
    if(!dateString.match(regEx)) return false;  // Invalid format
    let d = new Date(dateString);
    let dNum = d.getTime();
    if(!dNum && dNum !== 0) return false; // NaN value, Invalid date
    return d.toISOString().slice(0,10) === dateString;
}

const searchCaseByContractList = async function (req, res) {
    try {
        const { contractList } = req.body;
        if (!contractList || contractList.length == 0) {
            return res.status(200).json({
                code: -1,
                message: 'Missing params',
                data: []
            })
        }

        const contractListEntity = await loanContractRepo.findByContractList({ contractList });
        if (!contractListEntity) return res.status(200).json({
            code: 0,
            message: 'Không tìm thấy thông tin hợp lệ.',
            data: []
        })

        let dataReponse = [];
        for (const lcEntity of contractListEntity) {
            let contractDetail = {
                product_code: lcEntity?.product_code || null,
                status: lcEntity?.status || null,
                created_on: lcEntity?.created_date || null,
                tem_province: lcEntity?.province_cur || null,
                contract_number: lcEntity.contract_number || null,
                customer_name: lcEntity.cust_full_name,
                customer_phone: lcEntity.phone_number1,
                reference_phone_1: lcEntity.reference_phone_1,
                reference_name_1: lcEntity.reference_name_1,
                reference_phone_2: lcEntity.reference_phone_2,
                reference_name_2: lcEntity.reference_name_2,
                identity_card_customer: lcEntity.id_number,
                partner_code: lcEntity.partner_code
            }
            dataReponse.push(contractDetail);
        }
        return res.status(200).json({
            code: 1,
            msg: "Tìm kiếm thông tin thành công.",
            data: dataReponse
        })
    } catch (err) {
        console.log(`searchCaseByContractList error: ${err.message}`);
        console.log(err);

        return res.status(200).json({
            code: -1,
            message: 'searchCaseByContractList error: ' + err.message,
            data: []
        })
    }
}

const searchCaseByPhoneList = async function (req, res) {
    try {
        const { phoneList } = req.body;
        if (!phoneList || phoneList.length == 0) {
            return res.status(200).json({
                code: -1,
                message: 'Missing params',
                data: []
            })
        }

        const loanContractsByPhone = await loanContractRepo.findByPhoneNumberList({ phoneList });
        if (!loanContractsByPhone) return res.status(200).json({
            code: 0,
            message: 'Không tìm thấy thông tin hợp lệ.',
            data: []
        })

        let dataReponse = [];
        for (const lcEntity of loanContractsByPhone) {
            let contractDetail = {
                contract_number: lcEntity.contract_number || null,
                customer_name: lcEntity.cust_full_name,
                phone_number: lcEntity.phone_number1 || null,
                relative_reference_name_1: lcEntity.reference_name_1 || null,
                relative_reference_name_2: lcEntity.reference_name_2 || null,
                relative_reference_phone_1: lcEntity.reference_phone_1 || null,
                relative_reference_phone_2: lcEntity.reference_phone_2 || null
            }
            dataReponse.push(contractDetail);
        }
        return res.status(200).json({
            code: 1,
            msg: "Tìm kiếm thông tin thành công.",
            data: dataReponse || []
        })
    } catch (err) {
        console.log(`searchCaseByPhoneList error: ${err.message}`);
        console.log(err);

        return res.status(200).json({
            code: -1,
            message: 'searchCaseByPhoneList error: ' + err.message,
            data: []
        })
    }
}

const getApplicationForm = async (req, res) => {
  try {
    const { contractNumber } = req.query;
    if (!contractNumber) {
      const errors = [
        {
          code: "contractNumber",
          message: `missing contractNumber`,
        },
      ];
      throw new BadReqExp({ data: new BadRequestResponse(errors) });
    }

      let  data
      const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(contractNumber));
      if (kunn) {
          data = await kunnFromService.getApplicationForm(contractNumber);
      } else {
          data = await appFromService.getApplicationForm(contractNumber);
      }

    return res.json(data);
  } catch (error) {
    if (error instanceof ReqError)
      return res.status(error.statusCode).json(error.data);
    else return common.responseErrorPublic(res);
  }
};

module.exports = {
    searchCaseEasy,
    searchKunn,
    paymentInfo,
    installment,
    listContract,
    repayment,
    searchCaseByContractList,
    searchCaseByPhoneList,
    searchCaseEasyApp,
    getApplicationForm
}