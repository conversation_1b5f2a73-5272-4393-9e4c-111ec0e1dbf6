const utils = require("../utils/loggingService")
const {roleCode} = require("../const/definition")
const { saveUpdateCusLog } = require("../services/crm-service")
const { getLoanContract } = require("../repositories/loan-contract-repo")

const mappingParams = {
    fullName : {
        table : "loan_contract",
        attribute :"cust_full_name" 
    },
    id: {
        table : "loan_contract",
        attribute : "id_number"
    },
    "issueDate": {
        table : "loan_contract",
        attribute : "id_issue_dt"
    },
    "issuePlaceCode" : {
        table : "loan_contract",
        attribute : "id_issue_place"
    },
    "otherId": {
        table : "loan_contract",
        attribute : "other_id_number"
    },
    "otherIssueDate" : {
        table : "loan_contract",
        attribute : "other_issue_date"
    },
    "otherIssuePlaceCode" : {
        table : "loan_contract",
        attribute : "other_issue_place"
    },
    "dateOfBirth": {
        table : "loan_contract",
        attribute : "birth_date"
    },
    "permanentProvinceCode": {
        table : "loan_contract",
        attribute : "province_per"
    },
    "permanentDistrictCode": {
        table : "loan_contract",
        attribute : "district_per"
    },
    "permanentWardCode": {
        table : "loan_contract",
        attribute : "ward_per"
    },
    permanentAddress: {
        table : "loan_contract",
        attribute : "address_per"
    },
    provinceCode : {
        table : "loan_branch_address",
        attribute : "province"
    },
    districtCode : {
        table : "loan_branch_address",
        attribute : "district"
    },
    wardCode : {
        table : "loan_branch_address",
        attribute : "ward"
    },
    street : {
        table : "loan_branch_address",
        attribute : "address"
    },
    branchName : {
        table : "loan_branch_address",
        attribute : "branch_name"
    },
    smeName: {
        table: "loan_contract",
        attribute: "sme_name"
    },
    taxId: {
        table: "loan_contract",
        attribute: "sme_tax_id"
    },
    phoneNumber:{
        table: "loan_contract",
        attribute: "sme_phone_number"
    },
    smeRepresentationName: {
        table: "loan_contract",
        attribute: "sme_representation_name"
    },
    headquartersAddress: {
        table: "loan_contract",
        attribute: "sme_headquarters_address"
    },
    headquartersProvinceObj: {
        table: "loan_contract",
        attribute: "sme_headquarters_province"
    },
    headquartersDistrictObj: {
        table: "loan_contract",
        attribute: "sme_headquarters_district"
    },
    headquartersWardObj: {
        table: "loan_contract",
        attribute: "sme_headquarters_ward"
    },
    authorizedName: {
        table: "loan_contract",
        attribute: "authorized_name"
    },
    temporaryProvinceCode: {
        table: "loan_contract",
        attribute: "province_cur"
    },
    temporaryDistrictCode: {
        table : "loan_contract",
        attribute : "district_cur"
    },
    temporaryWardCode: {
        table : "loan_contract",
        attribute : "ward_cur"
    },
    temporaryAddress: {
        table : "loan_contract",
        attribute : "address_cur"
    },
    smeEmail: {
        table : "loan_contract",
        attribute : "sme_email"
    },
    warehouseProvinceCode: {
        table: "loan_contract",
        attribute: "ware_house_province"
    },
    warehouseDistrictCode: {
        table : "loan_contract",
        attribute : "ware_house_district"
    },
    warehouseWardCode: {
        table : "loan_contract",
        attribute : "ware_house_ward"
    },
    warehouseAddress: {
        table : "loan_contract",
        attribute : "ware_house_address"
    },
    firstRegistrationDate: {
        table : "loan_contract",
        attribute : "first_registration_date"
    },
    representationName: {
        table : "loan_contract",
        attribute : "sme_representation_name"
    },
    representationDOB: {
        table : "loan_contract",
        attribute : "sme_representation_dob"
    },
    representationId: {
        table : "loan_contract",
        attribute : "sme_representation_id"
    },
    representationIssueDate: {
        table : "loan_contract",
        attribute : "sme_representation_issue_date"
    },
    representationIssuePlaceCode: {
        table : "loan_contract",
        attribute : "sme_representation_issue_place"
    },
    representationPhoneNumber: {
        table : "loan_contract",
        attribute : "sme_representation_phone_number"
    },
    authorizedDOB: {
        table : "loan_contract",
        attribute : "authorized_dob"
    },
    authorizedId: {
        table : "loan_contract",
        attribute : "authorized_id"
    },
    authorizedIssueDate: {
        table : "loan_contract",
        attribute : "authorized_issue_date"
    },
    authorizedIssuePlaceCode: {
        table : "loan_contract",
        attribute : "authorized_issue_place"
    },
    authorizedPhoneNumber: {
        table : "loan_contract",
        attribute : "authorized_phone_number"
    },
    rPerProvinceCode: {
        table : "loan_contract",
        attribute : "sme_representation_province_per"
    },
    rPerDistrictCode: {
        table : "loan_contract",
        attribute : "sme_representation_district_per"
    },
    rPerWardCode: {
        table : "loan_contract",
        attribute : "sme_representation_ward_per"
    },
    rPerAddress: {
        table : "loan_contract",
        attribute : "sme_representation_address_per"
    },
    rTempProvinceCode: {
        table : "loan_contract",
        attribute : "sme_representation_province_cur"
    },
    rTempDistrictCode: {
        table : "loan_contract",
        attribute : "sme_representation_district_cur"
    },
    rTempWardCode: {
        table : "loan_contract",
        attribute : "sme_representation_ward_cur"
    },
    rTempAddress: {
        table : "loan_contract",
        attribute : "sme_representation_address_cur"
    },
    aPerProvinceCode: {
        table : "loan_contract",
        attribute : "authorized_province_cur"
    },
    aPerDistrictCode: {
        table : "loan_contract",
        attribute : "authorized_district_cur"
    },
    aPerWardCode: {
        table : "loan_contract",
        attribute : "authorized_ward_cur"
    },
    aPerAddress: {
        table : "loan_contract",
        attribute : "authorized_address_cur"
    },
    aTempProvinceCode: {
        table : "loan_contract",
        attribute : "authorized_province_per"
    },
    aTempDistrictCode: {
        table : "loan_contract",
        attribute : "authorized_district_per"
    },
    aTempWardCode: {
        table : "loan_contract",
        attribute : "authorized_ward_per"
    },
    aTempAddress: {
        table : "loan_contract",
        attribute : "authorized_address_per"
    },
    registrationNumber: {
        table : "loan_contract",
        attribute : "registration_number"
    },
}

async function updateAF(req,res) {
    const poolWrite = req.poolWrite
    let {contractNumber,userName,role,clientInfo,branchInfo, smeInfo} = req.body;
    const contractData = await getLoanContract(contractNumber);
    const custId = contractData?.cust_id;
    
    if(role != roleCode.SS) {
        return res.status(403).json({
            code : -1,
            msg : "Không có quyền thực thi"
        })
    }
    
    const requestFormCrm = {
        custId,
        updatedField: [...clientInfo,...branchInfo,...smeInfo]
      };

    const updateCRM = await saveUpdateCusLog(req,requestFormCrm);
    if(!updateCRM) return res.status(400).json({code : -1,msg : "error when update to crm"});

    const mistakeList = []
    if(clientInfo !== undefined && clientInfo != null) {
        clientInfo.map(element => {
            if(mappingParams.hasOwnProperty(element.updatedField)) {
                utils.saveUpdateHist(poolWrite,element.updatedField,element.oldValue,element.newValue,userName,contractNumber,null)
                const databaseTable = mappingParams[element.updatedField].table
                const databaseField = mappingParams[element.updatedField].attribute
                const sql = `update ${databaseTable} set ${databaseField} = $1 where contract_number = $2`   
                poolWrite.query(sql,[element.newValue,contractNumber])
            }
            else {
                mistakeList.push("invalid field : " + element.updatedField)
            }
        })
    }

    if(branchInfo !== undefined && branchInfo != null) {
        branchInfo.map(element => {
            const branchId = element.id
            if(mappingParams.hasOwnProperty(element.updatedField)) {
                utils.saveUpdateHist(poolWrite,element.updatedField,element.oldValue,element.newValue,userName,contractNumber,null)
                const databaseTable = mappingParams[element.updatedField].table
                const databaseField = mappingParams[element.updatedField].attribute
                const sql = `update ${databaseTable} set ${databaseField} = $1 where id = $2`   
                poolWrite.query(sql,[element.newValue,branchId])
            }
            else {
                mistakeList.push(`branch id ${branchId} invalid field : ` + element.updatedField)
            }
        })
    }

    if(branchInfo !== undefined && branchInfo != null) {
        branchInfo.map(element => {
            const branchId = element.id
            if(mappingParams.hasOwnProperty(element.updatedField)) {
                utils.saveUpdateHist(poolWrite,element.updatedField,element.oldValue,element.newValue,userName,contractNumber,null)
                const databaseTable = mappingParams[element.updatedField].table
                const databaseField = mappingParams[element.updatedField].attribute
                const sql = `update ${databaseTable} set ${databaseField} = $1 where id = $2`   
                poolWrite.query(sql,[element.newValue,branchId])
            }
            else {
                mistakeList.push(`branch id ${branchId} invalid field : ` + element.updatedField)
            }
        })
    }

    if(smeInfo !== undefined && smeInfo != null) {
        smeInfo.map(element => {
            if(mappingParams.hasOwnProperty(element.updatedField)) {
                utils.saveUpdateHist(poolWrite,element.updatedField,element.oldValue,element.newValue,userName,contractNumber,null)
                const databaseTable = mappingParams[element.updatedField].table
                const databaseField = mappingParams[element.updatedField].attribute
                const sql = `update ${databaseTable} set ${databaseField} = $1 where contract_number = $2`   
                poolWrite.query(sql,[element.newValue,contractNumber])
            }
            else {
                mistakeList.push("invalid field : " + element.updatedField)
            }
        })
    }

    if(mistakeList.length != 0) {
        return res.status(400).json({
            code : -1,
            msg : "invalid update field",
            data : mistakeList
        })
    }
    return res.status(200).json({
        code : 1,
        msg : "update form successfully."
    })
    
}

module.exports = {
    updateAF
}