function updateCheckList(req,res) {
	try {
		const {contractNumber, userCE, userRole,checkList} = req.body
		const isValid = checkValid(contractNumber, userCE, userRole,checkList)
		if(isValid && isValid.code !== 1) {
			res.status(200).json(isValid)
		}
		
		res.status(200).json({
		  "code": 1,
		  "message": "SUCCESS CORRECT",
		  "data": {}
		})
	}
	catch {
		res.status(500).json({
			"code": -1,
			"message": "ERROR",
		  })
	}
	
}

const checkValid=(contractNumber, userCE, userRole,checkList)=> {
	if(!contractNumber) return {
		code: -1,
		message : "Missing contractNumber"
	} 
	else if (!userCE) {
		return {
			code: -1,
			message : "Missing contractNumber"
		} 
	}
	else if (!userRole) {
		return {
			code: -1,
			message : "Missing userRole"
		} 
	}
	else if (!checkList || checkList.length <= 0 || typeof checkList !== 'object') {
		return {
			code: -1,
			message : "Missing or Invalid CheckList"
		} 
	}
	else {
		return {
			code: 1,
			message : ""
		} 
	}
}

module.exports = {
	updateCheckList
}