const common = require("../utils/common");
const { BUDGET_ANALYSIS,PARTNER_CODE } = require("../const/definition");
const camelcaseKeys = require("camelcase-keys");
const loanContractRepo = require("../repositories/loan-contract-repo")
const _ = require("underscore")

async function getBudgetAnalysis(req, res) {
    try {
        const productUri = req.config.data.productService.getBudgetTable;
        const productUrl =
            req.config.basic.product[req.config.env] + productUri;

        const result = await common.getAPI(productUrl);

        if (!result || !result.data) {
            res.status(400).json({
                code: -1,
                msg: "can not found product",
            });
        }

        const contractNumber = req.query.contractNumber;
        const contractData = await loanContractRepo.getLoanContractJoinMainScore(global.poolWrite,contractNumber)
        
        let sql = "select lc.turnover_12m ,ltt.info_type ,ltt.month_of_info ,ltt.value_of_month from loan_contract lc join loan_turnover_transaction ltt on lc.contract_number =ltt.contract_number where lc.contract_number =$1 and ltt.info_type ='turnover';";
        if(contractData.partner_code == PARTNER_CODE.VPL) {
            sql = "select lc.turnover_12m ,ltt.info_type ,ltt.month_of_info ,ltt.value_of_month from loan_contract lc join loan_turnover_transaction ltt on lc.contract_number =ltt.contract_number where lc.contract_number =$1 and ltt.info_type ='TOTAL_TURNOVER';";
        }
        else if(contractData.partner_code == PARTNER_CODE.VTP) {
            sql = "select lc.turnover_12m ,ltt.info_type ,ltt.month_of_info ,ltt.value_of_month from loan_contract lc join loan_turnover_transaction ltt on lc.contract_number =ltt.contract_number where lc.contract_number =$1 and ltt.info_type ='ESTIMATED_TURNOVER';";
        }
        const poolRead = req.poolRead;

        const resultTurnover = await poolRead.query(sql, [contractNumber]);

        if (!resultTurnover || !resultTurnover.rows) {
            res.status(400).json({
                code: -1,
                msg: "can not found contract number",
            });
        }

        let camelCaseProduct = camelcaseKeys(result.data, { deep: true });

        let resultFinal = [];
        // let t1 = 0;
        // let t2 = 0;
        // let t3 = 0;
        let total12 = 0;
        // let avg3M;
        // let avg6M;
        let costLiving = BUDGET_ANALYSIS.COST_LIVE_ANOTHER;

        
        if (contractData.province_cur.indexOf(BUDGET_ANALYSIS.COST_PROVINCE) !== -1) {
            costLiving = BUDGET_ANALYSIS.COST_LIVE_CITY;
        }
        // if(contractData.partner_code == PARTNER_CODE.VTP) {
        //     avg3M = _.filter(resultTurnover.rows,function(month){return month.month_of_info <= 3}).map(rs => rs.value_of_month).reduce((a,b) => parseFloat(a) +parseFloat(b)) / 3
        //     // Viettel COD avg 3 month = 6 nmonth
        //     avg6M = avg3M
        // }
        // else {
        //     avg3M = _.filter(resultTurnover.rows,function(month){return month.month_of_info <= 3}).map(rs => rs.value_of_month).reduce((a,b) => parseFloat(a) +parseFloat(b)) / 3
        //     avg6M = _.filter(resultTurnover.rows,function(month){return month.month_of_info <= 6}).map(rs => rs.value_of_month).reduce((a,b) => parseFloat(a) +parseFloat(b)) / 6
        // }

        resultTurnover.rows.forEach((itemTurnover) => {
            // if (itemTurnover.month_of_info <= 3) {
            //     switch (itemTurnover.month_of_info) {
            //         case 1:
            //             t1 = itemTurnover.value_of_month;
            //             break;

            //         case 2:
            //             t2 = itemTurnover.value_of_month;
            //             break;

            //         case 3:
            //             t3 = itemTurnover.value_of_month;
            //             break;

            //         default:
            //             break;
            //     }
            // }
            
            if (itemTurnover.month_of_info > 0) {
                total12 += parseInt(itemTurnover.value_of_month);
            }
        });

        

        const sqlBudgetAnalysis = `SELECT * FROM budget_analysis WHERE contract_number = $1`;

        let dataRowBudgetAnalysis = {};

        const resultBudgetAnalysis = await poolRead.query(sqlBudgetAnalysis, [
            contractNumber,
        ]);

        if (
            resultBudgetAnalysis &&
            resultBudgetAnalysis.rows &&
            resultBudgetAnalysis.rows[0]
        ) {
            dataRowBudgetAnalysis = resultBudgetAnalysis.rows[0];
        }

        for (let index = 0; index < camelCaseProduct.length; index++) {
            const itemProduct = camelCaseProduct[index];

            let newItem = {
                id: itemProduct.id,
                code: itemProduct.code,
                section: itemProduct.section,
            };

            let dataExtend;
            switch (itemProduct.code) {
                case "A.1":
                    // newItem = {
                    //     ...newItem,
                    //     estimatedMonthT: dataRowBudgetAnalysis.month_t || "",
                    //     estimatedRevenue:
                    //         ((total12 / resultTurnover.rows.length) * 
                    //             (contractData.revenue_growth_rate ||
                    //                 BUDGET_ANALYSIS.REVENUE_GROWTH_RATE)) /
                    //         100,
                    //     avg12: total12 / resultTurnover.rows.length,
                    //     t1,
                    //     t2,
                    //     t3,
                    //     avg3 : avg3M,
                    //     avg6 : avg6M,
                    // }
                    dataExtend = dataRowBudgetAnalysis.sum_turnover
                        ? JSON.parse(dataRowBudgetAnalysis.sum_turnover)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "A.2":
                    dataExtend = dataRowBudgetAnalysis.revenue_ratio
                        ? JSON.parse(dataRowBudgetAnalysis.revenue_ratio)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "A_":
                    dataExtend = dataRowBudgetAnalysis.captital_cost_ratio
                        ? JSON.parse(dataRowBudgetAnalysis.captital_cost_ratio)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "C":
                    dataExtend = dataRowBudgetAnalysis.cost_rent
                        ? JSON.parse(dataRowBudgetAnalysis.cost_rent)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "D":
                    dataExtend = dataRowBudgetAnalysis.employee_salary
                        ? JSON.parse(dataRowBudgetAnalysis.employee_salary)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "F":
                    dataExtend = dataRowBudgetAnalysis.hkd_tax
                        ? JSON.parse(dataRowBudgetAnalysis.hkd_tax)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "H":
                    if (dataRowBudgetAnalysis.living_cost) {
                        dataExtend = JSON.parse(dataRowBudgetAnalysis.living_cost)
                        newItem = {
                            ...newItem,
                            ...dataExtend,
                        };
                    } else {
                        newItem = {
                            ...newItem,
                            estimatedMonthT: costLiving,
                            estimatedRevenue: costLiving,
                            avg12: costLiving,
                            t1: costLiving,
                            t2: costLiving,
                            t3: costLiving,
                            avg3: costLiving,
                            avg6: costLiving,
                        };
                    }
                    break;

                case "H_":
                    dataExtend = dataRowBudgetAnalysis.number_of_depend
                        ? JSON.parse(dataRowBudgetAnalysis.number_of_depend)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "I":
                    dataExtend = dataRowBudgetAnalysis.depend_living_cost
                        ? JSON.parse(dataRowBudgetAnalysis.depend_living_cost)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "J":
                    dataExtend = dataRowBudgetAnalysis.house_rent
                        ? JSON.parse(dataRowBudgetAnalysis.house_rent)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "K":
                    dataExtend = dataRowBudgetAnalysis.debts_payable
                        ? JSON.parse(dataRowBudgetAnalysis.debts_payable)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                case "K_":
                    dataExtend = dataRowBudgetAnalysis.creadit_card_balance
                        ? JSON.parse(dataRowBudgetAnalysis.creadit_card_balance)
                        : {};

                    newItem = {
                        ...newItem,
                        ...dataExtend,
                    };
                    break;

                default:
                    break;
            }

            resultFinal.push(newItem);
        }

        res.status(200).json({
            code: 1,
            msg: "get Buget data successful",
            data: {
                numberOfStaffs: contractData.number_of_staffs,
                totalStock: contractData.total_stock,
                revenueOfSeller: contractData.revenue_of_seller,
                capitalTurnover: contractData.capital_turnover,
                revenueGrowthRate:
                    dataRowBudgetAnalysis.revenue_growth_rate ||
                    BUDGET_ANALYSIS.REVENUE_GROWTH_RATE,
                minCapital:
                    dataRowBudgetAnalysis.min_capital ||
                    BUDGET_ANALYSIS.MIN_CAPITAL,
                currentLoanOutstandingT1:
                    dataRowBudgetAnalysis.current_loan_outstanding_t_1 || "",
                currentLoanOutstandingT2:
                    dataRowBudgetAnalysis.current_loan_outstanding_t_2 || "",
                currentLoanOutstandingT3:
                    dataRowBudgetAnalysis.current_loan_outstanding_t_3 || "",
                plusTxt: dataRowBudgetAnalysis.plus_txt,
                minusTxt: dataRowBudgetAnalysis.minus_txt,
                suspectTxt: dataRowBudgetAnalysis.suspect_txt,
                otherTxt: dataRowBudgetAnalysis.other_txt,
                dataBudget: resultFinal,
            },
        });
    } catch (error) {
        res.status(500).json({
            code: -1,
            message: error.message,
        });
    }
}

const updateBudgetAnalysis = async (req, res) => {
    try {
        const {
            contractNumber,
            numberOfStaffs,
            totalStock,
            revenueOfSeller,
            capitalTurnover,
            revenueGrowthRate,
            minCapital,
            dataBudget,
            currentLoanOutstandingT1,
            currentLoanOutstandingT2,
            currentLoanOutstandingT3,
            plusTxt,
            minusTxt,
            suspectTxt,
            otherTxt,
        } = req.body;
        const { poolRead, poolWrite } = req;

        if (!contractNumber) {
            res.status(200).json({
                code: -1,
                message: "Missing params",
            });
        }

        const sqlLoanContract = `SELECT loan_contract.contract_number FROM loan_contract WHERE contract_number=$1`;

        const resultContractNumber = await poolRead.query(sqlLoanContract, [
            contractNumber,
        ]);

        if (!resultContractNumber || !resultContractNumber.rows) {
            res.status(200).json({
                code: -1,
                message: "can not found contract number",
            });
        }

        // update data loan_contract
        let sqlUpdateLoanContract = `UPDATE loan_contract SET 
                number_of_staffs = $1,
                total_stock = $2,
                revenue_of_seller = $3,
                capital_turnover = $4
                WHERE contract_number=$5`;

        await poolWrite.query(sqlUpdateLoanContract, [
            numberOfStaffs,
            totalStock,
            revenueOfSeller,
            capitalTurnover,
            contractNumber,
        ]);

        // update data budget_analysis
        let sql = `SELECT budget_analysis.contract_number FROM budget_analysis 
            WHERE contract_number = $1`;
        const data = await poolRead.query(sql, [contractNumber]);

        let dataParams = [];

        let month_t = "";
        dataBudget.forEach((itemBudget) => {
            const { t1, t2, t3, estimatedMonthT, estimatedRevenue, avg12,avg3,avg6 } =
                itemBudget;

            let itemJsonStringify = JSON.stringify({
                t1: t1 || "",
                t2: t2 || "",
                t3: t3 || "",
                estimatedMonthT: estimatedMonthT || "",
                estimatedRevenue: estimatedRevenue || "",
                avg12: avg12 || "",
                avg3 : avg3 || "",
                avg6 : avg6 || "",
            });

            switch (itemBudget.code) {
                case "A.1":
                    month_t = itemBudget.estimatedMonthT;
                    // dataParams.push(itemBudget.estimatedMonthT || "");
                    // break;

                case "A.2":
                case "A_":
                case "C":
                case "D":
                case "F":
                case "H":
                case "H_":
                case "I":
                case "J":
                case "K":
                case "K_":
                    dataParams.push(itemJsonStringify);
                    break;

                default:
                    break;
            }
        });

        dataParams.push(
            currentLoanOutstandingT1,
            currentLoanOutstandingT2,
            currentLoanOutstandingT3,
            plusTxt,
            minusTxt,
            suspectTxt,
            otherTxt,
            revenueGrowthRate,
            minCapital,
            month_t,
        );

        if (data && data.rows && data.rows[0]) {
            dataParams.push(contractNumber);
            let sqlUpdate = `UPDATE budget_analysis SET 
                sum_turnover = $1,
                revenue_ratio = $2,
                captital_cost_ratio = $3,
                cost_rent = $4,
                employee_salary = $5,
                hkd_tax = $6,
                living_cost = $7,
                number_of_depend = $8,
                depend_living_cost = $9,
                house_rent = $10,
                debts_payable = $11,
                creadit_card_balance = $12,
                current_loan_outstanding_t_1 = $13,
                current_loan_outstanding_t_2 = $14,
                current_loan_outstanding_t_3 = $15,
                plus_txt = $16,
                minus_txt = $17,
                suspect_txt = $18,
                other_txt = $19,
                revenue_growth_rate = $20,
                min_capital = $21,
                month_t = $22
                WHERE contract_number = $23`;

            await poolWrite.query(sqlUpdate, dataParams);
        } else {
            let sqlCreate = `INSERT INTO budget_analysis 
                (contract_number, sum_turnover, revenue_ratio, captital_cost_ratio, cost_rent,
                employee_salary, hkd_tax, living_cost, number_of_depend, depend_living_cost, house_rent, debts_payable, 
                creadit_card_balance, current_loan_outstanding_t_1, current_loan_outstanding_t_2, 
                current_loan_outstanding_t_3, plus_txt, minus_txt, suspect_txt, other_txt, revenue_growth_rate, min_capital, month_t)
                VALUES 
                ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23);`;
            dataParams.unshift(contractNumber);
            await poolWrite.query(sqlCreate, dataParams);
        }

        return res.status(200).json({
            code: 0,
            msg: "SUCCESS",
        });
    } catch (error) {
        res.status(500).json({
            code: -1,
            message: error.message,
        });
    }
};

module.exports = {
    getBudgetAnalysis,
    updateBudgetAnalysis,
};
