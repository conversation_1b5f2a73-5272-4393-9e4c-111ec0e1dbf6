const masterdataService = require("../utils/masterdataService")

async function formatError(data,config,kovCL=false) {
	let formatRs = []

	const partnerCode = data.partnerCode
	const requestId = data.requestId
	const customerName = data.customerName
	const dateOfBirth = data.dateOfBirth
	const issueDate = data.issueDate
	const idNumber = data.identityCardId
	const phoneNumber = data.phoneNumber
	const temProvince = data.temProvince
	const issuePlace = data.issuePlace
	const fundingFromEc = data.fundingFromEc
	const capitalNeed = data.capitalNeed
	const selfFinancing = data.selfFinancing
	const otherCapital = data.otherCapital
	const otherIdCard = data.otherIdentityCardId
	// const otherIssueDate = data.otherIssueDate
	const otherIssuePlace = data.otherIssuePlace
	let validDataType;
	if(kovCL){
		validDataType = formatDataType(data,true)
	}
	else{
		validDataType = formatDataType(data)
	}
	if (validDataType.length !== 0) {
		return validDataType
	}

	const isValidRs = isValidRequestID(partnerCode,requestId)
	const validName = validateName(customerName)
	const validDobFormat = dateTime("dateOfBirth",dateOfBirth)
	const validIssueFormat = dateTime("issueDate",issueDate)
	const validDob = validDateTime("dateOfBirth",dateOfBirth)
	const validIssue1 = validDateTime("issueDate",issueDate)
	const validIssue3 = validateIssueDate(issueDate,dateOfBirth)
	const validIdNumber = validateCardId(idNumber)
	const validPhoneNumber = validatePhoneNumber(phoneNumber)
	const validIssuePlace = await validateCodeField("issuePlace",issuePlace,"ISSUE_PLACE_VN",config)
	const validTempProvince = await validateCodeField("temProvince",temProvince,"PROVINCE",config)
	let validFunding=[]
	if(capitalNeed != undefined && capitalNeed !== '' && selfFinancing != undefined && selfFinancing !== '' &&  otherCapital != undefined && otherCapital !== ''  && fundingFromEc != undefined && fundingFromEc !== '') {
		validFunding = validateFunding(capitalNeed,selfFinancing,otherCapital,fundingFromEc)
	}
	
	let validOtherId = []
	let validOtherIssueDate = []
	let validOtherIssuePlace = []
	if(otherIdCard != undefined && otherIdCard != '') {
		validOtherId = validateCardId(otherIdCard)
	}
	if(otherIssuePlace != undefined && otherIssuePlace != '') {
		validOtherIssuePlace = await validateCodeField("otherIssuePlace",otherIssuePlace,"ISSUE_PLACE_VN",config)
	}
	/* remove validate other ID
	if(otherIssueDate != undefined && otherIssueDate != '') {
		validOtherIssueDate = dateTime("otherIssueDate",otherIssueDate)
	}*/


	formatRs = formatRs.concat(isValidRs).concat(validIdNumber).concat(validName).concat(validDobFormat).concat(validIssueFormat).concat(validDob).concat(validIssue1).concat(validDataType).concat(validIssue3).concat(validPhoneNumber).concat(validTempProvince).concat(validIssuePlace).concat(validFunding).concat(validOtherId).concat(validOtherIssueDate).concat(validOtherIssuePlace)
	return formatRs
}

//1 request id
function isValidRequestID(partnerCode,requestId) {
	try {

		let errorMessage = {
			"code" : "FORMAT",
			"field" : "requestId",
			"message" : "requestId is wrong format"
		}

		let timestamp = requestId.split(partnerCode)[1]
		let valid = (new Date(parseInt(timestamp))).getTime() > 0;
		if(valid) {
			return []
		}
		return [errorMessage]
	} 
	catch (error ) {
		return [errorMessage]
	}
}

function validateName(customerName) {

	let cusNameFormatError = []
	let errorMessage = {
			"code" : "FORMAT",
			"field" : "customerName",
			"message" : "Must not contain multiple consecutive spaces or (’) "
		}

	let splitName = customerName.split(" ")
	if(splitName.includes("")) {
		cusNameFormatError.push(errorMessage)
	}

	let splitName2 = customerName.split("'")
	if(splitName2.includes("")) {
		cusNameFormatError.push(errorMessage)
	}	

	if(customerName[0] == "'" || customerName[customerName.length - 1] == "'" ) {
		cusNameFormatError.push({
			"code" : "FORMAT",
			"field" : "customerName",
			"message" : "Must Not Contains Character (') At the End or Beginning"
		})
	}


	
	// const vnChars = "àáãạảăắằẳẵặâấầẩẫậèéẹẻẽêềếểễệđìíĩỉịòóõọỏôốồổỗộơớờởỡợùúũụủưứừửữựỳỵỷỹýÀÁÃẠẢĂẮẰẲẴẶÂẤẦẨẪẬÈÉẸẺẼÊỀẾỂỄỆĐÌÍĨỈỊÒÓÕỌỎÔỐỒỔỖỘƠỚỜỞỠỢÙÚŨỤỦƯỨỪỬỮỰỲỴỶỸÝaAbBcCdDeEgGhHiIkKlLmMnNoOpPqQrRsStTuUvVxXyY'"
	// const vnCharList = vnChars.split('')
	// const nameCharList = customerName.split('').filter(char => char !== ' ').filter((value,index,arr) => arr.indexOf(value) === index );
	// const nonVieChar = nameCharList.filter(char => !vnCharList.includes(char))
	
	/* 
	if(nonVieChar.length > 0) {
	//if(validNameRs) {
		cusNameFormatError.push({
			"code" : "FORMAT",
			"field" : "customerName",
			"message" : "Must only contains vietnamese characters or character (')"
		})	
	}*/
	return cusNameFormatError
}


function dateTime(fieldName,fieldValue) {

	let errorMessage = {
			"code" : "FORMAT",
			"field" : fieldName,
			"message" : "Must be in format 'yyyy-mm-dd'"
		}

	let valueList = fieldValue.split("-")
	if(valueList.length !== 3 || valueList[0].length !== 4 || valueList[1].length !== 2 || valueList[2].length !== 2) {
		return [errorMessage]
	}
			
	return []
}

function validDateTime(fieldName,fieldValue) {

	let errorMessage = {
			"code" : "FORMAT",
			"field" : fieldName,
			"message" : "Does not exist"
		}

	let valueList = fieldValue.split("-")
	if(parseInt(valueList[1])<1  || parseInt(valueList[1]) > 12 || parseInt(valueList[2]) < 1 || parseInt(valueList[2]) > 31) {
		return [errorMessage]
	}
			
	return []
}

function validateCardId(idNumber) {
	const regrex1 = /^\d{9}$/
	const regrex2 = /^\d{12}$/
	idNumber = '' + idNumber

	if(regrex1.test(idNumber) || regrex2.test(idNumber)) {
		return {}
	}

	return {
		"code" : "FORMAT",
		"field" : "identityCardId",
		"message" : 'Must be in pattern ^(\\d{9}|\\d{12})$'
	}
}

function validatePhoneNumber(phoneNumber) {
	const regrex1 = /^\d{10}$/
	phoneNumber = '' + phoneNumber

	if(regrex1.test(phoneNumber)) {
		return {}
	}
	return {
		"code" : "FORMAT",
		"field" : "phoneNumber",
		"message" : 'Must be in pattern ^(\\d{10}$'
	}
}


function formatDataType(body,kovCL=false) {
	const allField = Object.keys(body)
	let numberTypeField=[],arrayField=[]
	if(kovCL){
		numberTypeField = ['transaction12m','turnover12m','numberOfDependents','monthlyExpenses','timeDuration','monthlyIncome','fundingFromEc'] 
		arrayField = ['turnover','transaction']
	}
	else {
		numberTypeField = ['transaction12m','turnover12m','numberOfDependents','monthlyExpenses','loanAmount','capitalNeed','activeMonth','timeDuration','otherCapital','fundingFromEc','selfFinancing','numberBusinessAsset','timeUsingAsset'] 
		arrayField = ['turnover','transaction','branchAddress']
	}
	
	if(body['accountTrading'] != undefined) {
		arrayField.push('accountTrading')
	}
	let stringField = allField.filter(x => !numberTypeField.includes(x)).filter(x => !arrayField.includes(x))
	
	let formatList = []
	numberTypeField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'number' || body[fieldName] < 0) {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be number and greater than equal 0"
			})
		}
	})

	arrayField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'object') {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be array"
			})
		}
	})

	stringField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'string') {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be string"
			})
		}
	})

	body.turnover.forEach(turnMonth => {
		if(typeof(turnMonth.month) !== 'number') {
			formatList.push({
				code : "FORMAT",
				field : "turnover.month",
				message : "turnover.month must be number"
			})	
		}
		if(typeof(turnMonth.amount) !== 'number') {
			formatList.push({
				code : "FORMAT",
				field : "turnover.month",
				message : "turnover.amount must be number"
			})	
		}
	})

	body.transaction.forEach(transMonth => {
		if(typeof(transMonth.month) !== 'number' || transMonth.month < 0) {
			formatList.push({
				code : "FORMAT",
				field : "transaction.month",
				message : "transaction.month must be number and greater than or equal 0"
			})	
		}
		if(typeof(transMonth.amount) !== 'number') {
			formatList.push({
				code : "FORMAT",
				field : "transaction.month",
				message : "transaction.amount must be number"
			})	
		}
	})
	if(body.branchAddress != undefined){
		body.branchAddress.forEach(branchAdr => {
			const column = ['name','province','district','ward','detail']
			column.forEach(branchAdrDetail => {
				if(typeof(branchAdr[branchAdrDetail]) != 'string' ) {
					formatList.push({
						code : "FORMAT",
						field : "branchAddress." + branchAdrDetail,
						message : "branchAddress." + branchAdrDetail + "must be number"
					})
				}
			})
		})
	}
	
	return formatList
}

function validateIssueDate(issueDateString,dobString) {
	const issueDate = new Date(issueDateString)
	const currentDate = new Date()
	// const dobDate = new Date(dobString)

	const issueYear = issueDate.getFullYear()
	const currentYear = currentDate.getFullYear()

	let formatErrorList = []

	if(issueYear < 1900 || issueYear > currentYear) {
		formatErrorList.push({
			code : "FORMAT",
			field : "issueDate",
			message : "Must be in between year 1900 and yesterday"
		})
	}

	if(currentYear - issueYear > 15) {
		formatErrorList.push({
			code : "FORMAT",
			field : "issueDate",
			message : "Must be at most 15 years before current"
		})	
	}

	if(Date.parse(issueDateString) < Date.parse(dobString)){
		formatErrorList.push({
			code : "FORMAT",
			field : "issueDate",
			message : "Must Be After dateofbirth"
		})	
	}

	return formatErrorList
}

async function validateCodeField(fieldName,code,type,config) {
	if(code.toString().indexOf(' ') >= 0) {
		return {
			"code" : "FORMAT",
			"field" : fieldName,
			"message" :''+ fieldName+ ' is invalid'
		}
	}

	const data = await masterdataService.getValueCode_v2(config,code,type)
	if(data) {
		return []
	}
	return {
		"code" : "FORMAT",
		"field" : fieldName,
		"message" :''+ fieldName+ ' is invalid'
	}
}

function validateFunding(capitalNeed,selfFinancing,otherCapital,fundingFromEc) {
	if(fundingFromEc == (capitalNeed - selfFinancing - otherCapital)) {
		return {}
	}

	return {
		"code" : "FORMAT",
		"field" : "fundingFromEc",
		"message" :'fundingFromEc must be equal capitalNeed - selfFincancing - otherCapital'
	}
}


module.exports = {
	formatError
}
