const inputChecker = require("./a1-error-handler")
const productService = require("../utils/productService")

const getCount = async (req) => {
	const sql = "SELECT count(contract_number) FROM loan_contract WHERE contract_number LIKE $1"
	const date = '02' + getDateString() + '____'
	const result = await req.poolRead.query(sql, [date])
	if (result.rows) {
		return result.rows[0].count
	}
	else {
		return 0
	}
}

const getNextvalSequense = async (req) => {
	const sql = "SELECT nextval('contract_generate_number')";
	const result = await req.poolWrite.query(sql)
	if (result.rows) {
		return result.rows[0].nextval
	}
	else {
		return 0
	}
}

async function generateContractNumber_v2(req) {
	const count = await getNextvalSequense(req)
	let id = count
	return id;
}

async function generateContractNumber(req,prefix='02') {
	const count = await getCount(req)
	let id 
	if (count <= 9999) {
		id = prefix + getDateString() + ('000' + (parseInt(count) + 1)).slice(-4)
	}
	else {
		id = prefix + getDateString() + (parseInt(count) + 1)
	}
	return id
}

async function validateInput(data,config,kovCL=false) {
	let missing; 
	if(kovCL){
		missing = checkMissing(data,true)
	}
	else{
		missing = checkMissing(data)
	}
	if(missing.length !== 0 ) {
		return missing
	}
	let format;
	if(kovCL){
		format = await inputChecker.formatError(data,config,true)
	}
	else{
		format = await inputChecker.formatError(data,config)
	}
	 
	return missing.concat(format)
} 

async function validateInputV2(body) {
	return null
}

function checkMissing(data,kovCL=false) {
	let mandatoryField = [];
	// console.log('kovCL',kovCL)
	if(kovCL){
		mandatoryField = [
			"requestId",
			"channel",
			"partnerCode",
			"customerName",
			"customerName",
			"gender",
			"dateOfBirth",
			"identityCardId",
			"issueDate",
			"issuePlace",
			"phoneNumber",
			"numberOfDependents",
			"temProvince",
			"monthlyExpenses",
			"businessLegal",
			"turnover12m",
			"turnover",
			"transaction12m",
			"transaction",
			"timeDuration",
			"fundingFromEc"
			// "merchantContractNumber",
			// "representatiMerchantContract",
			// "startDateOnMerchant",
			// "endDateOnMerchant",
			// "latestTaxPayment",
		];
	}
	else{
		mandatoryField = [
			"requestId",
			"channel",
			"partnerCode",
			"customerName",
			"customerName",
			"gender",
			"dateOfBirth",
			"identityCardId",
			"issueDate",
			"issuePlace",
			"phoneNumber",
			"numberOfDependents",
			"temProvince",
			"monthlyExpenses",
			"productCode",
			"productCode",
			"typeTrading",
			"businessLegal",
			"turnover12m",
			"turnover",
			"transaction12m",
			"transaction",
			"timeDuration",
			"activeMonth",
			"sectorIndustry",
			"businessType",
			"timeUsingAsset",
			"branchAddress",
			// "merchantContractNumber",
			// "representatiMerchantContract",
			// "startDateOnMerchant",
			// "endDateOnMerchant",
			// "latestTaxPayment",
		];
	}

	let missingList = []
	mandatoryField.forEach(field => {
		if(data[field] === undefined || data[field] === '') {
			const missingObject = {
				"code" : "MISSING",
				"field" : field,
				"message" : field + " is missing."
			}
			missingList.push(missingObject)
		}
	})
	
	if(data.turnover !== undefined) {
		data.turnover.forEach(turnMonth => {
			if(turnMonth.month === undefined || turnMonth.month === '') {
				const missingObject = {
					"code" : "MISSING",
					"field" : "turnover.month",
					"message" : "turnover.month" + " is missing."
				}
				missingList.push(missingObject)	
			}
			if(turnMonth.amount === undefined || turnMonth.amount === '') {
				const missingObject = {
					"code" : "MISSING",
					"field" : "turnover.amount",
					"message" : "turnover.amout" + " is missing."
				}
				missingList.push(missingObject)
			}
		})
	}

	if(data.transaction !== undefined) {
		data.transaction.forEach(transMonth => {
			if(transMonth.month === undefined) {
				const missingObject = {
					"code" : "MISSING",
					"field" : "transaction.month",
					"message" : "transaction.month" + " is missing."
				}
				missingList.push(missingObject)	
			}
			if(transMonth.amount === undefined) {
				const missingObject = {
					"code" : "MISSING",
					"field" : "transaction.amount",
					"message" : "transaction.amount" + " is missing."
				}
				missingList.push(missingObject)	
			}
		})
	}

	if(data.branchAddress !== undefined) {
		data.branchAddress.forEach(branchAdr => {
			const column = ['name','province','district','ward','detail']
			column.forEach(branchAdrDetail => {
				if(branchAdr[branchAdrDetail] === undefined ) {
					missingList.push({
						code : "MISSING",
						field : "branchAddress." + branchAdrDetail,
						message : "branchAddress." + branchAdrDetail + "is missing"
					})
				}
			})
		})
	}
	return missingList
}

function checkFormat(data) {
	let formatData = inputChecker.formatError(data)
	return formatData
}

const getDateString = () => {
    const d = new Date()
    let month = d.getMonth() + 1
    let date = d.getDate()
    month = month < 10 ? '0' + month : month
    date = date < 10 ? '0' + date : date
    return '' + d.getFullYear().toString().slice(2, 4) + month + date 
}

async function validateLoanAmount(loanAmount,prouductCode,config) {
	const productInfo = await productService.getProductLimitInfo(config,prouductCode)
	const minAmount = parseInt(productInfo.productVar[0].minAmt)
	const maxAmount = parseInt(productInfo.productVar[0].maxAmt)
	if(parseInt(loanAmount) < minAmount || parseInt(loanAmount) > maxAmount) {
		return false
	}
	return true
}

module.exports = {
	generateContractNumber,
	generateContractNumber_v2,
	validateInput,
	validateInputV2,
	validateLoanAmount
}
