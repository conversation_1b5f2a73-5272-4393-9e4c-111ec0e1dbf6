{"requestId": "request_id", "contractNumber": "contract_number", "channel": "channel", "partnerCode": "partner_code", "customerName": "cust_full_name", "gender": "gender", "dateOfBirth": "birth_date", "identityCardId": "id_number", "issueDate": "id_issue_dt", "issuePlace": "id_issue_place", "phoneNumber": "phone_number1", "loanAmount": "request_amt", "productCode": "product_code", "tenor": "request_tenor", "rate": "request_int_rate", "typeTrading": "type_trading", "accountTrading": "", "businessLegal": "business_legal", "temProvince": "province_cur", "numberOfDependents": "num_of_dependants", "monthlyExpenses": "m_household_expenses", "turnover12m": "turnover_12m", "turnover": "null", "transaction12m": "transaction12m", "transaction": "", "timeDuration": "time_duration", "activeMonth": "active_month", "sectorIndustry": "sector_industry", "businessType": "business_type", "capitalNeed": "capital_need", "selfFinancing": "self_financing", "otherCapital": "other_capital", "fundingFromEc": "funding_from_ec", "repaymentSources": "repayment_sources", "fixedAsset": "fixed_asset", "planAsset": "plan_asset", "numberBusinessAsset": "number_business_asset", "timeUsingAsset": "time_using_asset", "otherIdentityCardId": "other_id_number", "otherIssueDate": "other_issue_date", "otherIssuePlace": "other_issue_place", "merchantContractNumber": "merchant_contract_number", "representatiMerchantContract": "representati_merchant_contract", "startDateOnMerchant": "start_date_on_merchant", "endDateOnMerchant": "end_date_on_merchant", "latestTaxPayment": "latest_tax_payment", "businessDuration": "bussiness_duration", "contractType": "contract_type", "lms_type": "lms_type"}