let currentTaskCode = 'MC_CRE_A1';
let common = require("../utils/common")
const utils = require("../utils/helper")
const fileReader = require('../utils/read_file.js')
const inputValidator = require("./a1-input-validator.js")
const {saveRequest} = require("../utils/loggingService")
const statusCode = 'KH01'
const {baseCheckEligible, baseCheckEligibleKOV} = require("../services/de-service")
const {baseCheckS37} = require("../services/de-service")
const {insertLoanContract} = require("../repositories/loan-contract-repo")
const {STATUS,CALLBACK_STAUS,WORKFLOW_STAGE} = require("../const/caseStatus")
const loggingService = require("../utils/loggingService")
const loggingRepo = require("../repositories/logging-repo")
const {convertBody} = require("../utils/converter/convert");
const {request_type,CONTRACT_TYPE, PARTNER_CODE} = require("../const/definition")
const offerRepo = require("../repositories/offer")
const turnoverRepo = require("../repositories/turnover-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const codService = require("../services/cod-service");

let paramMapper;
fileReader.readJson('./a1_application/a1-params-field.json')
.then(result => paramMapper = result)
.catch(err => common.log(err,"ERROR"))

function generate_query(json_data,json_param_mapper) {
	let basicQuery = "insert into loan_contract(" 
	let fieldQuery = "";
	let paramQuery = " values(";
	let params = []
	let paramIdx = 1
	for (let key of Object.keys(json_param_mapper)) {
		if(json_param_mapper[key] !== 'null' && json_param_mapper[key] !== "" && json_data[key] !== '' && json_data[key] != undefined) {
			fieldQuery += json_param_mapper[key] + ",";
			paramQuery += "$" + paramIdx + ","
			params.push(json_data[key]);
			paramIdx += 1
		}
	}
	fieldQuery = fieldQuery.slice(0,-1) + ")"
	paramQuery = paramQuery.slice(0,-1) + ")"
	return [basicQuery+fieldQuery+paramQuery,params]
}

async function a1_recieve(req,res) {
	try{
		
		const poolWrite = req.poolWrite;
		const config = req.config
		let response
		let validatedResult = []
		if(req.body.partnerCode == PARTNER_CODE.KOV) {
			validatedResult = await inputValidator.validateInput(req.body,config)
		}
		if(validatedResult.length != 0) {
			response = {
				"statusCode" : "200",
				"body" : {
					"code" : "INVALID REQUEST",
					"message" : "Request invalid",
					"errors" : validatedResult
				}
			}
			return res.status(200).json(response)
		} 
		else {
			const partnerCode = req.body.partnerCode

			const contractNumber = await inputValidator.generateContractNumber_v2(req)
			const branchAddressList = req.body.branchAddress
			const accountTrading = req.body.accountTrading

			req.body.contractNumber = contractNumber
			req.body.contractType = CONTRACT_TYPE.CREDIT_LINE
			req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE
			const query = generate_query(req.body,paramMapper)
			
			await Promise.all([poolWrite.query(query[0],query[1]),
							turnoverRepo.saveTurnOrTrans(poolWrite,req.body.turnover,"turnover",contractNumber),
							turnoverRepo.saveTurnOrTrans(poolWrite,req.body.transaction,"transaction",contractNumber),
							offerRepo.createLoanMainScore(contractNumber),
							offerRepo.createKOVLoanScore(contractNumber)],
							loanContractRepo.saveBranchAddress(contractNumber,branchAddressList))
			await turnoverRepo.duplicateRealTurnoverKov(contractNumber)
			
			utils.saveStatus(poolWrite,contractNumber,statusCode)
			if(accountTrading != '') {
				loanContractRepo.saveLoanAcountTrading(contractNumber,accountTrading)
			}
			
			let body = {}
			body.current_task_code = currentTaskCode
			body.data = req.body
			body.data.contractNumber = contractNumber
			
			let lb = req.config.basic.wfMcCredit[req.config.env];

			const envType = req.config.data.env.wf_uri;
			let wf_lb = lb
			if(envType=='local') {
				wf_lb = "http://localhost:1001"
			}
			
			let workflowUri = req.config.data.workflow.uri;
			let workflowUrl = wf_lb + workflowUri

			common.postAPI(workflowUrl,body).then()
			.catch(err =>{
				console.log(err)
				common.log("CALL WORKFLOW : error","ERROR")
			})
			response = {
				"statusCode" : "200",
				"body" : {
					"code" : "RECEIVE",
					"message" : "The application is received",
					"data": {
					    "conractNumber": contractNumber,
					    "partnerCode": partnerCode
				    }

				}
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			res.status(200).json(response)

		}
	}
	catch (error) {
		console.log(error)
		res.status(500).json({
			"msg" : "service " + currentTaskCode + " error",
			"code" : -1

		})
	}
}

async function basicInfo(req,res) {
	try{
		let responseBody;
		const requestType = request_type.basic
		
		let body = convertBody(req.body,requestType,global.convertCache)

		const contractNumber = await inputValidator.generateContractNumber_v2(req)
		body.contract_number = contractNumber

		if(utils.isNullOrEmpty(body.bank_account_owner)) {
			body.bank_account_owner = utils.nonAccentVietnamese(body.cust_full_name)
		}

		const insertLoanRs = await insertLoanContract(body)
		
		if(!insertLoanRs) {
			responseBody = {
				code : -1,
				msg : "Internal Server Error"
			}
			await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contractNumber,body.request_id,body.partner_code)
			return res.status(500).json(responseBody)
		}

		let finalStatus = STATUS.NOT_ELIGIBLE
		let code = 0
		let isEligible = await baseCheckEligible(body);
        if(isEligible) {
			let isNotBadDebt = await baseCheckS37(body)
		
            if(isNotBadDebt) {
                finalStatus = STATUS.ELIGIBLE;
                code = 1;
            }
        }
		
		if(finalStatus != STATUS.ELIGIBLE) {
			responseBody = {
				code,
				msg : "NOT ELIGIBLE"
			}	
		}
		else {
			responseBody = {
				code,
				msg : "ELIGIBLE",
				contractNumber
			}
		}
		await Promise.all([loggingService.saveRequestV2(poolWrite,req.body,responseBody,contractNumber,body.request_id,body.partner_code),
			utils.saveStatus(poolWrite,contractNumber,finalStatus),
			offerRepo.createLoanMainScore(contractNumber)])
		return res.status(200).json(responseBody)
	}
	catch(err) {
		console.log(err)
		return res.status(500).json({
			code : -1,
			msg : "Service error"
		})
	}
}

async function basicInfoCOD(req,res) {
	try{
		let responseBody;
		const requestType = request_type.basic
		
		let body = convertBody(req.body,requestType,global.convertCache)
		const contractNumber = await inputValidator.generateContractNumber_v2(req)
		body.contract_number = contractNumber
		body.contract_type = CONTRACT_TYPE.CASH_LOAN
		body.lms_type = CONTRACT_TYPE.CASH_LOAN
		if(utils.isNullOrEmpty(body.bank_account_owner)) {
			body.bank_account_owner = utils.nonAccentVietnamese(body.cust_full_name)
		}

		const insertLoanRs = await insertLoanContract(body)
		
		if(!insertLoanRs) {
			responseBody = {
				code : -1,
				msg : "Internal Server Error"
			}
			await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contractNumber,body.request_id,body.partner_code)
			return res.status(500).json(responseBody)
		}
		let finalStatus = STATUS.NOT_ELIGIBLE
		let callbackStatus = CALLBACK_STAUS.REJECTED
		let code = 0
	
		let isVerified = await codService.checkPhoneCode(body)
		
		if(isVerified == 1) {
			await loggingRepo.saveWorkflow("CHECK_PHONE_COD","VALID_PHONE_COD",contractNumber)
			let isEligible = await baseCheckEligible(body);
			if(isEligible) {

				await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_ELIGIBLE,STATUS.ELIGIBLE,contractNumber)
				let isNotBadDebt = await baseCheckS37(body)
			
				if(isNotBadDebt){
					await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_CIC_S37,STATUS.ELIGIBLE,contractNumber)
					finalStatus = STATUS.ELIGIBLE;
					callbackStatus= CALLBACK_STAUS.CONTINUEA2
					code = 1;
				}
				else {
					await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_CIC_S37,STATUS.NOT_ELIGIBLE,contractNumber)
				}
			}
		}

		else if(isVerified == -1) {
			await loggingRepo.saveWorkflow("CHECK_PHONE_COD","INVALID_PHONE_COD",contractNumber)
			finalStatus = "INVALID_PHONE_COD"
			code = 2
		}
		else {
			await loggingRepo.saveWorkflow("CHECK_PHONE_COD",STATUS.NOT_ELIGIBLE,contractNumber)
			finalStatus = STATUS.NOT_ELIGIBLE
			code = 0
		}
		
		responseBody = {
			code,
			msg : finalStatus,
			contractNumber
		}
		await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contractNumber,body.request_id,body.partner_code)
		await utils.saveStatus(poolWrite,contractNumber,finalStatus)
		codService.updateFieldCOD(contractNumber,req.body,callbackStatus,req.body.customerID)
		return res.status(200).json(responseBody)
	}
	catch(err) {
		console.log(err)
		return res.status(500).json({
			code : -1,
			msg : "Service error"
		})
	}
}
async function basicInfoKOV(req,res) {
	try{
		let body = req.body
		let responseBody;
		let finalStatus = STATUS.NOT_ELIGIBLE
		let code = 0

		let isEligible = await baseCheckEligibleKOV(body);
		if(isEligible){
			finalStatus = STATUS.ELIGIBLE;
			code = 1;
		}
		responseBody = {
			code,
			msg : finalStatus
		}
		return res.status(200).json(responseBody)
	}
	catch(err) {
		console.log(err)
		return res.status(500).json({
			code : -1,
			msg : "Service error"
		})
	}
}

module.exports = {
	a1_recieve,
	basicInfo,
	basicInfoCOD,
	basicInfoKOV
}