const common = require("../utils/common")
const utils = require("../utils/helper")
const callbackService = require("../utils/callbackService")
const loggingService = require("../utils/loggingService")
const statusCode = 'KH05'

async function a1GateWay(req,res) {
	try {

		const config = req.config
		const poolWrite = req.poolWrite
		const poolRead = req.poolRead
		const contractNumber = req.body.data.contractNumber
		const partnerCode = await utils.getPartnerCode(poolRead,contractNumber)
		const currentTimestamp = new Date().getTime()
		const requestID = partnerCode + currentTimestamp
		const callbackBody = {
			"requestId" : requestID,
			"contractNumber" : contractNumber,
			"code" : "CONTINUEA2",
			targetType : "credit-creation",
			"message": "The application is passed A1 check, the system is ready for A2 check",
			data : {
				contractNumber : contractNumber,
				partnerCode : partnerCode,
				customerName : req.body.data.customerName,
				identityCardId : req.body.data.identityCardId,
				phoneNumber : req.body.data.phoneNumber,
				productCode : req.body.data.productCode,
				loanStaus : statusCode
			}
		}
		
		callbackService.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)

		utils.saveStatus(poolWrite,contractNumber,statusCode)
		//await loggingService.saveCaseStatus(poolWrite,statusCode,contractNumber,'system')
		return res.status(200).json({
			"msg" : "finish a1",
			"code" : 1
		})
	}
	catch (error) {
		console.log(error)
		return res.status(500).json({
			"msg" : "service error",
			"code" : 1
		})
	}
}

module.exports = {
	a1GateWay
}

