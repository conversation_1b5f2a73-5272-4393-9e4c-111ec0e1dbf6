const masterdataService = require("../utils/masterdataService")

async function formatError(data,config) {
	let formatRs = []

	const partnerCode = data.partnerCode
	const requestId = data.requestId
	const marriedStatus = data.marriedStatus
	const relation1 = data.relation1
	const relation2 = data.relation2
	const temDistrict = data.temDistrict
	const temWard = data.temWard
	const permanentProvince = data.permanentProvince
	const permanentDistrict = data.permanentDistrict
	const permanentWard = data.permanentWard
	const jobType = data.jobType
	const houseType = data.houseType
	const incomeMethod = data.incomeMethod
	const incomeFrequency = data.incomeFrequency
	const workPlaceProvince = data.workPlaceProvince
	const workPlaceDistrict = data.workPlaceDistrict
	const  workPlaceWard = data.workPlaceWard
	const loanPurpose = data.loanPurpose
	const marriageMateId = data.marriageMateId

	const validDataType = formatDataType(data)
	if (validDataType.length !== 0) {
		return validDataType
	}
	const isValidRs = isValidRequestID(partnerCode,requestId)
	const validTempDistrict = await validateCodeField("temDistrict",temDistrict,"DISTRICT",config)
	const validTempWard = await validateCodeField("temWard",temWard,"WARD",config)
	const validPerProvince = await validateCodeField("permanentProvince",permanentProvince,"PROVINCE",config)
	const validPerDistrict = await validateCodeField("temDistrict",permanentDistrict,"DISTRICT",config)
	const validPerWard = await validateCodeField("temWard",permanentWard,"WARD",config)
	const validMarriedStatus = await validateCodeField("marriedStatus",marriedStatus,"MARRIED_STATUS",config)
	const validRelation1 = await validateCodeField("relation1",relation1,"FONCTION_INTERLOCUTEUR",config)
	const validRelation2 = await validateCodeField("relation2",relation2,"FONCTION_INTERLOCUTEUR",config)
	const validLoanPurpose = await validateCodeField("loanPurpose",loanPurpose,"LOAN_PURPOSE",config)
	const validJobType = (jobType != undefined && workPlaceWard !=='') ? await validateCodeField("jobType",jobType,"JOB_TYPE",config) : []
	const validHouseType = (houseType != undefined && workPlaceWard !=='') ? await validateCodeField("houseType",houseType,"HABITAT",config) : []
	const validIncomeMethod = (incomeMethod != undefined && workPlaceWard !=='') ? await validateCodeField("incomeMethod",incomeMethod,"SALARY_METHOD",config) : []
	const validIncomeFrequency = (incomeFrequency != undefined && workPlaceWard !=='') ? await validateCodeField("incomeFrequency",incomeFrequency,"SALARY_FREQUENCY",config) : []
	const validWorkPlaceProvince = (workPlaceProvince != undefined && workPlaceWard !=='') ? await validateCodeField("workPlaceProvince",workPlaceProvince,"PROVINCE",config) : []
	const validWorkPlaceDistrict = (workPlaceDistrict != undefined && workPlaceWard !=='') ? await validateCodeField("workPlaceDistrict",workPlaceDistrict,"DISTRICT",config) : []
	const validWorkPlaceWard = (workPlaceWard != undefined && workPlaceWard !=='') ? await validateCodeField("workPlaceWard",workPlaceWard,"WARD",config) : []
	const validMarriageMateId = (marriageMateId != undefined && workPlaceWard !=='') ? validateCardId(marriageMateId) : []

	formatRs = formatRs.concat(isValidRs).concat(validMarriedStatus).concat(validRelation1).concat(validRelation2).concat(validTempDistrict).concat(validTempWard).concat(validPerProvince).concat(validPerDistrict).concat(validPerWard).concat(validLoanPurpose)
	formatRs = formatRs.concat(validJobType).concat(validHouseType).concat(validIncomeMethod).concat(validIncomeFrequency).concat(validWorkPlaceProvince).concat(validWorkPlaceDistrict).concat(validWorkPlaceWard).concat(validMarriageMateId)
	return formatRs
}

function validateCardId(idNumber) {
	const regrex1 = /^\d{9}$/
	const regrex2 = /^\d{12}$/
	idNumber = '' + idNumber

	if(regrex1.test(idNumber) || regrex2.test(idNumber)) {
		return {}
	}

	return {
		"code" : "FORMAT",
		"field" : "identityCardId",
		"message" : 'Must be in pattern ^(\\d{9}|\\d{12})$'
	}
}

//1 request id
function isValidRequestID(partnerCode,requestId) {
	try {

		let errorMessage = {
			"code" : "FORMAT",
			"field" : "requestId",
			"message" : "requestId is wrong format"
		}

		let timestamp = requestId.split(partnerCode)[1]
		let valid = (new Date(parseInt(timestamp))).getTime() > 0;
		if(valid) {
			return []
		}
		return [errorMessage]
	} 
	catch (error ) {
		return [errorMessage]
	}
}

function dateTime(fieldName,fieldValue) {

	let errorMessage = {
			"code" : "FORMAT",
			"field" : fieldName,
			"message" : "Must be in format 'yyyy-mm-dd'"
		}

	let valueList = fieldValue.split("-")
	if(valueList.length !== 3 || valueList[0].length !== 4 || valueList[1].length !== 2 || valueList[2].length !== 2) {
		return [errorMessage]
	}
			
	return []
}

function validDateTime(fieldName,fieldValue) {

	let errorMessage = {
			"code" : "FORMAT",
			"field" : fieldName,
			"message" : "Does not exist"
		}

	let valueList = fieldValue.split("-")
	if(parseInt(valueList[1])<1  || parseInt(valueList[1]) > 12 || parseInt(valueList[2]) < 1 || parseInt(valueList[2]) > 31) {
		return [errorMessage]
	}
			
	return []
}



function formatDataType(body) {
	// const allField = Object.keys(body)
	const arrayField = ['listDocCollecting'] 
	const notMandatoryNumberTypeField = ['yearsOfStay','monthlyIncome','otherIncome','incomeReceivingDate','employmentContractYearFrom','employmentContractYearTo','taxId']
	const mandatoryStringField = ['requestId','contractNumber','partnerCode','employmentType','temDistrict','temWard','temAddress','temAddress','permanentProvince','permanentDistrict','permanentWard','permanentAddress','marriedStatus','loanPurpose','relation1','relation1Name','relation1PhoneNumber','relation2','relation2Name','relation2PhoneNumber']
	const notMandatoryStringField = ['jobType','marriageMateId','houseType','incomeMethod','incomeFrequency','workPlaceName','workPlaceProvince','workPlaceDistrict','workPlaceWard','workPlaceAddress','workPlacePhone','employmentContractType','employmentContractTerm','otherContact','detailOtherContact','email']
	let formatList = []


	notMandatoryNumberTypeField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'number' && body[fieldName] !== '' && body[fieldName] != undefined) {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be number"
			})
		}
	})

	arrayField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'object') {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be array"
			})
		}
	})

	mandatoryStringField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'string') {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be string"
			})
		}
	})

	notMandatoryStringField.forEach(fieldName => {
		if(typeof(body[fieldName]) !== 'string' && body[fieldName] != undefined && body[fieldName] != '') {
			formatList.push({
				code : "FORMAT",
				field : fieldName,
				message : fieldName + " must be string"
			})
		}
	})

	body.listDocCollecting.forEach(doc => {
		if(typeof(doc.docName) !== 'string') {
			formatList.push({
				code : "FORMAT",
				field : "listDocCollecting.name",
				message : "listDocCollecting.docId must be string"
			})	
		}
		if(typeof(doc.docId) !== 'string') {
			formatList.push({
				code : "FORMAT",
				field : "listDocCollecting.monnameth",
				message : "listDocCollecting.docId must be string"
			})	
		}
	})
	return formatList
}

function validateIssueDate(issueDateString,dobString) {
	const issueDate = new Date(issueDateString)
	const currentDate = new Date()
	// const dobDate = new Date(dobString)

	const issueYear = issueDate.getFullYear()
	const currentYear = currentDate.getFullYear()

	let formatErrorList = []

	if(issueYear < 1900 || issueYear > currentYear) {
		formatErrorList.push({
			code : "FORMAT",
			field : "issueDate",
			message : "Must be in between year 1900 and yesterday"
		})
	}

	if(currentYear - issueYear > 15) {
		formatErrorList.push({
			code : "FORMAT",
			field : "issueDate",
			message : "Must be at most 15 years before current"
		})	
	}

	if(Date.parse(issueDateString) < Date.parse(dobString)){
		formatErrorList.push({
			code : "FORMAT",
			field : "issueDate",
			message : "Must Be After dateofbirth"
		})	
	}

	return formatErrorList
}

async function validateMarriedStatus(marriedStatus,config) {
	const marriedStatusData = await masterdataService.getMarriedStatus(config,marriedStatus)
	if(marriedStatusData.code == 1 ) {
		return []

	}
	return {
		"code" : "FORMAT",
		"field" : "marriedStatus",
		"message" : 'marriedStatus is valid'
	}
}

async function validateCodeField(fieldName,code,type,config) {
	if(code.toString().indexOf(' ') >= 0) {
		return {
			"code" : "FORMAT",
			"field" : fieldName,
			"message" :''+ fieldName+ ' is invalid'
		}
	}
	const data = await masterdataService.getValueCode_v2(config,code,type)
	if(data) {
		return []
	}
	return {
		"code" : "FORMAT",
		"field" : fieldName,
		"message" :''+ fieldName+ ' is invalid'
	}
}

module.exports = {
	formatError
}
