let inputChecker = require("./a2-error-handler")

async function validateInput(data,config) {
	let missing = checkMissing(data)
	if(missing.length !== 0 ) {
		return missing
	}
	const format = await inputChecker.formatError(data,config)
	return missing.concat(format)
} 

function checkMissing(data) {
	const mandatoryField = ['requestId','contractNumber','partnerCode','employmentType','temDistrict','temWard','temAddress','permanentProvince','permanentDistrict','permanentWard','permanentAddress','marriedStatus','loanPurpose','relation1','relation1Name','relation1PhoneNumber','relation2','relation2Name','relation2PhoneNumber','listDocCollecting']

	let missingList = []	
	
	mandatoryField.forEach(field => {
		if(data[field] === undefined || data[field] === '') {
			const missingObject = {
				"code" : "MISSING",
				"field" : field,
				"message" : field + " is missing."
			}
			missingList.push(missingObject)
		}
	})


	return missingList
}

module.exports = {
	validateInput
}
