{"requestId": "request_id", "contractNumber": "null", "partnerCode": "partner_code", "employmentType": "empl_type", "temDistrict": "district_cur", "temWard": "ward_cur", "temAddress": "address_cur", "yearsOfStay": "current_address_years", "permanentProvince": "province_per", "permanentDistrict": "district_per", "permanentWard": "ward_per", "permanentAddress": "address_per", "jobType": "job_type", "marriedStatus": "married_status", "marriageMateId": "marriage_mate_id", "houseType": "house_type", "monthlyIncome": "monthly_income", "otherIncome": "other_income", "incomeMethod": "income_method", "incomeFrequency": "income_frequency", "incomeReceivingDate": "income_date", "monthlyExpenses": "null", "workPlaceName": "empl_name", "workPlaceProvince": "empl_province", "workPlaceDistrict": "empl_district", "workPlaceWard": "empl_ward", "workPlaceAddress": "empl_address", "workPlacePhone": "company_phone_number", "employmentContractType": "empl_ctrct_type", "employmentContractYearFrom": "empl_ctrct_from_year", "employmentContractYearTo": "empl_ctrct_to_year", "employmentContractTerm": "employment_contract_term", "taxId": "tax_id", "loanPurpose": "loan_purpose", "otherContact": "other_contact_type", "detailOtherContact": "other_contact_value", "relation1": "reference_type_1", "relation1Name": "reference_name_1", "relation1PhoneNumber": "reference_phone_1", "relation2": "reference_type_2", "relation2Name": "reference_name_2", "relation2PhoneNumber": "reference_phone_2", "email": "email"}