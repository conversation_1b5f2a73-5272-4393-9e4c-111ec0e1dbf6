const currentTaskCode = 'MC_CRE_a2';
const common = require("../utils/common")
const fileReader = require('../utils/read_file.js')
const inputValidator = require("./a2-input-validator.js")
const crmService = require("../utils/crmService")
const utils = require("../utils/helper")
const callbackService = require("../utils/callbackService")
const {saveRequest} = require("../utils/loggingService")
const {caseStatusCode,STATUS} = require("../const/caseStatus")
const {PARTNER_CODE,CONTRACT_TYPE, TASK_FLOW, roleCode} = require("../const/definition")
const camelcaseKeys = require("camelcase-keys");
const statusCode = 'KH06'
const loanContractRepo = require("../repositories/loan-contract-repo")
const loggingService = require("../utils/loggingService")
const {convertBody} = require("../utils/converter/convert")
const {request_type,RESPONSE_CODE} = require("../const/definition")
const documentRepo = require("../repositories/document")
const productService = require("../utils/productService")
const {routing} = require("../services/workflow-service")
const {saveVdsTurnOver} = require("../repositories/turnover-repo")
const {getPartnerCodeAndProductCode} = require("../repositories/loan-contract-repo")
const kovOffer = require("../offer/KOV-offer")
const aadService = require("../utils/aadService")
const {saveTurnOrTrans} = require("../repositories/turnover-repo")
const turnoverRepo = require("../repositories/turnover-repo");
const { body } = require("express-validator");
const moment = require('moment-timezone');
const { validNotEnoughDocument } = require("../utils/validator/base-api-validator");
moment().tz('Asia/Ho_Chi_Minh').format()


let paramMapper;
fileReader.readJson('./a2_application/a2-params-field.json')
.then(result => paramMapper = result)
.catch(err => console.log(err))

function generate_query(json_data,json_param_mapper) {
	let basicQuery = "update loan_contract set " 
	const params = []
	let paramIdx = 1
	for (let key of Object.keys(json_param_mapper)) {
		if(json_param_mapper[key] !== 'null' && json_param_mapper[key] !== "") {
			basicQuery += json_param_mapper[key] + "=$" + paramIdx + ","
			params.push(json_data[key]);
			paramIdx += 1
		}
	}
	basicQuery = basicQuery.slice(0,-1)
	const whereQuery = " where contract_number = $" + paramIdx + " RETURNING *"
	params.push(json_data.contractNumber)
	return [basicQuery + whereQuery,params]
}

async function a2_recieve(req,res) {
	const poolWrite = req.poolWrite
	const config = req.config
	const contractNumber = req.body.contractNumber
	try{
		const data = req.body
		let response
		const validatedResult = await inputValidator.validateInput(data,config)
		const numAdditiondoc = config.data.additionalDoc.numDoc
		if(validatedResult.length != 0) {
			response = {
				"statusCode" : "200",
				"body" : {
					"code" : "INVALID REQUEST",
					"message" : "Request invalid",
					"errors" : validatedResult
				}
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(200).json(response)
		} 
		let documentlist = req.body.listDocCollecting
		const externalDocs = req.body.externalDocument || [];
		const contractStatus = await loanContractRepo.getContractStaus(contractNumber)
		if(['KH01','KH02','KH03',STATUS.NOT_ELIGIBLE].includes(contractStatus) || contractStatus === '') {
			response = {
				"statusCode": 0,
				"body": {
				  "code": "INVALID",
				  "message": "Contract number is not pass A1",
					 }
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(400).json(response)
		}
		else if(contractStatus !== 'KH05' && contractStatus !== STATUS.ELIGIBLE) {
			response = {
				"statusCode": 0,
				"body": {
				  	"code": "INVALID",
					"message": "Contract number is passed A2 already",
				}
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(400).json(response)
		}
		let allListDoc = [...documentlist, ...externalDocs];
		
		const productData = await getPartnerCodeAndProductCode(contractNumber)		
		const isEnoughDoc = await validNotEnoughDocument(allListDoc,productData.product_code,req.body.partnerCode)
		if(!isEnoughDoc) {
			response = {
				"statusCode": 0,
				"body": {
				  	"code": "INVALID",
					"message": "Chưa đủ document.",
				}
			}
			saveRequest(poolWrite,req.body,response,contractNumber)
			return res.status(400).json(response)
		}

		let query = generate_query(req.body,paramMapper)
		const resultInsertLoanContract = await req.poolWrite.query(query[0],query[1])

		const bundleInfo = await productService.getBundle(global.config,productData.product_code)
		documentlist = productService.mapBundleGroupKOV(allListDoc,bundleInfo.data)
		await documentRepo.saveUploadedDocumentKOV(poolWrite,contractNumber,documentlist)
		documentRepo.insertOtherDoc(contractNumber,numAdditiondoc)


		await turnoverRepo.insertOtherTurnover(contractNumber)
		let paramsCRMService = {}

		if (resultInsertLoanContract && resultInsertLoanContract.rows && resultInsertLoanContract.rows[0]) {
			paramsCRMService = {
				...camelcaseKeys(resultInsertLoanContract.rows[0]),
				jobType: data.jobType,
				marriedStatus: data.marriedStatus,
				houseType: data.houseType,
				email: data.email,
				loanPurpose: data.loanPurpose
			};
		}

		crmService.processContract(config,contractNumber, paramsCRMService)
		callbackService.callbackRecieved(poolWrite,config,contractNumber)
		await aadService.pushTaskMcV2(roleCode.SS,contractNumber,CONTRACT_TYPE.CREDIT_LINE,STATUS.IN_SS_QUEUE)
		utils.saveStatus(poolWrite,contractNumber,caseStatusCode.IN_SS_QUEUE)
		response = {
			"statusCode": "200",
			"body": {
			  	"code": "RECEIVED",
			  	"message": "The application is received",
			  	"data": {
					"conractNumber": contractNumber,
					"partnerCode": body.partnerCode
			  	}
			}
		}
		saveRequest(poolWrite,req.body,response,contractNumber)
		res.status(200).json(response)
		kovOffer.computeKOVOffer(contractNumber)
	}
	catch (error) {
		common.log(`recieved A2 error : ${error.message}`,req.body.conractNumber)
		utils.saveStatus(poolWrite,contractNumber,statusCode)
		res.status(500).json({
			"msg" : "service " + currentTaskCode + " error",
			"code" : -1

		})
	} 
}

async function fullLoan(req,res) {
	try {
		const poolWrite = req.poolWrite

		let body = req.body
		body = convertBody(body,request_type.fullLoan,global.convertCache)

		const contractNumber = body.contract_number	
		const contractData = await loanContractRepo.getLoanContract(contractNumber)
		
		const partnerCode = contractData.partner_code
		body.partner_code = partnerCode
		const numAdditiondoc = config.data.additionalDoc.numDoc

		const requestId = body.request_id
		const turnover = body.turnover
		const cusTurnOver = body.cusTurnover
		let docList = body.doc_collecting_list
		
		let responseBody;
		if(contractData.status != STATUS.ELIGIBLE) {
			responseBody = {
				code : RESPONSE_CODE.ERROR,
				msg : "Contract number is not eligible"
			}
			await loggingService.saveRequestV2(poolWrite,body,responseBody,contractNumber,requestId,partnerCode)
			return res.status(400).json(responseBody)
		}
		if(utils.isNullOrEmpty(body.product_code)) {
			if(partnerCode == PARTNER_CODE.VPL) {
				body.product_code = 'VIETTELPAY_PRO'
				body.request_int_rate = 0.32
				body.contract_type = CONTRACT_TYPE.CASH_LOAN
				body.lms_type = CONTRACT_TYPE.CASH_LOAN
			}
			else if(partnerCode == PARTNER_CODE.VTP) {
				body.contract_type = CONTRACT_TYPE.CASH_LOAN
				body.product_code = contractData.product_code
				body.lms_type = CONTRACT_TYPE.CASH_LOAN
			}
			else if(partnerCode == PARTNER_CODE.VSK) {
				if(contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE){
					body.lms_type = CONTRACT_TYPE.CREDIT_LINE
					body.product_code = 'MCBAS_HMTD'
				}
				else{
					body.lms_type = CONTRACT_TYPE.CASH_LOAN
					body.product_code = 'MCBAS_ALL'
				}
				const isEnoughDoc = await validNotEnoughDocument(docList,body.product_code,undefined,partnerCode)
				if(!isEnoughDoc) {
					const response = {
						"statusCode": RESPONSE_CODE.ERROR,
						"body": {
							"code": "INVALID",
							"message": "Chưa đủ document.",
						}
					}
					saveRequest(poolWrite,req.body,response,contractNumber)
					return res.status(400).json(response)
				}
			}
		}

		const bundleInfo = await productService.getBundle(global.config,body.product_code)
		docList = productService.mapBundleGroup(docList,bundleInfo.data)
		if(partnerCode == PARTNER_CODE.VPL) {
			await saveVdsTurnOver(contractNumber,turnover)
		}
		if(partnerCode == PARTNER_CODE.VSK){
			if(['79','48','01'].includes(body.province_per)&&body.m_household_expenses<parseInt(6000000)) body.m_household_expenses = parseInt(6000000)
			saveTurnOrTrans(poolWrite,cusTurnOver,'custurnover',contractNumber)
		}
		if(!utils.isNullOrEmpty(body.accountTrading)) {
			loanContractRepo.saveLoanAcountTrading(contractNumber,body.accountTrading)
		}

		if(!utils.isNullOrEmpty(body.branchAddress)) {
			loanContractRepo.saveBranchAddress(contractNumber,body.branchAddress)	
		}

		if(partnerCode == PARTNER_CODE.VTP || partnerCode == PARTNER_CODE.VSK) {
			documentRepo.insertOtherDoc(contractNumber,numAdditiondoc)
		}
		const updateFullLoanRs = await Promise.all([
			documentRepo.saveUploadedDocument(poolWrite,contractNumber,docList),
			loanContractRepo.updateLoanContract(body),
			loanContractRepo.updateContractStatus(STATUS.FULL_LOAN,contractNumber),
			turnoverRepo.insertOtherTurnover(contractNumber)
		])

		if(!updateFullLoanRs[0] || !updateFullLoanRs[1] || !updateFullLoanRs[2] || !updateFullLoanRs[3]) {
			responseBody = {
				code : RESPONSE_CODE.ERROR,
				msg : "Internal Server Error"
			}
			await loggingService.saveRequestV2(poolWrite,body,responseBody,contractNumber,requestId,body.partner_code)
			return res.status(500).json(responseBody)
		}
		responseBody = {
			code : RESPONSE_CODE.RECIEVED,
			msg : "The application is recieved",
			data : {
				"contract_number" : contractNumber,
				"request_id" : requestId
			}
		}
		await loggingService.saveRequestV2(poolWrite,body,responseBody,contractNumber,requestId,body.partner_code)
		const curTaskCode = TASK_FLOW.FULL_LOAN
		body.currentTask = curTaskCode
		
		routing(body)

		return res.status(200).json(responseBody)
	}
	catch(err) {
		console.log(err)
		common.log(err.message,req.body.contract_number)
		return res.status(500).json({
			code : RESPONSE_CODE.ERROR,
			msg : "INTERNAL SERVER ERROR"
		})
	}
}

module.exports = {
	a2_recieve,
	fullLoan
}
