const Joi = require("joi/lib");

const docSchema = Joi.object({
  doc_type: Joi.string().required(),
  doc_id: Joi.string().required(),
});

const addressSchema = Joi.object({
  province_code: Joi.string().required(),
  ward_code: Joi.string().required(),
  detail: Joi.string().required(),
});

const personSchema = Joi.object({
  full_name: Joi.string().required(),
  position: Joi.string().required(),
  dob: Joi.date().required(),
  id: Joi.string().required(),
  issue_date: Joi.date().required(),
  issue_place: Joi.string().required(),
  phone_number: Joi.string().required(),
  email: Joi.string().email().optional(),
  per_address: addressSchema.required(),
  // cur_address: addressSchema.optional(),
  docs: Joi.array().items(docSchema).required(),
}).unknown(true);

const businessOwnerPartnerSchema = Joi.object({
  full_name: Joi.string().required(),
  dob: Joi.date().required(),
  id: Joi.string().required(),
  issue_date: Joi.date().required(),
  issue_place: Joi.string().required(),
  phone_number: Joi.string().required(),
  email: Joi.string().email().optional(),
  per_address: addressSchema.required(),
  docs: Joi.array().items(docSchema).allow(null).optional(),
}).unknown(true);

const managerSchema = Joi.object({
  full_name: Joi.string().required(),
  position: Joi.string().allow(null).optional(),
  dob: Joi.date().required(),
  gender: Joi.string().allow(null).valid("M", "F").optional(),
  id: Joi.string().required(),
  issue_date: Joi.date().required(),
  issue_place: Joi.string().required(),
  phone_number: Joi.string().required(),
  email: Joi.string().email().required(),
  per_address: addressSchema.required(),
  // cur_address: addressSchema.optional(),
  docs: Joi.array().items(docSchema).required(),
}).unknown(true);

const representationSchema = Joi.object({
  full_name: Joi.string().required(),
  position: Joi.string().required(),
  dob: Joi.date().required(),
  gender: Joi.string().allow(null).valid("M", "F").optional(),
  id: Joi.string().required(),
  management_experience: Joi.string().required(),
  issue_date: Joi.date().required(),
  issue_place: Joi.string().required(),
  phone_number: Joi.string().required(),
  email: Joi.string().email().required(),
  per_address: addressSchema.required(),
  docs: Joi.array().items(docSchema).required(),
}).unknown(true);

const shareholderRepresentationSchema = Joi.object({
  full_name: Joi.string().required(),
  id: Joi.string().required(),
  issue_date: Joi.date().required(),
  issue_place: Joi.string().required(),
  per_address: addressSchema.required()
}).unknown(true);

const warehousesSchema = Joi.object({
  warehouse_name: Joi.string().required(),
  province_code: Joi.string().required(),
  ward_code: Joi.string().required(),
  detail: Joi.string().required(),
  docs: Joi.array().items(docSchema).required(),
});

const branchesSchema = Joi.object({
  branch_name: Joi.string().required(),
  province_code: Joi.string().required(),
  ward_code: Joi.string().required(),
  detail: Joi.string().required(),
  docs: Joi.array().items(docSchema).required(),
});

const shareholdersMemberSchema = Joi.object({
  subject: Joi.string().valid("INDIVIDUAL", "ORGANIZATION").required(),
  full_name: Joi.string()
    .when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  identity_type: Joi.string()
    .when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  dob: Joi.date().optional(),
  id: Joi.string()
    .when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  issue_date: Joi.date()
    .when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  issue_place: Joi.string()
    .when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  capital_contribution_ratio: Joi.string().required(),
  phone_number: Joi.string().optional(),
  email: Joi.string().email().allow(null).optional(),
  tax_id: Joi.string()
    .when('subject', {
      is: Joi.valid('ORGANIZATION'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  company_name: Joi.string()
    .when('subject', {
      is: Joi.valid('ORGANIZATION'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  business_phone_number: Joi.string()
    .when('subject', {
      is: Joi.valid('ORGANIZATION'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  business_email: Joi.string()
    .when('subject', {
      is: Joi.valid('ORGANIZATION'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  authorization_doc_number: Joi.string().optional(),
  business_address: addressSchema.when('subject', {
    is: Joi.valid('ORGANIZATION'),
    then: Joi.required(),
    otherwise: Joi.allow(null).allow('').optional()
  }),
  per_address: addressSchema.when('subject', {
    is: Joi.valid('INDIVIDUAL'),
    then: Joi.required(),
    otherwise: Joi.allow(null).optional()
  }),
  cur_address: addressSchema.allow(null).optional(),
  married_status: Joi.string().allow(null).optional(),
  partner: personSchema.allow(null).optional(),
  docs: Joi.array().items(docSchema).optional(),
  representations: Joi.array().items(shareholderRepresentationSchema).when('subject', {
    is: Joi.valid('ORGANIZATION'),
    then: Joi.required(),
    otherwise: Joi.allow(null).optional()
  })
}).unknown(true);

const af2Schema = Joi.object({
  request_id: Joi.string().required(),
  partner_code: Joi.string().required(),
  contract_number: Joi.string().required(),
  loan_purpose: Joi.string().required(),
  total_turn_over_next_year: Joi.number().allow(null).optional(),
  total_cost_over_next_year: Joi.number().allow(null).optional(),
  profit_before_tax_next_year: Joi.number().allow(null).optional(),
  capital_need: Joi.number().allow(null).optional(),
  loan_amount: Joi.number().required(),
  loans_other_financial_institutions: Joi.number().allow(null).optional(),
  owner_equity: Joi.number().allow(null).optional(),
  other_capital: Joi.number().allow(null).optional(),
  tenor: Joi.number().required(),
  tenor_per_kunn: Joi.number().allow(null).optional(),
  repayment_method: Joi.string().required(),
  repayment_sources: Joi.string().required(),
  tax_id: Joi.string().required(),
  registration_number: Joi.string().allow(null).optional(),
  company_name: Joi.string().required(),
  address_on_license: Joi.string().required(),
  registration_cert_issue_date: Joi.date().required(),
  registration_cert_issue_place: Joi.string().required(),
  charter_capital: Joi.number().required(),
  business_type: Joi.string().required(),
  business_industry: Joi.string().required(),
  loan_purpose_detail: Joi.string()
    .when('business_industry', {
      is: Joi.valid('4101', '4102', '6810', '6820'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
  conditional_business_industry: Joi.number().integer().valid(0, 1).required(),
  number_of_staffs: Joi.number().required(),
  company_phone_number: Joi.string().required(),
  company_email: Joi.string().email().required(),
  company_website: Joi.string().allow(null).optional(),
  warehouses: Joi.array().items(warehousesSchema).allow(null).optional(),
  branches: Joi.array().items(branchesSchema).allow(null).optional(),
  representations: Joi.array().items(representationSchema).required(),
  managers: Joi.array().items(managerSchema).required(),
  business_owner: Joi.object({
    subject: Joi.string().valid("INDIVIDUAL", "ORGANIZATION").required(),
    full_name: Joi.string().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).allow('').optional()
    }),
    dob: Joi.date().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    id: Joi.string().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    issue_date: Joi.date().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    issue_place: Joi.string().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    phone_number: Joi.string().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    email: Joi.string().email().allow(null).optional(),
    per_address: addressSchema.when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    // cur_address: addressSchema.allow(null).optional(),
    married_status: Joi.string().when('subject', {
      is: Joi.valid('INDIVIDUAL'),
      then: Joi.required(),
      otherwise: Joi.allow(null).optional()
    }),
    partner: Joi.when('subject', {
      is: 'INDIVIDUAL',
      then: Joi.when('married_status', {
        is: 'MARRIED',
        then: businessOwnerPartnerSchema.required(),
        otherwise: businessOwnerPartnerSchema.allow(null).optional()
      }),
      otherwise: businessOwnerPartnerSchema.allow(null).optional()
    }),
  }).required().unknown(true),
  shareholders: Joi.object({
    members: Joi.array().items(shareholdersMemberSchema).allow(null).optional(),
  }).required(),
  financial_information: Joi.object().allow(null).optional(),
  bank_code: Joi.string().required(),
  bank_account: Joi.string().required(),
  sme_headquarters_ward: Joi.string().required(),
  sme_headquarters_province: Joi.string().required(),
  sme_headquarters_address: Joi.string().required(),
  bank_branch_code: Joi.string().required()
}).unknown(true);

module.exports = {
  af2Schema: af2Schema,
  docSchema: docSchema,
  addressSchema: addressSchema,
  personSchema: personSchema,
  managerSchema: managerSchema,
};
