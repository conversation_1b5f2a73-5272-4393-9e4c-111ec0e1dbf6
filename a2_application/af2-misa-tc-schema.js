const Joi = require("joi")

const validateAdressCode = (value, helpers) => {
  const isFilled = v => v !== undefined && v !== null && v !== '';
  const groupOldCode = [value.provinceCode, value.districtCode, value.wardCode];
  const groupNewCode = [value.newProvinceCode, value.newWardCode];

  const oldCodeAny = groupOldCode.some(isFilled);
  const oldCodeAll = groupOldCode.every(isFilled);
  const newCodeAny = groupNewCode.some(isFilled);
  const newCodeAll = groupNewCode.every(isFilled);

  if (newCodeAny && !newCodeAll && !oldCodeAll) {
    if (!isFilled(value.newProvinceCode)) {
      return helpers.error("address.missing", { field: "newProvinceCode" });
    }
    if (!isFilled(value.newWardCode)) {
      return helpers.error("address.missing", { field: "newWardCode" });
    }
  }

  if (oldCodeAny && !oldCodeAll && !newCodeAll) {
    if (!isFilled(value.provinceCode)) {
      return helpers.error("address.missing", { field: "provinceCode" });
    }
    if (!isFilled(value.districtCode)) {
      return helpers.error("address.missing", { field: "districtCode" });
    }
    if (!isFilled(value.wardCode)) {
      return helpers.error("address.missing", { field: "wardCode" });
    }
  }

  if (!newCodeAll && !oldCodeAll) {
    return helpers.error("address.missing", { field: "newProvinceCode" });
  }

  return value;
}

const af2Schema = Joi.object({
    requestId: Joi.string().required(),
    contractNumber: Joi.string().required(),
    productCode: Joi.string().required(), // for MISA TC1
    loanPurpose: Joi.string().required(), // Mục đích vay vốn
    totalTurnoverNextYear: Joi.string().required(), // Doanh thu dự kiến 12 tháng tiếp theo
    totalCostOverNextYear: Joi.string().required(), // Tổng chi phí dự kiến 12 tháng tiếp theo
    profitBeforeTaxNextYear: Joi.string().required(), // Lợi nhuận trước thuế dự kiến 12 tháng tiếp theo
    capitalNeed: Joi.string().required(), // Tổng nhu cầu vốn lưu động dự kiến (a)
    loanAmount: Joi.string().required(), // Vốn vay EVNFC (b)
    loansOtherFinancialInstitutions: Joi.string().required(),// Vốn vay các TCTD khác (c)
    ownerEquity: Joi.string().allow(null).allow(''), // Vốn chủ sở hữu tham gia vào phương án (d)
    otherCapital: Joi.string().required(), // Vốn huy động khác (e)
    charterCapital: Joi.string().required(), // Vốn điều lệ
    tenor: Joi.string().required(), // Thời hạn vay EVNFC
    tenorPerKunn: Joi.string().required(), // Thời hạn vay mỗi KUNN
    repaymentMethod: Joi.string().required(), // Phương thức trả nợ
    repaymentSources: Joi.string().required(), // Nguồn trả nợ
    taxId: Joi.string().required(), // mã số thuế DN
    registrationNumber: Joi.string().required(), // mã số DN
    companyName: Joi.string().required(), // Tên doanh nghiệp
    addressOnLicense: Joi.string().required(),  // Địa chỉ trên GPKD MISA
    addressOnLicenseDetail: Joi.object({ // Địa chỉ chi tiết trên GPKD MISA
        provinceCode: Joi.string().allow(null, '').optional(),
        districtCode: Joi.string().allow(null, '').optional(),
        wardCode: Joi.string().allow(null, '').optional(),
        newProvinceCode: Joi.string().allow(null, '').optional(),
        newWardCode: Joi.string().allow(null, '').optional(),
        detailAddress: Joi.string().required(),
    }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(),
    // businessRegistrationAddress, //đkkd
    // firstRegistrationDate: Joi.string().required(),  // Ngày cấp chứng nhận ĐKKD lần đầu
    registrationDate: Joi.string().required(), // Ngày cấp chứng nhận ĐKKD
    // registrationCertIssueDate, //Ngày cấp chứng nhận ĐKKD
    smeHeadquartersProvince: Joi.string().allow(null, '').optional(), // Địa chỉ trụ sở chính
    smeHeadquartersDistrict: Joi.string().allow(null, '').optional(), // Địa chỉ trụ sở chính
    smeHeadquartersWard: Joi.string().allow(null, '').optional(), // Địa chỉ trụ sở chính
    smeHeadquartersAddress: Joi.string().allow(null, '').optional(), // Địa chỉ trụ sở chính
    smeHeadquartersNewProvince: Joi.string().optional(), // Địa chỉ trụ sở chính theo địa giới hành chính mới
    smeHeadquartersNewWard: Joi.string().optional(), // Địa chỉ trụ sở chính theo địa giới hành chính mới
    registrationCertNo: Joi.string().required(), // Số chứng nhận ĐKKD
    registrationCertIssuePlace: Joi.string().required(), // Nơi cấp
    businessType: Joi.string().required(), // Loại hình pháp lý doanh nghiệp
    businessIndustry: Joi.string().required(), // Ngành nghề kinh doanh chính
    conditionalBusinessIndustry: Joi.number().required(), // Ngành nghề kinh doanh có điều kiện
    companyPhoneNumber: Joi.string().required(), // SĐT công ty
    companyEmail : Joi.string().required(), // Email công ty
    businessLicenseUrl: Joi.string().required(), // Ảnh chụp/bản scan giấy phép đăng ký kinh doanh
    charterUrl: Joi.string().required(), // Ảnh chụp/bản scan điều lệ doanh nghiệp
    taxRegistrationUrl: Joi.string().required(), // Ảnh chụp/bản scan đăng ký mã số thuế
    bankCode: Joi.string().required(), // Ngân hàng
    // bankBranchCode,
    bankAccount: Joi.string().required(), // Số tài khoản của khách hàng
    isChange: Joi.number().valid(0,1).required(), // có thay đổi thông tin hay không?
    changedInfo: Joi.when('isChange', { is: 1, then: Joi.object({  // thông tin thay đổi
        businessLicenseUrl: Joi.string().uri().optional(),
        companyName: Joi.string().optional(),
        provinceCode: Joi.string().allow(null, '').optional(),
        districtCode: Joi.string().allow(null, '').optional(),
        wardCode: Joi.string().allow(null, '').optional(),
        newProvinceCode: Joi.string().allow(null, '').optional(),
        newWardCode: Joi.string().allow(null, '').optional(),
        detailAddress: Joi.string().required(),
    }).unknown(true).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(), otherwise: Joi.optional() }),
    financialInformation: Joi.object({
        vatForms: Joi.array().items( // Tờ khai thuế GTGT 12 tháng gần nhất hoặc các quý gần nhất
            Joi.object({
                period: Joi.string().required(),
                name: Joi.string().required(),
                docs: Joi.array().required(),
            }).unknown(true).required()
        ).required(),
        inputPartners: Joi.array().items( // Thông tin 3  đối tác đầu vào
            Joi.object({
                taxId: Joi.string().required(),
                companyName: Joi.string().required()
            }).unknown(true).required()
        ).required(),
        outputPartners: Joi.array().items( // Thông tin 3  đối tác đầu ra
            Joi.object({
                taxId: Joi.string().required(),
                companyName: Joi.string().required()
            }).unknown(true).required()
        ).required()
    }).unknown(true).required(),
    loanApplicationFile: Joi.object({ // Đề nghị tái cấp vốn
        fileName: Joi.string().required(),
        fileType: Joi.string().required(),
        fileUrl: Joi.string().required(),
        docType: Joi.string().required()
    }).unknown(true).required(),
    // warehouses, branches // THÔNG TIN CHI NHÁNH/ KHO HÀNG ( nếu có )
    representations: Joi.array().items( // Thông tin người đại diện theo pháp luật
        Joi.object({
            fullName: Joi.string().required(), // Họ và tên
            position: Joi.string().required(), // Chức vụ
            dob: Joi.string().required(), // Ngày sinh
            id: Joi.string().required(), // Số CCCD/CC
            issueDate: Joi.string().required(), // Ngày cấp
            issuePlace: Joi.string().required(), // Nơi cấp
            phoneNumber: Joi.string().required(), // Di động
            email: Joi.string().required(), // Email
            // managementExperience: Joi.string().required(), // Kinh nghiệm quản lý
            perAddress: Joi.object({ // Địa chỉ thường trú
                provinceCode: Joi.string().allow(null, '').optional(),
                districtCode: Joi.string().allow(null, '').optional(),
                wardCode: Joi.string().allow(null, '').optional(),
                newProvinceCode: Joi.string().allow(null, '').optional(),
                newWardCode: Joi.string().allow(null, '').optional(),
                detail: Joi.string().required(),
            }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(),
            // curAddress, // KHÔNG CÓ - Địa chỉ hiện tại
            docs: Joi.array().required(), // Quyết định bổ nhiệm của người đại diện theo pháp luật + Ảnh chụp/bản scan mặt trước, mặt sau của CCCD/CC của Người đại diện pháp luật + Ảnh chụp trực tiếp người đại diện pháp luật tại trụ sở chính công ty
        }).unknown(true).required()
    ).required(),
    managers: Joi.array().items( // Thông tin giám đốc/ tổng giám đốc
        Joi.object({
            fullName: Joi.string().required(), // Họ và tên
            position: Joi.string().required(), // Chức vụ
            id: Joi.string().required(), // Số CCCD/CC
            issueDate: Joi.string().required(), // Ngày cấp
            issuePlace: Joi.string().required(), // Nơi cấp
            phoneNumber: Joi.string().required(), // Di động
            email: Joi.string().required(), // Email
            perAddress: Joi.object({  // Địa chỉ thường trú
                provinceCode: Joi.string().allow(null, '').optional(),
                districtCode: Joi.string().allow(null, '').optional(),
                wardCode: Joi.string().allow(null, '').optional(),
                newProvinceCode: Joi.string().allow(null, '').optional(),
                newWardCode: Joi.string().allow(null, '').optional(),
                detail: Joi.string().required(),
            }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(),
            curAddress: Joi.object({
                provinceCode: Joi.string().allow(null, '').optional(),
                districtCode: Joi.string().allow(null, '').optional(),
                wardCode: Joi.string().allow(null, '').optional(),
                newProvinceCode: Joi.string().allow(null, '').optional(),
                newWardCode: Joi.string().allow(null, '').optional(),
                detail: Joi.string().required(),
            }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}),
            docs: Joi.array().required(), // Ảnh chụp/bản scan mặt trước, mặt sau của CCCD/CC của giám đốc/ Tổng giám đốc + Quyết định bổ nhiệm của Giám đốc/ Tổng giám đốc
        }).unknown(true).required()
    ),
    businessOwner: Joi.object({ // Thông tin chủ doanh nghiệp (là cá nhân / là doanh nghiệp)
        subject: Joi.string().valid("INDIVIDUAL", "ORGANIZATION").required(), // INDIVIDUAL, ORGANIZATION
        
        // Fields for INDIVIDUAL
        id: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any() }),
        fullName: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        issueDate: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        issuePlace: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        dob: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        phoneNumber: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        email: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.any().optional() }),
        marriedStatus: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().valid("MARRIED", "SINGLE").required(), otherwise: Joi.any().optional() }),
        perAddress: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.object({
            provinceCode: Joi.string().allow(null, '').optional(),
            districtCode: Joi.string().allow(null, '').optional(),
            wardCode: Joi.string().allow(null, '').optional(),
            newProvinceCode: Joi.string().allow(null, '').optional(),
            newWardCode: Joi.string().allow(null, '').optional(),
            detail: Joi.string().required(),
        }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(), otherwise: Joi.any().optional() }),
        curAddress: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.object({
            provinceCode: Joi.string().allow(null, '').optional(),
            districtCode: Joi.string().allow(null, '').optional(),
            wardCode: Joi.string().allow(null, '').optional(),
            newProvinceCode: Joi.string().allow(null, '').optional(),
            newWardCode: Joi.string().allow(null, '').optional(),
            detail: Joi.string().required(),
        }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(), otherwise: Joi.any().optional() }),
        
        // Fields for ORGANIZATION
        taxId: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        companyName: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        businessAddress: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.object({
            provinceCode: Joi.string().allow(null, '').optional(),
            districtCode: Joi.string().allow(null, '').optional(),
            wardCode: Joi.string().allow(null, '').optional(),
            newProvinceCode: Joi.string().allow(null, '').optional(),
            newWardCode: Joi.string().allow(null, '').optional(),
            detail: Joi.string().required(),
        }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(), otherwise: Joi.any().optional() }),
        businessPhoneNumber: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        businessEmail: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
        
        // Optional fields
        position: Joi.optional(),
        entityType: Joi.string().valid("LEGAL_REPRESENTATIVE", "AUTHORIZED_PERSON").optional(),
        authorizationDocNo: Joi.string().allow(null, '').optional(),
        
        // Partner validation
        partner: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.when('marriedStatus', { is: 'MARRIED', then: Joi.object({
            fullName: Joi.string().required(),
            id: Joi.string().required(),
            docs: Joi.array().required()
        }).unknown(true).required(), otherwise: Joi.optional() }), otherwise: Joi.any().optional() }),
        
        docs: Joi.array().required(), // Với subject = ORGANIZATION: SBIZ: Ảnh chụp/bản scan giấy phép đăng ký kinh doanh (bắt buộc) + SDALR: Ảnh chụp/bản scan Quyết định bổ nhiệm của người đại diện theo pháp luật + SPALR: Ảnh chụp/bản scan văn bản uỷ quyền của người đại diện theo pháp luật cho người được uỷ quyền. ( Xuất hiện trong trường hợp người đại diện không phải là Đại diện theo pháp luật) 
                                        // Với subject = INDIVIDUAL: Ảnh chụp/bản scan mặt trước, mặt sau của CCCD/CC của chủ doanh nghiệp 
    }).unknown(true).required(),
    // loại hình doanh nghiệp thuộc: - công ty cổ phần, - công ty tnhh 2 thành viên trở lên, - công ty hơp danh
    shareholders: Joi.object({ // Cổ đông/Thành viên góp vốn
        members: Joi.array().items(
            Joi.object({
                subject: Joi.string().valid("INDIVIDUAL", "ORGANIZATION").required(),
                capitalContributionRatio: Joi.number().required(),

                // INDIVIDUAL
                id: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                fullName: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                issueDate: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                issuePlace: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                phoneNumber: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                email: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                perAddress: Joi.when('subject', { is: 'INDIVIDUAL', then: Joi.object({
                    provinceCode: Joi.string().allow(null, '').optional(),
                    districtCode: Joi.string().allow(null, '').optional(),
                    wardCode: Joi.string().allow(null, '').optional(),
                    newProvinceCode: Joi.string().allow(null, '').optional(),
                    newWardCode: Joi.string().allow(null, '').optional(),
                    detail: Joi.string().required(),
                }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(), otherwise: Joi.optional() }),

                // Optional fields
                curAddress: Joi.object({
                    provinceCode: Joi.string().allow(null, '').optional(),
                    districtCode: Joi.string().allow(null, '').optional(),
                    wardCode: Joi.string().allow(null, '').optional(),
                    newProvinceCode: Joi.string().allow(null, '').optional(),
                    newWardCode: Joi.string().allow(null, '').optional(),
                    detail: Joi.string().required(),
                }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).optional(),
                authorization_doc_number: Joi.string().optional(),
                entity_type: Joi.string().valid("LEGAL_REPRESENTATIVE", "AUTHORIZED_PERSON").optional(),
                identityType: Joi.string().optional(),

                // ORGANIZATION
                taxId: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                companyName: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                businessAddress: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.object({
                    provinceCode: Joi.string().allow(null, '').optional(),
                    districtCode: Joi.string().allow(null, '').optional(),
                    wardCode: Joi.string().allow(null, '').optional(),
                    newProvinceCode: Joi.string().allow(null, '').optional(),
                    newWardCode: Joi.string().allow(null, '').optional(),
                    detail: Joi.string().required(),
                }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(), otherwise: Joi.optional() }),
                businessEmail: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                businessPhoneNumber: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.string().required(), otherwise: Joi.any().optional() }),
                representations: Joi.when('subject', { is: 'ORGANIZATION', then: Joi.array().items(
                    Joi.object({
                        fullName: Joi.string().required(),
                        id: Joi.string().required(),
                        issueDate: Joi.string().required(),
                        issuePlace: Joi.string().required(),
                        perAddress: Joi.object({
                            provinceCode: Joi.string().allow(null, '').optional(),
                            districtCode: Joi.string().allow(null, '').optional(),
                            wardCode: Joi.string().allow(null, '').optional(),
                            newProvinceCode: Joi.string().allow(null, '').optional(),
                            newWardCode: Joi.string().allow(null, '').optional(),
                            detail: Joi.string().required(),
                        }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).required(),
                        curAddress: Joi.object({
                            provinceCode: Joi.string().allow(null, '').optional(),
                            districtCode: Joi.string().allow(null, '').optional(),
                            wardCode: Joi.string().allow(null, '').optional(),
                            newProvinceCode: Joi.string().allow(null, '').optional(),
                            newWardCode: Joi.string().allow(null, '').optional(),
                            detail: Joi.string().required(),
                        }).custom(validateAdressCode, 'validate address groups').messages({'address.missing': '{{#label}}.{{#field}} is required',}).optional()
                    }).unknown(true).required()
                ).required(), otherwise: Joi.any().optional() })
            }).unknown(true)
        ).required(),
        docs: Joi.array().required()
    }).unknown(true),
}).unknown(true);

module.exports = {
  af2Schema: af2Schema,
  validateAdressCode: validateAdressCode,
};
