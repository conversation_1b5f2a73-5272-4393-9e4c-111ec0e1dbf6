const Joi = require("joi/lib");

const documentsSchema = Joi.object({
  doc_id: Joi.string().required(),
  doc_type: Joi.string().required(),
}).unknown(true);

const af3Schema = Joi.object({
  request_id: Joi.string().required(),
  partner_code: Joi.string().optional(),
  contract_number: Joi.string().required(),
  documents: Joi.array().items(documentsSchema).required(),
  is_af3_re_submit: Joi.boolean().optional(),
}).unknown(true);

module.exports = {
  af3Schema: af3Schema,
};
