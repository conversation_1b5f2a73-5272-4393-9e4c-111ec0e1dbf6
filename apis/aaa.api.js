const common = require("../utils/common");

const authentApi = async () => {
  let rs;
  const url = `${config.basic.aaa[[config.env]]}/aaa/v02/authen/signin`;
  const username = global.config.data?.crmService?.username;
  const password = global.config.data?.crmService?.password;
  try {
    const payload = {
      username,
      password,
    };
    rs = await common.postApiV2(url, payload, {
      "Content-Type": "application/json",
    });
    rs = rs?.data || {};
    const { token, uiid } = rs;
    return { token, uiid };
  } catch (error) {
    return null;
  }
};

module.exports = {
  authentApi,
};
