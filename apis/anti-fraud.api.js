const common = require("../utils/common");
const uuid = require("uuid");
const sqlHelper = require("../utils/sqlHelper");
const loanCicLogRepo = require("../repositories/loan-cic-log-repo");
const helper = require("../utils/helper");
const moment = require("moment");
const {
  CIC_STEP_CHECK,
  SERVICE_NAME,
  MisaStep,
  TASK_FLOW,
} = require("../const/definition");
const loggingRepo = require("../repositories/logging-repo");
const { CIC_CONTRACT_TYPE } = require("../const/cusType");

/**
 * 
 * @param {*} legalRepresentative : nguoi dai dien phap luat, cau truc 
 *    {
        customerName,
        idNumber,
        otherIdNumber,
        issueDate,
        issuePlace,
        dateOfBirth,
        gender,
        phoneNumber,
      }
 * @param {*} otherPersons : danh sach cac the nhan khac, cau truc 
      {
        customerName,
        idNumber,
        otherIdNumber,
        issueDate,
        issuePlace,
        dateOfBirth,
        gender,
        phoneNumber,
      }
 * @returns eligible result
 */
const eligibleApiV2 = async ({
  requestId,
  legalRepresentative,
  otherPersons,
  productType,
  registrationNumber,
  taxCode,
  companyName,
  registrationDate,
  productCode,
  caseCreationTime,
  partnerCode,
  channel,
  contractNumber,
  companyType,
  contractType,
  otherEnterprises,
  options,
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}${
      config.data.antiFraud.eligibleApiV2
    }`;
    const payload = {
      request_id: requestId,
      contract_type: contractType,
      legal_representative: {
        customer_name: legalRepresentative.customerName,
        id_number: legalRepresentative.idNumber,
        other_id_number: legalRepresentative.otherIdNumber,
        issue_date: legalRepresentative.issueDate,
        issue_place: legalRepresentative.issuePlace,
        date_of_birth: legalRepresentative.dateOfBirth,
        gender: legalRepresentative.gender,
        phone_number: legalRepresentative.phoneNumber,
      },
      other_persons: (otherPersons || []).map((e) => ({
        customer_name: e.customerName,
        id_number: e.idNumber,
        other_id_number: e.otherIdNumber,
        issue_date: e.issueDate,
        issue_place: e.issuePlace,
        date_of_birth: e.dateOfBirth,
        gender: e.gender,
        phone_number: e.phoneNumber,
      })),
      other_enterprises: (otherEnterprises || []).map((e) => ({
        customer_name: e.customerName,
        id_number: e.idNumber,
        other_id_number: e.otherIdNumber,
        issue_date: e.issueDate,
        issue_place: e.issuePlace,
        date_of_birth: e.dateOfBirth,
        gender: e.gender,
        phone_number: e.phoneNumber,
        tax_code: e.taxCode,
        registration_number: e.registrationNumber,
        company_name: e.companyName,
        registration_date: e.registrationDate,
        company_type: e.companyType,
      })),
      product_type: productType,
      registration_number: registrationNumber,
      tax_code: taxCode,
      company_name: companyName,
      registration_date: registrationDate,
      product_code: productCode,
      case_creation_time: caseCreationTime,
      partner_code: partnerCode,
      channel: channel,
      contract_number: contractNumber,
      company_type: companyType,
    };

    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    let errorData;
    const res = await common
      .postApiV2(
        url,
        payload,
        header,
        {
          isLog: true,
          contractNumber: contractNumber,
          requestId,
          requestType: "ELIGIBLE_SME",
        }
      )
      .then()
      .catch((err) => {
        console.log(
          `[ANTI_FRAUD][eligibleApi] payload ${JSON.stringify(
            payload
          )} Error ${err}: ${JSON.stringify(errorData || {})}`
        );
      });

    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      TASK_FLOW.CHECK_ELIGIBLE_SME,
      payload,
      res?.data || errorData,
      url
    );
    return res?.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][eligibleApi] requestId ${requestId} Error ${error}`
    );
  }
};
/**
 *
 * @param {*} persons: danh sách cá nhân check
 * @param {*} enterprises: thông tin doanh nghiệp
 * @return kết quả check s37 , ELIGIBLE và NOT_ELIGIBLE
 */
const s37Api = async ({
  contractNumber,
  partnerCode,
  custId,
  persons,
  enterprises,
  stepCheck = CIC_STEP_CHECK.AF1,
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}${
      config.data.antiFraud.s37SmeApi
    }`;
    const payload = {
      contract_number: contractNumber,
      cust_id: custId,
      partner_code: partnerCode,
      persons: persons.map((e) => ({
        id_number: e.idNumber,
        other_id_number: e.otherIdNumber ?? "",
        full_name: e.fullName ?? "",
        address: e.address,
      })),
      enterprises: (enterprises || []).map((e) => ({
        registration_number: e.registrationNumber,
        tax_code: e.taxCode,
        company_name: e.companyName,
        address: e.address,
      })),
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    let errorData;
    const res = await common
      .postAPI(url, payload, header)
      .then()
      .catch((err) => {
        errorData = err?.response?.data || {};
        console.log(
          `[ANTI_FRAUD][s37Api] payload ${JSON.stringify(
            payload
          )} Error ${err}: ${JSON.stringify(errorData || {})}`
        );
      });

    const insertBody = {
      contract_number: contractNumber,
      step: stepCheck,
      request_payload: payload,
      response_payload: res || errorData,
    };
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      SERVICE_NAME.S37,
      payload,
      res || errorData,
      url
    );
    await sqlHelper.insertData(
      `loan_cic_log`,
      loanCicLogRepo.columns,
      sqlHelper.generateValues(insertBody, loanCicLogRepo.columns)
    );
    return res;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][s37Api] contractNumber ${contractNumber} Error ${error}`
    );
  }
};

/**
 * @param {*} contractNumber: so hop dong
 * @param {*} custId: ma khach hang
 * @param {*} contractType: loại hợp đồng LIMIT/KUNN
 * @param {*} persons: danh sách cá nhân check
 * @param {*} enterprises: danh sách thông tin doanh nghiệp
 * @param {*} taxCode: taxId doanh nghiệp chính
 * @return keets qua check r11a/s10
 */

const cicDetailsApi = async ({
  contractNumber,
  custId,
  persons,
  enterprises,
  contractType,
  partnerCode,
  stepCheck = CIC_STEP_CHECK.AF2_DETAIL,
  taxCode
}) => {
  try {
    if (contractType === CIC_CONTRACT_TYPE.KUNN)
      stepCheck = CIC_STEP_CHECK.KUNN;
    const url = `${config.basic.antiFraud[[config.env]]}${
      config.data.antiFraud.cicDetailsApi
    }`;
    const payload = {
      contract_number: contractNumber,
      cust_id: custId,
      contract_type: contractType,
      partner_code: partnerCode,
      tax_code: taxCode,
      persons: persons.map((e) => ({
        id_number: e.idNumber,
        other_id_number: e.otherIdNumber ?? "",
        full_name: e.fullName ?? "",
        address: e.address,
        subject: e.subject ?? "",
      })),
      enterprises: enterprises.map((e) => ({
        registration_number: e.registrationNumber,
        tax_code: e.taxCode,
        company_name: e.companyName,
        address: e.address,
        subject: e.subject ?? "",
      })),
    };

    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    let errorData;
    await loggingRepo.saveWorkflow(
      MisaStep.CHECK_CIC_DETAIL,
      `START_CHECKING`,
      contractNumber,
      "system"
    );
    const res = await common
      .postApiV2(url, payload, header)
      .then()
      .catch((err) => {
        errorData = err?.response?.data || {};
        console.log(
          `[ANTI_FRAUD][cicDetailsApi] payload ${JSON.stringify(
            payload
          )} Error ${err}: ${JSON.stringify(errorData || {})}`
        );
      });

    const insertBody = {
      contract_number: contractNumber,
      step: stepCheck,
      request_payload: payload,
      response_payload: res?.data || errorData,
    };
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      SERVICE_NAME.CIC_DETAIL,
      payload,
      res?.data || errorData,
      url
    );
    await sqlHelper.insertData(
      `loan_cic_log`,
      loanCicLogRepo.columns,
      sqlHelper.generateValues(insertBody, loanCicLogRepo.columns)
    );
    return res?.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][cicDetailsApi] taxCode ${enterprise.taxCode} Error ${error}`
    );
  }
};

/**
 * Makes an API call to the anti-fraud service to check eligibility for PERSONAL.
 *
 * @param {Object} params - The parameters for the API call.
 * @param {string} params.requestId - The unique identifier for the request.
 * @param {string} params.customerName - The name of the customer.
 * @param {string} params.idNumber - The ID number of the customer.
 * @param {string} params.otherIdNumber - Another ID number of the customer.
 * @param {string} params.issueDate - The issue date of the ID.
 * @param {string} params.issuePlace - The place where the ID was issued.
 * @param {string} params.dateOfBirth - The date of birth of the customer.
 * @param {string} params.gender - The gender of the customer.
 * @param {string} params.phoneNumber - The phone number of the customer.
 * @param {string} params.productType - The type of product.
 * @param {string} params.registrationNumber - The registration number.
 * @param {string} params.taxCode - The tax code.
 * @param {string} params.companyName - The name of the company.
 * @param {string} params.registrationDate - The registration date of the company.
 * @param {string} params.productCode - The product code.
 * @param {string} params.caseCreationTime - The time the case was created.
 * @param {string} params.partnerCode - The partner code.
 * @param {string} params.channel - The channel.
 * @param {string} params.contractNumber - The contract number.
 * @param {string} params.companyType - The type of company.
 * @param {list} params.referencePersons - list object reference Persons
 * @returns {Promise<Object>} The response data from the API call.
 */
const eligibleApi = async ({
  requestId,
  customerName,
  idNumber,
  otherIdNumber,
  issueDate,
  issuePlace,
  dateOfBirth,
  gender,
  phoneNumber,
  productType,
  registrationNumber,
  taxCode,
  companyName,
  registrationDate,
  productCode,
  caseCreationTime,
  partnerCode,
  channel,
  contractNumber,
  companyType,
  referencePersons,
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}${
      config.data.antiFraud.eligibleApi
    }`;
    const payload = {
      request_id: requestId,
      customer_name: customerName,
      id_number: idNumber,
      other_id_number: otherIdNumber,
      issue_date: issueDate,
      issue_place: issuePlace,
      date_of_birth: dateOfBirth,
      gender,
      phone_number: phoneNumber,
      product_type: productType,
      registration_number: registrationNumber,
      tax_code: taxCode,
      company_name: companyName,
      registration_date: registrationDate,
      product_code: productCode,
      case_creation_time: caseCreationTime,
      partner_code: partnerCode,
      channel: channel,
      contract_number: contractNumber,
      company_type: companyType,
      reference_persons: (referencePersons || []).map((e) => ({
        customer_name: e.customerName,
        id_number: e.idNumber,
      })),
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    // let errorData;
    const res = await common
      .postApiV2(url, payload, header)
      .then()
      .catch((err) => {
        // errorData = err?.response?.data || {};
        console.log(
          `[ANTI_FRAUD][eligibleApi] payload ${JSON.stringify(
            payload
          )} Error ${err}`
        );
      });

    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      SERVICE_NAME.ELIGIBLE,
      payload,
      res?.data
    );
    return res?.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][eligibleApi] requestId ${requestId} Error ${error}`
    );
  }
};

const eligibleApiCash = async ({
  request_id,
  partner_code,
  customer_name,
  phone_number,
  date_of_birth,
  contract_number,
  id_number,
  issue_date,
  issue_place,
  tem_province,
  gender,
  product_type,
  loan_amount,
  loan_tenor,
  channel,
  product_code,
  email,
  employment_type,
  sale_channel,
  dsa_agent_code,
  case_creation_time,
  is_insert,
  scan_data,
  reference_persons,
  s3_front_id_card_url,
  s3_back_id_card_url,
  s3_selfie_image_url,
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}${
      config.data.antiFraud.eligibleApiCash
    }`;

    const payload = {
      request_id,
      partner_code,
      channel: "CASE_CREATION",
      customer_name,
      phone_number,
      date_of_birth,
      contract_number,
      id_number,
      issue_date,
      issue_place,
      tem_province,
      gender,
      product_type,
      loan_amount,
      loan_tenor,
      sale_channel: sale_channel,
      case_creation_time: case_creation_time,
      product_code,
      email,
      employment_type,
      dsa_agent_code,
      is_insert: is_insert,
      scan_data,
      s3_front_id_card_url,
      s3_back_id_card_url,
      s3_selfie_image_url,
      reference_persons: (reference_persons || []).map((e) => ({
        customer_name: e.customerName,
        id_number: e.idNumber,
      })),
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    const res = await common
      .postApiV2(url, payload, header)
      .then()
      .catch((err) => {
        console.log(
          `[ANTI_FRAUD][eligibleApiCash] payload ${JSON.stringify(
            payload
          )} Error ${err}`
        );
      });

    loggingRepo.saveStepLog(
      contract_number,
      SERVICE_NAME.ANTI_FRAUD,
      SERVICE_NAME.ELIGIBLE,
      payload,
      res?.data
    );
    return res?.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][eligibleApiCash] requestId ${request_id} Error ${error}`
    );
  }
};

const checkFullLoanApi = async ({
  requestId,
  partnerCode,
  channel,
  contractNumber,
  contractNumberParent,
  slAdvanceContractType,
  custId,
  productType,
  riskGroup,
  productCode,
  productId,
  insuranceCode,
  customerName,
  gender,
  dateOfBirth,
  idNumber,
  issueDate,
  issuePlace,
  otherIdNumber,
  otherIdDateOfBirth,
  otherIdIssueDate,
  otherIdIssuePlace,
  phoneNumber,
  phoneTelco,
  email,
  disbursementMethod,
  accountNumber,
  bankCode,
  bankName,
  bankBranchCode,
  bankBranchName,
  beneficiaryName,
  temCountry,
  temProvince,
  temProvinceCode,
  temDistrict,
  temDistrictCode,
  temWard,
  temWardCode,
  temDetailAddress,
  permanentCountry,
  permanentProvince,
  permanentProvinceCode,
  permanentDistrict,
  permanentDistrictCode,
  permanentWard,
  permanentWardCode,
  permanentDetailAddress,
  nationality,
  reference1Type,
  reference2Type,
  relativeReferenceName1,
  relativeReferenceName2,
  relativeReferencePhone1,
  relativeReferencePhone2,
  otherContact,
  contactDetail,
  occupation,
  monthlyIncome,
  otherIncome,
  monthlyExpenses,
  companyName,
  companyAddress,
  companyCountry,
  companyProvince,
  companyDistrict,
  companyWard,
  contractFrom,
  contractTo,
  jobType,
  employmentType,
  employmentContractType,
  marriedStatus,
  houseType,
  otherHouseType,
  numOfDependents,
  yearsOfStay,
  incomeProof,
  salaryFrequency,
  salaryPaymentDate,
  taxId,
  loanPurpose,
  loanAmount,
  loanTenor,
  s3IdCardUrl,
  s3FrontIdCardUrl,
  s3BackIdCardUrl,
  s3SelfieImageUrl,
  s3VerifyIncomeImageUrls,
  phoneNumber2,
  phoneNumber3,
  education,
  custType,
  sameCities,
  countryPer,
  villageCur,
  villagePer,
  requestIntRate,
  requestInstalAmt,
  staffNumber,
  insuranceType,
  companyCode,
  companyTaxId,
  deviceId,
  deviceModel,
  deviceBrand,
  deviceOs,
  latitude,
  longitude,
  scanData,
  losType,
  referencePersons, // danh sach tham chieu
  step
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}${
      config.data.antiFraud.fullLoan
    }${partnerCode}`;

    const payload = {
      channel: channel || null,
      request_id: requestId || null,
      partner_code: partnerCode || null,
      contract_number: contractNumber || null,
      contract_number_parent: contractNumberParent || null,
      sl_advance_contract_type: slAdvanceContractType || null,
      cust_id: custId || null,
      los_type: losType,
      product_type: productType || null,
      product_id: productId || null,
      risk_group: riskGroup || null,
      product_code: productCode || null,
      insurance_code: insuranceCode || null,
      customer_name: customerName || null,
      gender: gender || null,
      date_of_birth: dateOfBirth || null,
      id_number: idNumber || null,
      issue_date: issueDate || null,
      issue_place: issuePlace || null,
      other_id_number: otherIdNumber || null,
      other_id_date_of_birth: otherIdDateOfBirth || null,
      other_id_issue_date: otherIdIssueDate || null,
      other_id_issue_place: otherIdIssuePlace || null,
      phone_number: phoneNumber || null,
      phone_telco: phoneTelco || null,
      email: email || null,
      disbursement_method: disbursementMethod || null,
      account_number: accountNumber || null,
      bank_code: bankCode || null,
      bank_name: bankName || null,
      bank_branch_code: bankBranchCode || null,
      bank_branch_name: bankBranchName || null,
      beneficiary_name: beneficiaryName || null,
      tem_country: temCountry || null,
      tem_province: temProvince || null,
      tem_province_code: temProvinceCode || null,
      tem_district: temDistrict || null,
      tem_district_code: temDistrictCode || null,
      tem_ward: temWard || null,
      tem_ward_code: temWardCode || null,
      tem_detail_address: temDetailAddress || null,
      permanent_country: permanentCountry || null,
      permanent_province: permanentProvince || null,
      permanent_province_code: permanentProvinceCode || null,
      permanent_district: permanentDistrict || null,
      permanent_district_code: permanentDistrictCode || null,
      permanent_ward: permanentWard || null,
      permanent_ward_code: permanentWardCode || null,
      permanent_detail_address: permanentDetailAddress || null,
      nationality: nationality || null,
      reference1_type: reference1Type || null,
      reference2_type: reference2Type || null,
      relative_reference_name1: relativeReferenceName1 || null,
      relative_reference_name2: relativeReferenceName2 || null,
      relative_reference_phone1: relativeReferencePhone1 || null,
      relative_reference_phone2: relativeReferencePhone2 || null,
      other_contact: otherContact || null,
      contact_detail: contactDetail || null,
      occupation: occupation || null,
      monthly_income: monthlyIncome,
      other_income: otherIncome,
      monthly_expenses: monthlyExpenses,
      company_name: companyName || null,
      company_address: companyAddress || null,
      company_country: companyCountry || null,
      company_province: companyProvince || null,
      company_district: companyDistrict || null,
      company_ward: companyWard || null,
      contract_from: contractFrom || null,
      contract_to: contractTo || null,
      job_type: jobType || null,
      employment_type: employmentType || null,
      employment_contract_type: employmentContractType || null,
      married_status: marriedStatus || null,
      house_type: houseType || null,
      other_house_type: otherHouseType || null,
      num_of_dependents: numOfDependents || null,
      years_of_stay: yearsOfStay || null,
      income_proof: incomeProof || null,
      salary_frequency: salaryFrequency || null,
      salary_payment_date: salaryPaymentDate || null,
      tax_id: taxId || null,
      loan_purpose: loanPurpose || null,
      loan_amount: loanAmount || null,
      loan_tenor: loanTenor || null,
      s3_id_card_url: s3IdCardUrl || null,
      s3_front_id_card_url: s3FrontIdCardUrl || null,
      s3_back_id_card_url: s3BackIdCardUrl || null,
      s3_selfie_image_url: s3SelfieImageUrl || null,
      s3_verify_income_image_urls: s3VerifyIncomeImageUrls || null,
      phone_number2: phoneNumber2 || null,
      phone_number3: phoneNumber3 || null,
      education: education || null,
      cust_type: custType || null,
      same_cities: sameCities || null,
      country_per: countryPer || null,
      village_cur: villageCur || null,
      village_per: villagePer || null,
      request_int_rate: requestIntRate || null,
      request_instal_amt: requestInstalAmt || null,
      staff_number: staffNumber || null,
      insurance_type: insuranceType || null,
      company_code: companyCode || null,
      company_tax_id: companyTaxId || null,
      device_id: deviceId || null,
      device_model: deviceModel || null,
      device_brand: deviceBrand || null,
      device_os: deviceOs || null,
      latitude: latitude || null,
      longitude: longitude || null,
      scan_data: scanData || null,
      agent_device_name: "",
      agent_device_id: "",
      agent_postal_address: "",
      agent_postal_code: "",
      agent_name: "",
      agent_code: "",
      case_creation_time: moment().format("YYYY-MM-DD HH:mm:ss"),
      reference_persons: (referencePersons || []).map((e) => ({
        customer_name: e.customerName,
        id_number: e.idNumber,
      })),
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    const res = await common
      .postApiV2(url, payload, header)
      .then()
      .catch((err) => {
        console.log(
          `[ANTI_FRAUD][checkFullLoanApi] payload ${JSON.stringify(
            payload
          )} Error ${err}`
        );
      });

    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      step,
      payload,
      res?.data,
      url
    );
    return res?.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkFullLoanApi] partnerCode ${partnerCode} Error ${error}`
    );
  }
};

const checkConsentApi = async ({ phoneNumber, phoneTelco }) => {
  try {
    const url = `${
      config.basic.antiFraud[[config.env]]
    }/anti_fraud/v1/telco_score/check_consent`;
    const payload = {
      phone_number: phoneNumber,
      phone_telco: phoneTelco || "",
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    // let errorData;
    const res = await common
      .postApiV2(url, payload, header)
      .then()
      .catch((err) => {
        // errorData = err?.response?.data || {};
        console.log(
          `[ANTI_FRAUD][checkConsentApi] payload ${JSON.stringify(
            payload
          )} Error ${err}`
        );
      });
    return res?.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkConsentApi] phoneNumber ${phoneNumber}, telco ${phoneTelco}, Error ${error}`
    );
  }
};

const ocrIdCardApi = async ({
  contractNumber,
  partnerCode,
  custId,
  s3FrontIdCardUrl,
  s3BackIdCardUrl,
  requestId,
  step
}) => {
  const url = `${
    config.basic.antiFraud[[config.env]]
  }/anti_fraud/v1/ekyc/ocr_id_card`;
  const payload = {
    contract_number: contractNumber,
    contract_number_parent: "",
    cust_id: "",
    phone_number: "",
    partner_code: partnerCode,
    s3_front_id_card_url: s3FrontIdCardUrl,
    s3_back_id_card_url: s3BackIdCardUrl,
  };
  let res;
  try {
    const header = {
      "Content-Type": "application/json",
      "request-id": requestId || uuid.v4(),
    };

    const rs = await common.postApiV2(url, payload, header);
    res = rs;
    if (rs?.data?.code !== "SUCCESS") {
      return;
    }
    return rs.data?.result;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][ocrIdCardApi] contractNumber ${contractNumber}, requestId ${requestId} error ${error}`
    );
    res = { error: error.message };
  } finally {
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      step,
      payload,
      res,
      url
    );
  }
};

const checkNfcApi = async ({
  request_id,
  contract_number,
  cust_id,
  id_number,
  step,
  s3_selfie_url,
  s3_front_id_card_url,
  s3_back_id_card_url,
  partner_code,
  ocr_data,
  nfc_data,
  is_check_c06,
}) => {
  let payload = {};
  let rs = {};
  try {
    const url = `${
      config.basic.antiFraud[[config.env]]
    }/anti_fraud/v1/ekyc/check-nfc`;
    payload = {
      request_id,
      is_check_c06,
      contract_number,
      cust_id: "",
      s3_selfie_url,
      partner_code,
      nfc_data,
      ocr_data,
      s3_front_id_card_url,
      s3_back_id_card_url,
      id_number,
      step,
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": request_id || uuid.v4(),
    };

    rs = await await common.postApiV2(url, payload, header);
    if (rs?.data?.code != 0)
      throw new Error(
        `${partner_code}, requestId ${request_id}, contractNumber ${contract_number}, payload ${JSON.stringify(
          payload
        )} checkNfcApi error`
      );
    rs = rs?.data; //data axios
    return rs?.data; //data object response
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkNfcApi] contractNumber ${contract_number}, requestId ${request_id} error ${error}`
    );
  } finally {
    loggingRepo.saveStepLog(
      contract_number,
      SERVICE_NAME.ANTI_FRAUD,
      !is_check_c06
        ? SERVICE_NAME.CHECK_NFC_WITHOUT_C06
        : SERVICE_NAME.CHECK_NFC_WITH_C06,
      payload,
      rs
    );
  }
};

const checkEkycApi = async ({
  requestId,
  contractNumber,
  partnerCode,
  selfiePath,
  idCardFrontPath,
  idCardBackPath,
  nfcPath,
  step, 
  idNumber, 
  custFullName
}) => {
  let rs = {};
  let payload = {};
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/ekyc/check_ekyc`;
  try {
    payload = {
      case_id: requestId || "",
      contract_number: contractNumber || "",
      channel: "LOS",
      product_type: "CASH",
      partner_code: partnerCode || "",
      files: {
        selfie: selfiePath || "",
        id_card: idCardFrontPath || "",
        nfc: nfcPath || "",
        id_card_front: idCardFrontPath || "",
        id_card_back: idCardBackPath || "",
      },
      metadata: {
        id_number: idNumber,
        name: custFullName
      },
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };

    rs = await common.postApiV2(url, payload, header);
    if (!rs?.data)
      throw new Error(
        `${partnerCode}, requestId ${requestId}, contractNumber ${contractNumber}, payload ${JSON.stringify(
          payload
        )} checkNfcApi error`
      );
    rs = rs.data;
    return rs;
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkEkycApi] error ${error}`);
    rs = { error: error.message };
  } finally {
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      step,
      payload,
      rs,
      url
    );
  }
};

/**
 *
 * @param {*} persons: danh sách cá nhân check
 * @param {*} enterprises: thông tin doanh nghiệp
 * @return kết quả check b11t , ELIGIBLE và NOT_ELIGIBLE
 */
const checkCicB11tApi = async ({
  contractNumber,
  partnerCode,
  custId,
  persons,
  stepCheck = CIC_STEP_CHECK.AF1,
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/cic/b11t/check`;
    const payload = {
      request_id: uuid.v4(),
      contract_number: contractNumber,
      cust_id: custId,
      partner_code: partnerCode,
      persons: persons.map((e) => ({
        id_number: e.idNumber,
        other_id_number: e.otherIdNumber ?? "",
        full_name: e.fullName ?? "",
        address: e.address,
      }))
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
    };
    let errorData;
    const res = await common
      .postAPI(url, payload, header)
      .then()
      .catch((err) => {
        errorData = err?.response?.data || {};
        console.log(
          `[ANTI_FRAUD][checkB11tApi] payload ${JSON.stringify(
            payload
          )} Error ${err}: ${JSON.stringify(errorData || {})}`
        );
      });

    const insertBody = {
      contract_number: contractNumber,
      step: stepCheck,
      request_payload: payload,
      response_payload: res || errorData,
    };
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      SERVICE_NAME.B11T,
      payload,
      res || errorData,
      url
    );
    await sqlHelper.insertData(
      `loan_cic_log`,
      loanCicLogRepo.columns,
      sqlHelper.generateValues(insertBody, loanCicLogRepo.columns)
    );
    return res;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkB11tApi] contractNumber ${contractNumber} Error ${error}`
    );
  }
};

/**
 *
 * @param {*} persons: danh sách cá nhân check
 * @return kết quả check b11t , ELIGIBLE và NOT_ELIGIBLE
*/
const checkCicB11tInvidualApi = async ({
  contractNumber,
  persons,
  stepCheck = CIC_STEP_CHECK.AF1,
  partnerCode,
}) => {
  try {
    const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/cic/b11t/check`;
    const payload = {
      request_id: uuid.v4(),
      contract_number: contractNumber,
      partner_code: partnerCode,
      persons: persons.map((e) => ({
        id_number: e.id_number,
        other_id_number: e.other_id_number ?? "",
        full_name: e.full_name ?? "",
        address: e.address,
      }))
    };
    const header = {
      "Content-Type": "application/json",
      "request-id": uuid.v4(),
      "flow": "CHECK_B11T",
    };
    let errorData;
    const res = await common
      .postAPI(url, payload, header)
      .then()
      .catch((err) => {
        errorData = err?.response?.data || {};
        console.log(
          `[ANTI_FRAUD][checkB11tApi] payload ${JSON.stringify(
            payload
          )} Error ${err}: ${JSON.stringify(errorData || {})}`
        );
      });

    const insertBody = {
      contract_number: contractNumber,
      step: stepCheck,
      request_payload: payload,
      response_payload: res || errorData,
    };
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      SERVICE_NAME.B11T,
      payload,
      res || errorData,
      url
    );
    await sqlHelper.insertData(
      `loan_cic_log`,
      loanCicLogRepo.columns,
      sqlHelper.generateValues(insertBody, loanCicLogRepo.columns)
    );
    return res;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkB11tApi] contractNumber ${contractNumber} Error ${error}`
    );
  }
};

/**
 * @description: Check blacklist for SME
 * @param {*} payload - contains request_id, partner_code, phone_number, contract_number, id_number, tax_code, email
*/
const checkBlacklistApi = async (payload) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/eligible/check-blacklist`;
  const header = {
    "Content-Type": "application/json",
    "request-id": payload.request_id,
  };
  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(
    payload.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    TASK_FLOW.CHECK_BLACK_LIST,
    payload,
    response?.data || {},
    url
  );
  return response;
};

/**
 * @description: Get elegible status for SME contract
 * @param {*} contract_number
 * @param {*} request_id
 * @param {*} partner_code
 * @param {*} tax_code
 * @param {*} stepName - default is TASK_FLOW.CHECK_CONTRACT_RENEW_DATE
*/
const checkEligibleStatusApi = async (payload,step_name) => {
  let url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/eligible/sme/check-contract-status`;
  const header = {
    "Content-Type": "application/json",
    "request-id": payload?.request_id,
  };

  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(payload.contract_number, SERVICE_NAME.ANTI_FRAUD,step_name, payload, response?.data || {}, url);
  return response;
};

/**
 * @description: Get renew date for SME contract
 * @param {*} contract_number
 * @param {*} request_id
 * @param {*} partner_code
 * @param {*} tax_code
 * @param {*} stepName - default is TASK_FLOW.CHECK_CONTRACT_RENEW_DATE
*/
const getRenewDateApi = async (params, step_name = TASK_FLOW.CHECK_CONTRACT_RENEW_DATE) => {
  let url;
  if (params.tax_code) {
    url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/decisions/sme/contract-renew-date`;
  } else if (params.identity_card) {
    url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/decisions/contract-renew-date`;
  }
  const header = {
    "Content-Type": "application/json",
    "request-id": params.request_id,
  };
  const response = await common.getApiV2(url, header, params);
  loggingRepo.saveStepLog(
    params.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    step_name,
    params,
    response?.data || {},
    url
  );
  return response;
};

/**
 * @description: Check CIC B11 SME
 * @param {*} request_id
 * @param {*} contract_number
 * @param {*} partner_code
 * @param {*} tax_code
*/
const checkCicB11SmeApi = async (payload) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/cic/b11t/check`;
  const header = {
    "Content-Type": "application/json",
    "flow": "CHECK_S37",
    "request-id": payload.request_id,
  };
  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(
    payload.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    TASK_FLOW.CHECK_CIC_B11T_SME,
    payload,
    response?.data || {},
    url
  );
  return response;
};

const checkWhitelistFullLoanApi = async ({
  request_id,
  partner_code,
  tax_code,
  contract_number,
  contract_type,
  business_operating_time,
  platform_usage_time,
  anchor_transaction_time,
  tax_report_revenue,
  fee,
  on_term_interest,
  grace_interest,
  prepayment,
  multiplier,
  repayment_period,
  last_3m_transaction_volume,
  request_limit
}) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v2/eligible/sme/whitelist/full-loan`;
  const header = {
    "Content-Type": "application/json",
    "request-id": request_id,
  };

  let payload = {
    request_id,
    partner_code,
    tax_code,
    contract_number,
    contract_type,
    business_operating_time,
    platform_usage_time,
    anchor_transaction_time,
    tax_report_revenue,
    fee,
    on_term_interest,
    grace_interest,
    prepayment,
    multiplier,
    repayment_period,
    last_3m_transaction_volume,
    request_limit
  }

  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(
    payload.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    TASK_FLOW.CHECK_WHITELIST_FULL_LOAN,
    payload,
    response?.data || {},
    url
  );
  return response;
};

const checkWhitelistHmFullLoanApi = async ({ request_id, partner_code, tax_code, contract_number, contract_type, business_operating_time, platform_usage_time, anchor_transaction_time, tax_report_revenue, fee, on_term_interest, grace_interest, prepayment, multiplier, repayment_period, request_limit, profit_pre_year }) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v2/eligible/sme/whitelist/full-loan`;
  const header = {
    "Content-Type": "application/json",
    "request-id": request_id,
  };

  let payload = {
    request_id,
    partner_code,
    tax_code,
    contract_number,
    contract_type,
    business_operating_time,
    platform_usage_time,
    anchor_transaction_time,
    tax_report_revenue,
    fee,
    on_term_interest,
    grace_interest,
    prepayment,
    multiplier,
    repayment_period,
    request_limit,
    profit_pre_year,
  };

  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(payload.contract_number, SERVICE_NAME.ANTI_FRAUD, TASK_FLOW.CHECK_WHITELIST_FULL_LOAN, payload, response?.data || {}, url);
  return response;
};

const checkModelApi = async ({
  request_id,
  contract_number,
  partner_code,
  tax_code,
  report_type,
  management_experience,
  number_of_staffs,
  conditional_business_industry,
  business_operating_time,
  platform_usage_time,
  report_data
}) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/decisions/sme/finance-model`;
  const header = {
    "Content-Type": "application/json",
    "request-id": request_id,
  };

  let payload = {
    contract_number,
    request_id,
    partner_code,
    tax_code,
    report_type,
    management_experience,
    number_of_staffs,
    conditional_business_industry,
    business_operating_time,
    platform_usage_time,
    report_data
  }

  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(
    payload.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    TASK_FLOW.CHECK_MODEL,
    payload,
    response?.data || {},
    url
  );
  return response;
};

const checkModelHmApi = async ({ request_id, contract_number, partner_code, tax_code, report_type, management_experience, number_of_staffs, conditional_business_industry, business_operating_time, platform_usage_time, report_data, request_limit, expected_revenue }) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/decisions/sme/finance-model`;
  const header = {
    "Content-Type": "application/json",
    "request-id": request_id,
  };

  let payload = {
    contract_number,
    request_id,
    partner_code,
    tax_code,
    report_type,
    management_experience,
    number_of_staffs,
    conditional_business_industry,
    business_operating_time,
    platform_usage_time,
    report_data,
    request_limit,
    expected_revenue,
  };

  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(payload.contract_number, SERVICE_NAME.ANTI_FRAUD, TASK_FLOW.CHECK_MODEL, payload, response?.data || {}, url);
  return response;
};

const checkModelFinvApi = async ({
  request_id,
  partner_code,
  contract_number,
  product_code,
  time_duration,
  total_purchase_amount,
  request_loan_amount,
}) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/eligible/finv/model`;
  const header = {
    "Content-Type": "application/json",
    "request-id": request_id,
  };

  let payload = {
    request_id,
    partner_code,
    contract_number,
    product_code,
    time_duration,
    total_purchase_amount,
    request_loan_amount,
  }

  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(
    payload.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    TASK_FLOW.CHECK_MODEL,
    payload,
    response?.data || {},
    url
  );
  return response;
};

const checkWhitelistAf1Api = async ({
  request_id,
  partner_code,
  tax_code,
  contract_number,
  contract_type,
  platform_usage_time,
  anchor_transaction_time
}) => {
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v2/eligible/sme/whitelist/basic`;
  const header = {
    "Content-Type": "application/json",
    "request-id": request_id,
  };
  let payload = {
    request_id,
    partner_code,
    tax_code,
    contract_number,
    contract_type,
    platform_usage_time,
    anchor_transaction_time
  }
  const response = await common.postApiV2(url, payload, header);
  loggingRepo.saveStepLog(
    payload.contract_number,
    SERVICE_NAME.ANTI_FRAUD,
    TASK_FLOW.CHECK_WHITELIST_AF1,
    payload,
    response?.data || {},
    url
  );
  return response;
};

/**
 * Check contract progress API
 * @param {Object} params - The parameters for the API call
 * @param {string} params.requestId - The unique identifier for the request
 * @param {string} params.partnerCode - The partner code
 * @param {string} params.customerName - The name of the customer
 * @param {string} params.phoneNumber - The phone number of the customer
 * @param {string} params.dateOfBirth - The date of birth of the customer
 * @param {string} params.contractNumber - The contract number
 * @param {string} params.idNumber - The ID number of the customer
 * @param {string} params.issueDate - The issue date of the ID
 * @param {string} params.issuePlace - The place where the ID was issued
 * @param {string} params.gender - The gender of the customer
 * @param {string} params.contractType - The type of contract
 * @param {string} step - Step name
 * @returns {Promise<Object>} The response data from the API call
 */
const checkContractProgressApi = async (
  {
    requestId,
    partnerCode,
    customerName,
    phoneNumber,
    dateOfBirth,
    contractNumber,
    idNumber,
    issueDate,
    issuePlace,
    gender,
    contractType,
  },
  step
) => {
  let rs;
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/eligible/mw/check-in-progress`;

  const payload = {
    request_id: requestId,
    partner_code: partnerCode,
    customer_name: customerName,
    phone_number: phoneNumber,
    date_of_birth: dateOfBirth,
    contract_number: contractNumber,
    id_number: idNumber,
    issue_date: issueDate,
    issue_place: issuePlace || "",
    gender,
    contract_type: contractType
  };
  try {
    const header = {
      "Content-Type": "application/json",
    };

    const res = await common.postApiV2(url, payload, header);
    if (!res.data) {
      throw new Error(
        `Call checkContractProgressApi contractNumber ${contractNumber} error ${res?.message}`
      );
    }
    rs = res.data;
    return res.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkContractProgressApi] requestId ${requestId} Error ${error}`
    );
    rs = { error: error.message };
    return rs;
  } finally {
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      step,
      payload,
      rs,
      url
    );
  }
};

/**
 * Check contract activated API
 * @param {Object} params - The parameters for the API call
 * @param {string} params.requestId - The unique identifier for the request
 * @param {string} params.partnerCode - The partner code
 * @param {string} params.customerName - The name of the customer
 * @param {string} params.phoneNumber - The phone number of the customer
 * @param {string} params.dateOfBirth - The date of birth of the customer
 * @param {string} params.contractNumber - The contract number
 * @param {string} params.idNumber - The ID number of the customer
 * @param {string} params.issueDate - The issue date of the ID
 * @param {string} params.issuePlace - The place where the ID was issued
 * @param {string} params.gender - The gender of the customer
 * @param {string} params.contractType - The type of contract
 * @param {string} step - Step name
 * @returns {Promise<Object>} The response data from the API call
 */
const checkContractActiveApi = async (
  {
    requestId,
    partnerCode,
    customerName,
    phoneNumber,
    dateOfBirth,
    contractNumber,
    idNumber,
    issueDate,
    issuePlace,
    gender,
    contractType,
  },
  step
) => {
  let rs;
  const url = `${config.basic.antiFraud[[config.env]]}/anti_fraud/v1/eligible/mw/check-in-activated`;

  const payload = {
    request_id: requestId,
    partner_code: partnerCode,
    customer_name: customerName,
    phone_number: phoneNumber,
    date_of_birth: dateOfBirth,
    contract_number: contractNumber,
    id_number: idNumber,
    issue_date: issueDate,
    issue_place: issuePlace || "",
    gender,
    contract_type: contractType
  };
  try {
    const header = {
      "Content-Type": "application/json",
    };

    const res = await common.postApiV2(url, payload, header);
    if (!res.data) {
      throw new Error(
        `Call checkContractActiveApi contractNumber ${contractNumber} error ${res?.message}`
      );
    }
    rs = res.data;
    return res.data;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkContractActiveApi] requestId ${requestId} Error ${error}`
    );
    rs = { error: error.message };
    return rs;
  } finally {
    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      step,
      payload,
      rs,
      url
    );
  }
};

module.exports = {
  eligibleApi,
  s37Api,
  cicDetailsApi,
  eligibleApiV2,
  checkConsentApi,
  checkFullLoanApi,
  eligibleApiCash,
  ocrIdCardApi,
  checkNfcApi,
  checkBlacklistApi,
  getRenewDateApi,
  checkEligibleStatusApi,
  checkCicB11SmeApi,
  checkEkycApi,
  checkCicB11tApi,
  checkWhitelistFullLoanApi,
  checkWhitelistHmFullLoanApi,
  checkModelApi,
  checkModelHmApi,
  checkWhitelistAf1Api,
  checkCicB11tInvidualApi,
  checkModelFinvApi,
  checkContractProgressApi,
  checkContractActiveApi,
};
