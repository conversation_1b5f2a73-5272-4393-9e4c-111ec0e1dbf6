const { SERVICE_NAME } = require("../const/definition");
const { saveStepLog } = require("../repositories/logging-repo");
const common = require("../utils/common");
const { STEP } = require("../const/variables-const");
const moment = require("moment");
const { snakeToCamel } = require("../utils/helper");

const generateFileApi = async ({ contractNumber, payload, isSaveLog = true }) => {
  let rs;
  const url = `${config.basic.convertService[[config.env]]}/convert-service/v1/convert/add-task`;
  try {
    rs = await common.postApiV2(
      url,
      payload,
      { "Content-Type": "application/json" },
      {
        isLog: true,
        contractNumber: contractNumber,
        requestType: "GENERAL_FILE",
      }
    );
    console.log(`[CONVERT_SERVICE][API][generateFile] contractNumber ${contractNumber} res: ${JSON.stringify(rs?.data ?? {})}`);
    rs = rs?.data;
    return rs;
  } catch (error) {
    console.log(`[CONVERT_SERVICE][API][generateFile] contractNumber: ${contractNumber}, error ${error.message}`);
    rs = error.data || error.message;
    return null;
  } finally {
    if (isSaveLog) {
      await saveStepLog(contractNumber, SERVICE_NAME.CONVERT_SERVICE, STEP.GENERAL_FILE, payload, rs, url);
    }
  }
};

const addTaskConvertFile = async (body) => {
  const path = "/convert-service/v1/convert/add-task";
  const config = global.config;
  const url = `${config.basic.convertService[[config.env]]}${path}`;
  try {
    const response = await common.postApiTimeout({url, data: body});
    return response?.data;
  } catch (error) {
    console.log(`[CONVERT_SERVICE][API][addTaskConvertFile] error: ${error.message}`);
    return null;
  }
}

module.exports = {
  generateFileApi,
  addTaskConvertFile
};
