const { SERVICE_NAME } = require("../const/definition");
const { saveStepLog } = require("../repositories/logging-repo");
const common = require("../utils/common");
const { STEP } = require("../const/variables-const");
const moment = require("moment");
const { snakeToCamel } = require("../utils/helper");
const { authentApi } = require("./aaa.api");

const checkNfcCacheApi = async ({ idNumber, issueDate}) => {
  let rs;
  const url = `${
    config.basic.crmService[[config.env]]
  }/crm/v1/customer-c06-log/check?id_number=${idNumber}&issue_date=${issueDate}`;
  try {
    // const { token, uiid } = await authentApi();
    rs = await common.getApiV2(url);
    console.log(
      `[CRM][API][checkNfcCacheApi] idNumber ${idNumber}, issueDate ${issueDate}  res: ${JSON.stringify(
        rs?.data ?? {}
      )}`
    );
    if(rs?.data?.code != 0)
    {
        throw new Error(`Check c06 cache idNumber ${idNumber} fail`);
    }
    return rs.data.data;
  } catch (error) {
    console.log(
      `[CRM][API][checkNfcCacheApi] idNumber: ${idNumber}, issueDate ${issueDate} error ${error.message}`
    );
    return null;
  }
};



module.exports = {
    checkNfcCacheApi
}