const common = require("../utils/common");
const checkBankAccountInfoApi = async ({
  kunnId,
  bankCode,
  customerName,
  accountNumber,
}) => {
  const payload = {
    bankCode,
    customerName,
    accountNumber,
  };
  try {
    const url =
      global.config.basic.disbursement[config.env] +
      global.config.data.disbursementService.checkAccountInfoApi;
    const rs = await common.postApiV2(url, payload);
    return rs?.data?.data;
  } catch (err) {
    console.log(
      `[DISBURSEMENT][API][checkBankAccountInfoApi] kunnId: ${kunnId}, payload: ${JSON.stringify(
        payload
      )}, error ${err}`
    );
  }
};

module.exports = {
  checkBankAccountInfoApi,
};
