const { SERVICE_NAME } = require("../const/definition");
const { saveStepLog } = require("../repositories/logging-repo");
const common = require("../utils/common");
const { STEP } = require("../const/variables-const");
const moment = require("moment");

const checkAvailableAmountApi = async (contractNumber) => {
  try {
    const body = {
      contractNumber: contractNumber,
    };
    const checkAvailableUrl =
      global.config.basic.lmsMc[config.env] +
      global.config.data.lms.checkAvailable;
    const availableRs = await common.postApiV2(checkAvailableUrl, body);

    return availableRs?.data?.data;
  } catch (error) {
    console.log(
      `[LMS][API][checkAvailableAmountApi] contractNumber: ${contractNumber}, error ${error.message}`
    );
    return null;
  }
};

const authApi = async (lmsUri) => {
  const url = global.config.data.app.urlAuthentSingIn;
  const bodySignIn = {
    username: global.config.data.lms.lmsUsername,
    password: global.config.data.lms.lmsPassword,
  };
  const SignInUrl = global.config.basic.aaa[global.config.env] + url;
  const result = await common.postAPI(SignInUrl, bodySignIn);
  const lmsHeader = {
    uiid: result.uiid,
    token: result.token,
    service: lmsUri,
  };
  return lmsHeader;
};

const cancelKunnApi = async (debtContractNumber) => {
  try {
    const header = await authApi(global.config.data.lms.checkAvailable);
    const body = {
      debtAckContractNumber: debtContractNumber,
      cancelDate: moment().format("YYYY-MM-DD"),
    };
    const url =
      global.config.basic.lmsMc[config.env] + global.config.data.lms.cancelDebt;
    const availableRs = await common.postApiV2(url, body, {
      ...header,
      "Content-Type": "application/json",
    });
    if (availableRs?.data?.code == 0) {
      return availableRs.data.data;
    }
    return null;
  } catch (error) {
    console.log(
      `[LMS][API][cancelKunnApi] debtContractNumber: ${debtContractNumber}, error ${error}`
    );
    return null;
  }
};
const getKunnInstallmentApi = async (contractNumber) => {
  try {
    let url = `${
      global.config.basic.lmsMc[config.env] + global.config.data.lms.installment
    }?contractNumber=${contractNumber}`;
    //url = "http://localhost:1028/lms-mc/v1/installment/get?debtAckContractNumber=2200009796";
    const installment = await common.getApiV2(url);
    // console.log(JSON.stringify(installment));
    if (installment?.data?.code == 0) {
      return installment.data.data;
    }
    return null;
  } catch (error) {
    console.log(
      `[LMS][API][getKunnScheduleApi] ${debtContractNumber}, error ${err}`
    );
    return null;
  }
};

const getKunnInstallmentByKunnApi = async (kunnId) => {
  try {
    const url = `${
      global.config.basic.lmsMc[config.env] +
      global.config.data.lms.installmentByKunn
    }?debtAckContractNumber=${kunnId}`;
    const installment = await common.getApiV2(url);
    if (installment?.data?.code == 0) {
      return installment.data.data;
    }
    return null;
  } catch (error) {
    console.log(
      `[LMS][API][getKunnInstallmentByKunnApi]: ${debtContractNumber}, error ${error.message}`
    );
    return null;
  }
};

const getKunnInstallmentByKunnApiV2 = async (kunnId) => {
  try {
    const url = `${
        global.config.basic.lmsMc[config.env] +
        global.config.data.lms.installmentByKunn
    }?debtAckContractNumber=${kunnId}`;
    const installment = await common.getApiV2(url);
    if (installment?.data?.code == 0) {
      return installment.data;
    }
    return null;
  } catch (error) {
    console.log(
        `[LMS][API][getKunnInstallmentByKunnApiV2]: ${kunnId}, error ${error.message}`
    );
    return null;
  }
};

const createDebtApi = async (kunnId, payload) => {
  let rs;
  let lmsUrl;
  try {
    const lmsUri = "/lms-mc/v1/debt-ack-contract/create";
    lmsUrl = `${config.basic.lmsMc[config.env]}${lmsUri}`;
    const header = await authApi(lmsUri);
    const lmsRs = await common.postApiV2(lmsUrl, payload, header, {
      isLog: true,
      contractNumber: kunnId,
      requestType: "CREATE_KUNN_LMS",
    });
    console.log(
      `[LMS][API] createDebtApi kunn ${kunnId} res: ${JSON.stringify(
        lmsRs?.data ?? {}
      )}`
    );
    rs = lmsRs?.data;
    return rs;
  } catch (error) {
    console.log(
      `[LMS][API][createDebtApi] createDebtApi: ${kunnId}, error ${error.message}`
    );
    rs = error.data ||  error.message;
    return null;
  } finally {
    await saveStepLog(
      kunnId,
      SERVICE_NAME.LMS,
      STEP.CREATE_LOAN_ACT,
      payload,
      rs,
      lmsUrl
    );
  }
};

const createBizziKunn = async (kunnId, payload) => {
  let rs;
  let lmsUrl;
  try {
    const lmsUri = "/lms-mc/v1/debt-ack-contract/create-factoring";
    lmsUrl = `${config.basic.lmsMc[config.env]}${lmsUri}`;
    const header = await authApi(lmsUri);
    const lmsRs = await common.postApiV2(lmsUrl, payload, header, {
      isLog: true,
      contractNumber: kunnId,
      requestType: "CREATE_KUNN_LMS",
    });
    console.log(
      `[LMS][API] createDebtApi kunn ${kunnId} res: ${JSON.stringify(
        lmsRs?.data ?? {}
      )}`
    );
    rs = lmsRs?.data;
    return rs;
  } catch (error) {
    console.log(
      `[LMS][API][createDebtApi] createDebtApi: ${kunnId}, error ${error.message}`
    );
    rs = error.data ||  error.message;
    return null;
  } finally {
    await saveStepLog(
      kunnId,
      SERVICE_NAME.LMS,
      STEP.CREATE_LOAN_ACT,
      payload,
      rs,
      lmsUrl
    );
  }
};

const createDebtApiV3 = async (kunnId, payload) => {
  let rs;
  let lmsUrl;
  try {
    const lmsUri = "/lms-mc/v3/debt-ack-contract/create";
    lmsUrl = `${config.basic.lmsMc[config.env]}${lmsUri}`;
    const header = await authApi(lmsUri);
    const lmsRs = await common.postApiV2(lmsUrl, payload, header, {
      isLog: true,
      contractNumber: kunnId,
      requestType: "CREATE_KUNN_LMS",
    });
    console.log(
      `[LMS][API] createDebtApi kunn ${kunnId} res: ${JSON.stringify(
        lmsRs?.data ?? {}
      )}`
    );
    rs = lmsRs?.data;
    return rs;
  } catch (error) {
    console.log(
      `[LMS][API][createDebtApi] createDebtApi: ${kunnId}, error ${error.message}`
    );
    rs = error.data ||  error.message;
    return null;
  } finally {
    await saveStepLog(
      kunnId,
      SERVICE_NAME.LMS,
      STEP.CREATE_LOAN_ACT,
      payload,
      rs,
      lmsUrl
    );
  }
};

const lockLimitApi = async (contractNumber) => {
  try {
    const body = {
      contractNumber: contractNumber,
    };
    const lockLimitUrl =
      global.config.basic.lmsMc[config.env] +
      "/lms-mc/v2/merchant-limit/lock";
    const lockRs = await common.postApiV2(lockLimitUrl, body);

    return lockRs?.data;
  } catch (error) {
    console.log(
      `[LMS][API][lockLimitApi] contractNumber: ${contractNumber}, error ${error.message}`
    );
    return null;
  }
};


const checkRemainPrinAmountApi = async (custId, partnerCode) => {
  try {
    // const params = {
    //   custId: custId,
    //   partnerCode: partnerCode,
    // };
    const checkRemainPrinAmountUrl =
      `${global.config.basic.lmsMc[config.env]}/lms-mc/v1/installment/prin-amount?custId=${custId}&partnerCode=${partnerCode}`;
    const rs = await common.getApiV2(checkRemainPrinAmountUrl);

    return rs?.data?.data;
  } catch (error) {
    console.log(
      `[LMS][API][checkRemainPrinAmountApi] custId: ${custId}, partnerCode: ${partnerCode}, error ${error.message}`
    );
    return null;
  }
};

module.exports = {
  checkAvailableAmountApi,
  cancelKunnApi,
  getKunnInstallmentApi,
  createDebtApi,
  createDebtApiV3,
  getKunnInstallmentByKunnApi,
  getKunnInstallmentByKunnApiV2,
  createBizziKunn,
  lockLimitApi,
  checkRemainPrinAmountApi
};
