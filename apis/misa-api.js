const {
  decryptDataMisa,
  encryptDataMisa,
} = require("../utils/encrypt/encrypt");
const common = require("../utils/common");
const loggingService = require("../utils/loggingService");
const querystring = require("querystring");
const { PARTNER_CODE, SERVICE_NAME } = require("../const/definition");
const { saveStepLog } = require("../repositories/logging-repo");
const { STEP } = require("../const/variables-const");

const callbackKunnCicApi = async (kunnId, payload) => {
  const url = global.config.data.partnerCallback.misaCicResultKunnUrl;
  let rs;
  try {
    const token = await getToken();
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "text/plain",
    };
    const reqLog = JSON.stringify(payload);
    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    console.log(
      `[MISA][KUNN][V2] callbackKunnCicApi ${kunnId} response ${response?.data}`
    );
    const respPayload =
      response?.status == 200
        ? await decryptDataMisa(response?.data)
        : response || {};
    rs = respPayload;
    loggingService.saveRequestV2(
      global.poolWrite,
      reqLog,
      JSON.stringify(respPayload),
      kunnId,
      null,
      PARTNER_CODE.MISA,
      "CALLBACK_KUNN_CIC",
      url
    );
    return true;
  } catch (err) {
    console.log(
      `[MISA][API][callbackKunnCicApi] kunnId: ${kunnId},payload ${JSON.stringify(
        payload
      )} error ${err}`
    );
    rs = err.data ||  err.message;
    return null;
  } finally {
    await saveStepLog(
      kunnId,
      SERVICE_NAME.EXTERNAL,
      STEP.CALLBACK_CIC_KUNN,
      payload,
      rs,
      url
    );
  }
};

const callbackKunnActApi = async (kunnId, payload, token) => {
  const url = global.config.data.partnerCallback.misaResultKunnActUrl;
  let rs;
  try {
    if (!token) token = await getToken();
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "text/plain",
    };
    const reqLog = JSON.stringify(payload);
    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    console.log(
      `[MISA][KUNN][V2] callbackKunnActApi ${kunnId} response ${response?.data}`
    );
    const respPayload = response?.data
      ? await decryptDataMisa(response?.data)
      : response || {};
    rs = respPayload;
    loggingService.saveRequestV2(
      global.poolWrite,
      reqLog,
      JSON.stringify(respPayload),
      kunnId,
      null,
      PARTNER_CODE.MISA,
      "CALLBACK_KUNN_ACT",
      url
    );
    return respPayload;
  } catch (err) {
    console.log(
      `[MISA][API][callbackKunnActApi] kunnId: ${kunnId},payload ${JSON.stringify(
        payload
      )} error ${err}`
    );
    rs = err.data ||  err.message;
    return null;
  } finally {
    await saveStepLog(
      kunnId,
      SERVICE_NAME.EXTERNAL,
      STEP.CALLBACK_ACT_KUNN,
      payload,
      rs,
      url
    );
  }
};

const callbackKunnCancelApi = async (kunnId, payload, token) => {
  try {
    const url = global.config.data.partnerCallback.misaCancelUrl;
    if (!token) token = await getToken();
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "text/plain",
    };
    const reqLog = JSON.stringify(payload);
    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    console.log(
      `[MISA][KUNN][V2] callbackKunnCancelApi ${kunnId} response ${response?.data}`
    );
    const respPayload = response?.data
      ? await decryptDataMisa(response?.data)
      : response || {};
    loggingService.saveRequestV2(
      global.poolWrite,
      reqLog,
      JSON.stringify(respPayload),
      kunnId,
      null,
      PARTNER_CODE.MISA,
      "CALLBACK_KUNN_CANCEL",
      url
    );
    return true;
  } catch (err) {
    console.log(
      `[MISA][API][callbackKunnCancelApi] kunnId: ${kunnId},payload ${JSON.stringify(
        payload
      )} error ${err}`
    );
    return null;
  }
};

const getToken = async () => {
  try {
    const url = global.config.data.partnerCallback.misaTokenUrlV2;
    const form = {
      grant_type: global.config.data.partnerCallback.misaTokenGrantType,
      client_id: global.config.data.partnerCallback.misaTokenClientId,
      client_secret: global.config.data.partnerCallback.misaTokenClientSecret,
    };
    const formBody = querystring.stringify(form);

    const response = await common.postApiV2(url, formBody, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });
    return response?.status === 200 ? response?.data?.access_token : undefined;
  } catch (e) {
    console.error(e);
    return undefined;
  }
};

const callbackLimitApi = async (contractNumber, payload) => {
  const url = global.config.data.partnerCallback.misaUpdateLimitApi;
  let rs;
  try {
    const token = await getToken();
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "text/plain",
    };
    const reqLog = JSON.stringify(payload);
    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    console.log(
      `[MISA][KUNN][V2][callbackLimitApi] ${contractNumber} response ${response?.data}`
    );
    const respPayload =
      response?.status == 200
        ? await decryptDataMisa(response?.data)
        : response || {};
    rs = respPayload;
    loggingService.saveRequestV2(
      global.poolWrite,
      reqLog,
      JSON.stringify(respPayload),
      contractNumber,
      null,
      PARTNER_CODE.MISA,
      "CALLBACK_KUNN_LIMIT",
      url
    );
    return respPayload;
  } catch (err) {
    console.log(
      `[MISA][API][callbackLimitApi] contractNumber or Kunn: ${contractNumber},payload ${JSON.stringify(
        payload
      )} error ${err}`
    );
    rs = err.data ||  err.message;
    return null;
  } finally {
    await saveStepLog(
      contractNumber,
      SERVICE_NAME.EXTERNAL,
      STEP.CALLBACK_KUNN_LIMIT,
      payload,
      rs,
      url
    );
  }
};


const callbackReceivedPaymentApi = async (kunnId, payload) => {
  const url = global.config.data.partnerCallback.misaReceivedPaymentUrl;
  let rs;
  try {
    const token = await getToken();
    const headers = {
      Authorization: `Bearer ${token}`,
      "Content-Type": "text/plain",
    };
    const reqLog = JSON.stringify(payload);
    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    console.log(
      `[MISA][KUNN][V2][callbackReceivedPaymentApi] ${kunnId} response ${response?.data}`
    );
    const respPayload =
      response?.status == 200
        ? await decryptDataMisa(response?.data)
        : response || {};
    rs = respPayload;
    loggingService.saveRequestV2(
      global.poolWrite,
      reqLog,
      JSON.stringify(respPayload),
      kunnId,
      null,
      PARTNER_CODE.MISA,
      "CALLBACK_RECEIVED_PAYMENT",
      url
    );
    return respPayload;
  } catch (err) {
    console.log(
      `[MISA][API][callbackReceivedPaymentApi] kunnId: ${kunnId},payload ${JSON.stringify(
        payload
      )} error ${err}`
    );
    rs = err.data ||  err.message;
    return null;
  } finally {
    await saveStepLog(
      kunnId,
      SERVICE_NAME.EXTERNAL,
      STEP.CALLBACK_RECEIVED_PAYMENT,
      payload,
      rs,
      url
    );
  }
};

module.exports = {
  callbackKunnCicApi,
  callbackKunnActApi,
  callbackLimitApi,
  callbackKunnCancelApi,
  callbackReceivedPaymentApi
};
