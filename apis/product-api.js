const common = require("../utils/common");
const getProductByCodeApi = async (productCode) => {
  const config = global.config;
  const url =
    config.basic.product[config.env] +
    `/product/v1/productInfo/?prdctCode=${productCode}`;
  const productInfo = await common.getApiV2(url);
  if (productInfo.data.status != 200) {
    return null;
  }
  return productInfo.data.data;
};

const getProductDetailByCodeApi = async (productCode) => {
  const config = global.config;
  const url =
    config.basic.product[config.env] +
    `/product/v1/product-detail/?productCode=${productCode}`;
  const productInfo = await common.getApiV2(url);
  if (productInfo.data?.status == 0) {
    return productInfo.data.data;
  }
  return null;
};

module.exports = {
  getProductByCodeApi,
  getProductDetailByCodeApi,
};
