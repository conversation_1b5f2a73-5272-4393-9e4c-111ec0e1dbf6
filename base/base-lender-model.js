const { request } = require('express');
const { LENDER_CHANGE_REQUEST_STATUS } = require('../const/status-const');
const { createLenderChangeRequest, insertChangeRequestDetails, getLatestLenderChangeRequest, getLenderChangeRequestDetail, updateChangeRequestDetails } = require('../repositories/lender-change-request-repo');
const lenderColumnConfigRepo = require('../repositories/lender-collumn-config-repo');
const loggingService = require("../utils/loggingService");
const _ = require('lodash');
const { saveStepLog } = require("../repositories/logging-repo");

class BaseLenderModel {
    constructor({ referenceNumber, data, partnerCode, table, url, changeRequest, changeRequestDetails, groupType = null }) {
        this.data = data;
        this.partnerCode = partnerCode;
        this.table = table;
        this.referenceNumber = referenceNumber;
        this.url = url;
        this.changeRequest = changeRequest;
        this.changeRequestDetails = changeRequestDetails;
        this.groupType = groupType;
    }

    static async init(data = {}) {
        const instance = new this(data);
        await instance.initialize();
        return instance;
    }

    async initialize() {

    }

    defaultExtend(defaultExtend = { status: true, latest_comment: "" }) {
        return {
            status: true,
            is_resubmit: defaultExtend?.status ? !defaultExtend?.status : false,
            comment: "",
            latest_comment: "",
            extend: {},
            table: this.table,
            ...defaultExtend,
        }
    }

    async getLenderColumnConfig() {
        const columnConfig = await lenderColumnConfigRepo.getLenderColumnConfig({ partnerCode: this.partnerCode, table: this.table, groupType: this.groupType });
        this.columnConfig = columnConfig;
        return columnConfig;
    }

    extendData(options = {}) {
        if (!Array.isArray(this.data)) {
          return this.data;
        }
        return this.data.map((item) => {
          const _options = {};
          const parentComment = this.getStatusAndLatestCommentByTable(
            `parent_${item?.id}`
          );
          _options.comment = options.comment || item?.comment || "";
          _options.latest_comment =
            parentComment?.latest_comment ||
            options.latest_comment ||
            item?.comment ||
            "";
          _options.status = parentComment?.status ?? (item?.status || true);
          return {
            ...item,
            ...this.defaultExtend(_options),
          };
        });
    }

    getStatusAndLatestComment(key, value) {
        if (
          Array.isArray(this.changeRequestDetails) &&
          this.changeRequestDetails.length > 0
        ) {
          const latestDetail = _.find(this.changeRequestDetails, { key: key });
          return {
            status: latestDetail?.status ?? true,
            latest_comment: latestDetail?.comment ?? "",
            old_value: latestDetail?.old_value ?? null,
            new_value: latestDetail?.new_value ?? null,
            value:
              latestDetail?.editable &&
              latestDetail?.is_change &&
              latestDetail?.new_value
                ? latestDetail?.new_value
                : value,
          };
        }
        return {
          status: true,
          latest_comment: "",
          old_value: null,
          value: value,
        };
    }

    getStatusAndLatestCommentByTable(key, value) {
        if (
          Array.isArray(this.changeRequestDetails) &&
          this.changeRequestDetails.length > 0
        ) {
          const latestDetail = _.find(this.changeRequestDetails, { key: key, ref_table: this.table?.toUpperCase() });
          return {
            status: latestDetail?.status ?? true,
            latest_comment: latestDetail?.comment ?? "",
            old_value: latestDetail?.old_value ?? null,
            new_value: latestDetail?.new_value ?? null,
            value:
              latestDetail?.editable &&
              latestDetail?.is_change &&
              latestDetail?.new_value
                ? latestDetail?.new_value
                : value,
          };
        }
        return {
          status: true,
          latest_comment: "",
          old_value: null,
          value: value,
        };
    }

    async bindingData(group = null) {
        const data = this.data;
        const lenderColumnConfig = this.columnConfig;
        let result = [];
        if (Array.isArray(lenderColumnConfig)) {
            for (const col of lenderColumnConfig) {
                if (group && col.group != group) {
                    continue;
                }
                const key = col.key;
                const value = data.hasOwnProperty(key) ? data[key] : null;
                const extendData = this.getStatusAndLatestComment(key, value);
                result.push({
                    ...col,
                    value: value,
                    ...this.defaultExtend(extendData),
                });
            }
        }
        this.bindedData = result;
        return result;
    }

    async logging(body, response) {
        await loggingService.saveRequestV2(
            global.poolWrite,
            body,
            response,
            this.referenceNumber,
            body?.requestId || body?.request_id || null,
            this.partnerCode,
            body?.requestType || body?.request_type || null,
            this.url || null
        );
    }

    async stepLogging({ serviceName, step, body, response = null, url = null }) {
        await saveStepLog(this.referenceNumber, serviceName, step, body, response, url);
    }

    findKeyColumnConfig(key) {
        const foundKey = _.find(this.columnConfig, { key: key });
        return foundKey;
    }

    async extractKeyValues(changeRequestId, info, refTable = null) {
        const data = this.data;
        return info.map(item => ({
            change_request_id: changeRequestId,
            key: item.key,
            old_value: data.hasOwnProperty(item.key) ? data[item.key] : null,
            new_value: item.value,
            status: item.status,
            comment: item.comment,
            editable: this.findKeyColumnConfig(item.key)?.editable || false,
            is_change: item.value !== (data.hasOwnProperty(item.key) ? data[item.key] : null),
            ref_table: item?.table || refTable
        }));
    }

    async process(body, refTable = null) {
        try {
            const result = {
                message: 'save success',
            };
            let request = await getLatestLenderChangeRequest({
                request_type: body.requestType || body.request_type || null,
                reference_code: body.referenceCode || body.reference_code || this.referenceNumber,
                reference_type: body.referenceType || body.reference_type || this.table,
                status: LENDER_CHANGE_REQUEST_STATUS.NEW,
            });
            let existedValues = [];
            if (!request) {
                request = await createLenderChangeRequest({
                    request_id: body.requestId || body.request_id || null,
                    request_type: body.requestType || body.request_type || null,
                    reference_code: body.referenceCode || body.reference_code || this.referenceNumber,
                    reference_type: body.referenceType || body.reference_type || this.table,
                    created_by: body.createdBy || 'system',
                    request_body: body,
                    parent_reference: body.parentReference || null,
                });
            } else {
                existedValues = await getLenderChangeRequestDetail(request.id);
            }
            //process info
            const keyValues = await this.extractKeyValues(request.id, body?.info ?? [], refTable);
            for (const value of keyValues) {
                const existedValue = existedValues.find(ev => ev.key === value.key);
                value.id = existedValue?.id;
            }
            await insertChangeRequestDetails(keyValues?.filter(item => !item.id));
            await updateChangeRequestDetails(keyValues?.filter(item => item.id));
            this.logging(body, result);
            return request;
        }
        catch (error) {
            this.logging(body, { error: error.message });
            console.log(`[base-lender-model] [save] Error saving lender data:`, error);
            throw new Error('Failed to save lender data');
        }
    }

}

module.exports = BaseLenderModel;