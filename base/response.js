const { MISA_ERROR_CODE } = require("../const/response-const");
const {
  ERROR_CODE,
  RESP_MESSAGE,
  RESPONSE_CODE,
} = require("../const/definition");

class Response {
  constructor(code, message, data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }
}

class SuccessResponse {
  constructor(data, message) {
    this.code = ERROR_CODE.SUCCESS;
    this.message = message ?? RESP_MESSAGE.SUCCESS;
    this.data = data ?? null;
  }
}

class BadRequestResponse {
  constructor(errors = [], message, data) {
    this.code = ERROR_CODE.INVALID_REQUEST;
    this.message = message ?? RESP_MESSAGE.INVALID_REQUEST;
    this.data = data ?? null;
    this.errors = errors;
  }
}

class BadRequestResponseV2 {
  constructor(errors = [], message, data) {
    this.code = ERROR_CODE.ERROR;
    this.message = message ?? RESP_MESSAGE.INVALID_REQUEST;
    this.data = data ?? null;
    this.errors = errors;
  }
}

class ServerErrorResponse {
  constructor(data, message, errors = []) {
    this.code = ERROR_CODE.INT_SERVER_ERROR;
    this.message = message ?? RESP_MESSAGE.INT_SERVER_ERROR;
    this.data = data ?? null;
    this.errors = errors;
  }
}

const handleResponseError = (res, error) => {
  if (error instanceof BadRequestResponse || error instanceof BadRequestResponseV2) {
    return res.status(400).json(error);
  } else if (error instanceof ServerErrorResponse) {
    return res.status(500).json(error);
  }
  const errors = [    
    {
      code: MISA_ERROR_CODE.E500.code,
      location: "",
      message: MISA_ERROR_CODE.E500.message,
      details: error?.message,
    },
  ];
  return res
    .status(500)
    .json(
      new ServerErrorResponse(null, "Internal Server Error", errors)
    );
};

const handleInternalResponseError = (res, error) => {
  if (error instanceof TypeError && /is not a function/.test(error.message)) {
    const msg = "Service method not implement for this partner_code";
    console.log(msg);
    return res.status(200).json(new SuccessResponse(null,msg));
  }
  if (error instanceof BadRequestResponse || error instanceof BadRequestResponseV2) {
    return res.status(400).json(error);
  } else if (error instanceof ServerErrorResponse) {
    return res.status(500).json(error);
  }
  const errors = [
    {
      code: MISA_ERROR_CODE.E500.code,
      location: "",
      message: MISA_ERROR_CODE.E500.message,
      details: error?.message,
    },
  ];
  return res.status(500).json(new ServerErrorResponse(null, "Internal Server Error", errors));
};

module.exports = {
  Response,
  SuccessResponse,
  BadRequestResponse,
  BadRequestResponseV2,
  ServerErrorResponse,
  handleResponseError,
  handleInternalResponseError,
};
