const CASE_STATUS = {
    CHECKED_OCR: {
       CODE: 1,
       TEXT: "CHECKED OCR",
       ACTION: {
          CANCELLED: "CANCELLED",
          REFUSED: "REFUSED",
          PASSED_EKYC: "PASSED EKYC",
          IN_MANUAL_KYC: "IN MANUAL KYC"
       },
       STEP_CODE: "EKYC_OCR",
    },
    CHECKED_FACEMATCHING: {
       CODE: 2,
       TEXT: "CHECKED FACEMATCHING",
       ACTION: {
          CANCELLED: "CANCELLED",
          REFUSED: "REFUSED",
          IN_MANUAL_KYC: "IN MANUAL KYC",
          PASSED_EKYC: "PASSED EKYC",
       },
       STEP_CODE: "EKYC_FACEMATCHING",
    },
    CHECKED_EKYC: {
       CODE: 3,
       TEXT: "CHECKED EKYC",
       ACTION: {
            CANCELLED: "CANCELLED",
            REFUSED: "REFUSED",
            IN_MANUAL_KYC: "IN MANUAL KYC",
            PASSED_EKYC: "PASSED EKYC",
       },
       STEP_CODE: "EKYC",
    },
    IN_MANUAL_KYC: {
       CODE: 4,
       TEXT: "IN MANUAL KYC",
       ACTION: {
          IN_KYC_QUEUE: "IN KYC QUEUE",
          COMPLETED_KYC: "COMPLETED KYC",
          REFUSED: "REFUSED",
          CANCELLED: "CANCELLED",
       },
       STEP_CODE: "PORTAL_KYC",
    },
    CHECKED_ELIGIBILITY: {
       CODE: 5,
       TEXT: "CHECKED ELIGIBILITY",
       ACTION: {
          ELIGIBLE: "ELIGIBLE",
          NOT_ELIGIBLE: "NOT ELIGIBLE",
       },
       STEP_CODE: "ELIGIBLE",
    },
    REVIEWED_OBSERVATION: {
       CODE: 6,
       TEXT: "REVIEWED OBSERVATION",
       ACTION: {
          EC_10M: "EC 10M",
          EC_20M: "EC 20M",
          TOPUP: "TOP UP",
       },
       STEP_CODE: "NTB_TOPUP",
    },
   CHECKED_S37: {
      CODE: 7,
      TEXT: "CHECKED S37",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_S37: "PASSED S37",
         ERROR: "ERROR"
      },
      STEP_CODE: "CIC_S37",
   },
    TO_BE_STUDIED: {
       CODE: 8,
       TEXT: "TO BE STUDIED",
       ACTION: {
          RECEIVED: "RECEIVED",
          CANCELLED: "CANCELLED",
       },
       STEP_CODE: "BASIC_APPLICATION_FORM",
    },
    APPLICATION_SUBMITED: {
       CODE: 9,
       TEXT: "APPLICATION SUBMITED",
       ACTION: {
          RECIVED: "RECIVED",
          CANCELLED: "CANCELLED",
       },
       STEP_CODE: "APPLICATION_FORM",
    },
    CHECKED_DEDUPLICATION: {
       CODE: 10,
       TEXT: "CHECKED DEDUPLICATION",
       ACTION: {
          AUTO_MERG: "AUTO MERG",
          NEW: "NEW",
          IN_MANUAL_PROCESS: "IN MANUAL PROCESS",
          FAILED: "FAILED"
       },
       STEP_CODE: "AUTO_DEDUPLICATION",
    },
    IN_MANUAL_PROCESS: {
       CODE: 11,
       TEXT: "IN MANUAL PROCESS",
       ACTION: {
          IN_CE_DUP_QUEUE: "IN CE DUP QUEUE",
          NEW: "NEW",
          MERGED: "MERGED",
          CANCELLED: "CANCELLED",
          REFUSED: "REFUSED"
       },
       STEP_CODE: "CE_DEDUPLICATION",
    },
    TO_BE_CLASSIFIED: {
       CODE: 12,
       TEXT: "TO BE CLASSIFIED",
       ACTION: {
          PASSED_CLASSIFIED: "PASSED CLASSIFIED",
          REFUSED: "REFUSED",
       },
       STEP_CODE: "SEGMENTATION_AT_RISK",
    },
    PRE_SCORING: {
       CODE: 13,
       TEXT: "PRE SCORING",
       ACTION: {
          APPROVED: "APPROVED",
          REFUSED: "REFUSED",
       },
       STEP_CODE: "PRE_DE",
    },
    SCORING: {
       CODE: 14,
       TEXT: "SCORING",
       ACTION: {
          APPROVED: "APPROVED",
          IN_MANUAL_DECISION: "IN MANUAL DECISION",
          REFUSED: "REFUSED",
       },
       STEP_CODE: "DE",
    },
    IN_MANUAL_DECISION: {
       CODE: 15,
       TEXT: "IN MANUAL DECISION",
       ACTION: {
          IN_CE_VER_QUEUE: "IN CE VER QUEUE",
          APPROVED: "APPROVED",
          REFUSED: "REFUSED",
          CANCELLED: "CANCELLED",
          REFUSED_DI_AFTER_CE: "REFUSED DI AFTER CE",
       },
       STEP_CODE: "CE_VERIFICATION",
    },
    CE_DOC_IN_PROGRESS: {
       CODE: 16,
       TEXT: "CE DOC IN PROGRESS",
       ACTION: {
          IN_CE_DOC_QUEUE: "IN CE DOC QUEUE",
          APPROVED: "APPROVED",
          CANCELLED: "CANCELLED",
          REFUSED: "REFUSED",
          RESUBMIT: "RESUBMIT",
       },
       STEP_CODE: "CE_CHECK_DOC",
    },
    VALIDATED_ACCEPTED: {
       CODE: 17,
       TEXT: "VALIDATED ACCEPTED",
       ACTION: {
          SELECTING_OFFER : "SELECTING OFFER",
          SELECTED_OFFER : "SELECTED OFFER",
          CANCELLED: "CANCELLED"
       },
       STEP_CODE: "OFFER_SELECTION",
    },
    TO_BE_SIGNED: {
       CODE: 18,
       TEXT: "TO BE SIGNED",
       ACTION: {
          UPLOADING: "UPLOADING",
          UPLOADED: "UPLOADED"
       },
       STEP_CODE: "TO_BE_SIGNED",
    },
    WAITING_TO_BE_CLAIMED: {
       CODE: 19,
       TEXT: "WAITING TO BE CLAIMED",
       ACTION: {
          WAITING_TO_BE_CLAIMED: "WAITING TO BE CLAIMED",
          CLAIMED: "CLAIMED",
       },
       STEP_CODE: "AUTHORIZATION",
    },
    GENERATING_CONTRACT: {
       CODE: 20,
       TEXT: "GENERATING CONTRACT",
       ACTION: {
          WAITING_TO_BE_GENERATED: "WAITING TO BE GENERATED",
          GENERATED_CONTRACT: "GENERATED CONTRACT",
       },
       STEP_CODE: "CONTRACT_GENERATION",
    },
    SIGNING_IN_PROGRESS: {
       CODE: 21,
       TEXT: "SIGNING IN PROGRESS",
       ACTION: {
          WAITING_TO_BE_SIGNED: "WAITING TO BE SIGNED",
          COMPLETED_SIGN: "COMPLETED SIGN",
          SIGNED_W_O_DOCS: "SIGNED W/O DOCS",
          CANCELLED: "CANCELLED",
          EVF_SIGNED: "EVF SIGNED"
       },
       STEP_CODE: "SIGN_CONTRACT",
    },
    IN_CP_CHECK_DOC: {
       CODE: 22,
       TEXT: "IN CP CHECK DOC",
       ACTION: {
          IN_CP_QUEUE: "IN CP QUEUE",
          CP_CHECK_OK: "CP CHECK OK",
          CANCELLED: "CANCELLED",
          REFUSED: "REFUSED",
          RESUBMIT: "RESUBMIT",
       },
       STEP_CODE: "CP",
    },
    DELIVERING: {
       CODE: 23,
       TEXT: "DELIVERING",
       ACTION: {
          
       },
       STEP_CODE: "DELIVERY_CONFIRMATION",
    },
    ACTIVATED: {
       CODE: 24,
       TEXT: "ACTIVATED",
       ACTION: {
          ACTIVATED: "ACTIVATED",
       },
       STEP_CODE: "ACTIVATE",
    },
    ACTIVATED_TO_BE_DISBURED: {
       CODE: 25,
       TEXT: "ACTIVATED TO BE DISBURED",
       ACTION: {
          WAITING_FOR_DOCS:"WAITING FOR DOCS",
          UPLOADED_DOCS: "UPLOADED DOCS",
       },
       STEP_CODE: "ACTIVATE",
    },
    TERMINATED: {
       CODE: 26,
       TEXT: "TERMINATED",
       ACTION: {
          TERMINATED: "TERMINATED"
       },
       STEP_CODE: "TERMINATION",
    },
    EARLY_TERMINATION: {
       CODE: 27,
       TEXT: "EARLY TERMINATION",
       ACTION: {
          
       },
       STEP_CODE: "TERMINATION",
    },
    TERMINATION_IN_PROGRESS: {
      CODE: 28,
      TEXT: "TERMINATION IN PROGRESS",
      ACTION: {
         TERMINATION_IN_PROGRESS: "TERMINATION IN PROGRESS"
      },
      STEP_CODE: "TERMINATION",
    },
    CONTRACT_BREACH: {
       CODE: 29,
       TEXT: "CONTRACT BREACH",
       ACTION: {
          
       },
       STEP_CODE: "TERMINATION",
    },
    POST_CHECK_PROCESSING: {
       CODE: 30,
       TEXT: "POST CHECK PROCESSING",
       ACTION: {
          
       },
       STEP_CODE: "DOC_POST_CHECK",
    },
    SIGNED_TO_BE_DISBURED: {
       CODE: 31,
       TEXT: "SIGNED TO BE DISBURED",
       ACTION: {
          DISBURSEMENT_PROCESSING: "DISBURSEMENT PROCESSING",
          DISBURED_SUCESSFULLY: "DISBURED SUCESSFULLY",
          CANCELLED: "CANCELLED"
       },
       STEP_CODE: "DISBURSEMENT",
    },
    TO_BE_ACTIVATED: {
       CODE: 32,
       TEXT: "TO BE ACTIVATED",
       ACTION: {
          CONSOLIDATING: "CONSOLIDATING",
          DISBURED_SUCESSFULLY: "DISBURED SUCESSFULLY"
       },
       STEP_CODE: "DISBURSEMENT",
    },
    CANCELLED: {
       CODE: 33,
       TEXT: "CANCELLED",
       ACTION: {
          CANCELLED: "CANCELLED"
       },
       STEP_CODE: "CANCEL",
    },
    REFUSED: {
       CODE: 34,
       TEXT: "REFUSED",
       ACTION: {
          REFUSED: "REFUSED"
       },
       STEP_CODE: "REFUSED",
    },
    SEND_SMS: {
       CODE: 35,
       TEXT: "SEND SMS",
       ACTION: {
          SEND_EMAIL: "SEND SMS"
       },
       STEP_CODE: "SEND_SMS"
    },
    SEND_EMAIL: {
       CODE: 36,
       TEXT: "SEND EMAIL",
       ACTION: {
          SEND_EMAIL: "SEND EMAIL"
       },
       STEP_CODE: "SEND_EMAIL"
    },
    UPLOAD_CONTRACT: {
       CODE: 37,
       TEXT: "UPLOAD CONTRACT",
       ACTION: {
          UPLOAD_CONTRACT: "LOAN_CONTRACT"
       },
       STEP_CODE: "UPLOAD_CONTRACT"
    },
    IT_CANCEL: {
       CODE: 38,
       TEXT: "IT SUPPORT CANCEL",
       ACTION: {
          CANCEL: "IT CANCEL"
       },
       STEP_CODE: "IT_CANCEL"
    },
   CHECKED_S37_OTHER_ID: {
      CODE: 39,
      TEXT: "CHECKED S37 OTHER ID",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_S37: "PASSED S37",
         ERROR: "ERROR"
      },
      STEP_CODE: "CIC_S37_OTHER_ID",
   },
   UI_CANCEL: {
      CODE: 40,
      TEXT: "UI SUPPORT CANCEL",
      REASON: " DSA/ TEL cancellation",
      ACTION: {
      CANCEL: "CANCELLED"
      },
      STEP_CODE: "UI_CANCELLED"
   },
   CHECKED_VEGA: {
      CODE: 41,
      TEXT: "CHECKED VEGA",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_VEGA: "PASSED VEGA",
      },
      STEP_CODE: "VEGA_SCORE_BLACKLIST",
   },
   JOB: { 
      CODE: 42,
      TEXT: "SCAN JOB",
      ACTION: {
         AF1_TIME: "CANCEL AF1 OVER TIME",
         SIGNING_TIME: "CANCEL SIGNING OVER TIME"
      },
      STEP_CODE: "SCAN_JOB"
   },
   IVR_KYC: { 
      CODE: 43,
      TEXT: "CHECK IVR KYC",
      ACTION: {
         CHECK_IVRKYC: "CHECK IVR KYC",
         CANCEL_IVRKYC: "CANCEL IVR KYC",
      },
      STEP_CODE: "CHECK_IVR_KYC"
   },
   CHECKED_ADRESS_FIZ: {
      CODE: 44,
      TEXT: "CHECKED ADRESS",
      ACTION: {
         REFUSED: "REFUSED",
         CANCELLED: "CANCELLED"
      },
      STEP_CODE: "CHECKED_ADRESS_FIZ",
   },
   CHECKED_CASE_CREATION_TIME: {
      CODE: 45,
      TEXT: "CHECKED CASE CREATION TIME",
      ACTION: {
         REFUSED: "REFUSED",
         CANCELLED: "CANCELLED"
      },
      STEP_CODE: "CHECKED_CASE_CREATION_TIME",
   },
   CHECKED_PHONENUMBER: {
      CODE: 46,
      TEXT: "CHECKED PHONENUMBER",
      ACTION: {
           CANCELLED: "CANCELLED",
           REFUSED: "REFUSED",
           IN_MANUAL_CHECK_PHONE: "IN MANUAL CHECK PHONE",
           PASSED: "PASSED CHECK PHONE",
      },
      STEP_CODE: "CHECKED_PHONE_NUMBER",
   },
   IN_MANUAL_CHECK_PHONE: {
      CODE: 47,
      TEXT: "IN MANUAL CHECK PHONE",
      ACTION: {
         APPROVED: "APPROVED",
         REFUSED: "REFUSED",
         CANCELLED: "CANCELLED",
      },
      STEP_CODE: "MANUAL_CHECK_PHONE",
   },
   ACTIVATED_CREDIT_LIMIT: {
      CODE: 48,
      TEXT: "ACTIVATED_CREDIT_LIMIT",
      ACTION: {
         ACTIVATED: "ACTIVATED_CREDIT_LIMIT",
      },
      STEP_CODE: "ACTIVATED_CREDIT_LIMIT",
   },
   FINAL_DI: {
      CODE: 49,
      TEXT: "FINAL DI",
      ACTION: {
         REFUSED: "DISQUALIFIED FINAL DI"
      },
      STEP_CODE: "FINAL_DI"
   },
   CHECKED_PRODUCT_CONDITION: {
      CODE: 50,
      TEXT: "CHECKED PRODUCT CONDITION",
      ACTION: {
         CANCELLED: "CANCELLED",
         REFUSED: "REFUSED",
         PASSED: "PASSED"
      },
      STEP_CODE: "CHECKED_PRODUCT_CONDITION",
   },
   CC_IN_PROGRESS: {
      CODE: 51,
      TEXT: "CC IN PROGRESS",
      ACTION: {
         IN_CC_QUEUE: "IN CC QUEUE",
         APPROVED: "APPROVED",
         CANCELLED: "CANCELLED",
         REFUSED: "REFUSED",
         RESUBMIT: "RESUBMIT",
      },
      STEP_CODE: "CC_CHECK",
   },
   C06_VALIDATION_CHECKING: {
      CODE: 52,
      TEXT: "C06 VALIDATION CHECKING",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED: "PASSED"
      },
      STEP_CODE: "C06_VALIDATION_CHECKING",
   },
   CHECKED_NFC: {
        CODE: 53,
        TEXT: "CHECKED NFC",
        ACTION: {
            ELIGIBLE: "ELIGIBLE",
            NOT_ELIGIBLE: "NOT ELIGIBLE",
            CANCELLED: "CANCELLED",
        },
        STEP_CODE: "ELIGIBLE",
   },
   CHECK_CONTRACT_RENEW_DATE: {
      CODE: 54,
      TEXT: "CHECKED NFC",
      ACTION: {
         ELIGIBLE: "ELIGIBLE",
         NOT_ELIGIBLE: "NOT ELIGIBLE",
         ERROR: "ERROR"
      },
      STEP_CODE: "ELIGIBLE",
   },
   CHECKED_B11T: {
      CODE: 55,
      TEXT: "CHECKED B11T",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_B11T: "PASSED B11T",
         ERROR: "ERROR"
      },
      STEP_CODE: "CIC_B11T",
   },
   CHECK_RENEW_DATE: {
      CODE: 56,
      TEXT: "CHECKED RENEW DATE",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_RENEW_DATE: "PASSED RENEW DATE",
         ERROR: "ERROR"
      },
      STEP_CODE: "CHECK_RENEW_DATE",
   },
   CHECK_FULL_LOAN: {
      CODE: 57,
      TEXT: "CHECKED FULL LOAN",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_FULL_LOAN: "PASSED FULL LOAN",
         ERROR: "ERROR",
         WAIT_CIC: "WAIT_CIC"
      },
      STEP_CODE: "CHECK_FULL_LOAN",
   },
   CHECK_CIC_DETAIL: {
      CODE: 58,
      TEXT: "CHECKED CIC DETAIL",
      ACTION: {
         REFUSED: "REFUSED",
         PASSED_CIC_DETAIL: "PASSED CIC DETAIL",
         ERROR: "ERROR",
         WAIT_CIC: "WAIT_CIC"
      },
      STEP_CODE: "CHECK_CIC_DETAIL",
   },
   KUNN_MANUAL_REVIEW: {
      CODE: 59,
      TEXT: "KUNN MANUAL REVIEW",
      ACTION: {
         PASSED_MANUAL_PROCESS: "PASSED_MANUAL_PROCESS",
         CANCELLED: "CANCELLED",
         REJECTED_MANUAL_PROCESS: "REJECTED_MANUAL_PROCESS",
         APPROVED_CHANGE_REQUEST: "APPROVED_CHANGE_REQUEST",
         WAITING_RESUBMIT: "WAITING_RESUBMIT",
         WAITING_APPROVE_CHANGE_REQUEST: "WAITING_APPROVE_CHANGE_REQUEST"
      },
      STEP_CODE: "KUNN_MANUAL_REVIEW"
   },
   CHECK_SIGNED_DOCUMENT: {
      CODE: 60,
      TEXT: "CHECKED SIGNED DOCUMENT",
      ACTION: {
         WAITING_RESUBMIT_SIGNED: "WAITING_RESUBMIT_SIGNED",
         APPROVED: "APPROVED",
      },
      STEP_CODE: "CHECK_SIGNED_DOCUMENT",
   },
   RESUBMITTED: {
      CODE: 61,
      TEXT: "RESUBMITTED",
      ACTION: {
         RESUBMITTED: "RESUBMITTED",
      },
      STEP_CODE: "RESUBMITTED",
   },
   SUBMITTED_SIGNED: {
      CODE: 62,
      TEXT: "SUBMITTED SIGNED",
      ACTION: {
         SIGNED: "SIGNED",
         RESUBMITTED_SIGNED: "RESUBMITTED_SIGNED",
      },
      STEP_CODE: "SUBMITTED_SIGNED",
   },
   EXPORT_CIC_REPORT: {
      CODE: 63,
      TEXT: "EXPORT CIC REPORT",
      ACTION: {
         SUCCESS: "SUCCESS",
         FAILED: "FAILED"
      },
      STEP_CODE: "EXPORT_CIC_REPORT"
   },
   CHECK_WHITELIST_FULL_LOAN: {
      CODE: 64,
      TEXT: "CHECKED WHITELIST FULL LOAN",
      ACTION: {
         ELIGIBLE: "ELIGIBLE",
         NOT_ELIGIBLE: "NOT ELIGIBLE",
         ERROR: "ERROR"
      },
      STEP_CODE: "CHECK_WHITELIST_FULL_LOAN"
   },
   CHECK_ELIGIBLE_SME: {
      CODE: 65,
      TEXT: "CHECKED ELIGIBLE SME",
      ACTION: {
         ELIGIBLE: "ELIGIBLE",
         NOT_ELIGIBLE: "NOT ELIGIBLE",
         ERROR: "ERROR"
      },
      STEP_CODE: "CHECK_ELIGIBLE_SME"
   },
   CHECK_CIC_B11T_SME: {
      CODE: 66,
      TEXT: "CHECKED CIC B11T SME",
      ACTION: {
         ELIGIBLE: "ELIGIBLE",
         NOT_ELIGIBLE: "NOT ELIGIBLE",
         ERROR: "ERROR"
      },
      STEP_CODE: "CHECK_CIC_B11T_SME"
   },
   CHECK_CIC_DETAIL_SME: {
      CODE: 67,
      TEXT: "CHECKED CIC DETAIL SME",
      ACTION: {
         ELIGIBLE: "ELIGIBLE",
         NOT_ELIGIBLE: "NOT ELIGIBLE",
         ERROR: "ERROR",
         WAIT_CIC: "WAIT_CIC"
      },
      STEP_CODE: "CHECK_CIC_DETAIL_SME"
   },
   CHECK_MODEL: {
      CODE: 68,
      TEXT: "CHECKED MODEL",
      ACTION: {
         ELIGIBLE: "ELIGIBLE",
         NOT_ELIGIBLE: "NOT ELIGIBLE",
         ERROR: "ERROR",
      },
      STEP_CODE: "CHECK_MODEL"
   },
   MERCHANT_LIMIT_CREATE: {
      CODE: 69,
      TEXT: "MERCHANT LIMIT CREATE",
      ACTION: {
         SUCCESS: "SUCCESS",
         FAILED: "FAILED",
      },
      STEP_CODE: "MERCHANT_LIMIT_CREATE"
   }
}
const RESPONSE_CODE = {
    SUCCESS: "201",
    ERROR: -1,
    ERROR_VALID: 901,
    EXCEPTION: 500
}
module.exports ={
    RESPONSE_CODE,
    CASE_STATUS
}
