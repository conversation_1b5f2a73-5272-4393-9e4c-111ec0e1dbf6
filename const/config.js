const serviceEndpoint = {
    deOfferVTP : "/de-score/v1/decisions/partners/cod/offer",
    VTP : {
        af1 : '/de-score/v1/decisions/partners/cod/loan-step/af1',
        af2 : '/de-score/v1/decisions/partners/cod/loan-step/af2',
    },
    deOfferVSK : "/de-score/v1/decisions/partners/vsk/offer",
    VSK : {
        af1 : '/de-score/v1/decisions/partners/vsk/loan-step/af1',
        af2 : '/de-score/v1/decisions/partners/vsk/loan-step/af2',
        di: '/de-score/v1/decisions/partners/vsk/di',
        offer: '/de-score/v1/decisions/partners/VSK/offer'
    },
    KOV : {
        af1 : '/de-score/v1/decisions/partners/KOV/loan-step/af1',
        af2 : '/de-score/v1/decisions/partners/vsk/loan-step/af2',
        di: '/de-score/v1/decisions/partners/KOV/di',
        offer: '/de-score/v1/decisions/partners/kov/offer'
    },
    CRM : {
        updateCustomerInfo : '/crm/v1/customer',
        updateCustomerAttribute : '/crm/v1/customer/attribute/insert'
    },
    LMS_MC : {
        getInstallment : '/lms-mc/v1/installment/get?debtAckContractNumber=',
        getKunnDetail: '/lms-mc/v1/loan-debt-ack-contract?debtAckContractNumber=',
        updateLoan : '/lms-mc/v1/merchant-limit/update',
        listKunn: '/lms-mc/v1/loan-account/list-contract?contractNumber=',
        repayment: '/lms-mc/v1/repayment-v2?custId=',
        refundMoney: '/lms-mc/v1/debt-ack-contract/refund-suspend-factoring',
        tranferCase: '/lms-mc/v1/debt-ack-contract/transfer-suspend-factoring',
    },
    ESIGN: {
        checkValidDigitalSign: '/esigning/internal/misa/check-signature',
        mcaKunnSign: '/esigning/internal/customer-sign-ec'
    },
    MCA : {
        af1 : '/de-score/v1/decisions/partners/mca/loan-step/af1',
        af2 : '/de-score/v1/decisions/partners/mca/loan-step/af2',
        di: '/de-score/v1/decisions/partners/mca/di',
        offer: '/de-score/v1/decisions/partners/mca/offer',
        updateInfoApp: '/easy-biz/v1/auth/los/updateInfo'
    },
    EASY_UI_SERVICE: {
        getPaymentInfo: '/ui-service/collection/v1/payment-info?contractNumber='
    },
    NOTIFICATION : {
        KUNN_ACTIVE: '/easy-biz/v1/notifications/active-debt-ack',
        KUNN_APPROVE: '/easy-biz/v1/notifications/approve-debt-ack',
        LIMIT_ACTIVE: '/easy-biz/v1/notifications/active-limit',
        LIMIT_APPROVE: '/easy-biz/v1/notifications/approve-limit',
        RESUBMIT: '/easy-biz/v1/notifications/resubmit',
        UNMATCH_OFFER: '/easy-biz/v1/notifications/unmatch-offer',
        CANCEL: '/easy-biz/v1/notifications/cancel',
    },
    SMA: {
        af1: '/de-score/v1/decisions/partners/sma/loan-step/af1',
        af2: '/de-score/v1/decisions/partners/sma/loan-step/af2',
        di: '/de-score/v1/decisions/partners/mc/di',
        offer: '/de-score/v1/decisions/partners/mc/offer',
        car: {
            af2: '/de-score/v1/decisions/steps/full-loan/channels/MCC',
            offer: '/product/v1/offer/findOffer'
        },
        callBackMobile: '/sma/v1/max-loan/los-callback'
    },
}

module.exports = {
    serviceEndpoint
}