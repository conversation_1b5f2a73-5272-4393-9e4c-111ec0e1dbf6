const ENDPOINT_CONST = {
    CONVERT_SERVICE: {
        ADD_TASK: '/convert-service/v1/convert/add-task',
    },
    ANTI_FRAUD: {
        SANITY: '/anti_fraud/v1/ekyc/check_sanity',
        SANITY_AND_OCR: '/anti_fraud/v1/ekyc/check_sanity_ocr_id_card',
        CHECK_SANITY_AND_LIVENESS_AND_MATCHING: '/anti_fraud/v1/ekyc/check_full_ekyc',
        LIVENESS: '/anti_fraud/v1/ekyc/check_liveness',
        MATCHING: '/anti_fraud/v1/ekyc/check_face_matching',
        UPDATE_ID_CARD: '/anti_fraud/v1/ekyc/update_id_card',
        PRE_ELIGIBLE_MW: '/anti_fraud/v1/eligible/mw/pre_check',
        ELIGIBLE_MW: '/anti_fraud/v1/eligible/mw/check',
        OCR_INSURANCE: '/anti_fraud/v1/ekyc/ocr_insurance',
        EKYC: '/anti_fraud/v1/ekyc/check_ekyc',
        STEP_FULL_LOAN: '/anti_fraud/v1/decisions/steps/full_loan/channels/',
        STEP_VERIFY_LOAN: '/anti_fraud/v1/decisions/steps/verify_loan/channels/',
        STEP_FAST_LOAN: '/anti_fraud/v1/decisions/steps/fast_loan/channels/',
        DI_CASHLOAN: '/anti_fraud/v1/decisions/di/cash_loan',
        DI_ESTIMATE: '/anti_fraud/v1/decisions/di/estimate',
        BASIC_INFO: '/anti_fraud/v1/eligible/cash_loan/check_basic_info',
        CHECK_NFC: '/anti_fraud/v1/ekyc/check-nfc',
        CHECK_BLACKLIST: '/anti_fraud/v1/eligible/check-blacklist',
        CHECK_WHITELIST: '/anti_fraud/v1/decisions/product-whitelist',
        CHECK_CONTRACT_RENEW_DATE: '/anti_fraud/v1/decisions/contract-renew-date',
        READ_OCR: '/anti_fraud/v1/ekyc/ocr_id_card',
        CALL_S37: '/anti_fraud/v1/cic/call_s37',
        CHECK_S37: '/anti_fraud/v2/cic/s37',
        CHECK_C06: '/anti_fraud/v1/ekyc/nfc-verify',
        CHECK_CONSENT: '/anti_fraud/v1/telco_score/check_consent',
        CHECK_IN_PROGRESS: '/anti_fraud/v1/eligible/mw/check-in-progress',
        CHECK_B11T: '/anti_fraud/v1/cic/b11t/check',
        CALL_B11T: '/anti_fraud/v1/cic/b11t',
        GET_REASON_DETAIL: '/anti_fraud/v1/decisions/get-reason-detail?contract_number='
    },
    DE_V02: {
        S37: '/de-score/v1/cic/check-s37',
        EKYC: '/de-score/v1/ekyc/check-ekyc',
        EKYC_BASE64: '/de-score/v1/ekyc/check-ekyc/base64',
        READ_OCR: '/de-score/v1/ekyc/read-ocr',
        READ_OCR_BASE64: '/de-score/v1/ekyc/read-ocr/base64',
        CHECK_OCR: '/de-score/v1/ekyc/check-ocr',
        SANITY: '/de-score/v1/ekyc/check-sanity',
        SANITY_BASE64: '/de-score/v1/ekyc/check-sanity/base64',
        LIVENESS: '/de-score/v1/ekyc/check-liveness',
        FACE_MATCHING: '/de-score/v1/ekyc/check-facematching',
        FACE_VERIFY: '/de-score/v1/ekyc/face-verify',
        ID_CARD: '/de-score/v1/ekyc/check-id-card',
        CANCEL: '/de-score/v1/decisions/cancel',
        CHECK_PCB: '/de-score/v2/requestpcb',
        CHECK_BLACKLIST: '/de-score/v1/check/blacklist',
        CHECK_ZALO_SCORE: '/de-score/v1/ces/scores/zalo',
        ELIGIBLE_V1: '/de-score/v1/eligible/cashloan/check',
        ELIGIBLE_V2: '/de-score/v2/eligible/cashloan/check',
        ELIGIBLE_MW: '/de-score/v1/eligible/mw/check',
        BHXH: '/de-score/v1/rate-plus/id-to-si',
        CALL_S37: '/de-score/v1/cic/call-s37',
        CANCEL_MANUAL: '/de-score/v1/decisions/manual/cancel',
        DI_CASHLOAN: '/de-score/v1/decisions/di/cashloan'
    },
    CRM: {
        WHITELIST_EXISTED: '/crm/v1/salary-advance/checkFilestaffByUsername',
        REGISTER_WHITELIST: '/crm/v1/salary-advance/files',
        APPROVE_WHITELIST: '/crm/v1/salary-advance/files/changestatusandcreateaccount',
        APPROVE_CREDIT_LIMIT: '/crm/v1/customer-loan/approve-limit',
        APPROVE_CREDIT_LIMIT_V2: '/crm/v1/customer-loan/approve-limit-v2',
        APPROVE_CREDIT_LIMIT_V3: '/crm/v1/customer-loan/approve-limit-v3',
        UPDATE_APPLICANT_STATUS: '/crm/v1/applicant/status',
        UPDATE_VOUCHER_DI: '/crm/v1/customer/di',
        GET_CUSTOMER_DI: '/crm/v1/customer/di',
        CHANGE_DI: '/crm/v1/customer/di/event',
        MERCHANT_DETAIL: '/crm/v1/merchant/detail?code=',
        MERCHANT_REFERRAL_DETAIL: '/crm/v1/merchant-referral-code/detail?code='
    },
    EASY_VIP_BE: {
        UPDATE_AMOUNT_LIMIT: '/easy-vip/v1/salary_advance/updateAmountLimit'
    },
    PUCP_CORE: {
        PREDICT_PRICE: '/pucp-core/v1/car-model/predict'
    },
    LMS_MC: {
        SIMULATION_EMI: '/lms-mc/v1/debt-ack-contract/simulation-emi',
        CREATE_MERCHANT_LIMIT: '/lms-mc/v1/merchant-limit/create',
        ACTIVE_CONTRACT: '/lms-mc/v1/debt-ack-contract/create',
        CREATE_ANNEX: '/lms-mc/v1/loan-annex/create-v2',
        CREATE_FULL_ANNEX: '/lms-mc/v1/loan-annex/create-full-v2',
        GET_ANNEX_DETAIL: '/lms-mc/v1/loan-annex/detail',
        GET_INSTALLMENT_BY_KUNN: '/lms-mc/v1/installment/get',
        CHECK_AVAILABLE: '/lms-mc/v1/merchant-limit/available',
    },
    PRODUCT: {
        AMOUNT_FROM_ANNUITY: '/product/v1/offer/getAmountFromAnnuity',
        PROMOTION_DETAIL:'/product/v1/voucher/mc/promotion/detail?mcPromotionCode=',
        PRODUCT_DETAIL:'/product/v1/product-detail?productCode='
    },
    VOUCHER_SERVICE: {
        VOUCHER_DETAIL: '/voucher-service/v1/voucher/detail?voucherCode=',
        REGISTER_FIZ: '/voucher-mobile-be/v1/users/register-fizo'
    },
    E_SIGNING: {
        INTERNAL_CUSTOMER_SIGN_EC: '/esigning/internal/customer-sign-ec'
    },
    PARTNER_CALLBACK: {
        FINV_WEBHOOK: '/integrate-evf/v1/webhooks/application-result'
    }
}

 module.exports = {
    ENDPOINT_CONST
 }
