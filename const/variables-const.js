const MASTER_DATA = {
  CODE_TYPE: {
    ISSUE_PLACE_VN: "ISSUE_PLACE_VN",
    DISTRICT: "DISTRICT",
    JOB_TYPE: "JOB_TYPE",
    LOAN_PURPOSE: "LOAN_PURPOSE",
    MARRIED_STATUS: "MARRIED_STATUS",
    PROFESSION: "PROFESSION",
    PROVINCE: "PROVINCE",
    WARD: "WARD",
    NEW_WARD: "NEW_WARD",
    BANK_BRANCH: "BANK_<PERSON>AN<PERSON>",
    BANK: "BANK",
    SALARY_FREQUENCY: "SALARY_FREQUENCY",
    SALARY_METHOD: "SALARY_METHOD",
    HABITAT: "HABITAT",
    EMPLOYMENT_CONTRACT_TYPE: "EMPLOYMENT_CONTRACT_TYPE",
    EMPLOYMENT_TYPE: "EMPLOYMENT_TYPE",
    TYPTEL: "TYPTEL",
    DOCUMENT: "DOCUMENT",
    FONCTION_INTERLOCUTEUR: "FONCTION_INTERLOCUTEUR",
    SME_EMPLOYMENT_TYPE_4: "SME_EMPLOYMENT_TYPE_4",
    ENTERPRISE_TYPE: "ENTERPRISE_TYPE",
    NEW_PROVINCE: "NEW_PROVINCE",
    MANAGEMENT_EXPERIENCE: "MANAGEMENT_EXPERIENCE",
  },
};
const CORE = {
  DE: "de",
  DE_V2: "decisionEngine",
  PRODUCT: "product",
  LMS_CASHLOAN: "lmsCashloan",
  CRM: "crmService",
  AAD: "aad",
  MASTERDATA: "masterData",
  AAA: "aaa",
  CDL: "CDL",
  LMS_MC: "lmsMc",
  LOS_MC_CREDIT: "losMcCredit",
  WF_VIETTEL: "workflowViettel",
  WF_CASHLOAN: "workflowCashloan",
  ACTION_AUDIT: "actionAudit",
  LOS_UNTIED: "losUnited",
  EKYC_SERVICE: "ekycService",
  DISBURSEMENT: "disbursement",
  REPAYMENT: "repayment",
  LOS_VIETTEL: "losViettel",
  LOS_CASHLOAN: "losCashloan",
  DSA_MBA: "dsaMobileApp",
  FINTOOL: "finTool",
  LMS_CREDIT: "lmsCredit",
  EASY_LUONG: "easyLuongBe",
  EASY_UI_SERVICE: "easy_ui_service",
  EASY_CREDIT_BE: "easyCreditBe",
  DECISIONS_V02: "decisionsV02",
  ANTI_FRAUD: "antiFraud",
};
const STEP = {
  CREATE_CUST: "CREATE_CUST",
  DEDUP: "CHECK_DEDUP",
  MANUAL_DEDUP: "MANUAL_DEDUP",
  MANUAL_DECISION: "MANUAL_DECISION",
  MANUAL_KYC: "MANUAL_KYC",
  CUT_OFF: "CUT_OFF",
  CONVERT_SCORE: "CONVERT_SCORE",
  OFFER: "GET_OFFER",
  S37: "CHECK_S37",
  CREATE_LOAN_ACT: "CREATE_LOAN_ACT",
  ELIGIBLE: "CHECK_ELIGIBLE",
  UPDATE: "UPDATE",
  INSERT: "INSERT",
  DOWNLOAD: "DOWNLOAD",
  UPLOAD: "UPLOAD",
  MC_CK_REPAYMENT_HST: "MC_CK_REPAYMENT_HST",
  CL_CK_REPAYMENT_HST: "CL_CK_REPAYMENT_HST",
  CK_EXIST_CUST: "CK_EXIST_CUST",
  CK_TOPUP: "CK_TOPUP_LIST",
  CK_EKYC: "CK_EKYC",
  CK_OCR: "CK_OCR",
  CK_SANITY: "CK_SANITY",
  CRM_CREATE_SERVICE: "CRM_CREATE_SERVICE",
  CK_DOC: "CK_DOC",
  CANCEL_CONTRACT: "CANCEL_CONTRACT",
  DECISION_ENGINE: "DECISION_ENGINE",
  CALL_DISBURSEMENT: "CALL_DISBURSEMENT",
  RECEIVE_RESULT_DISBURSEMENT: "RECEIVE_RESULT_DISBURSEMENT",
  CANCEL_LOAN: "CANCEL_LOAN",
  CE_RESUBMIT: "CE_RESUBMIT",
  CHECK_DOC: "CHECK_DOC",
  CP_APPROVE: "CP_APPROVE",
  CP_CANCEL: "CP_CANCEL",
  CP_REJECT: "CP_REJECT",
  CP_RESUBMIT: "CP_RESUBMIT",
  DE_PRESCORE: "DE_PRESCORE",
  DE_SCORING: "DE_SCORING",
  DOWNLOAD_ZALO_FILE: "DOWNLOAD_ZALO_FILE",
  MW_CE_APPROVE_CALLBACK: "CE_APPROVE_CALLBACK",
  MW_CE_CALLBACK: "CE_CALLBACK",
  MW_FULL_LOAN_CALLBACK: "FULL_LOAN_CALLBACK",
  MW_LOAN_CALLBACK: "LOAN_CALLBACK",
  MW_UPDATE_STATUS: "UPDATE_STATUS",
  PUSH_VTP_FILE: "PUSH_VTP_FILE",
  SELECT_OFFER: "SELECT_OFFER",
  UPDATE_CUSTOMER: "UPDATE_CUSTOMER",
  REQUEST_DISBURSE: "REQUEST_DISBURSE",
  DMB_CALLBACK: "DMB_CALLBACK",
  S37_OTHER_ID: "CHECK_S37_OTHER_ID",
  CUSTOMER_CALLBACK: "CUSTOMER_CALLBACK",
  VEGA: "CHECK_VEGA",
  SEND_LEAD: "SEND_LEAD",
  SEND_FULL_LOAN_TEL: "SEND_FULL_LOAN",
  UPLOAD_DOC_TEL: "UPLOAD_DOC",
  CREDIT_LIMIT_USAGE: "CREDIT_LIMIT_USAGE",
  CREDIT_ACCOUNT_CREATE: "CREDIT_ACCOUNT_CREATE",
  VDS_CALLBACK: "VDS_CALLBACK",
  LMS_CREATE_LIMIT: "LMS_CREATE_LIMIT",
  LMS_UPGRADE: "LMS_UPGRADE",
  CREDIT_LIMIT_REVERT: "CREDIT_LIMIT_REVERT",
  CREDIT_LIMIT_REMOVED: "CREDIT_LIMIT_REMOVED",
  CALL_LMS_UPGRADE: "CALL_LMS_UPGRADE",
  LMS_GET_LOAN_INFO: "LMS_GET_LOAN_INFO",
  CANCEL_LOAN_ACCOUNT: "CANCEL_LOAN_ACCOUNT",
  CHECK_AGENT_INFO: "CHECK_AGENT_INFO",
  COMPARE_PERSONAL_INFO: "COMPARE_PERSONAL_INFO",
  READ_OCR: "READ_OCR",
  CHECK_CREDIT_SCORE: "CHECK_CREDIT_SCORE",
  CHECK_FRAUD_PHONE: "CHECK_FRAUD_PHONE",
  PUSH_TASK: "PUSH_TASK",
  MANUAL_CHECK_PHONE: "MANUAL_CHECK_PHONE",
  FINAL_DI: "FINAL_DI",
  CHECK_PRODUCT_CONDITION: "CHECK_PRODUCT_CONDITION",
  REQUEST_KUNN: "REQUEST_KUNN",
  REQUEST_SIGN_KUNN: "REQUEST_SIGN_KUNN",
  CALLBACK_CIC_KUNN: "CALLBACK_CIC_KUNN",
  CALLBACK_ACT_KUNN: "CALLBACK_ACT_KUNN",
  CALLBACK_KUNN_LIMIT: "CALLBACK_KUNN_LIMIT",
  CALLBACK_RECEIVED_PAYMENT: "CALLBACK_RECEIVED_PAYMENT",
  GENERAL_FILE: "GENERAL_FILE",
};

const ADDRESS_CODE_TYPE = {
  WARD: "WARD",
  DISTRICT: "DISTRICT",
  PROVINCE: "PROVINCE",
  NEW_WARD: "NEW_WARD",
  NEW_PROVINCE: "NEW_PROVINCE"
};
const DOC_GROUP = {
  MISA_CONTRACT_KUNN: "SME_MISA_HM_LOAN CONTRACT_KUNN",
  MISA_HM_DISBURSAL: "SME_MISA_HM_DISBURSAL DOCS",
  
};

const LOAN_CUSTOMER_SUBJECT = {
  INDIVIDUAL: "INDIVIDUAL",
  ORGANIZATION: "ORGANIZATION",
  PARTNER: "PARTNER",
};

const FINANCIAL_REPORT_TYPE = {
  "200_BCTC": "200_BCTC",
  "133_B01A": "133_B01A",
  "133_B01B": "133_B01B",
  "133_B01": "133_B01",
};

const MAPPING_FINANCIAL_REGIME = {
  "200_BCTC": "TT200",
  "133_B01A": "TT133",
  "133_B01B": "TT133",
  "133_B01": "TT133",
};

const BCTC_MAPPING_REPORT = {
  TT200_BCTC: {
    CTieuTKhaiChinh: "TT200_CDKT",
    PL_KQHDSXKD: "TT200_KQHDKD",
    PL_KQHDXSKD: "TT200_KQHDKD",
    PL_LCTTGT: "TT200_LCTT_GT",
    PL_LCTTTT: "TT200_LCTT_TT",
  },
  TT133_B01A: {
    CTieuTKhaiChinh: "TT133_BCTC_B01A",
    PL_KQHDSXKD: "TT133_HDKD_B02",
    PL_KQHDXSKD: "TT133_HDKD_B02",
    PL_LCTTGT: "TT133_LCTT_GT_B03",
    PL_LCTTTT: "TT133_LCTT_TT_B03",
  },
  TT133_B01B: {
    CTieuTKhaiChinh: "TT133_BCTC_B01B",
    PL_KQHDXSKD: "TT133_HDKD_B02",
    PL_KQHDSXKD: "TT133_HDKD_B02",
    PL_LCTTGT: "TT133_LCTT_GT_B03",
    PL_LCTTTT: "TT133_LCTT_TT_B03",
  },
  TT133_B01: {
    CTieuTKhaiChinh: "TT133_BCTC_B01",
    PL_KQHDXSKD: "TT133_HDKD_B02",
    PL_KQHDSXKD: "TT133_HDKD_B02",
    PL_LCTTGT: "TT133_LCTT_GT_B03",
    PL_LCTTTT: "TT133_LCTT_TT_B03",
  },
};

const BCTC_KEY = {
  PL_KQHDSXKD: "PL_KQHDSXKD",
  PL_LCTTGT: "PL_LCTTGT",
  PL_LCTTTT: "PL_LCTTTT",
};

const KUNN_API_VERSION = {
  V1: "v1",
  V2: "v2",
};

const HTTP_STATUS = {
  BAD_REQUEST: 400,
  INTERNAL_SERVER: 500,
  SUCCESS: 200,
};

const HTTP_STATUS_MESSAGE = {
  [HTTP_STATUS.BAD_REQUEST]: "Bad Request",
  [HTTP_STATUS.INTERNAL_SERVER]: "Internal Server Error",
  [HTTP_STATUS.SUCCESS]: "Success",
};

const TASK_NAME = {
  GEN_CUSTOMER_ESIGNING_FILE: "GEN_CUSTOMER_ESIGNING_FILE",
};

const REF_TABLE = {
  KUNN: "KUNN",
  DOCUMENT: "DOCUMENT",
  INVOICE: "INVOICE",
  LOAN_CONTRACT: "LOAN_CONTRACT",
  LOAN_CONTRACT_DOCUMENT: "LOAN_CONTRACT_DOCUMENT",
  REPRESENTATION: "LOAN_CUSTOMER_REPRESENTATIONS",
  MANAGER: "LOAN_CUSTOMER_MANAGERS",
  SHAREHOLDER: "LOAN_CUSTOMER_SHAREHOLDERS",
  OWNER: "LOAN_BUSINESS_OWNER",//LOAN_CUSTOMER_OWNERS
  PARTNER: "LOAN_CUSTOMER_PARTNERS",
  REVENUE: 'LOAN_REVENUES',
  VAT_FORM: 'LOAN_VAT_FORMS',
  INVOICE_DOCUMENT: 'INVOICE_DOCUMENT',
  WAREHOUSE: "LOAN_CUSTOMER_WAREHOUSES",
  BRANCH: "LOAN_BRANCH_ADDRESS",
  LOAN_CUSTOMER: 'LOAN_CUSTOMER',
};

const LENDER_REQUEST_TYPE = {
  LENDER_KUNN_REJECT_CHANGE_REQ: "LENDER_KUNN_REJECT_CHANGE_REQ",
  LENDER_KUNN_APPROVE_CHANGE_REQ: "LENDER_KUNN_APPROVE_CHANGE_REQ",
  LENDER_KUNN_CANCEL: "LENDER_KUNN_CANCEL",
  LENDER_KUNN_PROCESS: "LENDER_KUNN_PROCESS",
  LENDER_KUNN_RESUBMIT: "LENDER_KUNN_RESUBMIT",
  LENDER_KUNN_APPROVE: "LENDER_KUNN_APPROVE",
  LENDER_LOAN_PROCESS: "LENDER_LOAN_PROCESS",
  LENDER_LOAN_AF3_PROCESS: "LENDER_LOAN_AF3_PROCESS",
  LENDER_LOAN_CANCEL: "LENDER_LOAN_CANCEL",
  LENDER_LOAN_APPROVE: "LENDER_LOAN_APPROVE",
  LENDER_LOAN_REJECT: "LENDER_LOAN_REJECT",
  LENDER_LOAN_RESUBMIT: "LENDER_LOAN_RESUBMIT",
  //approve change
  LENDER_LOAN_APPROVE_CHANGE_REQ: "LENDER_LOAN_APPROVE_CHANGE_REQ",
  LENDER_LOAN_REJECT_CHANGE_REQ: "LENDER_LOAN_REJECT_CHANGE_REQ",
  //approve af3 change
  LENDER_LOAN_APPROVE_AF3_CHANGE_REQ: "LENDER_LOAN_APPROVE_AF3_CHANGE_REQ",
  LENDER_LOAN_REJECT_AF3_CHANGE_REQ: "LENDER_LOAN_REJECT_AF3_CHANGE_REQ",
  LENDER_LOAN_RESUBMIT_AF3_CHANGE_REQ: "LENDER_LOAN_RESUBMIT_AF3_CHANGE_REQ",
  //sub info
  LENDER_KUNN_SUB_INFO_PROCESS: "LENDER_KUNN_SUB_INFO_PROCESS",
  LENDER_LOAN_SUB_INFO_PROCESS: "LENDER_LOAN_SUB_INFO_PROCESS",
};

const LENDER_REFERENCE_TYPE = {
  KUNN: "KUNN",
  LOAN_CONTRACT: "LOAN_CONTRACT",
  LOAN_CONTRACT_DOCUMENT: "LOAN_CONTRACT_DOCUMENT",
  LOAN_CUSTOMER: 'LOAN_CUSTOMER',
  REPRESENTATION: "LOAN_CUSTOMER_REPRESENTATIONS",
  MANAGER: "LOAN_CUSTOMER_MANAGERS",
  SHAREHOLDER: "LOAN_CUSTOMER_SHAREHOLDERS",
  PARTNER: "LOAN_CUSTOMER_PARTNERS",
  INVOICE: "INVOICE",
  INVOICE_DOCUMENT: "INVOICE_DOCUMENT",
  OWNER: "LOAN_BUSINESS_OWNER",
  WAREHOUSE: "LOAN_CUSTOMER_WAREHOUSES",
  BRANCH: "LOAN_BRANCH_ADDRESS",
};

const OWNER_ID = {
  EVF: "EVF",
  PARTNER: "PARTNER",
};

const KUNN_TEMPLATE_DOCTYPE = ["BTTKUNN", "BTTXNCN", "BTTTTCN", "BTTTTRV"];
const KUNN_TEMPLATE_DOCTYPE_SIGNED = ["BTTKUNN", "BTTXNCN", "BTTTTCN"];
const KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM = ["VLDKUNN"];
const KUNN_TEMPLATE_DOCTYPE_SIGNED_WITH_PARTNER = {
  BZHM: ["VLDKUNN"],
};
const BZHM_DOCS = ["VLDHDTD", "VLDBCTD", "VLDQDCV", "VLDCKBL"];
const BZHM_DOCS_SIGNED = ["VLDHDTD", "VLDCKBL"];
const AF3_TEMPLATE_DOCTYPE = ["BTTHDTD", "BTTCNKPT", "BTTKCBB", "BTTQDCV","BTTBCTD"];
const AF3_TEMPLATE_DOCTYPE_SIGNED = ["BTTHDTD", "BTTCNKPT", "BTTKCBB", "BTTQDCV"];
const KUNN_DOCS_BIZZ = ["BTTTLK", "BTTHDNCC"];
const KUNN_DOCS_BZHM = ["VLDVATKUN", "VLDHDNCC"];
const FINV_DOCTYPES = ["FVR", "LCT", "LD"];
const FINV_KUNN_DOCTYPES = ["LCTKU", "TTRV"];
const SUB_INFO_TYPE = {
  INVOICE: "INVOICE",
  FILE: "FILE",
  LOAN_CONTRACT_DOCUMENT: "LOAN_CONTRACT_DOCUMENT"
};
const KUNN_TEMPLATE_DOCTYPES_AND_PARTNER_MAPPING = {
  BZHM: [],
};
const CHECK_CIC_DETAILS_SUBJECT = {
  MAIN_SME: "MAIN_SME",
  OWNER: "OWNER",
  PARTNER: "PARTNER",
  REPRESENTATION: "REPRESENTATION",
  PARTNER_OF_OWNER: "PARTNER_OF_OWNER",
  SHAREHOLDER: "SHAREHOLDER",
};

const BIZZ_KUNN_DEFAULT_PRODUCT_CODE = "BIZZ_BTT_01";
const BIZZ_KUNN_DISBURSEMENT_BUNDLE = "EVF_DNRUTVON_BTT";
const BIZZ_CALLBACK_TYPE = {
  REQUEST_DISBURSE: "REQUEST_DISBURSE",
  REQUEST_DISBURSE_SIGNED: "REQUEST_DISBURSE_SIGNED",
}

const FINV_KUNN_DEFAULT_PRODUCT_CODE = "BIZZ_BTT_01";

const DOCUMENT_REFERENCE_TABLE = {
  INVOICE: 'invoices',
  DISBURSEMENT_INFO: 'kunn_disbursement_info',
}

const LENDER_CONFIG_GROUP_TYPE = {
  AF2: "AF2",
  AF2_COMMON: "AF2_COMMON",
  AF2_FINANCE_INFO: "AF2_FINANCE_INFO",
  AF2_CUST_INFO: "AF2_CUST_INFO",
  AF2_PERMANENT_ADDRESS: "AF2_PERMANENT_ADDRESS",
  AF2_CURRENT_ADDRESS: "AF2_CURRENT_ADDRESS",
  AF2_SPOUSE: "AF2_SPOUSE",
  AF2_BUSINESSES_INFO: "AF2_BUSINESSES_INFO",
  AF2_REFERENCE1: "AF2_REFERENCE1",
  AF2_REFERENCE2: "AF2_REFERENCE2",
  AF2_LOAN_PROPOSAL: "AF2_LOAN_PROPOSAL",
  AF2_BUSINESS_OPERATIONS: "AF2_BUSINESS_OPERATIONS",
};

const BIZZ_MODEL_DATA_TYPE = {
  number: "number",
  string: "string",
  currency: "currency",
  percent: "percent",
  month: "month",
}

const SIGNED_TYPE = {
  PARTNER_SIGNED: "PARTNER_SIGNED",
  EVF_SIGNED: "EVF_SIGNED"
}

const TABLE = {
  LOAN_CONTRACT_DOCUMENT: "loan_contract_document",
  LOAN_CONTRACT_DOCUMENT_HISTORY: "loan_contract_document_history",
  LOAN_CONTRACT: "loan_contract",
  KUNN: "kunn",
  INVOICES: "invoices",
}

const REFUND_STATUS = {
  NEW: "NEW",
  WAITING_REFUND: "WAITING_REFUND",
  SUCCESS: "SUCCESS",
};

const REFUND_ACTIONS = {
  REFUND: "REFUND",
  TRANSFER: "TRANSFER"
}

const REFUND_REQUEST_STATUS = {
  NEW: "NEW",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  CANCELLED: "CANCELLED",
  TRANSFERRED: "TRANSFERRED",
};

const MIME_TYPE = {
  jpeg: 'image/jpeg',
  jpg: 'image/jpeg',
  png: 'image/png',
  gif: 'image/gif',
  pdf: 'application/pdf',
  doc: 'application/msword',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  xls: 'application/vnd.ms-excel',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  xml: 'application/xml',
}

const ALLOW_FILE_EXTENSION = [
  'xlsx', 'xls', 'docx', 'doc', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'xml'
]

const TERMINATION_TYPE = {
  PARTIAL: "PARTIAL",
  FULL: "FULL"
}

const FINV_CALLBACK_OPERATION = {
  AF1: 9050,
  AF2: 9051,
  AF3: 9052,
  KUNN: 9053,
}

module.exports = {
  MASTER_DATA,
  CORE,
  STEP,
  ADDRESS_CODE_TYPE,
  DOC_GROUP,
  LOAN_CUSTOMER_SUBJECT,
  FINANCIAL_REPORT_TYPE,
  MAPPING_FINANCIAL_REGIME,
  BCTC_MAPPING_REPORT,
  BCTC_KEY,
  KUNN_API_VERSION,
  HTTP_STATUS,
  HTTP_STATUS_MESSAGE,
  TASK_NAME,
  REF_TABLE,
  LENDER_REQUEST_TYPE,
  LENDER_REFERENCE_TYPE,
  OWNER_ID,
  KUNN_TEMPLATE_DOCTYPE,
  AF3_TEMPLATE_DOCTYPE,
  AF3_TEMPLATE_DOCTYPE_SIGNED,
  BZHM_DOCS,
  BZHM_DOCS_SIGNED,
  SUB_INFO_TYPE,
  KUNN_TEMPLATE_DOCTYPE_SIGNED,
  CHECK_CIC_DETAILS_SUBJECT,
  BIZZ_KUNN_DEFAULT_PRODUCT_CODE,
  BIZZ_KUNN_DISBURSEMENT_BUNDLE,
  BIZZ_CALLBACK_TYPE,
  DOCUMENT_REFERENCE_TABLE,
  LENDER_CONFIG_GROUP_TYPE,
  BIZZ_MODEL_DATA_TYPE,
  SIGNED_TYPE,
  TABLE,
  REFUND_STATUS,
  REFUND_REQUEST_STATUS,
  REFUND_ACTIONS,
  KUNN_DOCS_BIZZ,
  MIME_TYPE,
  ALLOW_FILE_EXTENSION,
  FINV_KUNN_DEFAULT_PRODUCT_CODE,
  FINV_DOCTYPES,
  FINV_KUNN_DOCTYPES,
  KUNN_TEMPLATE_DOCTYPES_AND_PARTNER_MAPPING,
  KUNN_TEMPLATE_DOCTYPE_SIGNED_BZHM,
  KUNN_TEMPLATE_DOCTYPE_SIGNED_WITH_PARTNER,
  TERMINATION_TYPE,
  KUNN_DOCS_BZHM,
  FINV_CALLBACK_OPERATION
};
