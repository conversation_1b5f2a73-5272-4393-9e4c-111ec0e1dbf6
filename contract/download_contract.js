const s3Service = require("../upload_document/s3-service")
const url = require("url");
const utils = require("../utils/helper");
const { checkContract } = require("./get-contract-info");
const sqlHelper = require("../utils/sqlHelper");
const { BadRequestResponse, ServerErrorResponse, Response } = require("../base/response");
const { ERROR_CODE, RESP_MESSAGE } = require("../const/definition");

const downloadFileContract = (req, res) => {
    try {
        const contractNumber = req.query.contractnumber

        if (contractNumber === undefined || contractNumber === '') {
            return res.status(400).send({ code: 'INVALID_REQUEST', message: 'contractNumber param invalid.' })
        } else {
            //https://ms-los-ap-southeast-1-446567516155-document.s3.ap-southeast-1.amazonaws.com/mc-credit/unsigned-contract/20210510/MER2021511939648420.pdf
            req.poolRead.query("select unsigned_contract from loan_esigning where contract_number=$1", [contractNumber])
                .then(result => {
                    if (result.rows === undefined) {
                        return res.status(400).send({ code: 'INVALID_REQUEST', message: 'contractNumber param invalid.' })
                    }
                    else {
                        let unsignedFileUrl = result.rows[0].unsigned_contract
                        let unsignedFileKey = url.parse(unsignedFileUrl).path.slice(1)
                        s3Service.downloadFile(req.config.data, unsignedFileKey)
                            .then(buffer => {
                                updateSigningStatus(req.poolWrite, contractNumber)
                                return res.status(200).send(Buffer.from(buffer.Body).toString('base64'))
                            })
                            .catch(err => {
                                console.log(err)
                                return res.status(500).json({ code: 'ERROR', message: 'service error.' })
                            })
                    }

                })
                .catch(error => {
                    console.log(error)
                    return res.status(500).json({ code: 'ERROR', message: 'download contract error.' })
                })
        }
    }
    catch (error) {
        console.log(error)
        return res.status(500).json({ code: 'ERROR', message: 'service error.' })
    }
}

async function downloadFileContractForSmePartner(req, res) {
    try {
        const contractNumber = req.query.contractNumber
        const signData = await checkContract(req.poolRead, contractNumber);
        if (utils.isNullOrEmpty(signData)) {
            return res.status(400).send({ code: 'INVALID_REQUEST', message: 'isSigned param invalid.' })
        }
        const isSigned = signData?.rows[0].contract_signed_path == null ? false : true;
        // const isSigned = req.query.isSigned

        if (contractNumber === undefined || contractNumber === '') {
            return res.status(400).send({ code: 'INVALID_REQUEST', message: 'contractNumber param invalid.' })
        }
        let sql = "select unsigned_contract from loan_esigning where contract_number=$1";
        if (isSigned == true) {
            sql = "select contract_signed_path from loan_esigning where contract_number=$1";
        }

        req.poolRead.query(sql, [contractNumber]).then(result => {
            if (result.rowCount == 0) {
                return res.status(400).send({ code: 'INVALID_REQUEST', message: 'contractNumber param invalid.' })
            }
            else {
                let fileUrl;
                if (isSigned == 1) {
                    fileUrl = result.rows[0].contract_signed_path;
                }
                else {
                    fileUrl = result.rows[0].unsigned_contract;
                }
                let fileKey = url.parse(fileUrl).path.slice(1)
                s3Service.downloadFile(req.config.data, fileKey)
                    .then(buffer => {
                        // res.writeHead(200, { 'Content-Type': 'application/pdf' });
                        res.setHeader('Content-Type', 'application/pdf');
                        if (isSigned == 0) updateSigningSmeStatus(req.poolWrite, contractNumber)
                        return res.status(200).end(Buffer.from(buffer.Body))
                    })
                    .catch(err => {
                        console.log(err)
                        return res.status(500).json({ code: 'ERROR', message: 'service error.' })
                    })
            }

        })
            .catch(error => {
                console.log(error)
                return res.status(500).json({ code: 'ERROR', message: 'download contract error.' })
            })

    }
    catch (error) {
        console.log(error)
        return res.status(500).json({ code: 'ERROR', message: 'service error.' })
    }
}

function updateSigningStatus(poolWrite, contractNumber) {
    let sql = "update loan_esigning set status=$1 where contract_number=$2"
    poolWrite.query(sql, ['SIGN IN PROGRESS', contractNumber])
}

function updateSigningSmeStatus(poolWrite, contractNumber) {
    let sql = "update loan_esigning set status=$1 where contract_number=$2"
    poolWrite.query(sql, ['SME PARTNER SIGN IN PROGRESS', contractNumber])
}

const downloadFileForSmePartner = async (req, res) => {
    try {
        const { contractNumber, partnerCode, fileKey, contractType, debtContractNumber } = req.query || {}
        if(contractType !== "KUNN")
        {
            const [
                loan,
                document
            ] = await Promise.all([
                sqlHelper.findOne({
                    table: 'loan_contract',
                    whereCondition: {
                        contract_number: contractNumber,
                        partner_code: partnerCode
                    }
                }),
                sqlHelper.findOne({
                    table: 'loan_contract_document',
                    whereCondition: {
                        file_key: fileKey,
                        contract_number: contractNumber
                    },
                    orderBy: {
                        creation_time: 'desc'
                    }
                })
            ])
            if (!loan?.id) {
                const errors = [{
                    errorCode: 'contractNumber',
                    errorMessage: `not found`
                }]
                return res.status(400).json(new BadRequestResponse(errors));
            }
            if (!document?.id) {
                const errors = [{
                    errorCode: 'fileKey',
                    errorMessage: `not found`
                }]
                return res.status(400).json(new BadRequestResponse(errors));
            }
        }
        else {
            const[ kunn,
                document
            ] = await Promise.all([
                sqlHelper.findOne({
                    table: 'kunn',
                    whereCondition: {
                        kunn_id: debtContractNumber                
                    }
                }),
                sqlHelper.findOne({
                    table: 'loan_contract_document',
                    whereCondition: {
                        file_key: fileKey,
                        kunn_contract_number: debtContractNumber
                    },
                    orderBy: {
                        creation_time: 'desc'
                    }
                })
            ])
            if (!kunn?.kunn_id) {
                const errors = [{
                    errorCode: 'debtContractNumber',
                    errorMessage: `not found`
                }]
                return res.status(400).json(new BadRequestResponse(errors));
            }
            if (!document?.id) {
                const errors = [{
                    errorCode: 'fileKey',
                    errorMessage: `not found`
                }]
                return res.status(400).json(new BadRequestResponse(errors));
            }
        }

        const buffer = await s3Service.downloadFile(req.config.data, fileKey);
        if (!buffer) {
            return res.status(500).json(new ServerErrorResponse());
        }

        return res.status(200).json(new Response(
            ERROR_CODE.SUCCESS,
            RESP_MESSAGE.SUCCESS, {
            data: Buffer.from(buffer.Body).toString('base64')
        }));
    } catch (error) {
        console.log(error)
        return res.status(500).json(new ServerErrorResponse());
    }
}

const downloadFileKunnForSmePartner = async (req, res) => {
    try {
        const { debtContractNumber, fileKey } = req.query || {}
        const [
            kunn,
            document
        ] = await Promise.all([
            sqlHelper.findOne({
                table: 'kunn',
                whereCondition: {
                    kunn_id: debtContractNumber                
                }
            }),
            sqlHelper.findOne({
                table: 'loan_contract_document',
                whereCondition: {
                    file_key: fileKey,
                    kunn_contract_number: debtContractNumber
                },
                orderBy: {
                    creation_time: 'desc'
                }
            })
        ])
        if (!kunn?.id) {
            const errors = [{
                errorCode: 'debtContractNumber',
                errorMessage: `not found`
            }]
            return res.status(400).json(new BadRequestResponse(errors));
        }
        if (!document?.id) {
            const errors = [{
                errorCode: 'fileKey',
                errorMessage: `not found`
            }]
            return res.status(400).json(new BadRequestResponse(errors));
        }

        const buffer = await s3Service.downloadFile(req.config.data, fileKey);
        if (!buffer) {
            return res.status(500).json(new ServerErrorResponse());
        }

        return res.status(200).json(new Response(
            ERROR_CODE.SUCCESS,
            RESP_MESSAGE.SUCCESS, {
            data: Buffer.from(buffer.Body).toString('base64')
        }));
    } catch (error) {
        console.log(error)
        return res.status(500).json(new ServerErrorResponse());
    }
}
module.exports = {
    downloadFileContract,
    downloadFileContractForSmePartner,
    downloadFileForSmePartner,
    downloadFileKunnForSmePartner
}