const s3Service = require("../upload_document/s3-service")
const PizZip = require('pizzip')
const docxtemplater = require('docxtemplater')
const fs = require("fs")
const uuid = require("uuid")
const common = require("../utils/common")
const moment = require('moment-timezone')
const libre = require('libreoffice-convert');
moment().tz('Asia/Ho_Chi_Minh').format()
const unsigned_folder = "/mc-credit/unsigned-contract"
const calculator_repayment_folder = '/mc-credit/calculator-repayment-contract'
const { getPlace,getValueCode, getValueCode_v3 } = require('../utils/masterdataService')
const { numberToString } = require('../utils/numberToString')
const smsService = require("../utils/smsService")
const utils = require("../utils/helper")
const productService = require("../utils/productService")
const callbackService  = require("../utils/callbackService")
const {STATUS} = require("../const/caseStatus")
const { PARTNER_CODE } = require("../const/definition")
const { getLoanContract } = require("../repositories/loan-contract-repo")
const dateHelper = require("../utils/dateHelper")
const VNnum2words = require('vn-num2words');

async function getLimitContractData(poolRead, contractNumber, config, rootContractNumber=undefined) {
    // get current date 
    // const curDate = new Date()
    // const day = curDate.getDate()
    // const month = curDate.getMonth() + 1
    // const year = curDate.getFullYear()
    let sql = 'select lc.cust_full_name,lc.birth_date,lc.id_number,lc.id_issue_dt,lc.id_issue_place,lc.address_cur,lc.village_cur,lc.district_cur,lc.province_cur,lc.address_per,lc.village_per,lc.district_per,lc.province_per,lc.phone_number1,lc.email,los.offer_amt,los.tenor,los.int_rate from loan_contract lc join loan_offer_selection los on lc.offer_id = los.id where lc.contract_number=$1;';
    await poolRead.query(sql, [contractNumber])
    let lb = config.basic.product[config.env];
    let prodUri = config.data.productService.getTemplate;

    let productUrl = lb + prodUri + "?producttype=MC"
    await common.getAPI(productUrl)
    const req = {
        poolRead,
        config
    }
    const fulFillData = getContractData(req, contractNumber, rootContractNumber)
    return fulFillData
}

const getContract = async (req, contract_number) => {
    const sql = "select * from loan_contract where contract_number = $1"
    const result = await req.poolRead.query(sql, [contract_number])
    if (result.rows) return result.rows[0]
    else return null
}


const getContractOffer = async (req, contract_number) => {
    const sql = "select * from loan_offer_selection where contract_number = $1 and is_selected = 1 AND kunn_id IS NULL "
    const result = await req.poolRead.query(sql, [contract_number])
    if (result.rows) return result.rows[0]
    else return null
}


Number.prototype.format = function (n, x) {
    let re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\.' : '$') + ')';
    return this.toFixed(Math.max(0, ~~n)).replace(new RegExp(re, 'g'), '$&,');
};

async function getContractData(req, contract_number, rootContractNumber=undefined) {
    const contract = await getContract(req, contract_number)
    const contractOffer = await getContractOffer(req, contract_number)
    const contractRootData = await getLoanContract(rootContractNumber)
    const masterData = await Promise.all([
        getValueCode_v3(contract.id_issue_place,"ISSUE_PLACE_VN"),
        getValueCode_v3(contract.ward_cur,"WARD"),
        getValueCode_v3(contract.district_cur,'DISTRICT'),
        getValueCode_v3(contract.province_cur,'PROVINCE'),
        getValueCode_v3(contract?.other_issue_place,"ISSUE_PLACE_VN"),
        getValueCode_v3(contract.bank_code,'BANK'),
        getValueCode_v3(contract.ward_per,"WARD"),
        getValueCode_v3(contract?.district_per,'DISTRICT'),
        getValueCode_v3(contract?.province_per,'PROVINCE'),
    ])
    let cur_address = `${contract?.address_cur}, ${masterData[1]}, ${masterData[2]}, ${masterData[3]}`;
    let per_address = `${contract?.address_per}, ${masterData[6]}, ${masterData[7]}, ${masterData[8]}`;
    let issue_date_oth = !utils.isNullOrEmpty(contract?.other_issue_date)?dateHelper.formatDate(contract?.other_issue_date, "DD/MM/YYYY"):'';
    let loan_amount = parseFloat(contract.approval_amt);
    loan_amount = loan_amount || 0;
    let loan_amount_text = VNnum2words(loan_amount).trim();
    loan_amount_text = loan_amount_text.charAt(0).toUpperCase() + loan_amount_text.slice(1);
    const currentDate = new Date()
    const dataFulFill = {
        date: currentDate.getDate(),
        month: currentDate.getMonth() + 1,
        year: currentDate.getFullYear(),
        contract_number: contract_number || '',
        duration : 36,
        // contract_approval_date: moment(currentDate).format('DD-MM-YYYY') || '',
        customer_name: contract.cust_full_name || '',
        date_of_birth: moment(contract.birth_date).format('DD-MM-YYYY') || '',
        identify_id: contract.id_number || '',
        identify_date: moment(contract.id_issue_dt).format('DD-MM-YYYY') || '',
        identify_address: await getValueCode(req,contract.id_issue_place,'ISSUE_PLACE_VN') || '',
        current_address: contract.address_cur || '',
        current_ward: await getPlace(req, contract.ward_cur, 'wards') || '',
        current_district: await getPlace(req, contract.district_cur, 'districts') || '',
        current_province: await getPlace(req, contract.province_cur, 'provinces') || '',
        // per_address: contract.address_per || '',
        per_ward: await getPlace(req, contract.ward_per, 'wards') || '',
        per_district: await getPlace(req, contract.district_per, 'districts') || '',
        per_province: await getPlace(req, contract.province_per, 'provinces') || '',
        phone_number: contract.phone_number1 || '',
        email: contract.email || '',
        approval_amount: utils.numberWithCommas(parseInt(contractOffer.offer_amt)) || '',
        approval_amount_string: numberToString(+contractOffer.offer_amt),
        ngay : dateHelper.getDate(),
        thang : dateHelper.getMonth(),
        nam : dateHelper.getYear(),
        contract_number_old: rootContractNumber,
        identity_card: contract.id_number || '',
        issue_date: dateHelper.formatDate(contract.id_issue_dt, "DD/MM/YYYY"),
        issue_place: masterData[0],
        identity_card_oth: contract?.other_id_number||'',
        issue_date_oth: issue_date_oth||'',
        issue_place_oth: masterData[4]||'',
        account_number: contract.bank_account,
        bank_name: masterData[5],
        cur_address,
        per_address,
        contract_approval_date: moment(contractRootData?.approval_date).format('DD/MM/YYYY') || '',
        loan_amount,
        loan_amount_text
    }
    return dataFulFill
}

function fillData2Template(filePath, contractData) {
    try {
        let contractTemplatePath = filePath
        let fileBuffer = fs.readFileSync(contractTemplatePath, 'binary')
        let zip = new PizZip(fileBuffer)
        let doc = new docxtemplater(zip)
        doc.setData(contractData)

        doc.render()
        let buf = doc.getZip().generate({ type: 'nodebuffer' })
        let fileId = uuid.v4()
        let saveFilePath = "./static_file/" + fileId + ".docx"
        fs.writeFileSync(saveFilePath, buf)
        return saveFilePath
    }
    catch (error) {
        console.log(error)
    }
}

function convert2push(filePath, contractData, prefix, config, contractNumber) {
    return new Promise(function (resolve, reject) {
        let tempFilePath = fillData2Template(filePath, contractData)
        const file = fs.readFileSync(tempFilePath);

        libre.convert(file, ".pdf", undefined, async (err, done) => {
            if (err) {
                reject(err)
                console.log('Error converting file:err');
            }
            else {
                let fileName = contractNumber + ".pdf"
                try {
                    let data = await s3Service.upload(config.data, fileName, done, prefix)
                    fs.unlink(tempFilePath)
                    resolve(data.Location)
                } catch (error) {
                    reject(error)
                    console.log("catch error " + error)
                }
            }
        })
    })
}



async function generateContract(req, contractNumber) {
    const loanData = await getLoanContract(contractNumber);
    const rootContractNumber = loanData?.root_contract_number;
    let filePath = "./static_file/hanmuc_contract.docx"
    if(loanData.partner_code == PARTNER_CODE.SMA) filePath = "./static_file/hanmuc_contract_sma.docx"
    if(!utils.isNullOrEmpty(rootContractNumber)) filePath = "./static_file/phu_luc_review_hm.docx"
    const poolRead = req.poolRead
    const poolWrite = req.poolWrite
    const config = req.config
    const smsUrl = config.data.smsService.sendSMS
    const isEnable = config.data.smsService.useSMS
    let msg = config.data.smsService.esigningHMMsg
    if([PARTNER_CODE.MCAPP,PARTNER_CODE.SMA].includes(loanData?.partner_code)) msg = config.data.smsService.esigningHMMcaMsg
    msg = msg.replace("contractNumber", contractNumber)

    const date = new Date()
    //let contractData = await getContractData(poolRead,contractNumber,config)
    const contractData = await getLimitContractData(poolRead, contractNumber, config, rootContractNumber)

    return new Promise(function (resolve, reject) {
        convert2push(filePath, contractData, unsigned_folder, config, contractNumber)
        .then(async fileLocation => {
            let sql = "insert into loan_esigning(contract_number,status,unsigned_contract,updated_date) values($1,$2,$3,$4)"
            poolWrite.query(sql, [contractNumber, "not signed yet", fileLocation, date])
            const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
            utils.saveStatus(poolWrite,contractNumber,STATUS.WAITING_TO_BE_SIGNED)

            try {
                if (phoneNumberRs !== undefined) {
                    if (isEnable) {
                        await smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs)
                    }
                }
            }
            catch (error) {
                console.log(error)
            }
            try {
                await callbackService.callbackAprroved(poolWrite,config,contractNumber)
            }
            catch(callbackErr) {
                console.log(callbackErr)
            }
            resolve(true)
        })
        .catch(error => {
            console.log(error)
            reject(false)
        })
    })
}

async function genCalculatorRepayment(req, res) {
    try {
        const config = req.config
        const poolRead = req.poolRead
        
        // const date = new Date()
        let pl = req.body
        let contract_data = pl.contract_data
        const contractNumber = contract_data.contract_number_parent
        const contractData = await utils.getAllContractData(poolRead,contractNumber)
        const partnerCode = contractData?.partner_code;
        const productData = await productService.getProductLimitInfo(config ,contractData.product_code)
        const smeData = await Promise.all([
            getPlace(req, contractData.sme_headquarters_ward, 'wards'),
            getPlace(req, contractData.sme_headquarters_district, 'districts'),
            getPlace(req, contractData.sme_headquarters_province, 'provinces')
        ])
        const current_ward =  await getPlace(req, contractData.ward_cur, 'wards') || ''
        const current_district = await getPlace(req, contractData.district_cur, 'districts') || ''
        const current_province = await getPlace(req, contractData.province_cur, 'provinces') || ''
        contract_data.loan_amt_text = numberToString(contract_data.loan_amt)
        contract_data.loan_purpose = await getValueCode(req,contractData.loan_purpose,"LOAN_PURPOSE") || ''
        contract_data.product_name = productData.prdctName || contractData.product_code
        contract_data.address = contractData.address_on_license;
        contract_data.address_on_license = contractData.address_on_license;
        let filePath = "./static_file/Lich_Thanh_toan.docx"
        contract_data.address_cur = contractData.address_cur + ', ' + current_ward + ', ' + current_district + ', ' + current_province || ''
        if(partnerCode===PARTNER_CODE.MISA){
            if(contract_data.phone == undefined) contract_data.phone = contractData?.sme_phone_number||'';
            filePath = "./static_file/Lich_Thanh_toan_sme_v2.docx";
            contract_data.address_cur = contractData.sme_headquarters_address + ', ' + smeData[0] + ', ' + smeData[1] + ', ' + smeData[2] || '';
            contract_data.customer_name = contractData?.sme_name;
            contract_data.id_number = contractData?.sme_tax_id;
        }

        let contract_number = 'WELCOME_PACKAGE_'+contract_data.contract_number

        let fileLocation = await convert2push(filePath, contract_data, calculator_repayment_folder, config, contract_number)
        res.status(200).json({
            code: 0,
            message: 'success',
            data: fileLocation
        })
    } catch (err) {
        console.log(err)
        res.status(500).json({
            code: 999,
            message: err.message
        })
    }
}

//convert2push('./static_file/export_saoke.docx',{},'/MISA/SAOKE',global.config,'123')

async function genMisa(req,res) {
    try {
        const filePath = "./static_file/export_saoke.docx"
        await convert2push(filePath,{},'')
    }
    catch(err) {
        console.log(err)
        return common.responseErrorPublic(err)
    }
}

module.exports = {
    generateContract,
    convert2push,
    genCalculatorRepayment
}

