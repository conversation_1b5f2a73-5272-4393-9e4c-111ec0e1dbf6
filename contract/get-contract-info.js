const camelcaseKeys = require('camelcase-keys');
const {detectType} = require('../utils/detectContractType')
const utils = require("../utils/helper")
const callbackService = require("../utils/callbackService")
const common = require("../utils/common")
const {STATUS} = require("../const/caseStatus")
const loanContractRepo = require("../repositories/loan-contract-repo");
const { PARTNER_CODE, DOC_TYPE } = require('../const/definition');
const { getContractByKU } = require('../repositories/kunn-repo');

function getContractInfo(req, res) {
	try {
		let contractNumber = req.query.contractnumber
		let sql1 = "select * from loan_score where contract_number=" + "'" + contractNumber + "'"
		let sql2 = "select * from loan_turnover_transaction where contract_number=" + "'" + contractNumber + "'"
		let sql3 = "select * from loan_contract where contract_number=" + "'" + contractNumber + "'"
		let all = [req.poolRead.query(sql1), req.poolRead.query(sql2), req.poolRead.query(sql3)]
		//all.push(req.poolRead.query(sql1))
		Promise.all(all)
			.then(rs => {
				let rs1 = rs[0].rows
				let rs2 = rs[1].rows
				let rs3 = rs[2].rows
				res.status(200).json({
					"contractNumber": contractNumber,
					"score": rs1,
					"turn_tran": rs2,
					"info": rs3
				})
			})
			.catch(error => {
				console.log(error)
				res.status(500).json({
					"msg": "service error",
					"code": 1
				})
			})
	}
	catch (error) {
		console.log(error)
		res.status(500).json({
			"msg": "service error",
			"code": 1
		})
	}
}


async function getEsignContractInfo(req, res) {
	const poolWrite = req.poolWrite
	const poolRead = req.poolRead
	const config = req.config
	try {
		let contractNumber = req.query.contractnumber
		const isKunn = await detectType(contractNumber, req) === "KU" 
		const sql1 = "select contract_number,channel,contract_type,status,pstatus,cust_full_name,id_number,id_issue_dt,id_issue_place,address_cur,province_cur,district_cur,ward_cur,village_cur,address_per,province_per,district_per,ward_per,village_per,birth_date,phone_number1,partner_code,product_code from loan_contract where contract_number=$1"
		const sql2 = "select doc_type,doc_id,file_key as file_path from loan_contract_document where contract_number =$1"
		let sqlGetSpic = ""
		let path_spic = ""
		let contractStatus;
		if (isKunn) {
			const contractRoot = await getContractByKU(contractNumber)
			sqlGetSpic = `select * from loan_contract_document lcd where doc_type = 'SPIC' and is_deleted = 0 and contract_number = $1 limit 1`
			path_spic = (await req.poolRead.query(sqlGetSpic, [contractRoot])).rows[0]?.file_key
			const kunnNumber = contractNumber
			const sql1kunn = `select contract_number from kunn where kunn_id = $1`
			const contractNumberKunn = await req.poolRead.query(sql1kunn, [contractNumber])
			contractStatus = await utils.getOneKuStatus(poolRead,kunnNumber)
			if (contractNumberKunn.rows.length > 0) {
				contractNumber = contractNumberKunn.rows[0].contract_number
			}
			callbackService.callbackEsign(poolWrite,config,contractNumber,'disbursement-request',kunnNumber)
		}
		else {
			sqlGetSpic = `select * from loan_contract_document lcd where doc_type = 'SPIC' and is_deleted = 0 and contract_number = $1 limit 1`
			path_spic = (await req.poolRead.query(sqlGetSpic, [contractNumber])).rows[0]?.file_key
			callbackService.callbackEsign(poolWrite,config,contractNumber)
			contractStatus = await loanContractRepo.getContractStaus(contractNumber)
		}

		if(contractStatus!= STATUS.WAITING_CUSTOMER_SIGNATURE && contractStatus !==  STATUS.WAITING_TO_BE_SIGNED && contractStatus !== 'KKH09' && contractStatus !== STATUS.CP_RESUBMIT_ESIGN && contractStatus != STATUS.SIGNED_WO_DOCS && contractStatus !== STATUS.SS_RESUBMIT_ESIGN && contractStatus !== STATUS.SIGNING_IN_PROGRESS)  {
			return res.status(400).json({
				"code" : 0,
				"msg" : "contract is not eligible for signing"
			})
		}
		const checkRs = await checkContract(poolRead,req.query.contractnumber)
		if(checkRs.rows.length==0) {
			return res.status(404).json({
				"msg": "can not find contract",
				"code":  0
			})
		}

		Promise.all([req.poolRead.query(sql1, [contractNumber]), req.poolRead.query(sql2, [contractNumber])])
			.then(result => {

				let rs1 = result[0]
				let rs2 = result[1]

				let data = {}
				let format_data = {}

				data = rs1.rows[0]
				if(!data.contract_number) {
				 	res.status(500).json({
						"msg": "can not find contract",
						"code": -1
					})
				}
				format_data.contract_number = isKunn ? req.query.contractnumber : data.contract_number
				format_data.channel = data.channel
				format_data.contract_type = data.contract_type
				format_data.status = "SINGING_IN_PROGRESS"
				format_data.pStatus = data.pstatus
				format_data.customer_name = data.cust_full_name
				format_data.id_card_number = data.id_number
				format_data.issue_place = data.id_issue_place
				format_data.currentAddress = data.address_cur
				format_data.currentProvince = data.province_cur
				format_data.currentDistrict = data.district_cur
				format_data.currentWard = data.ward_cur
				format_data.currentVillage = data.village_cur
				format_data.permanentAddress = data.address_per
				format_data.permanentProvince = data.province_per
				format_data.permanentDistrict = data.district_per
				format_data.permanentWard = data.ward_per
				format_data.permanentVillage = data.village_per
				format_data.dateOfBirth = data.birth_date
				format_data.phoneNumber = data.phone_number1
				format_data.partnerCode = data.partner_code
				format_data.productCode = data.product_code
				format_data.docList = rs2.rows
				format_data.pathSpic = path_spic || null
				format_data.isKunn = isKunn;
				const resData =  camelcaseKeys(format_data, { deep: true })
				// do ben bss-esigning dâng dung snakecase nen  pidDocs khong chuyen sang camel
				resData.pidDocs = (rs2.rows || []).filter(e=> [DOC_TYPE.ID_CARD.BACK,DOC_TYPE.ID_CARD.FRONT].includes( e.doc_type))
				res.status(200).json({
					"code": "SUCCESS",
					"message": "contract info available.",
					"data":resData
				})
			})
			.catch(error => {
				common.log(`get esign contract error : ${error.message}`,req.query.contractnumber)
				res.status(500).json({
					"msg": "service error",
					"code": -1
				})
			})
	}
	catch (error) {
		res.status(500).json({
			"msg": "service error",
			"code": -1
		})
	}
}

async function checkContract(poolRead,contractNumber) {
	const sql = "select * from loan_esigning le where contract_number =$1"
	return await poolRead.query(sql,[contractNumber])
}

module.exports = {
	getContractInfo,
	getEsignContractInfo,
	checkContract
}