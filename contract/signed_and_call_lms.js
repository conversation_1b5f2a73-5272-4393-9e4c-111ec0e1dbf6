const common = require("../utils/common")
const aaaService = require("../utils/aaaService")
const utils = require("../utils/helper")
const smsService = require("../utils/smsService")
const crmService = require("../utils/crmService")
const callbackService = require("../services/callback-service")
const { STATUS, CALLBACK_STAUS } = require("../const/caseStatus")
const loanContractRepo = require("../repositories/loan-contract-repo")
const lmsService = require("../services/lms-service")

async function completed(req,res,contractNumber,status) {
	try {
		const poolWrite = req.poolWrite
		const poolRead = req.poolRead
		const config = req.config
		
		if(status == 'signed') {
			
			const lb = config.basic.lmsMc[config.env];
			const lms_uri = config.data.lms.createService;
			const url = lb + lms_uri

			const smsUrl = config.data.smsService.sendSMS
        	//const isEnable = config.data.smsService.useSMS
        	let msg = config.data.smsService.ActivateHMMsg
        	msg = msg.replace("contractNumber", contractNumber)

			let amount;
			let rate;
			let tenor;
			
			// todo 
			const custId = await getCustId(poolRead,contractNumber)
			const loanContractData = await loanContractRepo.getLoanContract(contractNumber)
			const productCode = loanContractData?loanContractData.product_code:''
			const contractType = loanContractData.contract_type
			let sql = "select offer_amt,int_rate,tenor from loan_offer_selection where contract_number=$1 or kunn_id=$1 and is_selected=1"
			req.poolRead.query(sql,[contractNumber]) 
			.then(result => {
				amount = result.rows[0].offer_amt
				rate = result.rows[0].int_rate
				tenor = result.rows[0].tenor

				let lmsBody = {
				    "productCode": productCode=='MCBAS_HMTD'?'HMTD':'',
				    "amount": amount,
				    "custId": custId,
				    "contractNumber":contractNumber,
				    "tenor": tenor,
				    "periodicity": 1,
				    "graceDayNumber":15,
				    "billDay": 20,
				    "startDate":"2021-05-06",
				    "endDate":"2022-07-06",
					contractType,
				    "irCharge":[
				        {
				            "irCode":"IR001",
				            "irName":"Lãi xuất theo kỳ",
				            "irType":1,
				            "irValue":rate
				        },
				        {
				            "irCode":"IR002",
				            "irName":"Lãi xuất gốc quá hạn",
				            "irType":2,
				            "irValue":rate
				        },
				        {
				            "irCode":"IR003",
				            "irName":"Lãi xuất lãi quá hạn",
				            "irType":3,
				            "irValue":rate
				        }
				    ]
				}

				aaaService.getKey(config)
				.then(key => {
					if(key.status === 0) {
						const uiid = key.uiid
						const token = key.token
						const headers = {
							"uiid" : uiid,
							"token" : token
						}
						common.postAPI(url,lmsBody,headers)
						.then(async rs =>{
							const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
							if (phoneNumberRs) {
								smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs)
							}
						})
						.catch(error => console.log(error))
						utils.saveStatus(poolWrite,contractNumber,STATUS.ACTIVATED)
					} 
					else {
						common.log("get key fail")
					}
				})
				.catch(error => console.log(error))
				
				
			})
			.catch(error => {
				console.log(error)
			})
	    }
	    else {
	    	let updateEsigningSql = "update loan_esigning set status='ESIGN FAILED' where contract_number=$1"
			let updateLoanContractSql  = "update loan_contract set status='ESIGN FAILED' where contract_number=$1" 
	        Promise.all([poolWrite.query(updateEsigningSql,[contractNumber]),poolWrite.query(updateLoanContractSql,[contractNumber])])
	        .then()
	        .catch(error => console.log(error))
	    	
	    }
	}catch (error) {

		console.log(error)
	}
}

module.exports = {
	completed
}