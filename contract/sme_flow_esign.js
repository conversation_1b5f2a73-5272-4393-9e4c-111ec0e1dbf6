const uuid = require('uuid')
const common = require("../utils/common");
const s3Service = require("../upload_document/s3-service")
const storageContractSignedPath = "/mc-credit/signed-contract"
const { detectType } = require("../utils/detectContractType");
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")
const {  CONTRACT_TYPE, roleCode } = require('../const/definition')
const { STATUS } = require('../const/caseStatus');
const FormData = require('form-data');
const { pushTaskMcV2 } = require('../utils/aadService');
const moment = require('moment-timezone');
moment().tz('Asia/Ho_Chi_Minh').format();
const { checkContract } = require("./get-contract-info")

const receiveContractSme = async (req, res) => {
    try {
        const config = req.config;
        // const poolRead = req.poolRead;
        const poolWrite = req.poolWrite;
        const contractNumber = req.body.contractNumber;
        // const checkSignUri = '/esigning/internal/misa/check-signature';
        // const signUri = '/esigning/internal/misa/sign';
        // const checkSignUrl = config.basic['bss-esigning-service'][config.env] + checkSignUri;
        // const signUrl = config.basic['bss-esigning-service'][config.env] + signUri;
        let contractFile = req.files;
        let response = {
            code: 1,
            msg: 'EC nhận file hợp đồng MISA đã ký thành công',
            nextStep: 'EC thẩm định'
        };
        let contractData;
        const isKunn = await detectType(contractNumber, req) === "KU";
        let contractStatus;
        const kunnNumber = contractNumber;
        if (isKunn) {
            let ctrNumber = await kunnRepo.getKunnData(kunnNumber);
            contractStatus = ctrNumber?.status;
            contractData = await loanContractRepo.getLoanContract(ctrNumber.contract_number);
        }
        else {
            contractStatus = await loanContractRepo.getContractStaus(contractNumber);
            contractData = await loanContractRepo.getLoanContract(contractNumber);
        }
        const signData = await checkContract(req.poolRead, contractNumber);
        const statusSign = signData?.rows[0].status;
        if (contractStatus !== STATUS.WAITING_TO_BE_SIGNED || statusSign !== 'SME PARTNER SIGN IN PROGRESS') {
            response.code = -1;
            response.msg = `Invalid contract number: ${contractNumber}`;
            response.nextStep = 'EC check';
            return res.status(400).json(response);

        }
        let statusResponse = 200;
        //let contractFile = req.files;
        let dataCheckSign = new FormData();
        let bufferFileCheckSign;
        for await (const cf of contractFile) {
            if (cf.fieldname === 'LCT') bufferFileCheckSign = cf.buffer;
        }
        dataCheckSign.append('file', bufferFileCheckSign);
        // const rsCheck = await common.postApiV2(checkSignUrl, dataCheckSign, dataCheckSign.getHeaders());
        const rsCheck = {
            data: {
                code: 0
            }
        }
        if (rsCheck.data.code == 0) {
            const contractType = contractData.contract_type === CONTRACT_TYPE.CREDIT_LINE ? 'HM' : 'VM';
            for await (const item of contractFile) {
                let bundleId = 'SME_MISA_' + contractType + '_LOAN CONTRACT';
                let docType = 'LCT';
                if (item.fieldname == 'LCT') {
                    if (isKunn) {
                        bundleId = 'SME_MISA_' + contractType + '_LOAN CONTRACT_KUNN';
                        docType = 'LCTKU';
                    }
                }
                if (item.fieldname == 'LSPC') {
                    if (!isKunn) {
                        break;
                    }
                    bundleId = 'SME_MISA_' + contractType + '_LIVE SIGNING PHOTO OF CUSTOMER';
                    docType = item.fieldname;
                }
                await s3Service.uploadV2(config.data, moment().format('yyyyMMDDHHmmss') + item.originalname, item.buffer, storageContractSignedPath)
                    .then(async data => {
                        const path = data.Key;
                        const fileLocation = data.Location;
                        let docBody = {
                            type: docType,
                            url: fileLocation,
                            key: path,
                            docId: uuid.v4(),
                            bundleId: bundleId,
                            fileName: contractFile[0].originalname
                        }
                        if (isKunn) {
                            await saveUpload(req.poolWrite, docBody.docId, docBody.url, docBody.type, docBody.bundleId, docBody.key, docBody.fileName, null, contractNumber, null);
                            const updateSql = `update loan_contract_document set type_collection='HM' where doc_group like '%_DISBURSAL DOCS' and kunn_contract_number=$1`;
                            await poolWrite.query(updateSql, [kunnNumber])
                        }
                        else {
                            saveUpload(req.poolWrite, docBody.docId, docBody.url, docBody.type, docBody.bundleId, docBody.key, docBody.fileName, contractNumber, null, 'DIBURSEMENT');
                        }
                    }).catch(err => {
                        common.log(`upload SME docs error : ${err.message}`, contractNumber)
                        console.log(err)
                        response.msg = 'service error';
                        response.code = -1;
                        response.nextStep = 'EC check log error!';
                        statusResponse = 500;
                    })
            }
            if (isKunn) {
                await pushTaskMcV2(roleCode.CP,contractNumber,contractData.contract_type,STATUS.IN_CP_BEFORE_SIGN_QUEUE)
                await loanContractRepo.updateKUStatus(STATUS.IN_CP_BEFORE_SIGN_QUEUE,contractNumber)
            }
            else {
                await pushTaskMcV2(roleCode.CP,contractNumber,contractData.contract_type,STATUS.IN_CP_POST_QUEUE)
                await loanContractRepo.updateContractStatus(STATUS.IN_CP_POST_QUEUE,contractNumber)
            }
        } else {
            response.code = -1;
            response.msg = `${contractNumber} EC kiểm tra thấy file hợp đồng chưa có chữ ký điện tử của MISA`;
            response.nextStep = 'MISA gửi lại file hợp đồng MISA đã ký'
            statusResponse = 500;
        }
        res.status(statusResponse).json(response);
    }
    catch (error) {
        console.log(error)
        res.status(500).json({
            "msg": "service error",
            "code": -1
        })
    }
}

async function saveUpload(poolWrite, docId, docUrl, docType, bundleId, fileKey, fileName, contractNumber, KunnNumber, typeCollection) {
    const sql = `insert into loan_contract_document(doc_id,doc_type,url,doc_group,file_key,file_name,contract_number,kunn_contract_number,type_collection) values ($1,$2,$3,$4,$5,$6,$7,$8,$9)`;
    poolWrite
        .query(sql, [docId, docType, docUrl, bundleId, fileKey, fileName, contractNumber, KunnNumber, typeCollection])
        .then()
        .catch((error) => {
            common.log(error.message);
        });
}

// async function saveDoc(poolWrite, poolRead, contractNumber, isKunn) {
//     // const isKunn = await detectType(contractNumber, req) === "KU";
//     const kunnNumber = contractNumber;
//     if (isKunn) {
//         contractNumber = await kunnRepo.getContractByKU(kunnNumber);
//     }

//     const sql1 = `select * from loan_contract_document lct where doc_type = 'SPALR' and contract_number=$1 and is_deleted=0 limit 1`;
//     const sql2 = `select * from loan_contract_document lct where doc_type in ('SPAR','SPLR') and contract_number=$1 and is_deleted=0`;

//     const resultSql = await Promise.all([
//         poolRead.query(sql1, [contractNumber]),
//         poolRead.query(sql2, [contractNumber])
//     ])
//     const rsSql1 = resultSql[0];
//     const rsSql2 = resultSql[1];


//     if (isKunn) {
//         if (!utils.isNullOrEmpty(rsSql1)) {
//             const docDataSql1 = rsSql1.rows[0];
//             const docGroup = 'SME_MISA_HM_POWER OF ATTORNEY OF THE LEGAL REPRESENTATIVE';
//             await saveUpload(poolWrite, docDataSql1.doc_id, docDataSql1.url, docDataSql1.doc_type, docGroup, docDataSql1.file_key, docDataSql1.file_name, null, kunnNumber, null)
//         }
//         if (!utils.isNullOrEmpty(rsSql2)) {
//             const docDataSql2 = rsSql2.rows;
//             // console.log({rsSql2})
//             const docGroup = 'SME_MISA_HM_SCAN PHOTO OF LEGAL REPRESENTATIVE';
//             for (const item of docDataSql2) {
//                 await saveUpload(poolWrite, item.doc_id, item.url, item.doc_type, docGroup, item.file_key, item.file_name, null, kunnNumber, null)
//             }
//         }
//     } else {
//         if (!utils.isNullOrEmpty(rsSql1)) {
//             const docDataSql1 = rsSql1.rows[0];

//             const docGroup = 'SME_MISA_HM_SCAN POWER OF ATTORNEY OF THE LEGAL REPRESENTATIVE';
//             await saveUpload(poolWrite, docDataSql1.doc_id, docDataSql1.url, docDataSql1.doc_type, docGroup, docDataSql1.file_key, docDataSql1.file_name, contractNumber, null, 'DIBURSEMENT')
//         }
//     }
// }

module.exports = {
    receiveContractSme
}