const uuid = require('uuid')
const common = require("../utils/common");
const s3Service = require("../upload_document/s3-service")
const storageContractSignedPath = "/mc-credit/signed-contract"
const completedFlow = require("./signed_and_call_lms.js")
const { detectType } = require("../utils/detectContractType");
const utils = require("../utils/helper")
const callbackService = require("../utils/callbackService");
const callbackServiceV2 = require("../services/callback-service")
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")
const {PARTNER_CODE,BUNDLE_STAGE,CONTRACT_TYPE, roleCode, CHANNEL, TASK_FLOW, SERVICE_NAME} = require('../const/definition')
const lmsService = require("../services/lms-service")
const esigingRepo = require("../repositories/loan-esigning")
const documentRepo = require("../repositories/document")
const productUtils = require("../utils/productService");
const { STATUS, KUNN_STATUS, CALLBACK_STAUS, CONTENT_DISBURSE } = require('../const/caseStatus');
const productService = require('../utils/productService');
const { serviceEndpoint } = require('../const/config');
const { sendNotification } = require('../services/notification-service');
const { checkContract } = require('./get-contract-info');
const aadGw = require("../services/aad-service");
const { routing } = require('../services/workflow-service');
const { checkEKYCKunnGateWay } = require('../services/de-service');
const kunnPrepareAttributeRepo = require('../repositories/kunn-prepare-attribute-repo')
const { getDataByContractNumber } = require("../repositories/loan-atribute-repo");
const { findOne } = require('../utils/sqlHelper.js');
const esigningRepo = require("../repositories/loan-esigning");
const loggingRepo = require('../repositories/logging-repo');

const uploadContractSignedFile = async (req, res) => {
    try {
        const poolWrite = req.poolWrite
        // const poolRead = req.poolRead
        const config = req.config
        let contractNumber = req.body.contractnumber

        if (req.files === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'File must be not null.',
            })
        }

        if (req.files.LCT === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'Mising LCT file.',
            })
        }

        if (contractNumber === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'contractnumber must be not null.',
            })
        }

        let sql = "select count(id) from loan_esigning where contract_number=$1 "
        let contractResult = await req.poolRead.query(sql, [contractNumber])
        if (parseInt(contractResult.rows[0].count) === 0) {
            res.status(404).json({
                "msg": "contract number does not exist or signed",
                "code": -1
            })
        }
        else {
            const isKunn = await detectType(contractNumber, req) === "KU"
            let eSigningObject = {};
            eSigningObject.contract_number = contractNumber
            // let ob = Object.keys(req.files)
            const file = req.files.LCT[0]

            s3Service.upload(req.config.data, file.originalname, file.buffer, storageContractSignedPath)
                .then(async data => {
                    let isSigned = false;
                    if (isKunn) { 
                        const signData = await checkContract(req.poolRead,contractNumber);
                        isSigned = signData?.rows[0].contract_signed_path==null?false:true;
                    }
                    const path = data.Key
                    const fileLocation = data.Location
                    if (isKunn) {
                        await esigingRepo.saveSignedContract(fileLocation,contractNumber)
                        const kunnData = await kunnRepo.getKunnData(contractNumber);
                        const isMcaKunn = utils.isMcaKunn(kunnData?.request_id);
                        if (isMcaKunn) {
                                const limitContractNumber = await kunnRepo.getContractByKU(contractNumber)
                                const contractData = await loanContractRepo.getLoanContract(limitContractNumber);
                                const partnerCode = contractData?.partner_code;
                            if (isSigned) {
                                // await callbackServiceV2.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.SIGNED_KUNN)
                                await kunnRepo.updateKUStatus(contractNumber,KUNN_STATUS.SIGNED)
                                kunnCreateDeb(req, path)
                            } else {
                                //kí lần đầu của kunn MCAPP
                                const _contractNumber = await kunnRepo.getContractByKU(contractNumber);
                                checkEKYCKunnGateWay(contractNumber, _contractNumber, partnerCode)
                                await kunnRepo.updateKUStatus(contractNumber,KUNN_STATUS.SIGNED)
                                const wfData = {
                                    kunn_id: contractNumber,
                                    currentTask: 'DIBUR_REQUEST',
                                    partner_code: partnerCode,
                                    contract_number: _contractNumber
                                }
                                routing(wfData)
                            }
                        } else {
                            const limitContractNumber = await kunnRepo.getContractByKU(contractNumber)
                            const contractData = await loanContractRepo.getLoanContract(limitContractNumber);
                            const partnerCode = contractData?.partner_code;
                            // callback signed kunn
                            if(partnerCode !== PARTNER_CODE.FINV){
                                await callbackServiceV2.callbackPartner(limitContractNumber,partnerCode,CALLBACK_STAUS.SIGNED_KUNN,undefined,undefined,contractNumber)
                            }
                            await kunnRepo.updateKUStatus(contractNumber,KUNN_STATUS.SIGNED)
                            //insert signed file
                            if (partnerCode === PARTNER_CODE.MISA) {
                                const docGroup = 'SME_MISA_HM_LOAN CONTRACT_KUNN';
                                await documentRepo.insertSingleDocumentV2(null, "LCTKU", uuid.v4(), docGroup, path, file.originalname, 'KU', fileLocation, contractNumber);
                            }
                            kunnCreateDeb(req, path)
                        }
                        
                        return res.send({
                            code: 'ACCEPTED',
                            message: 'contract update success.',
                            data: {
                                path: path
                            }
                        })
                    }
                    else {
                        const contractData = await loanContractRepo.getLoanContract(contractNumber);
                        // const rootContractNumber = contractData?.root_contract_number;
                        const partnerCode = contractData?.partner_code;
                        const contractType = contractData?.contract_type;
                        const phoneNumber = contractData?.phone_number1;

                        const signedStatus = partnerCode == 'FINV' ? 'CUSTOMER_SIGNED' : 'SIGNED';
                        await esigingRepo.saveSignedContract(fileLocation,contractNumber, signedStatus)
                        // if (partnerCode == PARTNER_CODE.SMA) callbackServiceV2.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.ACTIVE)
                        if (partnerCode != PARTNER_CODE.SMA) {
                            if(partnerCode !== PARTNER_CODE.FINV){
                                await callbackServiceV2.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.SIGNED)
                            }
                        }
                        // if(!utils.isNullOrEmpty(rootContractNumber)){
                        //     let bodyUpdateLos = {
                        //         contract_number: rootContractNumber,
                        //         approval_amt: contractData?.approval_amt,
                        //         approval_tenor: contractData?.approval_tenor,
                        //         approval_int_rate: contractData?.approval_int_rate,
                        //         approval_tenor_kunn: contractData?.approval_tenor_kunn
                        //     }
                        //     let bodyUpdateLms = {
                        //         amount: contractData?.approval_amt,
                        //         contractNumber: rootContractNumber,
                        //         tenor: contractData?.approval_tenor
                        //     }
                        //     const updateLoanLmsUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.updateLoan}`;
                        //     //call cap nhat thong tin review hm ở lms, los
                        //     await Promise.all([
                        //         loanContractRepo.updateLoanContract(bodyUpdateLos),
                        //         loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber),
                        //         loanContractRepo.updateContractStatus(STATUS.ACTIVATED,rootContractNumber),
                        //         common.putAPIV2(updateLoanLmsUrl,bodyUpdateLms)
                        //     ]) 
                        // }else{
                        if(partnerCode == PARTNER_CODE.KOV) {
                            await loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber)
                            await callbackService.callbackSigned(poolWrite,config,contractNumber)
                            await lmsService.createLMS(contractNumber,contractType,phoneNumber,partnerCode)
                        }
                        if([PARTNER_CODE.VSK,PARTNER_CODE.MCAPP,PARTNER_CODE.SMA].includes(partnerCode) && contractType == CONTRACT_TYPE.CREDIT_LINE) {
                            if(partnerCode == PARTNER_CODE.SMA) {
                                // let bundleListResult = []
                                let data = await productUtils.getBundle(global.config, contractData.product_code, BUNDLE_STAGE.DIBURSEMENT)
                                const bundleList = data.data
                                for (let idx in bundleList) {
                                    const element = bundleList[idx]
                                    let tmpDocList = element.docList
                                    let isBundle = true
                                    for(let i in tmpDocList) {
                                        const item = tmpDocList[i]
                                        if (item.docType == 'LCT') {
                                            const docId = uuid.v4()
                                            await documentRepo.insertSingleDocument(contractNumber,"LCT",docId,element.bundleName,path,file.originalname,'KU')
                                            isBundle = false
                                        }
                                    };
                                    if (isBundle) {
                                        element.docGroup = element.bundleName
                                        // bundleListResult.push(element)
                                    }
                                };
                            }
                            await loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber)
                            await lmsService.createLMS(contractNumber,contractType,phoneNumber,partnerCode)
                        }
                        if(partnerCode == PARTNER_CODE.MISA || [PARTNER_CODE.SPL].includes(partnerCode)) {
                            await loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber)
                            await lmsService.createLMS(contractNumber,contractType,phoneNumber,partnerCode)
                        }
                        else if(partnerCode == PARTNER_CODE.VPL) {
                            await loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber)
                            await lmsService.createCashLoanAccount(contractNumber)
                        }
                        else if(partnerCode == PARTNER_CODE.VTP || (partnerCode == PARTNER_CODE.VSK && contractType == CONTRACT_TYPE.CASH_LOAN)) {
                            await loanContractRepo.updateContractStatus(STATUS.SIGNED_WO_DOCS,contractNumber)
                            let bundleListResult = []
                            let data = await productUtils.getBundle(global.config, contractData.product_code, BUNDLE_STAGE.DIBURSEMENT)
                            const bundleList = data.data
                            for (let idx in bundleList) {
                                const element = bundleList[idx]
                                let tmpDocList = element.docList
                                let isBundle = true
                                for(let i in tmpDocList) {
                                    const item = tmpDocList[i]
                                    if (item.docType == 'LCT') {
                                        const docId = uuid.v4()
                                        await documentRepo.insertSingleDocument(contractNumber,"LCT",docId,element.bundleName,path,file.originalname,'DIBURSEMENT')
                                        isBundle = false
                                    }
                                };
                                if (isBundle) {
                                    element.docGroup = element.bundleName
                                    bundleListResult.push(element)
                                }
                            };
                            return res.status(200).json({
                                "response_code" : 201,
                                "response_message" : "Upload file signed successful",
                                data : {
                                    nextStatus : "IN_CP_CHECK_DOC",
                                    bundleList : bundleListResult
                                }
                            })
                        } 
                        else if(partnerCode == PARTNER_CODE.FINV) {
                            await loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber)
                            await callbackServiceV2.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.SIGNED)
                        }
                    // }
                    
                    return res.send({
                        code: 'ACCEPTED',
                        message: 'contract update success.',
                        data: {
                            path: path
                        }
                    })
                    }
                }).catch(err => {
                    common.log(`upload signed contract error : ${err.message}`,contractNumber)
                    console.log(err)
                    eSigningObject.status = 'FAIL'
        
                    updateStatus(req.poolWrite, contractNumber, "null", "FAIL")
                    res.status(500).json({
                        "msg": "service error",
                        "code": -1
                    })
                })
        }
    }
    catch (error) {
        console.log(error)
        res.status(500).json({
            "msg": "service error",
            "code": -1
        })
    }
}

async function getOfferKunn(kunn_id, req) {
    const sql = "SELECT * FROM loan_offer_selection WHERE kunn_id = $1 and is_selected = 1 AND is_delt=0"
    const params = [kunn_id]
    const data = await req.poolRead.query(sql, params)
    return data.rows.length ? data.rows[0] : null
}

async function kunnCreateDeb(req, docId) {
    try {
        const kunn_id = req.body.contractnumber
        const poolWrite = req.poolWrite
        const config = req.config
        const kunnInfo = await getKunnIn(kunn_id, poolWrite)
        let sql = "UPDATE kunn SET esigned_contract = $1,bill_day = $2 WHERE kunn_id = $3 and bill_day is null"
        let sql1 = "UPDATE loan_esigning SET status = $1 WHERE contract_number = $2"
        await req.poolWrite.query(sql, [docId,kunnInfo.contract.bill_day,kunn_id])
        await req.poolWrite.query(sql1, ['SIGNED',kunn_id])
        const kunnInfo2 = await getKunnIn(kunn_id, poolWrite)
        const contractNumber = kunnInfo2.kunn.contract_number;
        const contractData = await loanContractRepo.getLoanContract(contractNumber);
        const prepareKunnData = await kunnPrepareAttributeRepo.getDataByContractNumber(kunn_id)
        const partnerCode = kunnInfo2.kunn.partner_code;
        const contractType = [PARTNER_CODE.SMA,PARTNER_CODE.SMASYNC].includes(partnerCode) ? prepareKunnData?.paymentMethod : contractData.contract_type;
        const bodySignIn = { username: global.config.data.lms.lmsUsername, password: global.config.data.lms.lmsPassword }
        const offer = await getOfferKunn(kunn_id, req)
        const kunnData = await kunnRepo.getKunnData(kunn_id)
        const loanContractData = await loanContractRepo.getLoanContract(kunnData?.contract_number)
        const productInfo = await productService.getProductInfoV2(loanContractData?.product_code)
        let ir = parseFloat(productInfo.productVar[0].intRate / 100)
        let irChargeSmes = [];
        if(partnerCode===PARTNER_CODE.MISA){
            ir=kunnInfo2.kunn.ir;
            const productCode = kunnInfo2.contract.product_code;
            const productInfo = await productService.getProductInfoV2(productCode);
            const rates = productInfo.rate;
            const irType = '4';
            for await (const rate of rates) {
                if(rate.intRateName === 'EARLY_TERMINATION_RATE'){
                    let irChargeSme = {};
                    irChargeSme.irName = rate.intRateName;
                    irChargeSme.irType = irType;
                    irChargeSme.irValue = rate.intRateVal/100;
                    irChargeSme.tenorFrom = rate.tenorFrom;
                    irChargeSme.tenorTo = rate.tenorTo;
                    irChargeSme.installmentFrom = rate.installmentFrom;
                    irChargeSme.installmentTo = rate.installmentTo;
                    irChargeSmes.push(irChargeSme);
                }
            }
        } 
        let irCharge = [
            {
                "irName": "Lãi xuất lãi quá hạn",
                "irType": "3",
                "irValue": "0.1"
            },
            {
                "irName": "Lãi xuất gốc quá hạn",
                "irType": "2",
                "irValue": parseInt(ir * 100 * 1.5) / 100 + ""
            },
            {
                "irName": "Lãi xuất gốc",
                "irType": "1",
                "irValue": parseFloat(ir) + ""
            }
        ]
        CONTENT_DISBURSE.CONTENT1 = CONTENT_DISBURSE.CONTENT1.replace("kunn",kunn_id)
        CONTENT_DISBURSE.CONTENT2 = CONTENT_DISBURSE.CONTENT2.replace("kunn",kunn_id)
        CONTENT_DISBURSE.CONTENT3 = CONTENT_DISBURSE.CONTENT3.replace("kunn",kunn_id)
        CONTENT_DISBURSE.CONTENT4 = CONTENT_DISBURSE.CONTENT4.replace("kunn",kunn_id)
        let bankInfo = [
            {
                beneficiaryName: kunnInfo2?.kunn?.beneficiary_name,
                bankCode: kunnInfo2?.kunn?.bank_code,
                bankName: kunnInfo2?.kunn?.bank_name,
                bankAccount: kunnInfo2?.kunn?.bank_account,
                disbursedAmount: kunnInfo2?.kunn?.disbursed_amount,
                content: `${CONTENT_DISBURSE.CONTENT1}${kunnInfo2?.kunn?.content}` 
            },
            {
                beneficiaryName: kunnInfo2?.kunn?.beneficiary_name2,
                bankCode: kunnInfo2?.kunn?.bank_code2,
                bankName: kunnInfo2?.kunn?.bank_name2,
                bankAccount: kunnInfo2?.kunn?.bank_account2,
                disbursedAmount: kunnInfo2?.kunn?.disbursed_amount2,
                content: `${CONTENT_DISBURSE.CONTENT2}${kunnInfo2?.kunn?.content2}`
            },
            {
                beneficiaryName: kunnInfo2?.kunn?.beneficiary_name3,
                bankCode: kunnInfo2?.kunn?.bank_code3,
                bankName: kunnInfo2?.kunn?.bank_name3,
                bankAccount: kunnInfo2?.kunn?.bank_account3,
                disbursedAmount: kunnInfo2?.kunn?.disbursed_amount3,
                content: `${CONTENT_DISBURSE.CONTENT3}${kunnInfo2?.kunn?.content3}`
            },
            {
                beneficiaryName: kunnInfo2?.kunn?.beneficiary_name4,
                bankCode: kunnInfo2?.kunn?.bank_code4,
                bankName: kunnInfo2?.kunn?.bank_name4,
                bankAccount: kunnInfo2?.kunn?.bank_account4,
                disbursedAmount: kunnInfo2?.kunn?.disbursed_amount4,
                content: `${CONTENT_DISBURSE.CONTENT4}${kunnInfo2?.kunn?.content4}`
            }
        ]
        let acronymSmeName = '';
        if(partnerCode===PARTNER_CODE.MISA){
            acronymSmeName = kunnInfo2?.kunn?.acronym_sme_name;
            if(Object.entries(irChargeSmes).length != 0){
                irCharge = [...irCharge,...irChargeSmes];
            }
            for await (const bi of bankInfo) {
                if(utils.isNullOrEmpty(bi.disbursedAmount)) bankInfo.pop(bi);
                bankInfo = [...bankInfo]
            }
        }
        const periodicity = [PARTNER_CODE.MISA,PARTNER_CODE.SMA,PARTNER_CODE.SMASYNC].includes(partnerCode)&&[CONTRACT_TYPE.CASH_LOAN,"PIM"].includes(contractType)?1:[PARTNER_CODE.MISA,PARTNER_CODE.SMA].includes(partnerCode)?kunnInfo2.kunn.tenor:kunnInfo2.kunn.method || 6
        const dataProduct = await getProduct(kunnInfo.contract.product_code, req)
        let createDebtBody = {}
        if ([PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(partnerCode)) {
            const productInfoSma = await productService.getProductInfoV3(kunnInfo2.kunn.kunn_code)
            irCharge = productInfoSma?.irCharge
            let feeArr = []
            const _fee = dataProduct?.fee || []
            _fee.map(f => {
                let _feeDetail = f?.feeDetail || []
                _feeDetail.map(fd => {
                    let obj = {
                        "prdctId": f?.prdctId,
                        "feeId": f.feeId,
                        "ccycd": fd?.ccycd,
                        "feeName": fd?.feeName,
                        "feeDesc": fd?.feeDesc,
                        "refCalculaId": fd?.refCalculaId,
                        "periodicity": fd?.periodicity,
                        "feeCode": fd?.feeCode,
                        "feeType": fd?.feeType,
                        "calculaType": fd?.calculaDetail[0]?.calculaType || "",
                        "feeAmt": fd?.calculaDetail[0]?.fixAmt || 0
                    }
                    feeArr.push(obj)
                })
            })
            createDebtBody = {
                "contractNumber": kunnInfo2.kunn.contract_number,
                "debtAckContractNumber": kunn_id,
                "productCode": kunnInfo2.kunn.kunn_code,
                "amount": offer.offer_amt || 0,
                "tenor": offer.tenor || 6,
                "fee": feeArr,
                "periodicity": periodicity,
                "partnerCode": kunnInfo2.kunn.partner_code,
                "beneficiaryName": kunnInfo2.kunn.beneficiary_name,
                "bankCode": kunnInfo2.kunn.bank_code,
                "bankAccount": kunnInfo2.kunn.bank_account,
                "branchCode": kunnInfo2.kunn.bank_branch_code,
                "partnerTranNo": kunnInfo2.contract.partner_code,
                "irCharge": irCharge,
                "billDay": kunnInfo2.kunn.bill_day || 5,
                "graceDayNumber": dataProduct.graceDayNumber,
                "email": kunnInfo2.contract.email || "",
                "contractType": contractType == "PPEOP" ? CONTRACT_TYPE.CREDIT_LINE : CONTRACT_TYPE.CASH_LOAN,
                "channel": CHANNEL.SMA
            }
        } else {
            const amount = [PARTNER_CODE.FINV].includes(partnerCode) ? kunnInfo2.kunn.with_draw_amount : offer?.offer_amt || 0;
            const tenor = [PARTNER_CODE.FINV].includes(partnerCode) ? kunnInfo2.kunn.tenor : offer?.tenor || 6;
            createDebtBody = {
                contractNumber: kunnInfo2.kunn.contract_number,
                debtAckContractNumber: kunn_id,
                productCode: kunnInfo2.contract.product_code,
                amount: amount,
                tenor: tenor,
                fee: [PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(partnerCode) ? feeArr : dataProduct.fee,
                periodicity,
                partnerCode: kunnInfo2.kunn.partner_code,
                beneficiaryName: kunnInfo2.kunn.beneficiary_name,
                bankCode: kunnInfo2.kunn.bank_code,
                bankName: kunnInfo2.kunn.bank_name,
                bankAccount: kunnInfo2.kunn.bank_account,
                branchCode : kunnInfo2.kunn.bank_branch_code,
                partnerTranNo : kunnInfo2.contract.partner_code,
                irCharge,
                billDay: kunnInfo2.kunn.bill_day || 5,
                graceDayNumber: dataProduct.graceDayNumber,
                email: await getEmailRepresentation(kunnInfo2), 
                ccycd : "VND",
                contractType,
                bankInfo: null,
                description: partnerCode == PARTNER_CODE.MISA ? `Easy SME giai ngan KUNN ${kunn_id} cho ${acronymSmeName} de thanh toan tien hang` : null
                // bankInfo: kunnInfo2.kunn.partner_code===PARTNER_CODE.MISA?bankInfo:null
            }
        }

        const SignInUrl = config.basic.aaa[config.env] + config.data.app.urlAuthentSingIn
        const result = await common.postAPI(SignInUrl, bodySignIn)
        let lmsUri = [PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(partnerCode) ? "/lms-mc/v1/mobile-sma/debt-ack-contract/create" : "/lms-mc/v1/debt-ack-contract/create";
        let lmsUrl = config.basic.lmsMc[config.env] + lmsUri
        const lmsHeader = {
            uiid: result.uiid,
            token: result.token,
            service: lmsUri
        }
        if ([PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(partnerCode)) callbackServiceV2.callbackPartner(kunn_id, partnerCode, CALLBACK_STAUS.SIGNED_KUNN)
        await Promise.all([kunnRepo.updateKUStatus(kunn_id,KUNN_STATUS.SIGNED_TO_BE_DISBURED),
                        kunnRepo.updateLmsType(CONTRACT_TYPE.CREDIT_LINE,kunn_id)])

        const signedContractPath = await esigningRepo.getSingedContract(kunn_id);
        const presignedS3 = await s3Service.genPresignedDownloadUrl(signedContractPath);
        if(partnerCode === PARTNER_CODE.FINV) {
          await callbackServiceV2.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.SIGNED_KUNN, null, null, kunn_id, {contractData: [{url: presignedS3, docType: 'LCTKU'}]})
        }
        const debtAckContractCreateResult = await common.postApiV2(lmsUrl, createDebtBody, lmsHeader);
        loggingRepo.saveStepLog(
            kunn_id,
            SERVICE_NAME.LMS,
            TASK_FLOW.ACTIVE_KUNN,
            createDebtBody,
            JSON.stringify(debtAckContractCreateResult?.data || {}),
            lmsUrl
        );
    }
    catch(error) {
        console.log(error)
    }
}

const getEmailRepresentation = async (kunnInfo) => {
  let email = kunnInfo.contract.email;
  if (kunnInfo?.kunn?.partner_code === PARTNER_CODE.MISA) {
    if (kunnInfo.contract.sme_representation_email) {
      email = kunnInfo.contract.sme_representation_email;
    } else {
      const representation = await findOne({
        table: `loan_customer_representations`,
        whereCondition: {
          contract_number: kunnInfo.contract.contract_number,
        },
        orderBy: {
            created_at: 'desc'
        }
      });
      email = representation?.email ?? ''
    }
  }
  return email;
};


const getProduct = async (productCode, req) => {
    const lb = req.config.basic.product[req.config.env]

    const url = lb +req.config.data.productService.productInfo + "/?prdctCode=" + productCode
    const data = await common.getAPI(url)
    if (data.status === 200) {
        return data.data
    }
    else {
        return -3
    }
}

const getKunnIn = async (kunn_id, poolRead) => {
    const GET_CONTRACT_BY_ID = "SELECT * FROM loan_contract WHERE contract_number = (SELECT contract_number FROM kunn WHERE kunn_id = $1)"
    const GET_KUNN_BY_ID = "SELECT * FROM kunn WHERE  kunn_id = $1"
    const GET_OFFER = "SELECT * FROM loan_offer_selection WHERE  kunn_id = $1 AND is_selected = 1"

    const queryRes = await poolRead.query(GET_KUNN_BY_ID, [kunn_id])
    const kunn = queryRes.rows[0]

    const queryRes2 = await poolRead.query(GET_CONTRACT_BY_ID, [kunn_id])
    const contract = queryRes2.rows[0]

    const queryRes3 = await poolRead.query(GET_OFFER, [kunn_id])
    const offer = queryRes3.rows[0]
    return {
        kunn, contract, offer
    }
}

function updateStatus(poolWrite, contractNumber, path, status) {
    let sql = "update loan_esigning set status=$1,contract_signed_path=$2 where contract_number=$3"
    poolWrite.query(sql, [status, path, contractNumber])
}

module.exports = {
    uploadContractSignedFile
}   