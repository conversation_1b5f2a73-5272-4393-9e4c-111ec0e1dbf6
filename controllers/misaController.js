
const moment = require('moment-timezone');
moment().tz('Asia/Ho_Chi_Minh').format();
const loanEsigningRepo = require("../repositories/loan-esigning");
const sqlHelper = require("../utils/sqlHelper");
const { ERROR_CODE, RESP_MESSAGE, PARTNER_CODE, MisaStep } = require("../const/definition");
const loanContractRepo = require("../repositories/loan-contract-repo");
const { STATUS } = require("../const/caseStatus");
const utils = require("../utils/helper")
const { ServerErrorResponse, Response, BadRequestResponse, handleResponseError } = require("../base/response")
const Joi = require("joi")
const scoringConfigurationRepo = require('../repositories/scoring-configuration-repo')
const loanRatingRepo = require('../repositories/loan-rating-repo')
const misaFileHandler = require("../utils/misa-file-handler")
const smeMisaService = require("../services/sme-misa-v2")
const utilsCrmService = require("../utils/crmService")
const loggingRepo = require("../repositories/logging-repo")
const masterdataService = require("../utils/masterdataService")
const loanContractService = require("../services/loan-contract-service")
const kunnRepo = require("../repositories/kunn-repo")
const helper = require("../utils/helper");
const { MISA_ERROR_CODE } = require("../const/response-const");

const validateLoanEvaluationReport = async (requestPayload) => {
  const schema = Joi.object({
    requestId: Joi.string().required(),
    contractNumber: Joi.string().required(),
    forecastCapitalNeeds: Joi.array().items(
      Joi.object({
        code: Joi.string().required(),
        name: Joi.string().required(),
        formula: Joi.string().allow(null).allow(''),
        formulaInterpretation: Joi.string().allow(null).allow(''),
        yearN: Joi.number().allow(null).allow(0),
        previousYear: Joi.number().allow(null).allow(0)
      }).unknown(true).required()
    ).required(),
    scoring: Joi.array().items(
      Joi.object({
        code: Joi.string().required(),
        name: Joi.string().required(),
        score: Joi.string().allow(null).allow(''),
        targetScore: Joi.string().required(),
        proportion: Joi.string().required(),
        custScore: Joi.string().required()
      }).unknown(true).required()
    ).required(),
    rating: Joi.object({
      totalScore: Joi.string().required(),
      rank: Joi.string().required(),
      maxLimitByRank: Joi.number().required(),
      capitalNeededYearNEvnfc: Joi.number().required(),
      capitalNeededCustomerDeclared: Joi.number().required(),
      approvedLimit: Joi.number().required(),
      approvedInterestRate: Joi.number().required(),
      productCode: Joi.string().required(),
    }).unknown(true).required(),
    financialStatements: Joi.array().items(
      Joi.object({
        itemCode: Joi.string().required(),
        itemName: Joi.string().required(),
        reportType: Joi.string().required(),
        reportCode: Joi.string().required(),
        value: Joi.string().allow(null).allow('')
      }).unknown(true).required()
    ).required()
  }).unknown(true)
  const { error } = schema.validate(requestPayload)
  if (error) {
    console.log(`error: `, JSON.stringify(error))
    return { isValid: false, errorCode: error?.details?.[0]?.context?.key, errorMessage: error?.details?.[0].message }
  }
  return { isValid: true }
}

const exportLoanEvaluationReportController = async (req, res) => {
  try {
    let { body } = req;
    const validateSmeInfoResult = await validateLoanEvaluationReport(body);
    if (!validateSmeInfoResult.isValid) {
      const errors = [{
        errorCode: validateSmeInfoResult.errorCode,
        errorMessage: validateSmeInfoResult.errorMessage
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    body.rating.approvedInterestRate = body.rating.approvedInterestRate / 100;
    const { contractNumber } = body;
    const loan = await sqlHelper.findOne({
      table: 'loan_contract',
      whereCondition: {
        contract_number: contractNumber
      }
    })
    if (!loan?.id) {
      const errors = [{
        errorCode: 'contractNumber',
        errorMessage: `${contractNumber} not found!`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    if (loan.status !== STATUS.APPROVED) {
      const errors = [{
        errorCode: 'status',
        errorMessage: `${contractNumber} | trạng thái hồ sơ không hợp lệ`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }

    const loanContractUpdateColumns = ["approval_amt", "approval_int_rate", "approval_tenor", "updated_date","product_code"];
    await Promise.all([
      sqlHelper.insertData(
        `scoring_configuration`,
        scoringConfigurationRepo.columns,
        sqlHelper.generateValues(
          utils.convertCamelToSnake({ contractNumber: contractNumber, ...body.scoringConfiguration }),
          scoringConfigurationRepo.columns
        )
      ),
      sqlHelper.insertData(
        `loan_rating`,
        loanRatingRepo.columns,
        sqlHelper.generateValues(
          utils.convertCamelToSnake({ contractNumber: contractNumber, ...body.rating }),
          loanRatingRepo.columns
        )
      ),
      loanContractRepo.insertForecastCapitalNeeds(contractNumber, body.forecastCapitalNeeds),
      loanContractRepo.insertLoanScoring(contractNumber, body.scoring),
      loanContractRepo.insertFinancialStatements(contractNumber, body.financialStatements),
      sqlHelper.patchUpdate({
        table: 'loan_contract',
        columns: loanContractUpdateColumns,
        values: sqlHelper.generateValues(
          {
            approval_amt: +body?.rating?.approvedLimit,
            approval_int_rate: +body?.rating?.approvedInterestRate,
            updated_date: new Date(),
            approval_tenor: body?.rating?.approvalTenor ? +body?.rating?.approvalTenor : 12,
            product_code: body?.rating?.productCode
          },
          loanContractUpdateColumns
        ),
        conditions: {
          contract_number: contractNumber
        }
      }),
      loggingRepo.saveWorkflow(MisaStep.AF3, MisaStep.RECEIVED_MODEL_RESULT, contractNumber, 'misa.api')
    ])

    await loanContractRepo.updateContractStatus(STATUS.WAITING_CUSTOMER_SIGNATURE, contractNumber);
    await utilsCrmService.createCustomerService(contractNumber);
    misaFileHandler.generateBctdMisa(contractNumber, loanContractService.isRefinance(loan))

    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS, {
      contractNumber: contractNumber
    }
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const getDataBctdMisaByContractNumber = async (contractNumber) => {
  const loan = await sqlHelper.findOne({
    table: 'loan_contract',
    whereCondition: {
      contract_number: contractNumber
    }
  })
  return await misaFileHandler.getDataBctdMisa(contractNumber, loanContractService.isRefinance(loan))
}

const generateBctdMisaByContractNumber = async (contractNumber) => {
  const loan = await sqlHelper.findOne({
    table: 'loan_contract',
    whereCondition: {
      contract_number: contractNumber
    }
  })
  return await misaFileHandler.generateBctdMisa(contractNumber, loanContractService.isRefinance(loan))
}

const calculateAvailableAmountByContractNumber = async (contractNumber) => {
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  const { availableAmount, lastestLoanAmount } = await loanContractService.calculateAvailableAmount(loan);
  return { availableAmount, lastestLoanAmount } 
}

const calculateAvailableAmountByKunnNumber = async (debtContractNumber) => {
  const kunn = await kunnRepo.getKunnData(debtContractNumber);
  const loan = await loanContractRepo.getLoanContract(kunn.contract_number);
  const { availableAmount, lastestLoanAmount } = await loanContractService.calculateAvailableAmount(loan);
  return { availableAmount, lastestLoanAmount }
}

const getKunnPresignInfoController = async (debtContractNumber) => {
  try {
    const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(debtContractNumber));
    if (!kunn?.status) {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} not found`, MISA_ERROR_CODE.E412);
    }
    if (kunn?.status !== STATUS.WAITING_CUSTOMER_SIGNATURE) {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} status invalid ${kunn?.status || ''}`, MISA_ERROR_CODE.E413);
    }
    // const avalibleAmountRs = await checkAvailableAmountApi(kunn.contractNumber);
    const { availableAmount, lastestLoanAmount } = await calculateAvailableAmountByContractNumber(kunn.contractNumber);
    
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: {
        contractNumber: kunn.contractNumber,
        debtContractNumber: kunn.kunnId,
        availableCreditLimit: availableAmount,
        outstandingPrinBalance: lastestLoanAmount,
        expiredDate: kunn.expiredDate
      },
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2] getKunnPresignInfo error kunnId ${debtContractNumber}, error ${error}`);
    throw error;
  }
}

const getCreditLimit = async (contractNumber) => {
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan) {
      helper.throwBadReqError(`contractNumber`, `ContractNumber ${contractNumber} not found`, MISA_ERROR_CODE.E401);
    }
    if (loan.status !== STATUS.ACTIVATED) {
      helper.throwBadReqError(`contractNumber`, `ContractNumber ${contractNumber} status invalid ${loan.status}`, MISA_ERROR_CODE.E401);
    }

    const { availableAmount, lastestLoanAmount } = await loanContractService.calculateAvailableAmount(loan);
    
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: {
        contractNumber: contractNumber,
        availableCreditLimit: availableAmount,
        outstandingPrinBalance: lastestLoanAmount
      },
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2] getAvailableLimit ${contractNumber} error: ${error}`);
    throw error;
  }
}

const fillEvfSignatureController = async (req, res) => {
  try {
    let { body } = req;
    if (!body?.contractNumber) {
      return res.status(400).json(new BadRequestResponse([], `Missing contractNumber`));
    }
    const { contractNumber } = body
    const [
      loanEsigning,
      loan
    ] = await Promise.all([
      loanEsigningRepo.findOne(contractNumber),
      sqlHelper.findOne({
        table: `loan_contract`,
        whereCondition: {
          contract_number: contractNumber
        }
      })
    ])
    if (!loan?.id) {
      const errors = [{
        errorCode: 'contract_number',
        errorMessage: `The contract not found`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    if (loan.status !== STATUS.WAITING_CUSTOMER_SIGNATURE) {
      const errors = [{
        errorCode: 'status',
        errorMessage: `Trạng thái hợp đồng không hợp lệ`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    if (loanEsigning?.status == 'SIGNED') {
      const errors = [{
        errorCode: 'status',
        errorMessage: `The contract has been signed`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    
    // const isValidSignature = await smeMisaService.isValidMisaSignature(body);
    // if (!isValidSignature) {
    //   const errors = [{
    //     errorCode: 'signature',
    //     errorMessage: `${contractNumber} EC kiểm tra thấy file hợp đồng chưa có chữ ký điện tử của MISA`
    //   }]
    //   return res.status(400).json(new BadRequestResponse(errors));
    // }

    if (loanEsigning?.status == 'SIGNED') {
      const errors = [{
        errorCode: 'status',
        errorMessage: `The contract has been signed`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    };

    body = await masterdataService.convertEvfLov({ partnerCode: PARTNER_CODE.MISA, convertObject: body });

    await sqlHelper.patchUpdate({
      table: 'loan_contract',
      columns: ['status','updated_date','approval_date'],
      values: sqlHelper.generateValues({
        status: STATUS.WAITING_EVF_SIGNATURE,
        updated_date: new Date(),
        approval_date: new Date()
      }, ['status','updated_date','approval_date']),
      conditions: {
        contract_number: contractNumber
      }
    })
    await loggingRepo.saveWorkflow(MisaStep.SIGNATURE, MisaStep.RECEIVED_SIGNED_FILE, contractNumber, 'misa.api')
    try {
      if (Array.isArray(body?.representationDocs) && body?.representationDocs?.length > 0) {
        smeMisaService.saveAf2Documents(body.representationDocs, contractNumber)
          .then(() => {
            return smeMisaService.updateDocGroup(contractNumber);
          })
          .catch((error) => {
            console.error('Error while processing func fillEvfSignatureController() smeMisaService.saveAf2Documents:', error);
          });
      }
    } catch (e) {
      console.error(e);
    }
    smeMisaService.fillEvfSignature(body, { req }, loanContractService.isRefinance(loan));

    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS, {
      contractNumber: contractNumber
    }
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const manualCallbackCic = async (req, res) => {
  try {
    const { body } = req;
    if (!body?.contractNumber) {
      return res.status(400).json(new BadRequestResponse([], `Missing contractNumber`));
    }
    const { contractNumber, step } = body
    const loan = await sqlHelper.findOne({table: `loan_contract`, whereCondition: {contract_number: contractNumber}});
    if (!loan?.id) {
      const errors = [{
        errorCode: 'contract_number',
        errorMessage: `The contract not found`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    if (!step || step === 'AF2_DETAIL') {
      smeMisaService.callbackCicDetailToMisa(contractNumber);
    }
    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS, {
      contractNumber: contractNumber
    }
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const test = async (req, res) => {
  try {
    smeMisaService.saveAf2Documents(req.body.representationDocs, req.body.contractNumber)
      .then(() => {
        return smeMisaService.updateDocGroup(req.body.contractNumber);
      })
      .catch((error) => {
        console.error('Error while processing func fillEvfSignatureController() smeMisaService.saveAf2Documents:', error);
      });
    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const reGenLoanEvaluationReportController = async (req, res) => {
  try {
    const { contractNumber } = req.body;
    const loan = await sqlHelper.findOne({ table: `loan_contract`, whereCondition: { contract_number: contractNumber } });
    if (!loan?.id) {
      const errors = [{
        errorCode: 'contract_number',
        errorMessage: `The contract not found`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    misaFileHandler.generateBctdMisa(contractNumber, loanContractService.isRefinance(loan));
    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const updateStatus = async (req, res) => {
  try {
    const { contractNumber, status, reason } = req.body || {};
    if (!contractNumber || !status || !reason) {
      let errorField = `contractNumber`;
      if (!status) {
        errorField = `status`;
      } else if (!reason) {
        errorField = 'reason'
      }
      let error = {
        errorCode: errorField,
        errorMessage: `${errorField} is missing`
      }
      return res.status(400).json(new BadRequestResponse([error]));
    }
    const validStatuses = [STATUS.CANCELLED, STATUS.REFUSED];
    if (!validStatuses.includes(status)) {
      const errors = [{
        errorCode: 'status',
        errorMessage: `The status invalid`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    const loan = await sqlHelper.findOne({ table: `loan_contract`, whereCondition: { contract_number: contractNumber } });
    if (!loan?.id) {
      const errors = [{
        errorCode: 'contractNumber',
        errorMessage: `The contract not found`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    const cancelableStatuses = [
      STATUS.RECEIVEDA1,
      STATUS.ELIGIBLE, 
      STATUS.RECEIVEDA2,
      STATUS.APPROVED,
      STATUS.WAITING_CUSTOMER_SIGNATURE
    ];
    const rejectableStatuses = [STATUS.ELIGIBLE, STATUS.APPROVED];
    if (status == STATUS.CANCELLED && !cancelableStatuses.includes(loan.status)) {
      const errors = [{
        errorCode: 'status',
        errorMessage: `The status cannot be canceled.`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    if (status == STATUS.REFUSED && !rejectableStatuses.includes(loan.status)) {
      const errors = [{
        errorCode: 'status',
        errorMessage: `The status cannot be rejected.`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }

    let loanContractUpdateColumns = ['status', 'rejection_reason', 'updated_date'];
    let losStatus = STATUS.CANCELLED;
    if (status == STATUS.REFUSED) {
      losStatus = STATUS.REFUSED;
    }
    const updateResult = await sqlHelper.patchUpdate({
      table: 'loan_contract',
      columns: loanContractUpdateColumns,
      values: sqlHelper.generateValues({
        status: losStatus,
        rejection_reason: reason,
        updated_date: new Date()
      }, loanContractUpdateColumns),
      conditions: {
        contract_number: loan.contract_number
      }
    })

    if (status == STATUS.CANCELLED) {
      if (updateResult.status !== STATUS.CANCELLED) {
        return res.status(500).json(new ServerErrorResponse());
      }
      await loggingRepo.saveWorkflow('Misa Lending', STATUS.CANCELLED, contractNumber, 'misa.api');
      utilsCrmService.removeContract(global.config, contractNumber)
    } else if (status == STATUS.REFUSED) {
      if (updateResult.status !== STATUS.REFUSED) {
        return res.status(500).json(new ServerErrorResponse());
      }
      await loggingRepo.saveWorkflow('Misa Lending', STATUS.REFUSED, contractNumber, 'misa.api')
      utilsCrmService.rejectContract(global.config, contractNumber)
    }

    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS,
      {
        contractNumber: contractNumber,
        status: updateResult.status
      }
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const refillEvfSignatureController = async (req, res) => {
  try {
    const { body } = req;
    if (!body?.contractNumber) {
      return res.status(400).json(new BadRequestResponse([], `Missing contractNumber`));
    }
    const { contractNumber } = body
    const [
      loan
    ] = await Promise.all([
      sqlHelper.findOne({
        table: `loan_contract`,
        whereCondition: {
          contract_number: contractNumber
        }
      })
    ])
    if (!loan?.id) {
      const errors = [{
        errorCode: 'contract_number',
        errorMessage: `The contract not found`
      }]
      return res.status(400).json(new BadRequestResponse(errors));
    }
    
    await sqlHelper.patchUpdate({
      table: 'loan_contract',
      columns: ['status','updated_date','approval_date'],
      values: sqlHelper.generateValues({
        status: STATUS.WAITING_EVF_SIGNATURE,
        updated_date: new Date(),
        approval_date: new Date()
      }, ['status','updated_date','approval_date']),
      conditions: {
        contract_number: contractNumber
      }
    })

    smeMisaService.fillEvfSignature(body, { req }, loanContractService.isRefinance(loan));

    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS, {
      contractNumber: contractNumber
    }
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const downloadMisaFile = async (req, res) => {
  try {
    let url = req.body.misaUrl;
    if (!url) {
      return res.status(400).json(new BadRequestResponse([], `Missing $misaUrl`));
    }
    const buffer = await smeMisaService.downloadMisaFile(url);
    if (!buffer) {
      return res.status(400).json(new BadRequestResponse([], `invalid $misaUrl`));
    }
    const urlObj = new URL(url);
    const contentType = urlObj.searchParams.get('contentType');
    res.set("Content-Type", contentType)
    return res.status(200).send(Buffer.from(buffer, 'buffer'));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}
const downloadMisaFileTest = async (req, res) => {
  try {
    let url = req.body.misaUrl;
    const {clientId,clientSecret,key,passphrase} = req.body;
    if (!url) {
      return res
        .status(400)
        .json(new BadRequestResponse([], `Missing $misaUrl`));
    }
    const buffer = await smeMisaService.downloadMisaFileTest(url,clientId,clientSecret,key,passphrase);
    if (!buffer) {
      return res
        .status(400)
        .json(new BadRequestResponse([], `invalid $misaUrl`));
    }
    const urlObj = new URL(url);
    const contentType = urlObj.searchParams.get("contentType");
    res.set("Content-Type", contentType);
    return res.status(200).send(Buffer.from(buffer, "buffer"));
  } catch (e) {
    console.error(e);
    return res.status(500).json(new ServerErrorResponse());
  }
}

const manualUpdateDocGroup = async (req, res) => {
  try {
    if (!req.body.contractNumbers || !Array.isArray(req.body.contractNumbers)) {
      return res.status(400).json(new BadRequestResponse([], `Missing or Invalid $contractNumbers`));
    }
    for (const contractNumber of req.body.contractNumbers) {
      smeMisaService.updateDocGroup(contractNumber);
    }
    return res.status(200).json(new Response(
      ERROR_CODE.SUCCESS,
      RESP_MESSAGE.SUCCESS, {
      affectedElements: req.body.contractNumbers?.length
    }
    ));
  } catch (e) {
    console.error(e)
    return res.status(500).json(new ServerErrorResponse());
  }
}

const uploadFinancialReport = async (req, res) => {
  try {
    const data = await smeMisaService.uploadFinancialReport(req.body);
    return res.json(data);
  } catch (error) {
    return handleResponseError(res, error);
  }
};

const uploadDocumentReport = async (req, res) => {
  try {
    const data = await smeMisaService.uploadDocumentReport(req.body);
    return res.json(data);
  } catch (error) {
    return handleResponseError(res, error);
  }
};

const getInstallments = async (req, res) => {
  try {
    const data = await smeMisaService.getInstallments(req.query);
    return res.json(data);
  } catch (error) {
    return handleResponseError(res, error);
  }
};

const downloadFinancialDocuments = async (req, res) => {
  try {
    const { contractNumbers } = req.body;

    if (!contractNumbers || contractNumbers?.length === 0) {
      return res.status(200).json(new Response(
        ERROR_CODE.SUCCESS,
        RESP_MESSAGE.SUCCESS, {
        affectedElements: contractNumbers?.length ?? 0
      }));
    }
    const promises = contractNumbers.map(contract =>
      smeMisaService.scanDownloadDocuments(contract)
    );

    Promise.all(promises)
      .then(() => {
        return res.status(200).json(new Response(
          ERROR_CODE.SUCCESS,
          RESP_MESSAGE.SUCCESS, {
          affectedElements: contractNumbers?.length ?? 0
        }));
      })
      .catch(error => {
        console.error('downloadFinancialDocuments | Có lỗi xảy ra:', error);
        return res.status(200).json(new Response(
          ERROR_CODE.INT_SERVER_ERROR,
          error?.message));
      });
  } catch (error) {
    return handleResponseError(res, error);
  }
};

module.exports = {
  exportLoanEvaluationReportController,
  fillEvfSignatureController,
  manualCallbackCic,
  test,
  reGenLoanEvaluationReportController,
  updateStatus,
  refillEvfSignatureController,
  downloadMisaFile,
  manualUpdateDocGroup,
  downloadMisaFileTest,
  uploadFinancialReport,
  uploadDocumentReport,
  getInstallments,
  getDataBctdMisaByContractNumber,
  generateBctdMisaByContractNumber,
  calculateAvailableAmountByContractNumber,
  calculateAvailableAmountByKunnNumber,
  getKunnPresignInfoController,
  getCreditLimit,
  downloadFinancialDocuments
}