const { SuccessResponse, BadRequestResponse, ServerErrorResponse } = require('../base/response');
const { HTTP_STATUS, HTTP_STATUS_MESSAGE } = require("../const/variables-const")

class ResponseBaseFinv {
    constructor(statusCode, code, message, data, errors) {
        this.statusCode = statusCode;
        this.body = new Body(code, message, data, errors);
    }
}

class Body {
    constructor(code, message, data, errors) {
        this.code = code;
        this.message = message;
        this.errors = errors || [];
        this.data = data;
    }
}

class CustomError {
    constructor(code, message) {
        this.code = code;
        this.message = message;
    }
}

class FinVietSuccessResponse extends SuccessResponse {
  constructor(data) {
    super(data, "success");
    this.errors = [];
  }
}

class FinVietBadRequestResponse extends BadRequestResponse {
  constructor(message, data, errors = []) {
    super(errors, message, data);
  }
}

class FinVietServerErrorResponse extends ServerErrorResponse {
  constructor(data = {}, errors = []) {
    super(data, HTTP_STATUS_MESSAGE[HTTP_STATUS.INTERNAL_SERVER], errors);
  }
}

const handleResponse = (res, response) => {
  let statusCode;
  if (response instanceof FinVietSuccessResponse) {
    statusCode = HTTP_STATUS.SUCCESS;
  } else if (response instanceof FinVietBadRequestResponse) {
    statusCode = HTTP_STATUS.BAD_REQUEST;
  } else {
    statusCode = HTTP_STATUS.INTERNAL_SERVER;
  }
  return res.status(statusCode).send(response);
};

module.exports = {
    ResponseBaseFinv,
    CustomError,
    FinVietSuccessResponse,
    FinVietBadRequestResponse,
    FinVietServerErrorResponse,
    handleResponse,
}