class ResponseBaseKov {
    constructor(statusCode, code, message, data, errors) {
        this.statusCode = statusCode;
        this.body = new Body(code, message, data, errors);
    }
}

class Body {
    constructor(code, message, data, errors) {
        this.code = code;
        this.message = message;
        this.errors = errors || [];
        this.data = data;
    }
}

module.exports = {
    ResponseBaseKov
}