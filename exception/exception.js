class ReqError extends Error {
  constructor(statusCode, data, details, name) {
    // Pass remaining arguments (including vendor specific ones) to parent constructor
    super();
    this.statusCode = statusCode;
    this.data = { ...(data || {}), details };
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ReqError);
    }
    this.name = name;
  }
}

class BadReqExp extends ReqError {
  constructor({
    statusCode = 400,
    data = { code: -1, message: "INVALID_REQUEST" },
    details,
  }) {
    // Pass remaining arguments (including vendor specific ones) to parent constructor
    super(statusCode, data, details, "BadReqExp");
  }
}

class IntReqExp extends ReqError {
  constructor({
    statusCode = 500,
    data = { code: -2, message: "INT_SERVER_ERROR" },
    details,
  }) {
    // Pass remaining arguments (including vendor specific ones) to parent constructor
    // Maintains proper stack trace for where our error was thrown (only available on V8)
    super(statusCode, data, details, "IntReqExp");
  }
}
module.exports = {
  ReqError,
  IntReqExp,
  BadReqExp,
};
