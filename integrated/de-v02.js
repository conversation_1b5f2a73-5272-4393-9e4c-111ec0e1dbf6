const { postApiTimeout } = require("../utils/common");
const { ENDPOINT_CONST } = require("../const/endpoint-const");
const { CORE } = require("../const/variables-const");
const { getServiceLink } = require("../utils/helper");

const checkConsent = async ({ phoneNumber, partnerCode, requestId }) => {
  try {
    let url = getServiceLink(config, CORE.ANTI_FRAUD) + ENDPOINT_CONST.ANTI_FRAUD.CHECK_CONSENT;
    
    let body = {
      phone_number: phoneNumber,
      partner_code: partnerCode,
    };

    const rs = await postApiTimeout({
      url: url,
      data: body,
      headers: {
        request_id: requestId,
      },
      timeout: 30000,
    });

    return rs?.data?.valid_consent || 0
  } catch (error) {
    throw error;
  }
};

module.exports = {
  checkConsent,
};
