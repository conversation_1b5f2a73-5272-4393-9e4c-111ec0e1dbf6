// // Update with your config settings.

// /**
//  * @type { Object.<string, import("knex").Knex.Config> }
//  */
// module.exports = {

//   dev: {
//     client: 'postgresql',
//     connection: {
//       host : 'db-postgres-uat-cls-master.cdpeb1g0buju.ap-southeast-1.rds.amazonaws.com',
//       database: 'los',
//       user:     'los',
//       password: 'evnfc#cfd$126',
//       port : 5432
//     },
//     pool: {
//       min: 2,
//       max: 10
//     },
//     migrations: {
//       tableName: 'mc_los_migrations'
//     }
//   },

//   uat: {
//     client: 'postgresql',
//     connection: {
//       host : process.env.DB_HOST_WRITE || 'db-postgres-uat-cls-master.cdpeb1g0buju.ap-southeast-1.rds.amazonaws.com',
//       database: process.env.DB_DATABASE || 'los',
//       user: process.env.DB_USER || 'los',
//       password: process.env.DB_PASSWORD ||  'evnfc#cfd$126',
//       port : process.env.DB_PORT || 5432
//     },
//     pool: {
//       min: 2,
//       max: 10
//     },
//     migrations: {
//       tableName: 'mc_los_migrations'
//     }
//   },

//   production: {
//     client: 'postgresql',
//     connection: {
//       host : process.env.DB_HOST_WRITE,
//       database: process.env.DB_DATABASE,
//       user: process.env.DB_USER,
//       password: process.env.DB_PASSWORD,
//       port : process.env.DB_PORT || 5432
//     },
//     pool: {
//       min: 2,
//       max: 10
//     },
//     migrations: {
//       tableName: 'mc_los_migrations'
//     }
//   }

// };
