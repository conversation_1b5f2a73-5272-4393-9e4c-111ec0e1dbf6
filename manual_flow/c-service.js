const common = require("../utils/common")
const utils = require("../utils/helper")
const crmService = require("../utils/crmService")
const {caseStatus,caseStatusCode,STATUS, CALLBACK_STAUS} = require("../const/caseStatus")
const {PARTNER_CODE,MANUAL_TASK_CODE} = require("../const/definition")
const contractService = require("../contract/gen_contract")
const utilCallback = require("../utils/callbackService")
const loggingService = require("../utils/loggingService")
const loanContractRepo = require("../repositories/loan-contract-repo")
const callbackService = require("../services/callback-service")
const {CONTRACT_TYPE} = require("../const/definition")
const offerRepo = require("../repositories/offer")
const smsService = require("../utils/smsService")
const contractGw = require("../services/contract-service")
const {routing} = require("../services/workflow-service")

async function updateCaseStatus(req,res,contractNumber,body) {
	const config = req.config
	const poolRead = req.poolRead
	const poolWrite = req.poolWrite
	const decision = body.decision
	// const userName = body.userName
	const smsUrl = config.data.smsService.sendSMS
	const isEnable = config.data.smsService.useSMS
	let msg = ''
	let phoneNumberRs
	// let callbackBody = {
	// 	"contractNumber" : contractNumber,
	// }
	const contractData = await loanContractRepo.getLoanContract(contractNumber)
	const contractType = contractData?.contract_type
	const partnerCode = contractData?.partner_code
	// const productCode = contractData?.product_code
	// let workflowBody = {
	// 	contract_number : contractNumber,
	// 	partner_code : partnerCode,
	// 	product_code : productCode
	// }
	let responseBody = {
		"code": "200",
		"step" : "CE check",
		"message": "save info successful",
		"nextStep" : "generate contract"
	}

	if(decision === "REJECT") {
		await utils.saveStatusAsync(poolWrite,contractNumber,caseStatusCode.C03)
		await callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED);
		crmService.rejectContract(config,contractNumber)
		res.status(200).json({
			"code" : "200",
			"msg" : "callback reject to 3rd party"
		})

	}
	else if (decision === 'APPROVE') {
		await utils.saveStatusAsync(poolWrite,contractNumber,caseStatus.C02)
		checkMatchOffer(poolRead,contractNumber)
		.then(async matchOfferRs => {
			if(partnerCode == PARTNER_CODE.MISA){
				utils.updateOfferLoanContract(poolRead,poolWrite,contractNumber,matchOfferRs)
				contractGw.generateContract(contractNumber,partnerCode)
				responseBody.message = "Found matching offer and generated contract"
			}
			if(partnerCode == PARTNER_CODE.KOV) {
				utils.updateOfferLoanContract(poolRead,poolWrite,contractNumber,matchOfferRs)

				contractService.generateContract(req,contractNumber)

				responseBody.message = "Found matching offer and generated contract"
			}
			else {
				await loanContractRepo.updateContractStatus(STATUS.ACCEPTED_WITH_OFFERS,contractNumber)
				if(partnerCode==PARTNER_CODE.VSK){
					if(contractType==CONTRACT_TYPE.CASH_LOAN){
						msg = config.data.smsService.selectOfferVMMsg
					} 
					else if(contractType==CONTRACT_TYPE.CREDIT_LINE) {
						msg = config.data.smsService.selectOfferHMMsg
					}
					const offerData = await offerRepo.getSelectedOffer(contractNumber)
					msg = msg.replace("contractNumber", contractNumber).replace("contractNumber", contractNumber).replace("offer",utils.formatCash(offerData?.offer_amt)||0).replace("mm",offerData?.tenor||0)
					phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
				}
				
				await callbackService.callbackPartner(contractNumber,contractData.partner_code,CALLBACK_STAUS.APPROVED)
			}
			res.status(200).json(responseBody)
		})
		.catch(async unmatch =>{
			if(contractData.partner_code == PARTNER_CODE.KOV) {
				await loanContractRepo.updateContractStatus(STATUS.ACCEPTED_WITH_OFFERS,contractNumber)
				responseBody.message = "unmatch offer,callback to 3rd party with offer list"
				responseBody.nextStep = "callback to 3rd party"
				responseBody.code = "200"
				utilCallback.callbackOffer(poolWrite,config,contractNumber)
			}
			else {
				await loanContractRepo.updateContractStatus(STATUS.ACCEPTED_WITH_OFFERS,contractNumber)
				if(partnerCode==PARTNER_CODE.VSK){
					const smsUrl = config.data.smsService.sendSMS
					const isEnable = config.data.smsService.useSMS
					let msg = ''
					if(contractType==CONTRACT_TYPE.CASH_LOAN){
						msg = config.data.smsService.selectOfferVMMsg
					} 
					else if(contractType==CONTRACT_TYPE.CREDIT_LINE) {
						msg = config.data.smsService.selectOfferHMMsg
					}
					const offerData = await offerRepo.getSelectedOffer(contractNumber)
					msg = msg.replace("contractNumber", contractNumber).replace("contractNumber", contractNumber).replace("offer",utils.formatCash(offerData?.offer_amt)||0).replace("mm",offerData?.tenor||0)
					const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
				}
				if(partnerCode===PARTNER_CODE.MISA){
					msg = config.data.smsService.unmatchOfferSmeMsg;
					msg = msg.replace("contractNumber", contractNumber);
					phoneNumberRs = await utils.getPhoneNumberSme(contractNumber)
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs, true)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
				}
				await callbackService.callbackPartner(contractNumber,contractData.partner_code,CALLBACK_STAUS.ALT_OFFER)
			}
			res.status(200).json(responseBody)	
		})
	} 
	else {
		res.status(400).json({
			"code" : "400",
			"step" : "MC check",
			"message" : "INVALID DECISION"
		})
	}
}

function checkMatchOffer(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select id,request_amt,offer_amt from loan_offer_selection where is_selected=1 and contract_number=$1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			const offer = result.rows[0]
			if(parseFloat(offer.request_amt) == parseFloat(offer.offer_amt)) {
				resolve(offer.id)
			}
			else {
				reject(false)
			}
		})
		.catch(error => {
			console.log(error)
			reject(error)
		})
		
	})
}

module.exports = {
	updateCaseStatus
}
