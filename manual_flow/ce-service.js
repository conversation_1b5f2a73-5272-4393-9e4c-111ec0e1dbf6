const contractService = require("../contract/gen_contract")
const crmService = require("../utils/crmService")
const callbackService = require("../services/callback-service")
const { STATUS, CALLBACK_STAUS } = require("../const/caseStatus")
const { collectingDocType, PARTNER_CODE, MANUAL_TASK_CODE, VTP_CUST_SCHEMA, roleCode, CONTRACT_TYPE, CHANNEL, DOC_TYPE } = require("../const/definition")
const offerRepo = require("../repositories/offer")
const loanContractRepo = require("../repositories/loan-contract-repo")
const { routing } = require("../services/workflow-service")
const aadService = require("../utils/aadService")
const common = require("../utils/common")
const smsService = require("../utils/smsService")
const utils = require("../utils/helper")
const { SCHEME } = require("../const/scheme-kov-const")
const contractServiceFile = require("../services/contract-service")
const dateHelper = require("../utils/dateHelper")
const { serviceEndpoint } = require("../const/config")
const { sendNotification } = require("../services/notification-service")
const lmsService = require("../services/lms-service")
const kunnPrepareAttributeRepo = require("../repositories/kunn-prepare-attribute-repo")
const { createKunnWithoutLimit } = require("../super_app/application-form-service")
const { genKunn } = require("../KUNN/update_status")
const { isIncreaseLimitLoanFlow } = require("../utils/helper")
const kunnRepo = require("../repositories/kunn-repo");
const {getValueCodeByCodeType} = require("../utils/masterdataService");
const cicReportService = require("../services/cic-report-service");	

async function updateCaseStatus(req, res, contractNumber, body, taskId) {
	try {
		const status = body.decision
		const contractData = await loanContractRepo.getLoanContract(contractNumber)
		const statusCur = contractData?.status
		const partnerCode = contractData.partner_code
		const productCode = contractData.product_code
		const contractType = contractData?.contract_type
		const custType = contractData.cust_type
		const approvalTenorKunn = body.approvalTenorKunn
		const channel = contractData?.channel
		const isSuperApp = channel == CHANNEL.SMA ? true : false
		// const  = await isIncreaseLimitLoanFlow(contractNumber)
		let workflowBody = {
			contract_number: contractNumber,
			partner_code: partnerCode,
			product_code: productCode
		}
		let currentTask

		let responseBody = {
			"code": "200",
			"step": "CE check",
			"message": "save info successful",
			"nextStep": "generate contract"
		}
		if (status === 'CALCULATED_MAXLOAN') {
			await loanContractRepo.updateContractStatus(STATUS.ELIGIBLE, contractNumber);
			currentTask = MANUAL_TASK_CODE.CE_MANUAL.CE_CALCULATE_MAXLOAN;
			workflowBody.currentTask = currentTask;
			routing(workflowBody);
			responseBody.nextStep = `CONTINUE A2 : ${contractNumber}`
			callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ELIGIBLE)
			return res.status(200).json(responseBody)
		}
		if (status === 'CANCEL') {
			if (partnerCode === PARTNER_CODE.SMA) {
				loanContractRepo.updateContractStatus(STATUS.IN_SS_QUEUE, contractNumber)
				await aadService.pushTaskMcV2(roleCode.SS, contractNumber, contractData.contract_type, STATUS.IN_SS_QUEUE)
			} else {
				await Promise.all([
					loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber),
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.CANCELLED),
					crmService.removeContract(global.config, contractNumber)
				])
				if (partnerCode === PARTNER_CODE.MCAPP) {
					//send noti mc app
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Hạn mức đã bị từ chối',
						message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị huỷ`,
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
					sendNotification(bodyNoti, endPoint, req.config);
				}
			}
			
			responseBody.nextStep = `CANCEL contractNumber : ${contractNumber}`
			return res.status(200).json(responseBody)
		}
		else if (status === 'APPROVE') {
			// const isOfferValid = await offerRepo.validateOffer(contractNumber);
			// if(!isOfferValid){
			// 	responseBody.code = "400";
			// 	responseBody.nextStep = 'SELECT OFFER AGAIN';
			// 	responseBody.message = `OFFER IS INVALID: ${contractNumber}`
			// 	return res.status(400).json(responseBody)
			// }
			await aadService.completedTaskByTaskId(contractNumber,taskId)
			await loanContractRepo.updateContractStatus(STATUS.ACCEPTED_WITH_OFFERS,contractNumber)
			await loanContractRepo.updateFieldLoanContract(contractNumber,'approval_tenor_kunn',approvalTenorKunn)
			const isMatchingOffer = await offerRepo.isMatchingOffer(contractNumber)
			if (isMatchingOffer) {
				// const isGenerateContractReview = await loanContractRepo.checkGenerateContractReview(contractNumber)
				// if(!utils.isNullOrEmpty(contractData?.root_contract_number)){
				// 	const oldContractNumber = contractData?.root_contract_number;
				// 	Promise.all([
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'province_cur',contractData?.province_cur),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'district_cur',contractData?.district_cur),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'ward_cur',contractData?.ward_cur),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'address_cur',contractData?.address_cur),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'province_per',contractData?.province_per),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'district_per',contractData?.district_per),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'ward_per',contractData?.ward_per),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'address_per',contractData?.address_per),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'id_number',contractData?.id_number),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'issue_date',contractData?.issue_date),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'issue_place',contractData?.issue_place),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'phone_number1',contractData?.phone_number1),
				// 		loanContractRepo.updateFieldLoanContract(oldContractNumber,'birth_date',contractData?.birth_date)
				// 	])
				// }
				await offerRepo.updateSelectedOffer(contractNumber, isMatchingOffer)
				if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL, PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)) {
					const kovCashLoan = PARTNER_CODE.KOV === partnerCode && contractType == CONTRACT_TYPE.CASH_LOAN ? true : false;
					if (kovCashLoan || [PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)) {
						await contractServiceFile.generateContract(contractNumber, partnerCode)
					} else {
						// if(isGenerateContractReview){
							await contractService.generateContract(req, contractNumber)
						// }else{
							// Promise.all([
							// 	loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber),
							// 	loanContractRepo.updateContractStatus(STATUS.ACTIVATED,contractData?.root_contract_number)
							// ])
						// }
					}
				}
				else if (partnerCode == PARTNER_CODE.VPL) {
					currentTask = MANUAL_TASK_CODE.CE_MANUAL.CE_APPROVE_MATCH_OFFER
					workflowBody.currentTask = currentTask
					routing(workflowBody)
				}
				else if (partnerCode == PARTNER_CODE.VTP) {
					responseBody.nextStep = "Chờ khách hàng vào webview chọn offer."
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.APPROVED)
				}
				else if (partnerCode == PARTNER_CODE.VSK) {
					if (contractData.contract_type == CONTRACT_TYPE.CASH_LOAN) {
						currentTask = MANUAL_TASK_CODE.CE_MANUAL.CE_APPROVE_MATCH_OFFER
						workflowBody.currentTask = currentTask
						routing(workflowBody)
					}
					else if (contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE) {
						// if(isGenerateContractReview){
							await contractService.generateContract(req, contractNumber)
						// }else{
						// 	Promise.all([
						// 		loanContractRepo.updateContractStatus(STATUS.SIGNED,contractNumber),
						// 		loanContractRepo.updateContractStatus(STATUS.ACTIVATED,contractData?.root_contract_number)
						// 	])
						// }
					}
				}
				else if (partnerCode == PARTNER_CODE.MISA) {
					await aadService.pushTaskMcV2(roleCode.CL, contractNumber, contractData.contract_type, STATUS.IN_CL_QUEUE)
					await loanContractRepo.updateContractStatus(STATUS.IN_CL_QUEUE, contractNumber)
					responseBody.nextStep = 'move task to CL';
				}

				responseBody.message = `FOUND MATCHING OFFER ${contractNumber}`
				return res.status(200).json(responseBody)
			}
			else {
				if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL, PARTNER_CODE.MISA].includes(partnerCode)) {
					const kovCashLoan = PARTNER_CODE.KOV === partnerCode && contractType == CONTRACT_TYPE.CASH_LOAN ? true : false;
					let isPushMc = true;
					if (kovCashLoan && contractData.product_code === SCHEME.KOV_STANDARD) {
						isPushMc = false;
						await callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ALT_OFFER);
					};
					if (isPushMc) {
						await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
						await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
					}
				}
				else if ([PARTNER_CODE.VPL].includes(partnerCode)) {
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ALT_OFFER)
				}
				else if ([PARTNER_CODE.VTP].includes(partnerCode)) {
					if (custType == VTP_CUST_SCHEMA.STANDARD) {
						await callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ALT_OFFER)
					}
					else {
						await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
						await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
					}
				}
				else if (partnerCode == PARTNER_CODE.VSK) {
					if (contractData.contract_type == CONTRACT_TYPE.CASH_LOAN) {
						if (custType == VTP_CUST_SCHEMA.VIP || custType == VTP_CUST_SCHEMA.PREMIUM) {
							await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
							await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
						}
						else if (custType == VTP_CUST_SCHEMA.STANDARD) {
							const config = req.config
							const poolRead = req.poolRead
							const smsUrl = config.data.smsService.sendSMS
							const isEnable = config.data.smsService.useSMS
							let msg = config.data.smsService.selectOfferVMMsg
							const offerData = await offerRepo.getSelectedOffer(contractNumber)
							msg = msg.replace("contractNumber", contractNumber).replace("contractNumber", contractNumber).replace("offer", utils.formatCash(offerData?.offer_amt) || 0).replace("mm", offerData?.tenor || 0)
							const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
							try {
								if (phoneNumberRs !== undefined && isEnable) {
									await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs)
								}
							}
							catch (error) {
								console.log(error)
							}
						}
					}
					else if (contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE) {
						await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
						await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
					}
				}

				if([PARTNER_CODE.MCAPP,PARTNER_CODE.SMA].includes(partnerCode)){
					//send noti mc app
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Vui lòng chọn số tiền được duyệt',
						message: `Vui lòng chọn số tiền được duyệt của hợp đồng hạn mức ${contractNumber}`, 
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.UNMATCH_OFFER;
					sendNotification( bodyNoti, endPoint, req.config );
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ALT_OFFER) 
				}
				responseBody.message = "APPROVE UNMATCH OFFER"
				return res.status(200).json(responseBody)
			}
		}
		else if (status === 'DEVIATION') {
			await loanContractRepo.updateContractStatus(STATUS.DEVIATION)
			await aadService.pushTaskMcV2(roleCode.RP, contractNumber, contractData.contract_type, STATUS.IN_RP_QUEUE)
			await loanContractRepo.updateContractStatus(STATUS.IN_RP_QUEUE, contractNumber)
			responseBody.nextStep = `move task to RP : ${contractNumber}`
		}
		else if (status === 'SAVE') {
			responseBody.nextStep = 'save successfully.'
			return res.status(200).json(responseBody)
		}
		else if (status === 'RESUBMIT') {
			const config = req.config
			const poolRead = req.poolRead
			const smsUrl = config.data.smsService.sendSMS
			const isEnable = config.data.smsService.useSMS
			let msg = ''
			if (contractType == CONTRACT_TYPE.CASH_LOAN) {
				msg = config.data.smsService.resubmitCEVMMsg
				if (partnerCode == PARTNER_CODE.MCAPP) 
					msg = config.data.smsService.resubmitCEVMMcaMsg
			}
			else if (contractType == CONTRACT_TYPE.CREDIT_LINE) {
				msg = config.data.smsService.resubmitCEHMMsg
				if ([PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode))
					msg = config.data.smsService.resubmitCEHMMcaMsg
			}
			if (partnerCode == PARTNER_CODE.VSK) {
				msg = msg.replace("contractNumber", contractNumber)
				const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
				let countInclude = 0
				for await (const item of body.resubmit) {
					if (item.bundleName == 'OTHER DOCUMENTS') countInclude++
				}
				if (countInclude == 0) {
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
				}
			}
			if ([PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)) {
				msg = msg.replace("contractNumber", contractNumber)
				const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
				try {
					if (phoneNumberRs !== undefined) {
						if (isEnable) {
							await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs)
						}
					}
				}
				catch (error) {
					console.log(error)
				}
				//send noti mc app
				if (!isSuperApp) {
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Vui lòng bổ sung lại chứng từ!',
						message: `Vui lòng bổ sung lại chứng từ của hợp đồng hạn mức ${contractNumber}`,
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.RESUBMIT;
					sendNotification(bodyNoti, endPoint, config);
				}
			}
			await Promise.all([
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.RESUBMIT, body.resubmit),
				loanContractRepo.updateContractStatus(STATUS.CE_RESUBMIT, contractNumber)
			])

			if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL, PARTNER_CODE.VTP, PARTNER_CODE.VSK].includes(partnerCode)) {
				let isPushTaskSS = true;
				const kovCashLoan = PARTNER_CODE.KOV === partnerCode && contractType == CONTRACT_TYPE.CASH_LOAN ? true : false;
				if (kovCashLoan) {
					const docsNotCallKov = ['OTHER', 'EXTERNAL']; //lỗi kiếm ông TuấnNM
					let onlyCustDoc = true;
					checkSSTaskLoop:
					for (const item of body.resubmit) {
						for (const i of docsNotCallKov) {
							if (item?.bundleName.includes(i)) {
								onlyCustDoc = false;
								break checkSSTaskLoop;
							}
						}
					}
					isPushTaskSS = onlyCustDoc ? false : true;
				}
				if (isPushTaskSS) {
					await aadService.pushTaskMcV2(roleCode.SS, contractNumber, contractData.contract_type, STATUS.CE_RESUBMIT)
				}
			}
			responseBody.nextStep = "RESUBMIT contract"
			return res.status(200).json(responseBody)
		}
		else if (status === 'SECURITY') {
			loanContractRepo.updateContractStatus(STATUS.IN_SECURITY_CE_QUEUE, contractNumber)
			responseBody.nextStep = "CE hold on and recheck"
			return res.status(200).json(responseBody)
		}
		else {
			await loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber)
			if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL].includes(partnerCode) || (partnerCode === PARTNER_CODE.MISA && statusCur !== STATUS.IN_CE_MAXLOAN_QUEUE)) {

				const isKovCash = PARTNER_CODE.KOV === partnerCode && contractData.contract_type === CONTRACT_TYPE.CASH_LOAN ? true : false;
				let isPushMC = true;
				if (isKovCash && contractData.product_code === SCHEME.KOV_STANDARD) isPushMC = false;
				if (isPushMC) {
					await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
					await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
				}
			}
			else if (partnerCode == PARTNER_CODE.VPL) {
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED)
			}
			else if (partnerCode == PARTNER_CODE.VTP) {
				if (custType == VTP_CUST_SCHEMA.STANDARD) {
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED)
				}
				else {
					await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
					await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
				}
			}
			else if (partnerCode == PARTNER_CODE.VSK) {
				if (contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE) {
					await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
					await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
				}
				else if (contractData.contract_type == CONTRACT_TYPE.CASH_LOAN) {
					if (custType == VTP_CUST_SCHEMA.VIP || custType == VTP_CUST_SCHEMA.PREMIUM) {
						await aadService.pushTaskMcV2(roleCode.MC, contractNumber, contractData.contract_type, STATUS.IN_MC_QUEUE)
						await loanContractRepo.updateContractStatus(STATUS.IN_MC_QUEUE, contractNumber)
					}
				}
			}
			else if (partnerCode === PARTNER_CODE.MISA) {
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED)
				const config = req.config;
				const smsUrl = config.data.smsService.sendSMS;
				const isEnable = config.data.smsService.useSMS;
				const msg = config.data.smsService.notEligibleMsg;
				const phoneNumberRs = await utils.getPhoneNumberSme(contractNumber)
				try {
					if (phoneNumberRs !== undefined) {
						if (isEnable) {
							await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true)
						}
					}
				}
				catch (error) {
					console.log(error)
				}
			}
			else {
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED)
			}

			// if ([PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)) {
			// 	//send noti mc app
			// 	if (!isSuperApp) {
			// 		const bodyNoti = {
			// 			phoneNumber: contractData?.phone_number1,
			// 			title: 'Hạn mức đã bị từ chối',
			// 			message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị từ chối`,
			// 			value: {
			// 				contractNumber: contractNumber
			// 			}
			// 		};
			// 		const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
			// 		sendNotification(bodyNoti, endPoint, req.config);
			// 	}
			// 	callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED) 
			// }

			responseBody.nextStep = `REJECT contract ${contractNumber}`
			return res.status(200).json(responseBody)
		}
	}
	catch (err) {
		common.responseErrorInternal(res, err)
	}
}


function saveCeUploadDoc(req, res) {
	const poolWrite = req.poolWrite
	const updatedDate = new Date()
	const { contractNumber, docId, comment, userName } = req.body
	const sql = "update loan_contract_document set contract_number = $1,created_by=$2,type_collection=$3,comment=$4,updated_date = $5 where doc_id =$6"
	poolWrite.query(sql, [contractNumber, userName, collectingDocType.CE, comment, updatedDate, docId])
	return res.status(200).json({
		code: 1,
		msg: "save ce upload doc successfully"
	})
}

async function saveCeUploadDocV2(req, res) {
	try {
		const poolWrite = req.poolWrite
		const updatedDate = new Date()
		const { 
			contractNumber, 
			kunnContractNumber, 
			docId, 
			comment, 
			userName, 
			typeCollection,
			period,
			reportType
		} = req.body

		if (!comment || !docId || !userName || (!contractNumber && !kunnContractNumber)) {
			return res.status(400).json({
				code: 0,
				msg: "invalid parameter"
			})
		}

		const selectSql = "select type_collection, file_name, doc_type, url from loan_contract_document where doc_id =$1 and contract_number is null and kunn_contract_number is null"
		const queryRs = await poolRead.query(selectSql, [docId])
		if (queryRs.rows.length == 0) {
			return res.status(200).json({
				code: 0,
				msg: "upload ce upload doc fail"
			})
		}
		let docType = queryRs.rows[0].doc_type;
		if ((docType === DOC_TYPE.SFSTD2 || docType === DOC_TYPE.SNFS2) && (!period || !reportType)) {
			return res.status(400).json({
				code: 0,
				msg: "period and reportType is required"
			})
		}

		let kunnData, contractData;
		if (contractNumber) {
			contractData = await loanContractRepo.getLoanContract(contractNumber);
		}

		if (kunnContractNumber) {
			kunnData = await kunnRepo.getKunnData(kunnContractNumber);
		}

		const documentRs = await getValueCodeByCodeType("DOCUMENT");
		let docNameVnDetail = '';
		if (Array.isArray(documentRs) && documentRs.length > 0) {
			docNameVnDetail = documentRs.find(masDoc => masDoc.code == queryRs.rows[0].doc_type)?.value
		}

		const sql = "update loan_contract_document set contract_number = $1,kunn_contract_number = $7,created_by=$2,type_collection=$3,comment=$4,updated_date = $5,doc_name_vn_detail=$8 where doc_id =$6 and contract_number is null and kunn_contract_number is null"
		await poolWrite.query(sql, [contractNumber, userName, typeCollection, comment, updatedDate, docId, kunnContractNumber, docNameVnDetail])

		const historySql = "INSERT INTO loan_contract_document_upload_histories " +
			"(contract_number, kunn_contract_number, new_doc_id, reason, created_by, contract_status, kunn_status, new_file_name) " +
			"values ($1,$2,$3,$4,$5,$6, $7,$8)"

		await poolWrite.query(historySql, [contractNumber, kunnContractNumber, docId, comment, userName, contractData?.status, kunnData?.status, queryRs.rows[0].file_name])

		//xử lý dữ liệu cho báo cáo cic
		// Nếu docType là SFSTD2 hoặc SNFS2 thì sẽ thực hiện lưu thông tin vào bảng loan_revenues 
		// và revenue_documents. Riêng loan_revenues thì chỉ tạo mới khi cặp year(period) và contract_number chưa tồn tại.
		if (docType === DOC_TYPE.SFSTD2 || docType === DOC_TYPE.SNFS2) {
			const cb = async (results) => {
				for (const revenueDoc of results || []) {
					revenueDoc.docId = revenueDoc.id;
					await cicReportService.handleExportDocumentData({
						doc: revenueDoc,
						custId: contractData.cust_id,
						contractNumber,
					});
				}
			};

			const revenue = await loanContractRepo.getLoanRevenueByPeriod(contractNumber, period);
			if (!revenue) {
				await loanContractRepo.insertLoanRevenues(contractNumber, [{
					year: period,
					netRevenue: 0,
					totalAssets: 0,
					financialReportType: reportType,
					financialReportDocs: [
						{
							fileUrl: null,
							fileType: null,
							docType,
							evfFileUrl: queryRs.rows[0].url,
						}
					]
				}], cb);
			} else {
				await loanContractRepo.insertRevenueDocuments(revenue.id, [
					{
						fileUrl: null,
						fileType: null,
						docType,
						evfFileUrl: queryRs.rows[0].url,
					}
				], cb);
			}
		}

		return res.status(200).json({
			code: 1,
			msg: "save ce upload doc v2 successfully"
		})
	} catch (error) {
		console.error(error);
		return res.status(200).json({
			code: 0,
			msg: error?.message ?? "internal error"
		})
	}
}

async function replaceCeUploadDoc(req, res) {
	try {
		const poolWrite = req.poolWrite
		const updatedDate = new Date()
		const { contractNumber, kunnContractNumber, docId, comment, userName, oldDocId } = req.body

		if (!comment || !oldDocId || !docId || !userName || (!contractNumber && !kunnContractNumber)) {
			return res.status(400).json({
				code: 0,
				msg: "invalid parameter"
			})
		}

		const selectSql = "select type_collection, file_name from loan_contract_document where doc_id =$1 and contract_number is null and kunn_contract_number is null"
		const queryRs = await poolRead.query(selectSql,[docId])

		const oldSelectSql = "select type_collection, file_name, doc_name_vn_detail from loan_contract_document where doc_id =$1"
		const oldQueryRs = await poolRead.query(oldSelectSql,[oldDocId])

		if (queryRs.rows.length == 0) {
			return res.status(200).json({
				code: 0,
				msg: "replace ce upload doc fail"
			})
		}

		let kunnData, contractData;
		if (contractNumber) {
			contractData = await loanContractRepo.getLoanContract(contractNumber);
		}

		if (kunnContractNumber) {
			kunnData = await kunnRepo.getKunnData(kunnContractNumber);
		}

		const sql = "update loan_contract_document set contract_number = $1, kunn_contract_number = $7,created_by=$2,type_collection=$3,comment=$4,updated_date = $5,doc_name_vn_detail=$8 " +
			"where doc_id =$6 and contract_number is null and kunn_contract_number is null"
		await poolWrite.query(sql, [contractNumber, userName, queryRs.rows[0].type_collection, comment, updatedDate, docId, kunnContractNumber, oldQueryRs.rows[0].doc_name_vn_detail])

		const deleteSql = "update loan_contract_document set is_deleted=$1,comment=$2,updated_date = $3 where doc_id =$4"
		await poolWrite.query(deleteSql, [1, comment, updatedDate, oldDocId ])

		const historySql = "INSERT INTO loan_contract_document_upload_histories " +
			"(contract_number, kunn_contract_number, old_doc_id, new_doc_id, reason, created_by, contract_status, kunn_status, old_file_name, new_file_name) " +
			"values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)"

		await poolWrite.query(historySql, [contractNumber, kunnContractNumber, oldDocId, docId, comment, userName, contractData?.status, kunnData?.status, oldQueryRs.rows[0]?.file_name, queryRs.rows[0].file_name ])

		return res.status(200).json({
			code: 1,
			msg: "replace ce upload doc successfully"
		})
	} catch (e) {
		console.log('error at replaceCeUploadDoc', e);
		return res.status(200).json({
			code: 0,
			msg: "internal error"
		})
	}
}

async function getCeUploadDoc(req, res) {
	const poolRead = req.poolRead
	const { contractNumber } = req.query
	const sql = "select id, doc_type,doc_id,file_name,comment,updated_date,created_by from loan_contract_document where contract_number=$1 and type_collection=$2 and is_deleted = 0 and doc_type != 'RATING' order by creation_time desc"
	const rs = await poolRead.query(sql, [contractNumber, collectingDocType.CE])
	if (rs.rows.length == 0) {
		return res.status(200).json({
			code: 0,
			msg: 'Can not found contractNumber'
		})
	}
	return res.status(200).json({
		code: 1,
		msg: "Get document sucessfully.",
		data: rs.rows.map(row => {
			return {
				idLoanContractDocument: row.id,
				docType: row.doc_type,
				fileName: row.file_name,
				docId: row.doc_id,
				comment: row.comment,
				createdBy: row.created_by,
				createdDate: row.updated_date
			}
		})
	})
}

async function getDocUploadHistory(req, res) {
	try {
		const poolRead = req.poolRead
		const { contractNumber, kunnContractNumber, docId } = req.query
		let params = [];

		let sql = "select * from loan_contract_document_upload_histories where 1 = 1"

		let index = 1;
		if (contractNumber && kunnContractNumber) {
			sql += ` and (contract_number = $${index++} or kunn_contract_number = $${index++})`
			params.push(contractNumber)
			params.push(kunnContractNumber)
		} else {
			if (contractNumber) {
				sql += ` and contract_number = $${index++}`
				params.push(contractNumber)
			}

			if (kunnContractNumber) {
				sql += ` and kunn_contract_number = $${index++}`
				params.push(kunnContractNumber)
			}
		}

		if (docId) {
			sql += ` and (new_doc_id = $${index++})`
			params.push(docId)
		}

		let recursiveSql = "WITH RECURSIVE history AS (" +
			sql +
			"    UNION ALL" +
			"    SELECT t.*" +
			"    FROM loan_contract_document_upload_histories t" +
			"    INNER JOIN history h ON t.new_doc_id = h.old_doc_id" +
			")" +
			"SELECT * FROM history order by creation_time desc;"

		const rs = await poolRead.query(recursiveSql, params)

		return res.status(200).json({
			code: 1,
			msg: "Get document upload history sucessfully.",
			data: rs.rows.map(row => {
				return {
					id: row.id,
					contract_number: row.contract_number,
					kunn_contract_number: row.kunn_contract_number,
					old_doc_id: row.old_doc_id,
					new_doc_id: row.new_doc_id,
					creation_time: row.creation_time,
					reason: row.reason,
					created_by: row.created_by,
					contract_status: row.contract_status,
					kunn_status: row.kunn_status,
					old_file_name: row.old_file_name,
					new_file_name: row.new_file_name,
				}
			})
		})
	} catch (e) {
		console.log("error getDocUploadHistory: ", e)
		return res.status(500).json({
			code: 0,
			msg: "Internal error.",
			data: []
		})
	}

}

async function getUploadDocSme(req, res) {
	const poolRead = req.poolRead
	const { contractNumber } = req.query
	const sql = "select id, doc_type,doc_id,file_name,comment,updated_date,created_by from loan_contract_document where contract_number=$1 and type_collection=$2 and is_deleted = 0 and doc_type = 'RATING' "
	const rs = await poolRead.query(sql, [contractNumber, collectingDocType.CE])
	if (rs.rows.length == 0) {
		return res.status(200).json({
			code: 0,
			msg: 'Can not found contractNumber'
		})
	}
	return res.status(200).json({
		code: 1,
		msg: "Get document sucessfully.",
		data: rs.rows.map(row => {
			return {
				idLoanContractDocument: row.id,
				docType: row.doc_type,
				fileName: row.file_name,
				docId: row.doc_id,
				comment: row.comment,
				createdBy: row.created_by,
				createdDate: row.updated_date
			}
		})
	})
}

async function updateUploadDoc(req, res) {
	try {
		const poolWrite = req.poolWrite;
		const updatedDate = new Date();
		const { contractNumber, docId, comment, idLoanContractDocument } = req.body;

		if (!contractNumber || !docId) {
			res.status(200).json({
				code: 0,
				message: 'Missing params',
			})
		}

		const sql = `UPDATE loan_contract_document 
			SET doc_id=$1, comment=$2, updated_date=$3 WHERE contract_number=$4 AND id=$5`;
		await poolWrite.query(sql, [
			docId,
			comment,
			updatedDate,
			contractNumber,
			idLoanContractDocument
		]);

		return res.status(200).json({
			code: 1,
			msg: "udpate doc successfully",
		});
	} catch (error) {
		return res.status(500).json({
			message: error.message,
			code: -1,
		});
	}
}

async function deleteUploadDoc(req, res) {
	try {
		const poolWrite = req.poolWrite;
		const { contractNumber, docId } = req.body;
		if (!contractNumber || !docId) {
			res.status(200).json({
				code: 0,
				message: 'Missing params',
			})
		}
		const sql = `UPDATE loan_contract_document 
		SET is_deleted=1 WHERE doc_id=$1 AND contract_number=$2`

		await poolWrite.query(sql, [
			docId,
			contractNumber,
		]);
		return res.status(200).json({
			code: 1,
			msg: "delete doc successfully",
		});
	} catch (error) {
		return res.status(500).json({
			message: error.message,
			code: -1,
		});
	}
}

module.exports = {
	updateCaseStatus,
	saveCeUploadDoc,
	saveCeUploadDocV2,
	replaceCeUploadDoc,
	getCeUploadDoc,
	getUploadDocSme,
	updateUploadDoc,
	deleteUploadDoc,
	getDocUploadHistory,
}