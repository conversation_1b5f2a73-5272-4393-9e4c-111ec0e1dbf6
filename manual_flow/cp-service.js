const crmService = require("../utils/crmService")
const callbackService = require("../services/callback-service")
const { STATUS, CALLBACK_STAUS } = require("../const/caseStatus")
const { routing } = require("../services/workflow-service")
const { MANUAL_TASK_CODE, PARTNER_CODE, roleCode, CONTRACT_TYPE } = require("../const/definition")
const loanContractRepo = require("../repositories/loan-contract-repo")
const aadService = require("../utils/aadService")
const lmsService = require("../services/lms-service")
const common = require('../utils/common')
const smsService = require("../utils/smsService")
const utils = require("../utils/helper")
const documentRepo = require("../repositories/document")
const { serviceEndpoint } = require("../const/config")
const { sendNotification } = require("../services/notification-service")

async function updateCaseStatus(req, res, contractNumber, body) {
	try {
		const decision = body.decision
		const contractData = await loanContractRepo.getLoanContract(contractNumber)
		const partnerCode = contractData.partner_code
		const productCode = contractData.product_code
		const contractType = contractData?.contract_type
		const contractStatus = contractData?.status
		const config = req.config;
		const smsUrl = config.data.smsService.sendSMS
		const isEnable = config.data.smsService.useSMS
		let phoneNumberRs
		let msg = ''

		let workflowBody = {
			contract_number: contractNumber,
			partner_code: partnerCode,
			product_code: productCode
		}
		let currentTask;

		let responseBody = {
			"code": "200",
			"step": "CP check",
			"message": "save info successful",
			"nextStep": "move task to CE"
		}
		const isSigned = await loanContractRepo.validSigned(contractNumber)
		if (decision === 'APPROVE') {
			if (contractStatus === STATUS.IN_CP_POST_QUEUE&&partnerCode===PARTNER_CODE.MISA) {
				const signUri = '/esigning/internal/misa/sign';
				const signUrl = config.basic['bss-esigning-service'][config.env] + signUri;
				const contractData = await documentRepo.getAllLctDocument(contractNumber);
				const fileKey = contractData?.file_key;
				const body = {
					contractNumber: contractNumber,
					filePath: fileKey
				}
				const rsSign = await common.postApiV2(signUrl, body);
				if (rsSign.data.code == 0) {
					msg = config.data.smsService.activeSmeHmMsg
					msg = msg.replace("contractNumber", contractNumber)
					phoneNumberRs = await utils.getPhoneNumberSme(contractNumber)
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
					
					responseBody.nextStep = 'ACTIVE contract';
					return res.status(200).json(responseBody);
				}
				else{
					responseBody.nextStep = 'ACTIVE contract fails';
					return res.status(400).json(responseBody);
				}
			}
			else{
				if(isSigned) {
					//const rs = await lmsService.createCashLoanAccount(contractNumber)
					await lmsService.createLMS(contractNumber,contractType,contractData.phone_number1,partnerCode)
					return res.status(200).json(responseBody)	
				}
				else if(partnerCode==PARTNER_CODE.KOV&&contractType==CONTRACT_TYPE.CASH_LOAN){
					currentTask = MANUAL_TASK_CODE.CP_MANUAL.CP_APPROVE_CL
					workflowBody.currentTask = currentTask
					routing(workflowBody)
					return res.status(200).json(responseBody)	
				}
				else {
					currentTask = MANUAL_TASK_CODE.CP_MANUAL.CP_APPROVE
					workflowBody.currentTask = currentTask
					routing(workflowBody)
					return res.status(200).json(responseBody)	
					
				}
			}
		}
		else if (decision === 'RESUBMIT') {
			if ([PARTNER_CODE.VSK].includes(partnerCode)) {
				const config = req.config
				const poolRead = req.poolRead
				if (contractType == CONTRACT_TYPE.CASH_LOAN) {
					msg = config.data.smsService.resubmitCPVMMsg
				}
				else if (contractType == CONTRACT_TYPE.CREDIT_LINE) {
					msg = config.data.smsService.resubmitCPHMMsg
				}
				msg = msg.replace("contractNumber", contractNumber)
				phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
				let countInclude = 0
				for await (const item of body.resubmit) {
					if (item.bundleName == 'OTHER DOCUMENTS') countInclude++
				}
				if (countInclude == 0) {
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
				}
			}
			await Promise.all([
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.RESUBMIT, body.resubmit),
			])
			if (isSigned) {
				await loanContractRepo.updateContractStatus(STATUS.CP_RESUBMIT_ESIGN, contractNumber)
			}
			else {
				await loanContractRepo.updateContractStatus(STATUS.CP_RESUBMIT, contractNumber)
			}
			if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL].includes(partnerCode)) {
				let isPushTaskSS = true;
				const kovCashLoan = PARTNER_CODE.KOV === partnerCode && contractType == CONTRACT_TYPE.CASH_LOAN ? true : false;
				if (kovCashLoan) {
					const docsNotCallKov = ['OTHER DOCUMENTS', 'EXTERNAL_DOCUMENT'];
					let onlyCustDoc = true;
					for (const item of body.resubmit) {
						if (docsNotCallKov.indexOf(item.bundleName) !== -1) {
							onlyCustDoc = false;
							break;
						}
					}
					isPushTaskSS = onlyCustDoc ? false : true;
				}
				if (isPushTaskSS) await aadService.pushTaskMcV2(roleCode.SS,contractNumber,contractData.contract_type,STATUS.CP_RESUBMIT)
			}
			responseBody.nextStep = "RESUBMIT contract"
			return res.status(200).json(responseBody)
		}
		else if (decision == 'REJECT') {
			try {
				if (partnerCode === PARTNER_CODE.MCAPP) {
					//send noti mc app
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Hạn mức đã bị từ chối',
						message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị từ chối`, 
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
					await sendNotification( bodyNoti, endPoint, req.config );   
				}
			} catch (err) {
				common.log('send notification to appMC error', contractNumber)
				console.log(err?.message)
			}
			await Promise.all([
				loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber),
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED),
				crmService.rejectContract(global.config, contractNumber)
			])
			responseBody.nextStep = "REJECT contract"
			return res.status(200).json(responseBody)
		}
		else if (decision == 'CANCEL') {
			await Promise.all([
				loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber),
				callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.CANCELLED),
				crmService.removeContract(global.config, contractNumber)
			])
			responseBody.nextStep = "CANCEL contract"
			return res.status(200).json(responseBody)
		}
		return res.status(200).json(responseBody)
	}
	catch (err) {
		common.responseErrorInternal(res, err)
	}

}

module.exports = {
	updateCaseStatus
}