const common = require("../utils/common")
const crmService = require("../utils/crmService")
const utils = require("../utils/helper")
const callbackService = require("../services/callback-service")
const {caseStatus,caseStatusCode,CALLBACK_STAUS,STATUS} = require("../const/caseStatus")
const aadService = require("../utils/aadService")
const { roleCode, PARTNER_CODE, CONTRACT_TYPE } = require("../const/definition")
const loanContractRepo = require("../repositories/loan-contract-repo")
const codService = require("../services/cod-service")
const loanManualDecisionRepo = require("../repositories/loan-manual-decision-repo")
const smsService = require("../utils/smsService")
const offerRepo = require("../repositories/offer")

async function updateCaseStatus(req,res,contractNumber,body) {
	try {
		const poolRead = req.poolRead
		const decision = body.decision
		// const comment = body.deviation.comment
		const taskId = body.taskId
		// const config = req.config
		// const poolWrite = req.poolWrite
		const userName = body.userName
		const contractData = await loanContractRepo.getLoanContract(contractNumber)
		const partnerCode = contractData.partner_code
		const userRole = body.role
		const step = userRole
		const deviation = body.deviation
		// let callbackBody = {
		// 	"contractNumber" : contractNumber,
		// }

		let responseBody = {
			"code": "200",
			"step" : "MC check",
			"message": "save info successful",
			"nextStep" : "move task to CE"
		}
		
		if(decision === 'REJECT') {
			await Promise.all([
				loanContractRepo.updateContractStatus(STATUS.REFUSED,contractNumber),
				callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.REJECTED)
			]) 
		}
		else if (decision === 'APPROVE'){
			await loanContractRepo.updateContractStatus(STATUS.MC_APPROVED,contractNumber);
			const sql = "select role,result_chk from loan_manual_decision where contract_number = $1 and role ='CE' order by created_date desc;"
			const queryRs = await poolRead.query(sql,[contractNumber]) 
			
			if(queryRs.rows[0].role == 'CE' && queryRs.rows[0].result_chk == 'REJECT') {
				if(partnerCode===PARTNER_CODE.MISA){
					responseBody.nextStep = `move contract ${contractNumber} to C-Level`;
					await aadService.pushTaskMcV2(roleCode.CL,contractNumber,contractData.contract_type,STATUS.IN_CL_QUEUE)
					await loanContractRepo.updateContractStatus(STATUS.IN_CL_QUEUE,contractNumber)
				}
				else{
					await Promise.all([
						loanContractRepo.updateContractStatus(STATUS.REFUSED,contractNumber),
						crmService.rejectContract(global.config,contractNumber),
						callbackService.callbackPartner(contractNumber,partnerCode,STATUS.REJECTED)
					])
					responseBody.nextStep = `REJECT contract ${contractNumber}`
				}
			}
			else {
				await Promise.all([
					loanContractRepo.updateContractStatus(STATUS.ACCEPTED_WITH_OFFERS,contractNumber),
					callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.ALT_OFFER)
				])
				if(partnerCode=='VSK'){
					const contractType = contractData?.contract_type
					const config = req.config
					const poolRead = req.poolRead
					const smsUrl = config.data.smsService.sendSMS
					const isEnable = config.data.smsService.useSMS
					let msg = ''
					if(contractType==CONTRACT_TYPE.CASH_LOAN){
						msg = config.data.smsService.selectOfferVMMsg
					} 
					else if(contractType==CONTRACT_TYPE.CREDIT_LINE) {
						msg = config.data.smsService.selectOfferHMMsg
					}
					const offerData = await offerRepo.getSelectedOffer(contractNumber)
					msg = msg.replace("contractNumber", contractNumber).replace("contractNumber", contractNumber).replace("offer", utils.formatCash(offerData?.offer_amt)||0).replace("mm",offerData?.tenor||0)
					const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
					try {
						if (phoneNumberRs !== undefined) {
							if (isEnable) {
								await smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs)
							}
						}
					}
					catch (error) {
						console.log(error)
					}
				}
				if(partnerCode == PARTNER_CODE.MISA){
					await aadService.pushTaskMcV2(roleCode.CL,contractNumber,contractData.contract_type,STATUS.IN_CL_QUEUE)
					await loanContractRepo.updateContractStatus(STATUS.IN_CL_QUEUE,contractNumber)
				}
				responseBody.nextStep = `ALT_OFFER contract ${contractNumber}`
			}
		}
		else if (decision === 'RESUBMIT') {
			await aadService.pushTaskMcV2(roleCode.CE,contractNumber,contractData.contract_type,STATUS.IN_CE_QUEUE)
			await loanContractRepo.updateContractStatus(STATUS.IN_CE_QUEUE,contractNumber)
			responseBody.nextStep = 'move task to CE'
			res.status(200).json(responseBody)  

		}
		else if (decision === 'ESCALATION') {
			loanManualDecisionRepo.saveCaseWithReason(taskId,contractNumber,userName,userRole,step,decision,deviation)	
			await aadService.pushTaskMcV2(roleCode.RP,contractNumber,contractData.contract_type,STATUS.IN_RP_QUEUE)
			await loanContractRepo.updateContractStatus(STATUS.IN_RP_QUEUE,contractNumber)
			responseBody.nextStep = 'move taskt to RP'
		}
		return res.status(200).json(responseBody)
	}
	catch(err) {
		common.responseErrorInternal(res,err)
	}
}

module.exports = {
	updateCaseStatus
}