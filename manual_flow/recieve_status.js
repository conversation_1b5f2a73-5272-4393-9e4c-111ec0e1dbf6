const cpService = require("./cp-service")
const ceService = require("./ce-service")
const mcService = require("./mc-service")
const cService = require("./c-service")
const rpService = require("./rp-service")
const ssService = require("./ss-service")
const coService = require("./co-service")
const {chekedDocByRoleField,roleCode, PARTNER_CODE, MANUAL_DECISION} = require("../const/definition")
const aadService = require("../utils/aadService")
const masterdataService = require("../utils/masterdataService")
const manualDecisionRepo = require("../repositories/loan-manual-decision-repo")
const documentRepo = require("../repositories/document")
const offerRepo = require("../repositories/offer")
const {getPartnerCode} = require("../utils/helper")
const utils = require("../utils/helper")
const smsService = require("../utils/smsService")
const { getContractStaus, updateContractStatus, getLoanContract } = require("../repositories/loan-contract-repo")
const { STATUS } = require("../const/caseStatus")

async function updateCaseCenter(req,res) {
	try {
		const poolRead = req.poolRead
		const poolWrite = req.poolWrite
		const contractNumber = req.body.contractNumber
		const userName = req.body.userName
		const userRole = req.body.role
		const step = userRole
		let decision = req.body.decision
		const taskId = req.body.taskId
		const resubmitDoc = req.body.resubmit
		const listRejectCode = req.body.reject
 		const deviation = req.body.deviation
 		// const offers = req.body.offers
 		const selectedOffer = req.body.selectedOffer
		 if(selectedOffer !== undefined && decision === 'APPROVE'){
			let offerId
			if(Array.isArray(selectedOffer)){
				offerId =  selectedOffer[0]
			}else{
				offerId = selectedOffer.id
			}
			const offer = await offerRepo.getOfferById(offerId)	
			if(offer.offer_amt < 0)
			return res.status(500).json({
				"code": "500",
				"message": "Offer amount cannot be less than 0"
		  	})
		 }
		const cancel = req.body.cancel
		const checked = req.body.checkedDoc
		const config = req.config
		const partnerCode = await getPartnerCode(poolRead,contractNumber)
		const loanContractData = await getLoanContract(contractNumber)
		const statusCur = await getContractStaus(loanContractData?.root_contract_number)
		const smsUrl = config.data.smsService.sendSMS;
		const isEnable = config.data.smsService.useSMS;
		let msg = config.data.smsService.resubmitSmeHmMsg;
		msg = msg.replace("contractNumber", contractNumber)
		const phoneNumberRs = await utils.getPhoneNumberSme(contractNumber)
		// const approvalTenorKunn = req.body.approvalTenorKunn
		if(checked !== undefined) {
			await documentRepo.saveCheckedDoc(checked,userRole)
		}

		switch(decision) {
			case 'REJECT':
				if (statusCur !== STATUS.CREDIT_REVIEW) {
					await aadService.completedTaskByTaskId(contractNumber, taskId);
				}
				break;
				
			case 'SAVE':
			case 'SECURITY':
				break;
				
			default:
				if (userRole !== roleCode.CE || (userRole === roleCode.CE && decision !== MANUAL_DECISION.APPROVE)) {
					await aadService.completedTaskByTaskId(contractNumber, taskId);
				}
				break;
		}

		if(resubmitDoc !== undefined && decision === 'RESUBMIT') {
			if(resubmitDoc.length == 0 ) {
				return res.status(200).json({
				  	"code": "400",
				  	"message": "Resubmit reason is missing"
				})
			}
			if(partnerCode === PARTNER_CODE.MISA){
                try {
                    if (phoneNumberRs !== undefined) {
                        if (isEnable) {
                            await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true)
                        }
                    }
                }
                catch (error) {
                    console.log(error)
                }
			}
			manualDecisionRepo.saveResubmit(taskId,contractNumber,userName,userRole,step,decision,resubmitDoc)		
		}
		
		if(listRejectCode !== undefined && decision === 'REJECT') {
			if(statusCur === STATUS.CREDIT_REVIEW){
				return res.status(200).json({
					"code": "400",
					"message": "Review flow is not access to REJECT"
			  })
			}
			if(listRejectCode.length == 0 ) {
				return res.status(200).json({
				  	"code": "400",
				  	"message": "Reject reason is missing"
				})
			}
			if(partnerCode===PARTNER_CODE.MISA){
				try {
					msg = config.data.smsService.cancelSmeHmMsg;
					msg = msg.replace("contractNumber",contractNumber)
					if (phoneNumberRs !== undefined) {
						if (isEnable) {
							await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true)
						}
					}
				}
				catch (error) {
					console.log(error)
				}
			}
			manualDecisionRepo.saveCaseWithReasonV2(taskId,contractNumber,userName,userRole,step,decision,listRejectCode)			
		}

		if(cancel !== undefined && (userRole == 'CE' || userRole== 'CP')) {
			if(cancel.length == 0 ) {
				return res.status(200).json({
				  	"code": "400",
				  	"message": "Cancel reason is missing"
				})
			}
			manualDecisionRepo.saveCaseWithReason(taskId,contractNumber,userName,userRole,step,decision,cancel)				
		}

		if(deviation !== undefined) {
			decision = (userRole == roleCode.MC && decision != 'ESCALATION') ? 'AGREE WITH CE' : decision
			if(userRole == "CE") { 
				manualDecisionRepo.saveCaseWithReason(taskId,contractNumber,userName,userRole,step,decision,deviation)			
			}
			else {
				manualDecisionRepo.saveDeviation(taskId,contractNumber,userName,userRole,step,decision,deviation)
			}
		}

		if(selectedOffer !== undefined && Object.entries(selectedOffer).length != 0 && decision === 'APPROVE' && userRole == roleCode.CL) {
			if(Array.isArray(selectedOffer)){
				for(const offerId of selectedOffer) {
					await offerRepo.selectOffer(poolWrite,offerId, contractNumber,userName,userRole)
				}	
			}
			else{
				const offerId = selectedOffer.id
				await offerRepo.selectOffer(poolWrite,offerId,contractNumber,userName,userRole)	
			}
		}
		else if(selectedOffer !== undefined && Object.entries(selectedOffer).length != 0 && userRole != roleCode.CL) {
			if(Array.isArray(selectedOffer)){
				for(const offerId of selectedOffer) {
					await offerRepo.proposeOffer(contractNumber,offerId,userName,userRole)
				}	
			}else{
				const offerId = selectedOffer.id
				await offerRepo.proposeOffer(contractNumber,offerId,userName,userRole)
			}
		}

		if(decision === 'APPROVE' && (userRole == roleCode.CP || userRole == roleCode.CE)) {
			manualDecisionRepo.saveManualDecision(contractNumber,decision,userName,userRole,taskId)
		}
		
		if(decision === 'CANCEL'){
			if(statusCur === STATUS.CREDIT_REVIEW){
				updateContractStatus(STATUS.ACTIVATED,contractNumber)
			}
			if(partnerCode === PARTNER_CODE.MISA){
				try {
					msg = config.data.smsService.cancelSmeHmMsg;
					msg = msg.replace("contractNumber",contractNumber)
					if (phoneNumberRs !== undefined) {
						if (isEnable) {
							await smsService.sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true)
						}
					}
				}
				catch (error) {
					console.log(error)
				}
			}
		}
		
		if(userRole === 'CP') {
			cpService.updateCaseStatus(req,res,contractNumber,req.body)
		}
		else if(userRole === 'CE') {
			ceService.updateCaseStatus(req,res,contractNumber,req.body,taskId)
		}
		else if(userRole === 'MC') {
			mcService.updateCaseStatus(req,res,contractNumber,req.body)
		}
		else if(userRole == roleCode.CL) {
			cService.updateCaseStatus(req,res,contractNumber,req.body)
		}
		else if(userRole === 'RP') {
			rpService.updateCaseStatus(req,res,contractNumber,req.body)
		}
		else if(userRole === 'SS') {
			ssService.updateCaseStatus(req,res,contractNumber,req.body)
		}
		else {
			res.status(200).json({
			  	"code": "500",
			  	"message": "invalid role"
			})
		}
	}
	catch(error) {
		console.log(error)
		res.status(200).json({
		  	"code": "200",
		  	"message": "update case status error",
		})
	}
}

module.exports = {
	updateCaseCenter
}
