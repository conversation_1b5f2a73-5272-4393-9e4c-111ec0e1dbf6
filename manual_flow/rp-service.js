const utils = require("../utils/helper")
const {caseStatusCode, STATUS} = require("../const/caseStatus")
const {roleCode} = require("../const/definition")
const loanContractRepo = require("../repositories/loan-contract-repo")
const aadService = require("../utils/aadService")

async function updateCaseStatus(req,res,contractNumber,body) {
	const poolWrite = req.poolWrite
	// const userName = body.userName

	await utils.saveStatusAsync(poolWrite,contractNumber,caseStatusCode.RP03)
	const contractData = await loanContractRepo.getLoanContract(contractNumber)

	await aadService.pushTaskMcV2(roleCode.CL,contractNumber,contractData.contract_type,STATUS.IN_CL_QUEUE)
	await loanContractRepo.updateContractStatus(STATUS.IN_CL_QUEUE,contractNumber)

	let responseBody = {
		"code": "200",
		"step" : "RP check",
		"message": "save info successful",
		"nextStep" : "move task to C-level"
	}

	res.status(200).json(responseBody)		
}

module.exports = {
	updateCaseStatus
}