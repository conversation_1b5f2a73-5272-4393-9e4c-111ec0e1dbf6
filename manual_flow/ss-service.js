const s3Service = require("../upload_document/s3-service")
const uuid = require('uuid')
const { getAPI } = require("../utils/common");
let storageDocumentStoragePath = "document/"
const common = require("../utils/common")
const utils = require("../utils/helper")
const crmService = require("../utils/crmService")
const {caseStatusCode,caseStatus, STATUS, CALLBACK_STAUS} = require("../const/caseStatus")
const {roleCode, PARTNER_CODE, collectingDocType, MANUAL_TASK_CODE, CHANNEL} = require('../const/definition')
const aadService = require("../utils/aadService")
const loanContractRepo = require("../repositories/loan-contract-repo")
const lmsService = require("../services/lms-service");
const { routing } = require("../services/workflow-service");
const callbackService = require("../services/callback-service");
const { getAllLctDocument } = require("../repositories/document");
const { sendSMS } = require("../utils/smsService");
const { serviceEndpoint } = require("../const/config");
const { sendNotification } = require("../services/notification-service");
const { computeOffer } = require("../offer/offer");

async function uploadResubmit(req, res) {
    try {
		const poolWrite = req.poolWrite
        let files = req.files;
        let config = req.config.data;
		const {contractNumber} = req.body

        if (files === undefined) {
            return res.send({
                code: 'INVALID_REQUEST',
                message: 'File must be not null.',
            })
        }
		if(files.length !== 1) {
			return res.status(500).json({
				code : 0,
				msg : "invalid file"
			})
		}
        const item = files[0]
        
		const fieldName = item.fieldname
		const originalFileName = item.originalname
		const bundleId = await getDocGroup(contractNumber,fieldName)
		if (!bundleId) {
			return res.status(200).json({
				code: 0,
				message: "Invalid fieldname"
			})
		}
		const fileName = Date.now() + item.originalname.replace(/\s/g, "")
        const data = await s3Service.upload(config, fileName, item.buffer, storageDocumentStoragePath)
		const docId = uuid.v4()
		const fileKey = data.Key
		const url = data.Location
        await saveUploadResubmit(poolWrite,contractNumber, docId, url, fieldName, bundleId,fileKey,originalFileName)

		return res.status(200).json({
			code : 1,
			msg : "upload resubmit sucessfully",
			data : {
				docId,
				fieldName,
				fileName : originalFileName	
			}
		})
    }
    catch (error) {
        console.log(error)
        return res.status(500).send({ code: 'SERVER_ERROR', message: 'server error.' })
    }
}

async function updateCaseStatus(req,res,contractNumber,body) {
	try {
		const poolWrite = req.poolWrite
		const decision = body.decision
		const contractData = await loanContractRepo.getLoanContract(contractNumber)
		const partnerCode = contractData.partner_code;
		const productCode = contractData.product_code;
		const contractType = contractData?.contract_type;
		// const chanel = contractData?.chanel;

		let workflowBody = {
			contract_number: contractNumber,
			partner_code: partnerCode,
			product_code: productCode
		}
		let currentTask;

		let responseBody = {
			"code": "200",
			"step": "SS check",
			"message": "save info successful",
			"nextStep": "move task to CE"
		};

		if([PARTNER_CODE.KOV,PARTNER_CODE.SPL].includes(partnerCode)){
			const decision = body.decision
			const contractType = contractData?.contract_type

			const isSigned = await loanContractRepo.validSigned(contractNumber)
			if (decision === 'APPROVE') {
				if(isSigned) {
					await lmsService.createLMS(contractNumber,contractType,contractData.phone_number1,partnerCode)
					return res.status(200).json(responseBody)	
				}
				else {
					currentTask = MANUAL_TASK_CODE.SS_MANUAL.SS_APPROVE
					workflowBody.currentTask = currentTask
					routing(workflowBody)
					return res.status(200).json(responseBody)	
				}
			}
			else if (decision === 'RESUBMIT') {
				await Promise.all([
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.RESUBMIT, body.resubmit),
				])
				if (isSigned) {
					await loanContractRepo.updateContractStatus(STATUS.SS_RESUBMIT_ESIGN, contractNumber)
				}
				else {
					await loanContractRepo.updateContractStatus(STATUS.SS_RESUBMIT, contractNumber)
				}
				responseBody.nextStep = "RESUBMIT contract"
				return res.status(200).json(responseBody)
			}
			else if (decision == 'REJECT') {
				await Promise.all([
					loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber),
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED),
					crmService.rejectContract(global.config, contractNumber)
				])
				responseBody.nextStep = "REJECT contract"
				return res.status(200).json(responseBody)
			}
			else if (decision == 'CANCEL') {
				await Promise.all([
					loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber),
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.CANCELLED),
					crmService.removeContract(global.config, contractNumber)
				])
				responseBody.nextStep = "CANCEL contract"
				return res.status(200).json(responseBody)
			}
			return res.status(200).json(responseBody)
		}
		if(partnerCode == PARTNER_CODE.MCAPP || partnerCode == PARTNER_CODE.SMA){
			const isSigned = await loanContractRepo.validSigned(contractNumber);

			if (decision === 'APPROVE') {
				if(isSigned) {
					await lmsService.createLMS(contractNumber,contractType,contractData.phone_number1,partnerCode);
					return res.status(200).json(responseBody);
				}
				else {
					//call compute offer
					const loanMainScore = await loanContractRepo.getLoanContractJoinMainScore(poolWrite, contractNumber);
					computeOffer(contractNumber, partnerCode, 0, loanMainScore?.di_before_ce || 0);
					currentTask = MANUAL_TASK_CODE.SS_MANUAL.SS_APPROVE;
					workflowBody.currentTask = currentTask;
					routing(workflowBody);
					return res.status(200).json(responseBody);
				}
			}
			else if (decision === 'RESUBMIT') {
				await Promise.all([
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.RESUBMIT, body.resubmit)
				])
				if (isSigned) {
					await loanContractRepo.updateContractStatus(STATUS.SS_RESUBMIT_ESIGN, contractNumber);
				}
				else {
					await loanContractRepo.updateContractStatus(STATUS.SS_RESUBMIT, contractNumber);
				}
				
				let config = req.config;
				let poolRead = req.poolRead;
				let msg = config.data.smsService.resubmitCPHMMcaMsg
				const smsUrl = config.data.smsService.sendSMS
				const isEnable = config.data.smsService.useSMS
				msg = msg.replace("contractNumber", contractNumber)
				let phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
				try {
					if (phoneNumberRs !== undefined) {
						if (isEnable) {
							await sendSMS(contractNumber, msg, smsUrl, phoneNumberRs);
						}
					}
				} catch (error) {
					console.log(error)
				}
				//send noti mc app
				const bodyNoti = {
					phoneNumber: contractData?.phone_number1,
					title: 'Vui lòng bổ sung lại chứng từ!',
					message: `Vui lòng bổ sung lại chứng từ của hợp đồng hạn mức ${contractNumber}`, 
					value: {
						contractNumber: contractNumber
					}
				};
				const endPoint = serviceEndpoint.NOTIFICATION.RESUBMIT;
				sendNotification( bodyNoti, endPoint, config );    
				
				responseBody.nextStep = "RESUBMIT contract";
				return res.status(200).json(responseBody);
			}
			else if (decision == 'REJECT') {
				await Promise.all([
					loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber),
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED),
					crmService.rejectContract(global.config, contractNumber)
				]);

				//send noti mc app
				const bodyNoti = {
					phoneNumber: contractData?.phone_number1,
					title: 'Hạn mức đã bị từ chối',
					message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị từ chối`, 
					value: {
						contractNumber: contractNumber
					}
				};
				const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
				sendNotification( bodyNoti, endPoint, req.config );   

				responseBody.nextStep = "REJECT contract";
				return res.status(200).json(responseBody);
			}
			else if (decision == 'CANCEL') {
				await Promise.all([
					loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber),
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.CANCELLED),
					crmService.removeContract(global.config, contractNumber)
				]);
				//send noti mc app
				const bodyNoti = {
					phoneNumber: contractData?.phone_number1,
					title: 'Hạn mức đã bị từ chối',
					message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị huỷ`, 
					value: {
						contractNumber: contractNumber
					}
				};
				const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
				sendNotification( bodyNoti, endPoint, req.config );   

				responseBody.nextStep = "CANCEL contract";
				return res.status(200).json(responseBody);
			}
			return res.status(200).json(responseBody);
		}
		if(partnerCode == PARTNER_CODE.MISA){
			try {
				const contractType = contractData?.contract_type
				const contractStatus = contractData?.status
				const config = req.config;
				const smsUrl = config.data.smsService.sendSMS
				const isEnable = config.data.smsService.useSMS
				let phoneNumberRs
				let msg = ''
				
				if (decision === 'CALCULATED_MAXLOAN') {
					await loanContractRepo.updateContractStatus(STATUS.ELIGIBLE, contractNumber);
					currentTask = MANUAL_TASK_CODE.SS_MANUAL.SS_CALCULATE_MAXLOAN;
					workflowBody.currentTask = currentTask;
					routing(workflowBody);
					responseBody.nextStep = `CONTINUE A2 : ${contractNumber}`
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ELIGIBLE)
					return res.status(200).json(responseBody)
				}
				const isSigned = await loanContractRepo.validSigned(contractNumber)
				if (decision === 'APPROVE') {
					if (contractStatus === STATUS.IN_CP_POST_QUEUE) {
						const signUri = '/esigning/internal/misa/sign';
						const signUrl = config.basic['bss-esigning-service'][config.env] + signUri;
						const contractData = await getAllLctDocument(contractNumber);
						const fileKey = contractData?.file_key;
						const body = {
							contractNumber: contractNumber,
							filePath: fileKey
						}
						const rsSign = await common.postApiV2(signUrl, body);
						if (rsSign.data.code == 0) {
							msg = config.data.smsService.activeSmeHmMsg
							msg = msg.replace("contractNumber", contractNumber)
							phoneNumberRs = await utils.getPhoneNumberSme(contractNumber)
							try {
								if (phoneNumberRs !== undefined) {
									if (isEnable) {
										await sendSMS(contractNumber, msg, smsUrl, phoneNumberRs, true)
									}
								}
							}
							catch (error) {
								console.log(error)
							}
							
							responseBody.nextStep = 'ACTIVE contract';
							return res.status(200).json(responseBody);
						}
						else{
							responseBody.nextStep = 'ACTIVE contract fails';
							return res.status(400).json(responseBody);
						}
					}
					else{
						if(isSigned) {
							await lmsService.createLMS(contractNumber,contractType,contractData.phone_number1,partnerCode)
							return res.status(200).json(responseBody)	
						}
						else {
							currentTask = MANUAL_TASK_CODE.SS_MANUAL.SS_APPROVE
							workflowBody.currentTask = currentTask
							routing(workflowBody)
							return res.status(200).json(responseBody)	
						}
					}
				}
				else if (decision === 'RESUBMIT') {
					await Promise.all([
						callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.RESUBMIT, body.resubmit),
					])
					if (isSigned) {
						await loanContractRepo.updateContractStatus(STATUS.SS_RESUBMIT_ESIGN, contractNumber)
					}
					else {
						await loanContractRepo.updateContractStatus(STATUS.SS_RESUBMIT, contractNumber)
					}
					responseBody.nextStep = "RESUBMIT contract"
					return res.status(200).json(responseBody)
				}
				else if (decision == 'REJECT') {
					await Promise.all([
						loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber),
						callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED),
						crmService.rejectContract(global.config, contractNumber)
					])
					responseBody.nextStep = "REJECT contract"
					return res.status(200).json(responseBody)
				}
				else if (decision == 'CANCEL') {
					await Promise.all([
						loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber),
						callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.CANCELLED),
						crmService.removeContract(global.config, contractNumber)
					])
					responseBody.nextStep = "CANCEL contract"
					return res.status(200).json(responseBody)
				}
				return res.status(200).json(responseBody)
			}
			catch (err) {
				common.responseErrorInternal(res, err)
			}
		}
		else{
			if(decision == 'APPROVE') {
				const contractData = await loanContractRepo.getLoanContract(contractNumber)
				const resubmitRole = await loanContractRepo.getLastStepResubmit(contractNumber)
				if(!resubmitRole) {
					common.log(`INVALID SALE SUPPORT RESUBMIT `,contractNumber)
				}
				let status;
				switch(resubmitRole) {
					case roleCode.CP:
						status = STATUS.IN_CP_QUEUE;
						break;
					case roleCode.CE :
						status = STATUS.IN_CE_QUEUE;
						break;
					default:
						status = `IN_${roleCode}_QUEUE`
						break;
				}
				await aadService.pushTaskMcV2(resubmitRole,contractNumber,contractData.contract_type,status)
				await loanContractRepo.updateContractStatus(status,contractNumber)
				let responseBody = {
					"code": "200",
					"step" : "SS Aprove",
					"msg " : `pust task to ${resubmitRole}`
				}
	
				return res.status(200).json(responseBody)		
			}
			else {
				await utils.saveStatusAsync(poolWrite,contractNumber,caseStatusCode.CA01)
				crmService.rejectContract(contractNumber,contractNumber)
				return res.status(200).json({
					code : 1,
					msg : "close case"
				})		
			}
		}
	}catch(err) {
		console.log(err)
		return false
	}
}
 
const getBundleId = async (req, fieldName) => {
    const lb = req.config.basic.masterData[req.config.env];
    const uri = req.config.data.masterDataService.getBundle
    const url = lb + uri + '?documentId=' + fieldName
    const result = await getAPI(url)
    if (result && result.code === 1) {
        return result.bundleName
    }
    else return null
}

async function getDocGroup(contractNumber,docType) {
	try {
		const poolWrite = global.poolWrite
		const sql = "select doc_group  from loan_contract_document lcd where contract_number  =$1 and doc_type =$2"
		const rs = await poolWrite.query(sql,[contractNumber,docType])
		if(rs.rowCount == 0) {
			return false
		}
		return rs.rows[0].doc_group
	}
	catch(err) {
		console.log(err)
		return false
	}
}

async function saveUploadResubmit(poolWrite, contractNumber,docId, docUrl, docType, bundleId,fileKey,fileName) {
	const updateToDeleteSql = "update loan_contract_document set updated_date = now(),is_deleted=1,waiting_resubmit=0 where doc_type = $1 and contract_number = $2"
	await poolWrite.query(updateToDeleteSql,[docType,contractNumber])
    let sql = "insert into loan_contract_document(contract_number,doc_id,doc_type,url, doc_group,file_key,file_name,is_resubmit) values ($1,$2,$3,$4,$5,$6,$7,$8)"
    poolWrite.query(sql, [contractNumber,docId, docType, docUrl, bundleId,fileKey,fileName,1])
    .then()
    .catch(error => {
        common.log("INSERT - loan_contract_document : error")
    })
}

async function saveSScheckDoc(req,res) {
	const poolWrite = req.poolWrite
	const body = req.body
	const {contractNumber,userName,recievedBundle} = body
	const sql = "insert into sale_support_bundle_check (contract_number,bundle_name,recieved_from,user_name) values ($1,$2,$3,$4)"
	recievedBundle.map(row => {
		poolWrite.query(sql,[contractNumber,row.bundleName,row.recievedFrom,userName])
	})
	return res.status(200).json({
		code : 1,
		msg : "save SS bundle check successfully"
	})
}

async function saveSSComment(req,res) {
	const poolWrite = req.poolWrite
	const {contractNumber,comment,caseType,taskId,userName} = req.body
	const sql = "insert into loan_manual_decision (contract_number,deviation_cmt,step,task_id,assignee,role) values ($1,$2,$3,$4,$5,$6)"
	poolWrite.query(sql,[contractNumber,comment,caseType,taskId,userName,roleCode.SS])
	return res.status(200).json({
		code : 1,
		msg : "save successfully"
	})
}

async function getSSComment(req,res) {
	const poolRead = req.poolRead
	const {contractNumber,partnerCode} = req.query
	let sql = "select * from loan_manual_decision where contract_number = $1 and role = $2";
	let rs = await poolRead.query(sql,[contractNumber,roleCode.SS])
	if(!utils.isNullOrEmpty(partnerCode)&&partnerCode===PARTNER_CODE.MISA){
		sql = "select * from loan_manual_decision where contract_number = $1 and role in ('CP','CE')";
		rs = await poolRead.query(sql,[contractNumber])
	}

	if(rs.rows.length == 0) {
		return res.status(200).json({
			code : 0,
			msg : "empty data"
		})
	}
	let data = {}
	const statusCode = await utils.getStatus(poolRead,contractNumber).catch(() => {})
	let statusDesc = statusCode
	if(caseStatus.hasOwnProperty(statusCode)) {
		statusDesc = caseStatus[statusCode]
	}
	data.currentCaseType = statusDesc
	data.commentList = rs.rows.map(row => {
		return {
			idComment : row.id,
			caseType : row.step,
			comment : row.deviation_cmt,
			doc : row.mistake_desc,
			createdDate : row.created_date,
			userName : row.assignee
		}
	})
	return res.status(200).json({
		code : 1,
		msg : "get sale support comment successfully",
		data
	})
}

async function updateSSComment(req,res) {
	try {
        const poolWrite = req.poolWrite;
        const { idComment, comment } = req.body;

		if (!idComment || !comment) {
			return res.status(200).json({
				code: 0,
				message: 'Missing params',
			})
		}

        const updatedDate = new Date();
        const sql = `UPDATE loan_manual_decision 
				SET deviation_cmt=$1, updated_date=$2 WHERE id=$3 AND role=$4`;

        await poolWrite.query(sql, [comment, updatedDate, idComment, roleCode.SS]);
        return res.status(200).json({
            code: 1,
            msg: "save successfully",
        });
    } catch (error) {
        return res.status(200).json({
            code: -1,
            msg: error.message,
        });
    }
}

function saveSsUploadDoc(req, res) {
	const poolWrite = req.poolWrite
	const updatedDate = new Date()
	const { contractNumber, docId, comment, userName } = req.body
	const sql = "update loan_contract_document set contract_number = $1,created_by=$2,type_collection=$3,comment=$4,updated_date = $5 where doc_id =$6"
	poolWrite.query(sql, [contractNumber, userName, collectingDocType.SS, comment, updatedDate, docId])
	return res.status(200).json({
		code: 1,
		msg: "save ss upload doc successfully"
	})
}

async function getSsUploadDoc(req, res) {
	const poolRead = req.poolRead
	const { contractNumber } = req.query
	const sql = "select id, doc_type,doc_id,file_name,comment,updated_date,created_by from loan_contract_document where contract_number=$1 and type_collection=$2 and is_deleted = 0 and doc_type != 'RATING' order by creation_time desc"
	const rs = await poolRead.query(sql, [contractNumber, collectingDocType.SS])
	if (rs.rows.length == 0) {
		return res.status(200).json({
			code: 0,
			msg: 'Can not found contractNumber'
		})
	}
	return res.status(200).json({
		code: 1,
		msg: "Get document sucessfully.",
		data: rs.rows.map(row => {
			return {
				idLoanContractDocument: row.id,
				docType: row.doc_type,
				fileName: row.file_name,
				docId: row.doc_id,
				comment: row.comment,
				createdBy: row.created_by,
				createdDate: row.updated_date
			}
		})
	})
}

module.exports = {
    uploadResubmit,
	updateCaseStatus,
	saveSScheckDoc,
	saveSSComment,
	getSSComment,
	updateSSComment,
	saveSsUploadDoc,
	getSsUploadDoc
}