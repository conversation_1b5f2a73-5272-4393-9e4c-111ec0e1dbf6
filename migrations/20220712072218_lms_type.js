/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
    await knex.raw('alter table loan_contract add column lms_type varchar');
    await knex.raw("update loan_contract set lms_type = contract_type ")
    await knex.raw("update loan_contract set lms_type = 'CREDITLINE' where partner_code = 'MIS' and contract_type = 'CASHLOAN' ")
    await knex.raw('alter table kunn drop column lms_type');
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw('alter table loan_contract drop column lms_type');
    await knex.raw('alter table kunn add column lms_type varchar');
};
