/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
 exports.up = async function(knex) {
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','disbursedAmount','disbursed_amount',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','disbursedAmount2','disbursed_amount2',1);"); 
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','disbursedAmount3','disbursed_amount3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','disbursedAmount4','disbursed_amount4',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','beneficiaryName2','beneficiary_name2',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankCode2','bank_code2',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankBranchCode2','bank_branch_code2',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankAccount2','bank_account2',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','beneficiaryName3','beneficiary_name3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankCode3','bank_code3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankBranchCode3','bank_branch_code3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankAccount3','bank_account3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','beneficiaryName4','beneficiary_name4',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankCode4','bank_code4',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankBranchCode4','bank_branch_code4',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','bankAccount4','bank_account4',1);");
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'disbursedAmount'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'disbursedAmount2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'disbursedAmount3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'disbursedAmount4'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'beneficiaryName2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'beneficiaryName3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'beneficiaryName4'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankCode2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankCode3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankCode4'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankBranchCode2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankBranchCode3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankBranchCode4'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankAccount2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankAccount3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'bankAccount4'");
};
