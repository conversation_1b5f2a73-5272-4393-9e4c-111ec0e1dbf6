/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
 exports.up = async function(knex) {
    await knex.raw('ALTER TABLE loan_contract ALTER COLUMN authorized_gender DROP DEFAULT');
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw('ALTER TABLE loan_contract ALTER COLUMN authorized_gender SET DEFAULT 1');
};




