/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {

    await knex.raw('ALTER TABLE loan_contract ADD root_contract_number varchar(100) NULL');
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw('alter table loan_contract drop column root_contract_number');
};
