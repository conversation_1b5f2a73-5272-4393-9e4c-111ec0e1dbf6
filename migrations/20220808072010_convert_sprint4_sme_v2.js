/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
 exports.up = async function(knex) {
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','content','content',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','content2','content2',1);"); 
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','content3','content3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','content4','content4',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','withdrawPurpose2','with_draw_purpose2',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','withdrawPurpose3','with_draw_purpose3',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','withdrawPurpose4','with_draw_purpose4',1);");
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'content'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'content2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'content3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'content4'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'withdrawPurpose2'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'withdrawPurpose3'");
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'withdrawPurpose4'");
};
