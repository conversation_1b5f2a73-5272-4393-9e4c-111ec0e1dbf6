/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    await knex.raw(`INSERT INTO converter (request_type,input_var,input_dtype,input_format,output_var,output_dtype,output_format,created_date,updated_date,is_deleted,owner_id) VALUES
        ('basic','temAddress','','','address_cur','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1),
        ('basic','temWard','','','ward_cur','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1),
        ('basic','temDistrict','','','district_cur','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1),
        ('basic','permanentProvince','','','province_per','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1),
        ('basic','permanentDistrict','','','district_per','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1),
        ('basic','permanentWard','','','ward_per','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1),
        ('basic','permanentAddress','','','address_per','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1)`
    );
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
    await knex.raw(`delete from converter where request_type = 'basic' and input_var in ('temAddress','temWard','temDistrict','permanentProvince',
    'permanentDistrict','permanentWard','permanentAddress')`);
};
