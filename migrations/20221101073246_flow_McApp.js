/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    await knex.raw(`INSERT INTO workflow (partner_code,product_code,workflow_code,status) VALUES ('MCA','','MCA_WF',1);`);
    await knex.raw(`INSERT INTO workflow_detail (workflow_code,current_task,next_task) VALUES
        ('MCA_WF','FULL_LOAN','CHECK_EKYC'),
        ('MCA_WF','CHECK_EKYC','CHECK_DEDUP'),
        ('MCA_WF','CHECK_DEDUP','CHECK_AF1'),
        ('MCA_WF','CHECK_AF1','CHECK_AF2'),
        ('MCA_WF','CHECK_AF2','COMPUTE_OFFER'),
        ('MCA_WF','COMPUTE_OFFER','PUSH_TASK_SS'),
        ('MCA_WF','DEDUP_APPROVE','CHECK_AF1'),
        ('MCA_WF','EKYC_APPROVE','CHECK_DEDUP'),
        ('MCA_WF','EKYC_CANCEL','CALLBACK_REJECT'),
        ('MCA_WF','EKYC_REJECT','CHECK_DEDUP'),
        ('MCA_WF','DEDUP_CANCEL','CALLBACK_CANCEL'),
        ('MCA_WF','SS_APPROVE_DOC','PUSH_TASK_CE'),
        ('MCA_WF','DIBUR_REQUEST','CHECK_ELIGIBLE_KUNN'),
        ('MCA_WF','CHECK_ELIGIBLE_KUNN','CHECK_CIC_KU'),
        ('MCA_WF','CHECK_CIC_KU','CHECK_PCB_KU'),
        ('MCA_WF','CHECK_PCB_KU','PUSH_TASK_SS_KU')`
    );
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
    await knex.raw(`delete from workflow where workflow_code = 'MCA_WF'`);
    await knex.raw(`delete from workflow_detail where workflow_code = 'MCA_WF'`);
};