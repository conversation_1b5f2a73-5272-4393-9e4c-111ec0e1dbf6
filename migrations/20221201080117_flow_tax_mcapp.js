/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
 exports.up = async function (knex) {
    await knex.raw(`INSERT INTO converter (request_type,input_var,input_dtype,input_format,output_var,output_dtype,output_format,created_date,updated_date,is_deleted,owner_id) VALUES
        ('fullLoan','taxId','','','tax_id','','','2022-02-09 16:32:29.546649','2022-02-09 16:32:29.546649',0,1)`
    );
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
    await knex.raw(`delete from converter where request_type = 'fullLoan' and input_var = 'taxId'`);
};
