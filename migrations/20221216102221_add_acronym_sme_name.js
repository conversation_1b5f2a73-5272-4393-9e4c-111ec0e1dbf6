/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
    await knex.raw('alter table kunn add column acronym_sme_name varchar(100)');
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','acronymSmeName','acronym_sme_name',1);");
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw('alter table kunn drop column if exists acronym_sme_name');
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'acronymSmeName'");
};
