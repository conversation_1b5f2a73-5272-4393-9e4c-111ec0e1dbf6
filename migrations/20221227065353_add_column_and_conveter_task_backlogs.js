/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
    await knex.raw('alter table loan_contract add column sme_employment_type1_code varchar(100)');
    await knex.raw('alter table loan_contract add column sme_employment_type4_code varchar(100)');
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('fullLoan','smeEmploymentType1Code','sme_employment_type1_code',1);");
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('fullLoan','smeEmploymentType4Code','sme_employment_type4_code',1);");
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw('alter table loan_contract drop column if exists sme_employment_type1_code');
    await knex.raw('alter table loan_contract drop column if exists sme_employment_type4_code');
    await knex.raw("DELETE FROM converter where request_type = 'fullLoan' and input_var = 'smeEmploymentType1Code'");
    await knex.raw("DELETE FROM converter where request_type = 'fullLoan' and input_var = 'smeEmploymentType4Code'");
};
