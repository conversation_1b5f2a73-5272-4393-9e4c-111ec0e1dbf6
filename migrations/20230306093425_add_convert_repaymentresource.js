/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function(knex) {
    await knex.raw("INSERT INTO converter (request_type,input_var,output_var,owner_id) values ('dibursement','repaymentSources','repayment_source',1);");
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function(knex) {
    await knex.raw("DELETE FROM converter where request_type = 'dibursement' and input_var = 'repaymentSources'");
};
