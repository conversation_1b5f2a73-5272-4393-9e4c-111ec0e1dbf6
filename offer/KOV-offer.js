const common = require("../utils/common")
const loanContractRepo = require("../repositories/loan-contract-repo")
const turnoverRepo = require("../repositories/turnover-repo")
const productService = require("../utils/productService")
const offerRepo = require("../repositories/offer")
const utils = require("../utils/helper")

async function computeKOVOffer(contractNumber) {
    try {
        const data = await Promise.all([loanContractRepo.getLoanContractJoinLoanScore(contractNumber),turnoverRepo.getRealTurnover(contractNumber)])
        const contractData = data[0]
        const turnoverData = data[1]
        // console.log('turnoverdata',turnoverData)
        const productData = await productService.getProductInfo(global.config,contractData.product_code)
        const bussinessLegalOffer = offerAccordingBussinessLegal(contractData.business_legal)
        const netIncomeOffer = offerAccordingNetIncome(contractNumber,contractData,turnoverData,productData)
        const vongquayvonOffer = maxLoanArcodingCapital(contractNumber,contractData,turnoverData)
        const timeDurationOffer = offerAccordingTimeDuration(contractNumber,contractData)
        let maxLoan = Math.max(...[Math.min(...[netIncomeOffer.offer, vongquayvonOffer, timeDurationOffer, contractData.request_amt,bussinessLegalOffer]),0])
        maxLoan = parseInt(maxLoan / 1000000) * 1000000
        await Promise.all([offerRepo.saveOfferV2(contractNumber,maxLoan,parseFloat(productData.productVar[0].intRate / 100),productData.productVar[0].defTenor,'STANDARD',contractData.product_code,contractData.request_amt,contractData.request_tenor),
                        offerRepo.saveOfferKov(contractNumber,netIncomeOffer.offer,vongquayvonOffer,timeDurationOffer),
                        offerRepo.updateMainScore(contractNumber,'max_loan',maxLoan)])
        
        return {
            maxLoan,
            netIncome : netIncomeOffer.netIncome
        }
    }
    catch(err) {
        console.log(err)
        common.log(`compute KOV offer error : ${err.message}`,contractNumber)
    }
}

function offerAccordingBussinessLegal(bussinessLegal) {
    if(bussinessLegal.toLowerCase().includes('không')) {
        return 300000000
    }
    else {
        return 500000000
    }
}

function offerAccordingNetIncome(contractNumber,contractData,turnoverData,productData) {
    const netIncome = computeNetIncome(contractNumber,contractData,turnoverData)
    if(utils.isNullOrEmpty(contractData.di_before_ce)) {
        offerRepo.updateMainScore(contractNumber,'di_before_ce',netIncome)
    }
    const c = global.config.offerConfig.c
    return {
        offer : netIncome / (parseFloat(productData.productVar[0].intRate) / 100 / 12 * c),
        netIncome
    }
}

function offerAccordingTimeDuration(contractNumber,contractData) {
    const offerConfig = global.config.offerConfig
    const timeDuration = contractData.time_duration
    if(timeDuration >= offerConfig.time_duration) {
        return offerConfig.max_offer_by_duration
    }
    else  {
        return offerConfig.min_offer_by_duration
    }
}

function maxLoanArcodingCapital(contractNumber,contractData,turnoverData) {
    const otherCapital = contractData.other_capital
    const selfFinancing = parseFloat(contractData.self_financing)
    const tax = contractData.latest_tax_payment
    const offerConfig = global.config.offerConfig
    const mothlyER = computeMonthlyEstimatedRevenue(contractNumber,turnoverData)
    const revenue = computeRevunue(mothlyER,offerConfig.doanh_thu_n_1)
    const lnst = computeLNST(revenue,offerConfig.a)
    const capitalNeed = computeCapitalNeed(revenue,tax,lnst,offerConfig.d,offerConfig.e)
    const selfFinancingCritira = computeSelfFinancing(selfFinancing,capitalNeed)
    const maxLoan = capitalNeed.capitalNeed_n1 - selfFinancingCritira.selfFinancing_n1 - otherCapital - lnst.lnst_n* offerConfig.b
    return maxLoan
}

function computeMonthlyEstimatedRevenue(contractNumber,turnoverData) {
    const offerConfig = global.config.offerConfig
    let mappingMonthToConfig = {
        1 : "revenue_rate_n_1",
        2 : "revenue_rate_n_2",
        3 : "revenue_rate_n_3",
        4 : "revenue_rate_n_4",
        5 : "revenue_rate_n_5",
        6 : "revenue_rate_n_6",
        7 : "revenue_rate_n_7",
        8 : "revenue_rate_n_8",
        9 : "revenue_rate_n_9",
        10 : "revenue_rate_n_10",
        11 : "revenue_rate_n_11",
        12 : "revenue_rate_n_12",
    }
    let monthlyER = 0;
    turnoverData.forEach(month => {
        
        if(month.month_of_info != 0) {
            monthlyER += parseFloat(month.value_of_month) * parseFloat(offerConfig[mappingMonthToConfig[month.month_of_info]])
        }
    })
    return monthlyER
}

function computeRevunue(estimatedRevenue,constant) {
    return {
        revenue_n : estimatedRevenue * 12,
        revenue_n1 : estimatedRevenue * 12 * constant
    }
}

function computeLNST(revenue,a) {
    return {
        lnst_n : revenue.revenue_n * a,
        lnst_n1 : revenue.revenue_n1 * a
    }
} 

function computeCapitalNeed(revenue,tax,lnst,d,e) {
    return {
        capitalNeed_n : (revenue.revenue_n - tax - lnst.lnst_n) / d,
        capitalNeed_n1 : (revenue.revenue_n1 - tax - lnst.lnst_n1) / e
    }
}

function computeSelfFinancing(selfFinancing,capitalNeed) {
    return {
        selfFinancing_n : selfFinancing,
        selfFinancing_n1 : Math.max(capitalNeed.capitalNeed_n,selfFinancing)
    }
}

function computeNetIncome(contractNumber,contractData,turnoverData) {
    const IIMX = computeIByMaxInstallMent(contractNumber,contractData,turnoverData)
    const monthlyExpenses = computeExpenses(contractData)
    offerRepo.updateMainScore(contractNumber,'monthly_expenses',monthlyExpenses)
    const pcbExpenses = computePCB(contractNumber,contractData) 
    offerRepo.updateMainScore(contractNumber,'pcb_payment',pcbExpenses)
    return IIMX - monthlyExpenses - Math.max(pcbExpenses,contractData.cic_payment) - contractData.cic_payment_2
}

function getExpensesWithProvince(proCode) {
	if (proCode === '79' || proCode === '48' || proCode === '01' ) {
		return 6000000
	}
	return 4000000
}

function computeExpenses(contractData) {
    const proCode = contractData.province_cur
    const monthlyExpenses = contractData.m_household_expenses
    const numDepenPer = contractData.num_of_dependants
	const proExpenses = getExpensesWithProvince(proCode)
	let E1 = Math.max(monthlyExpenses,proExpenses)

	let depenCost = 0
	for (let i = 0; i < numDepenPer; i ++) {
		if (i == 0) {
			depenCost += 0.8 * E1
		}
		else {
			depenCost += 0.6 * E1
		}
	}
	const totalExpenses = E1 + depenCost
	return totalExpenses
}

function computeIByMaxInstallMent(contractNumber,contractData,turnoverData) {
    const offerConfig = global.config.offerConfig
    const monthlyER = parseFloat(computeMonthlyEstimatedRevenue(contractNumber,turnoverData))
    const timeDuration = contractData.time_duration
    let maxInstallMent;
    if(timeDuration <= 2) {
		return monthlyER
	}
	else if(timeDuration >2 && timeDuration <= 6) {
		maxInstallMent = monthlyER * offerConfig.max_installment_3_6_rate
	}
	else if(timeDuration > 6 && timeDuration <= 12 ) {
		maxInstallMent = monthlyER * offerConfig.max_installment_6_12_rate
	}
	else  {
        maxInstallMent = monthlyER * offerConfig.max_installment_12_rate  
	}
    const IIMX = maxInstallMent * offerConfig.I_rate
    return IIMX
}

function computePCB(contractNumber,contractData) {
    const pcbMonthlyPayment = parseInt(contractData.pcb_score1)
	const pcbCardDebt = contractData.pcb_score2
	const pcbCardPay = 0.05 * parseInt(pcbCardDebt)
	const pcbTotal = pcbMonthlyPayment + pcbCardPay
	return pcbTotal
}

module.exports = {
    computeKOVOffer,
    computeNetIncome
}