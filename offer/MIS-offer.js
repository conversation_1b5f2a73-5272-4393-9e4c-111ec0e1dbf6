const common = require("../utils/common")
const loanContractRepo = require("../repositories/loan-contract-repo")
const { min } = require("underscore")

async function saveMISOffer(req, res) {
    try {
        const payLoad = req.body
        const poolWrite = global.poolWrite
        let offerQuery = ''
        if (payLoad.flag == 'HM') {
            offerQuery = 'total_short_term_loan_limit = $2 where contract_number = $3 and registration_number = $4 and sme_tax_id = $5'
        } else if (payLoad.flag == 'VM') {
            offerQuery = 'approval_tenor = $2 where contract_number = $3 and registration_number = $4 and sme_tax_id = $5'
        }
        await saveLoanMainScore(payLoad.contractNumber, payLoad.loanData, payLoad.flag)
        const queryMIS = `update loan_contract set approval_amt = $1, ${offerQuery}`
        const loanData = await loanContractRepo.getLoanContract(payLoad.contractNumber)
        const requestAmt = parseFloat(loanData?.request_amt)
        const maxLoanSystem = parseFloat(payLoad?.maxLoan)||0
        const maxLoan = min([requestAmt,maxLoanSystem])
        let saveResult = await poolWrite.query(queryMIS,
            [
                maxLoan,
                payLoad.flag == 'HM' ? payLoad.totalShortTermLoanLimit : payLoad.tenor,
                payLoad.contractNumber,
                payLoad.registrationNumber,
                payLoad.taxId
            ]
        )
        if (saveResult.rowCount == 0) {
            return res.status(200).json({ code: -1, message: "Lưu thông tin không thành công!" })
        }
        return res.status(200).json({ code: 0, message: "Lưu thông tin thành công!" })
    } catch (error) {
        common.log('saveMISOffer error: ', error.message, 'error')
        return res.status(500).json({ code: -1, message: "Lưu thông tin không thành công!" })
    }
}

async function saveLoanMainScore(contractNumber, payLoad, flag) {
    try {
        const poolWrite = global.poolWrite
        let queryMainScore = ''
        if(flag == 'HM'){
            queryMainScore = `update loan_main_score set percent_compared_last_year = $1, working_capital_turnover = $2, loan_sector = $3 where contract_number = $4`
        }else if(flag == 'VM'){
            queryMainScore = `update loan_main_score set medium_term_debt_amount_u = $1, long_term_debt_amount_u = $2, medium_term_debt_amount_m = $3, long_term_debt_amount_m = $4, percent_compared_last_year = $5, loan_amount = $6, tenor_max_loan = $7, rate_max_loan = $8 where contract_number = $9`
        }
        let paramsHM = [
            payLoad.percentComparedLastYear,
            payLoad.workingCapitalTurnover,
            payLoad.loanSector,
            contractNumber
        ]
        let paramsVM = [
            payLoad.mediumTermDebtAmoutU,
            payLoad.longTermDebtAmoutU,
            payLoad.mediumTermDebtAmoutM,
            payLoad.longTermDebtAmoutM,
            payLoad.percentComparedLastYear,
            payLoad.loanAmount,
            payLoad.tenorMaxLoan,
            payLoad.rateMaxLoan,
            contractNumber
        ]
        let saveResult = await poolWrite.query(queryMainScore, flag == 'HM' ? paramsHM : paramsVM
        )
        if (saveResult.rowCount == 0) {
            return res.status(200).json(
                { code: -1, message: "Lưu thông tin không thành công!" }
            )
        }
    } catch (error) {
        common.log('saveLoanMainScore error: ', error.message, 'error')
    }
}

module.exports = {
    saveMISOffer
}