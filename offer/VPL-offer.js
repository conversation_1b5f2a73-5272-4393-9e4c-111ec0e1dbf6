const turnoverRepo = require("../repositories/turnover-repo")
const offerRepo = require("../repositories/offer")
const offerService = require("../services/offer-service")
const productService = require("../utils/productService")
const callbackService = require("../services/callback-service")
const {OFFER_CONFIG,PARTNER_CODE} = require("../const/definition")
const common = require("../utils/common")
const { CALLBACK_STAUS } = require("../const/caseStatus")

async function computeVPLOffer(contractNumber) {
    try {
        const data = await Promise.all([turnoverRepo.getTurnOver(contractNumber),offerRepo.getDataToCalculateOffer(contractNumber)])
        const turnoverData = data[0]
        const contractData = data[1]
        let totalTurnover = 0;
        let totalCP = 0;
        let numMonth = 0
        for (let key in turnoverData) {
            numMonth += 1;
            const monthData = turnoverData[key]
            totalCP += monthData.TOTAL_CP
            
            totalTurnover += (monthData.DT_PINCODE_VT_N + monthData.DT_TT_VT_N + monthData.DT_GD_VINA_N + monthData.DT_GD_MOBI_N + monthData.DT_GD_BEELINE_N + monthData.DT_GD_VNMOBILE_N + monthData.DT_GAME_RECHARGE_N + monthData.DT_GAME_KHAC_N)
        }
        totalTurnover = Math.floor(totalTurnover / 1000000) * 1000000

        const avgTotalTurnOver = totalCP / numMonth
        let maxAnnuity;
        if(contractData.time_duration >= 12) {
            maxAnnuity = avgTotalTurnOver * 2.5
        }
        else{
            maxAnnuity = avgTotalTurnOver * 2
        }
        let score = {
            averageTurnover : avgTotalTurnOver,
            estimatedRevenue : totalTurnover
        }
        await offerRepo.updateScore2(contractNumber,score)
        const offerResult = await offerService.getOffer(contractNumber,contractData.request_amt,contractData.request_tenor,contractData.risk_grade,contractData.empl_type,contractData.birth_date,contractData.partner_code,contractData.product_code,maxAnnuity,1)
        
        if(offerResult.code == 1 || offerResult.code == 3) {
            const offer1 = parseFloat(offerResult.data[0].requestedAmount)
            let offerTenor = parseFloat(offerResult.data[0].tenor)
            const finalOffer = Math.min(offer1,totalTurnover)
            if(finalOffer < OFFER_CONFIG.MIN_VPL_OFFER) {
                console.log(`${contractNumber} | final offer ${finalOffer} < ${OFFER_CONFIG.MIN_VPL_OFFER} : CANCEL`)
                callbackService.callbackPartner(contractNumber,PARTNER_CODE.VPL,CALLBACK_STAUS.CANCELLED)
                return false   
            }
            if(totalTurnover == finalOffer) {
                offerTenor = contractData.request_tenor
            }
            const annuity = await productService.getMonthlyInstallment(finalOffer,contractData.request_int_rate,offerTenor)
            await Promise.all([offerRepo.saveOfferV3(contractNumber,finalOffer,offerTenor,annuity),offerRepo.saveVPLScore(contractNumber,offerResult.data[0].requestedAmount,avgTotalTurnOver,totalTurnover,maxAnnuity),offerRepo.updateMainScore(contractNumber,'max_loan',finalOffer)])
            return true
        }
        else {
            console.log(`${contractNumber} | empty offer : CANCEL`)
            callbackService.callbackPartner(contractNumber,PARTNER_CODE.VPL,CALLBACK_STAUS.CANCELLED)
            return false
        }
    }
    catch(err) {
        console.log(err)
        common.log(`compute offer error :` ,contractNumber)
        return
    }
}

module.exports = {
    computeVPLOffer
}