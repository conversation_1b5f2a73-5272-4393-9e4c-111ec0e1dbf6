const common = require("../utils/common")
const loanContractRepo = require("../repositories/loan-contract-repo")
const turnoverRepo = require("../repositories/turnover-repo")
const productService = require("../utils/productService")
const offerRepo = require("../repositories/offer")
const utils = require("../utils/helper")
const status = require("../const/caseStatus")
const dataEntryRepo = require("../repositories/data-entry-repo")
const {serviceEndpoint} = require("../const/config")
const dateHelper = require("../utils/dateHelper")
const defind = require("../const/definition")

async function computeVSKOffer(contractNumber,type=0,di=0){//type=0: return for workflow, type=1: return offer data
    try {
        const turnOver = await dataEntryRepo.getDataEntry(global.poolRead,contractNumber)
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        let turnover1=0,turnover2=0,turnover3=0,turnover4=0,turnover5=0,turnover6=0
        if(turnOver != null){
            turnOver.rows.forEach(e => {
                if(e.info_type == 'turnover'){
                    switch (e.month_of_info) {
                        case 1:
                            turnover1 = e.value_of_month
                            break;
                        case 2:
                            turnover2 = e.value_of_month
                            break;
                        case 3:
                            turnover3 = e.value_of_month
                            break;
                        case 4:
                            turnover4 = e.value_of_month
                            break;
                        case 5:
                            turnover5 = e.value_of_month
                            break;
                        case 6:
                            turnover6 = e.value_of_month
                            break;
                    }
                }
            });
        }
        
        let offerBody = {
            "contractNumber": contractNumber,
            "partnerCode": "VSK",
            "custId": null,
            "offerData": {
                "di": di,
                "tax": contractData.latest_tax_payment || 0,
                "turnover1": turnover1,
                "turnover2": turnover2,
                "turnover3": turnover3,
                "turnover4": turnover4,
                "turnover5": turnover5,
                "turnover6": turnover6,
                "timeDuration": Math.max(contractData.name_of_app_duration, contractData.name_of_tool_duration, contractData.ecommerce_duration) || 0,
                "scheme": contractData.product_code=='MCBAS_STANDARD'?'S':contractData.product_code=='MCBAS_VIP'?'V':contractData.product_code=='MCBAS_PREMIUM'?'P':'',
                "legalStatus": contractData.business_legal,
                "businessType": contractData.type_trading == 'ON'?'ON':contractData.type_trading == 'OF'?'OFF':contractData.type_trading == 'BO'?'BOTH':'',
                "timeRegistration": contractData.first_registration_date,
                "monthRegistration": null,
                "requestAmount": contractData.request_amt ? Number.parseFloat(contractData.request_amt) : 0,
                "otherCapital": contractData.other_capital ? Number.parseFloat(contractData.other_capital) : 0,
                "capital": contractData.capital_need ? Number.parseFloat(contractData.capital_need) : 0,
                "loanType": contractData.contract_type == 'CREDITLINE'?'CREDIT':contractData.contract_type == 'CASHLOAN'?'CASH':''
            }
        }
        console.log('offerBody: ', JSON.stringify(offerBody));
        const url = config.basic.decisionsV02[config.env] + serviceEndpoint.VSK.offer
        const headers = utils.initHeaders('DECISION','OFFER');
        const offerRs = await common.postApiV2(url,offerBody,headers)
        console.log("offerRs: ", JSON.stringify(offerRs));
        if(type==0){
            if(contractData.contract_type == 'CREDITLINE') offerRepo.saveOfferV3(contractNumber,parseInt(offerRs.data.data.offer/1000000)*1000000,contractData.request_tenor,offerRs.data.data.anuity)
            return true
        }
        else if(type==1){
            if(offerRs){
                return offerRs.data
            }
            return {}
        }
        
    } catch (error) {
        common.log(` error : ${error.message}`)
        if(type==0){
            return false
        }
        else if(type==1){
            return {}
        }
        // return res.status(500).json(res.body = {
        //     code: 1,
        //     message: 'Service error'
        // })
    }
}

async function computeEstimatedRevenue(contractNumber,turnOver){//type=0: return for workflow, type=1: return offer data
    try {
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        
        let offerBody = {
            "contractNumber": contractNumber,
            "partnerCode": "VSK",
            "custId": null,
            "offerData": {
                "di": 0,
                "tax": contractData.latest_tax_payment || 0,
                "turnover1": turnOver['0'].amount || 0,
                "turnover2": turnOver['1'].amount || 0,
                "turnover3": turnOver['2'].amount || 0,
                "turnover4": turnOver['3'].amount || 0,
                "turnover5": turnOver['4'].amount || 0,
                "turnover6": turnOver['5'].amount || 0,
                "timeDuration": contractData.name_of_app_duration?contractData.name_of_app_duration:contractData.name_of_tool_duration?contractData.name_of_tool_duration:0,
                "scheme": contractData.product_code=='MCBAS_STANDARD'?'S':contractData.product_code=='MCBAS_VIP'?'V':contractData.product_code=='MCBAS_PREMIUM'?'P':'',
                "legalStatus": contractData.business_legal,
                "businessType": contractData.type_trading == 'ON'?'ON':contractData.type_trading == 'OF'?'OFF':contractData.type_trading == 'BO'?'BOTH':'',
                "timeRegistration": contractData.first_registration_date,
                "monthRegistration": null,
                "requestAmount": contractData.request_amt ? Number.parseFloat(contractData.request_amt) : 0,
                "otherCapital": contractData.other_capital ? Number.parseFloat(contractData.other_capital) : 0,
                "capital": contractData.capital_need ? Number.parseFloat(contractData.capital_need) : 0,
                "loanType": contractData.contract_type == 'CREDITLINE'?'CREDIT':contractData.contract_type == 'CASHLOAN'?'CASH':''
            }
        }
        const url = config.basic.decisionsV02[config.env] + serviceEndpoint.VSK.offer
        const headers = utils.initHeaders('DECISION','OFFER');
        const offerRs = await common.postApiV2(url,offerBody,headers)
        if(offerRs){
            return offerRs.data
        }
        return {} 
    } catch (error) {
        common.log(` error : ${error.message}`)
        return {}
    }
}

module.exports = {
    computeVSKOffer,
    computeEstimatedRevenue
}