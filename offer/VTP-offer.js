const loanContractRepo = require("../repositories/loan-contract-repo")
const offerRepo = require("../repositories/offer")
const productService = require("../utils/productService")
const common = require("../utils/common")

async function computeVTPOffer(contractNumber) {
    try {
        const contractData = await loanContractRepo.getLoanContractJoinLoanScore(contractNumber)
        const maxLoan = contractData.request_amt
        const annuity = await productService.getMonthlyInstallment(maxLoan,contractData.request_tenor,contractData.request_tenor)
        await offerRepo.deleteOfferByContract(contractNumber)
        await offerRepo.saveOfferV3(contractNumber,maxLoan,contractData.request_tenor,annuity)
        return true
    }
    catch(err) {
        common.log(`compute VTP offer error : ${err.message}`,contractNumber)
        return false
    }
}

module.exports = {
    computeVTPOffer
}