const common = require("../utils/common")
const loanContractRepo = require("../repositories/loan-contract-repo")
const offerRepo = require("../repositories/offer")
const utils = require("../utils/helper")
const { serviceEndpoint } = require("../const/config")
const { getDataByContractNumber } = require("../repositories/loan-atribute-repo")
const { PARTNER_CODE, PRODUCT_CODE } = require("../const/definition")
const loanAttributeRepo = require("../repositories/loan-atribute-repo")
const turnOverRepo = require("../repositories/turnover-repo")
const { getProductByCodeApi } = require("../apis/product-api")


async function computeOffer(contractNumber, partnerCode, type = 0, diBeforeCE = 0, diAfterCE = 0) {//type=0: return for workflow, type=1: return offer data
    try {
        const data = await Promise.all([
            loanContractRepo.getLoanContract(contractNumber),
            loanAttributeRepo.getDataByContractNumber(contractNumber),
            turnOverRepo.getTurnOver(contractNumber)
        ])
        const contractData = data[0]
        const loanAttributeData = data[1]
        const turnOverData = data[2]
        // const isCarProduct = contractData?.product_code.includes("CAR") ? true : false;
        // let turnover1 = 0, turnover2 = 0, turnover3 = 0, turnover4 = 0, turnover5 = 0, turnover6 = 0;
        let offerBody = {
            "contractNumber": contractNumber,
            "partnerCode": partnerCode,
            "custId": null,
            "idNumber": contractData?.id_number || null,
            "taxId": contractData?.tax_id || null,
            "temProvince": contractData?.province_cur || null,
            "productScheme": utils.detectSuperAppSchema(contractData.product_code) || null,
            "slAdvanceContractType": "HM",
            "offerData": {
                "assetsPrice": loanAttributeData?.assetsPrice || null,
                "tenor": contractData?.approval_int_rate || contractData?.request_int_rate || null,
                "interestRate": contractData?.approval_int_rate || contractData?.request_int_rate || null,
                "diBeforeCE": diBeforeCE,
                "diAfterCE": diAfterCE,
                "tax": contractData?.latest_tax_payment || 0,
                "turnover1": turnOverData['0']?.turnover || 0,
                "turnover2": turnOverData['1']?.turnover || 0,
                "turnover3": turnOverData['2']?.turnover || 0,
                "turnover4": turnOverData['3']?.turnover || 0,
                "turnover5": turnOverData['4']?.turnover || 0,
                "turnover6": turnOverData['5']?.turnover || 0,
                "timeDuration": Math.max(contractData.name_of_app_duration ?? 0, contractData.name_of_tool_duration ?? 0, contractData.ecommerce_duration ?? 0) ?? 0,
                // "scheme": contractData.product_code == 'MCBAS_STANDARD' ? 'S' : contractData.product_code == 'MCBAS_VIP' ? 'V' : contractData.product_code == 'MCBAS_PREMIUM' ? 'P' : '',
                "legalStatus": contractData.business_legal ?? null,
                "businessType": contractData.type_trading == 'ON' ? 'ON' : contractData.type_trading == 'OF' ? 'OFF' : contractData.type_trading == 'BO' ? 'BOTH' : '',
                "timeRegistration": contractData.first_registration_date ?? null,
                "monthRegistration": null,
                "requestAmount": contractData.request_amt ? Number.parseFloat(contractData.request_amt) : 0,
                "otherCapital": contractData.other_capital ? Number.parseFloat(contractData.other_capital) : 0,
                "capital": contractData.capital_need ? Number.parseFloat(contractData.capital_need) : 0,
                "loanType": contractData.contract_type == 'CREDITLINE' ? 'CREDIT' : contractData.contract_type == 'CASHLOAN' ? 'CASH' : ''
            }
        }
        if (partnerCode == PARTNER_CODE.SMA) {
            offerBody.temProvince = contractData?.province_cur
            offerBody.assetsType = loanAttributeData?.assetsType || ""
            // offerBody.paymentMethod = loanAttributeData?.paymentMethod || ""
            offerBody.productScheme = contractData?.product_code || ""
            offerBody.tenor = contractData?.approval_tenor || contractData?.request_tenor || ""
            offerBody.requestAmount = contractData?.approval_amt || contractData?.request_amt || ""
            offerBody.interestRate = contractData?.approval_int_rate || contractData?.request_int_rate || ""
        }
        let url = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].offer;
        // if (isCarProduct) {
        //     const loanAttributeData = await getDataByContractNumber(contractNumber)
        //     const carAmount = loanAttributeData?.carAmount || 0
        //     offerBody = {
        //         "requestedAmount": contractData?.request_amt,
        //         "requestedTenor": contractData?.request_tenor,
        //         "riskGrade": null,
        //         "employmentType": "E",
        //         "currency": "VND",
        //         "customerAge": utils.caculateAge(contractData.birth_date),
        //         "partnerCode": partnerCode,
        //         "contractType": "CREDIT_LINE",
        //         "productCode": contractData.product_code,
        //         "chanel": "LOS",
        //         "requestedAnnuity": diAfterCE != 0 ? diAfterCE : diBeforeCE,
        //         "carAmount": carAmount
        //     }
        //     url = global.config.basic.product[config.env] + global.config.data.productService.getOfferCash
        // }
        console.log('offerBody: ', JSON.stringify(offerBody));
        const headers = utils.initHeaders('DECISION', 'OFFER');
        const offerRs = await common.postApiV2(url, offerBody, headers);
        console.log(`offerRs | ${partnerCode}: `, JSON.stringify(offerRs));
        if (type == 0) {
            if (contractData.contract_type == 'CREDITLINE'){
                if (partnerCode == PARTNER_CODE.SMA) 
                    offerRepo.saveOfferV3(contractNumber, parseInt(offerRs?.data?.data?.offer / 1000000) * 1000000, contractData.approval_tenor || contractData.request_tenor || 0, offerRs?.data?.data?.anuity || 0)
                else 
                    offerRepo.saveOfferV3(contractNumber, parseInt(offerRs?.data?.data[0]?.requestedAmount / 1000000) * 1000000 || 0, contractData.request_tenor, 0)
            } 
            // if (contractData.contract_type == 'CREDITLINE') offerRepo.saveOfferV3(contractNumber, 100000000, contractData.request_tenor, 0)
            return true
        }
        else if (type == 1) {
            if (offerRs) {
                return offerRs.data
            }
            return {}
        }

    } catch (error) {
        common.log(`compute offer error | ${partnerCode}: ${error.message}`)
    }
}

const computeOfferFinv = async (contractNumber, loan) => {
  try {
    //todo: handle
    if (!loan) {
      loan = await loanContractRepo.getLoanContract(contractNumber);
    }
    if (!loan) {
      throw Error(`[computeOfferFinv] cannot get contract ${contractNumber}`);
    }
    loan = utils.snakeToCamel(loan);
    const productInfo = await  getProductByCodeApi(loan.productCode);
    loan.productInfo = productInfo;
    const limitAmountMax = parseInt(loan.productInfo?.maxAmount || (50000000)) ;
    let avgRevenue = 0;
    let limitAmount = 0;
    const businessData = JSON.parse(loan.businessData || '{}');
    switch(loan.productCode)
    {
        case  PRODUCT_CODE.FINV.LIMIT.HKD_FINVIET_HM_STANDARD:{
            avgRevenue = parseInt( Number(businessData.turnover3M)/3)
            limitAmount =Math.min(limitAmountMax, avgRevenue * 1.2)
            break;
        }
        case PRODUCT_CODE.FINV.LIMIT.HKD_FINVIET_HM_SILVER :{
            avgRevenue = parseInt( Number(businessData.turnover6M)/6)
            limitAmount =Math.min(limitAmountMax, avgRevenue * 1.5)
            break;
        }
        case PRODUCT_CODE.FINV.LIMIT.HKD_FINVIET_HM_GOLD  :{
            avgRevenue = parseInt( Number(businessData.turnover3M)/3)
            limitAmount =Math.min(limitAmountMax, avgRevenue * 1.7)
            break;
        }
        case PRODUCT_CODE.FINV.LIMIT.HKD_FINVIET_HM_DIAMOND :{
            avgRevenue = parseInt( Number(businessData.turnover6M)/6)
            limitAmount =Math.min(limitAmountMax, avgRevenue * 2)
            break;
        }
    }
    await loanContractRepo.updateLoanContract({
        contract_number: contractNumber,
        approval_amt: limitAmount,
        updated_date: new Date()
    });
    return true;
  } catch (error) {
    console.log(
      `[computeOfferFinv] contractNumber ${contractNumber} error ${error.message}`
    );
    return false;
  }
};

module.exports = {
    computeOffer,
    computeOfferFinv
}