{"name": "los", "version": "1.0.0", "description": "los service", "main": "app.js", "scripts": {"start": "NODE_ENV=dev node app.js", "uat": "set NODE_ENV=uat&& nodemon ./app.js", "uat-linux": "NODE_ENV=uat nodemon index.js", "dev": "cross-env NODE_ENV=dev nodemon app.js"}, "author": "", "license": "ISC", "dependencies": {"aws-sdk": "^2.899.0", "axios": "^1.12.0", "body-parser": "^1.20.3", "camelcase-keys": "^6.2.2", "cross-spawn": "^7.0.5", "dateformat": "^4.5.1", "docxtemplater": "^3.31.5", "dotenv": "^9.0.2", "exceljs": "^4.4.0", "express": "^4.17.1", "express-validator": "^6.13.0", "fast-xml-parser": "^4.4.1", "form-data": "^4.0.4", "formula-pmt": "^1.0.1", "fs": "0.0.1-security", "joi": "^17.13.3", "jose": "^5.6.3", "libreoffice-convert": "^1.3.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.29.1", "moment-timezone": "^0.5.38", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-jose": "^2.2.0", "nodemon": "^3.0.1", "number-to-text-vietnamese": "^1.0.9", "numeral": "^2.0.6", "object-sizeof": "^1.6.3", "openpgp": "^6.1.1", "path": "^0.12.7", "path-to-regexp": "^0.1.12", "pdf-parse": "^1.1.1", "pg": "^8.6.0", "pg-promise": "^11.5.5", "pizzip": "^3.0.6", "querystring": "^0.2.1", "underscore": "^1.13.2", "uuid": "^8.3.2", "validatorjs": "^3.22.1", "vn-num2words": "^1.0.4"}, "devDependencies": {"cross-env": "^7.0.3"}}