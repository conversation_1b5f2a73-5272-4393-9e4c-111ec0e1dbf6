const common = require("../utils/common")
const helper = require("../utils/helper")
const moment = require('moment-timezone')
const { DATE_FORMAT } = require("../const/definition")
moment().tz('Asia/Ho_Chi_Minh').format()

async function saveDataEntry(poolWrite,contractNumber,type,data) {
    try {
        const sqlInsert = "insert into loan_turnover_transaction (contract_number,month_of_info,info_type,value_of_month) values ($1,$2,$3,$4)"
        if(Array.isArray(data)&&data.length){
            let updated_date = moment().format(DATE_FORMAT.YYYYMMDD_HHmmss)
            const sqlGet = "select * from loan_turnover_transaction where info_type in ('sme_profit','sme_turnover') and contract_number=$1"
            const sqlUpdate = "update loan_turnover_transaction set value_of_month=$1, updated_date=$5 where info_type = $2 and month_of_info = $3 and contract_number = $4"
            const turnoverProfitDatas = (await poolWrite.query(sqlGet,[contractNumber])).rows
            let smeProfitMonthCur=[],smeTurnoverMonthCur=[]
            for (const tpd of turnoverProfitDatas) {
                if(tpd.info_type=='sme_profit') smeProfitMonthCur.push(tpd.month_of_info)
                if(tpd.info_type=='sme_turnover') smeTurnoverMonthCur.push(tpd.month_of_info)
            }
            let promiseList = []
            for (const d of data) {
                if(smeProfitMonthCur.includes(parseInt(d.month_of_info))&&d.info_type=='sme_profit'){
                    promiseList.push(poolWrite.query(sqlUpdate,[d.value_of_month,'sme_profit',parseInt(d.month_of_info),contractNumber,updated_date]))
                }else if(!smeProfitMonthCur.includes(parseInt(d.month_of_info))&&d.info_type=='sme_profit'){
                    promiseList.push(poolWrite.query(sqlInsert,[contractNumber,parseInt(d.month_of_info),'sme_profit',d.value_of_month]))
                }
                else if(smeTurnoverMonthCur.includes(parseInt(d.month_of_info))&&d.info_type=='sme_turnover'){
                    promiseList.push(poolWrite.query(sqlUpdate,[d.value_of_month,'sme_turnover',parseInt(d.month_of_info),contractNumber,updated_date]))
                }else if(!smeTurnoverMonthCur.includes(parseInt(d.month_of_info))&&d.info_type=='sme_turnover'){
                    promiseList.push(poolWrite.query(sqlInsert,[contractNumber,parseInt(d.month_of_info),'sme_turnover',d.value_of_month]))
                }
            }
            await Promise.all(promiseList)
        }else{
            return await poolWrite.query(sqlInsert,[contractNumber,data.month_of_info,type,data.value_of_month])
        }
    }
    catch(err) {
        common.log(`save data entry error : ${err.message}`)
    }
}

async function saveDataEntryV2(poolWrite,contractNumber,type,data) {
    try {
        const sql = "insert into loan_turnover_transaction (contract_number,month_of_info,info_type,value_of_month_string) values ($1,$2,$3,$4)"
        return await poolWrite.query(sql,[contractNumber,data.month_of_info,type,data.value_of_month])
    }
    catch(err) {
        common.log(`save data entry error : ${err.message}`)
    }
}

async function updateDataEntry(poolWrite,contractNumber,type,data,updated_date) {
    try {
        const sql = "update loan_turnover_transaction set value_of_month=$1, updated_date=$5 where info_type = $2 and month_of_info = $3 and contract_number = $4"
        if(Array.isArray(data)&&data.length){
            let promiseList = []
            for (const d of data) {
                promiseList.push(poolWrite.query(sql,[d.value_of_month,type,d.month_of_info,contractNumber,updated_date]))
            }
            await Promise.all(promiseList)
        }else{
            return await poolWrite.query(sql,[data.value_of_month,type,data.month_of_info,contractNumber,updated_date])
        }
    } catch (error) {
        common.log(`update data entry error : ${error.message}`)
    }
}

async function updateDataEntryV2(poolWrite,contractNumber,type,data,updated_date) {
    try {
        const sql = "update loan_turnover_transaction set value_of_month_string=$1, updated_date=$5 where info_type = $2 and month_of_info = $3 and contract_number = $4"
        return await poolWrite.query(sql,[data.value_of_month,type,data.month_of_info,contractNumber,updated_date])
    } catch (error) {
        common.log(`update data entry error : ${error.message}`)
    }
}

async function getDataEntry(poolRead,contractNumber) {
    try {
        let sql = "select info_type, month_of_info, value_of_month::float, value_of_month_string from loan_turnover_transaction where 1 = 1";
        let index = 1
        
        if(!helper.isNullOrEmpty(contractNumber)){
            sql = sql + " and contract_number = $"+index+" order by month_of_info, info_type";
            index += 1
        }
        return index == 1 ? null : await poolRead.query(sql,[contractNumber]);
    }
    catch(err) {
        common.log(`get data entry error : ${err.message}`)
    }
}

async function getHistory(poolRead,contractNumber) {
    try {
        let sql = `
            select updated_field, old_value, new_value, updated_by, updated_date, count_assignment from update_log_hist where 1 = 1
        `;
        let index = 1
        
        if(contractNumber != undefined){
            sql = sql + " and contract_number = $"+index+" order by count_assignment";
            index += 1
        }
        if(index==1) return {}
        return await poolRead.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`get history error : ${err.message}`)
    }
}

async function getMaxCount(poolRead,contractNumber) {
    try {
        let sql = "select count_assignment from update_log_hist where contract_number = $1 order by count_assignment desc limit 1";
        return await poolRead.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`get max count error : ${err.message}`)
    }
}


async function getTurnOver(poolRead,contractNumber) {
    try {
        let sql = "select month_of_info, value_of_month::float from loan_turnover_transaction where 1 = 1 and info_type = 'turnover'";
        let index = 1
        
        if(contractNumber != undefined){
            sql = sql + " and contract_number = $"+index+" order by month_of_info";
            index += 1
        }
        const result = await poolRead.query(sql,[contractNumber])
        if(index==1 || result.rowCount == 0) return false
        return result.rows
    }
    catch(err) {
        common.log(`get data entry error : ${err.message}`)
    }
}

async function getMistakeInfo(poolRead,contractNumber) {
    try {
        let sql = "select mistake_desc, result_chk mistake_type, created_date date_created, assignee task_manager from loan_manual_decision where contract_number = $1;";
        let index = 1
        
        if(contractNumber != undefined){
            index += 1
        }
        const result = await poolRead.query(sql,[contractNumber])
        if(index==1 || result.rowCount == 0) return {}
        return result.rows
    }
    catch(err) {
        common.log(`get mistake info error : ${err.message}`)
    }
}

module.exports = {
    saveDataEntry,
    updateDataEntry,
    getDataEntry,
    saveDataEntryV2,
    updateDataEntryV2,
    getHistory,
    getMaxCount,
    getTurnOver,
    getMistakeInfo
}