const { caseStatusCode, STATUS } = require("../const/caseStatus");
const { getStatusV2, saveStatusAsync, isNullOrEmpty } = require("../utils/helper");
const { additionDoc, chekedDocByRoleField, PARTNER_CODE } = require("../const/definition");
const common = require("../utils/common");
const uuid = require("uuid");
const { detectType } = require("../utils/detectContractType");
const { getContractByKU } = require("../repositories/kunn-repo");
const { getValueCodeByCodeType } = require("../utils/masterdataService");
const _ = require("lodash");
const { OWNER_ID } = require("../const/variables-const");

async function resubmitDoc(contractNumber, resubmitBody, isSme = false) {
  try {
    const poolWrite = global.poolWrite;
    const contractStatus = await getStatusV2(poolWrite, contractNumber);
    if (![caseStatusCode.CP03, caseStatusCode.CE05, caseStatusCode.MC05, STATUS.CP_RESUBMIT, STATUS.CE_RESUBMIT, STATUS.SS_RESUBMIT].includes(contractStatus)) {
      return {
        result: false,
        msg: "Invalid contract_number",
      };
    }

    let promiseList = [];
    const updateWaitingSql = "update loan_contract_document set waiting_resubmit = 0,is_deleted = 1 where contract_number = $1 and doc_type = $2 and waiting_resubmit = 1";
    let updateResubmitSql = `update loan_contract_document set is_resubmit=1,contract_number = $1,doc_group = $2,doc_type=$3 where doc_id = $4`;
    if (isSme) updateResubmitSql = `update loan_contract_document set is_resubmit=1,contract_number = $1,doc_group = $2,doc_type=$3,type_collection='DIBURSEMENT' where doc_id = $4`;
    for (let idx in resubmitBody) {
      let doc = resubmitBody[idx];
      if (doc.hasOwnProperty("doc_type")) {
        promiseList.push(poolWrite.query(updateWaitingSql, [contractNumber, doc.doc_type]));
      }
      if (doc.hasOwnProperty("docType")) {
        promiseList.push(poolWrite.query(updateWaitingSql, [contractNumber, doc.docType]));
      }
      if (doc.hasOwnProperty("docName")) {
        promiseList.push(poolWrite.query(updateWaitingSql, [contractNumber, doc.docName]));
      }
      if (doc.hasOwnProperty("docId") && doc.hasOwnProperty("docName")) {
        promiseList.push(poolWrite.query(updateResubmitSql, [contractNumber, doc.docGroup, doc.docName, doc.docId]));
      } else if (doc.hasOwnProperty("docId") && doc.hasOwnProperty("docType")) {
        promiseList.push(poolWrite.query(updateResubmitSql, [contractNumber, doc.docGroup, doc.docType, doc.docId]));
      }
      if (doc.hasOwnProperty("doc_id")) {
        promiseList.push(poolWrite.query(updateResubmitSql, [contractNumber, doc.docGroup, doc.doc_type, doc.doc_id]));
      }
    }
    const updateRs = await Promise.all(promiseList);
    updateRs.forEach((rs) => {
      if (rs.rowCount == 0) {
        common.log(`update resubmit error `, contractNumber);
        return {
          result: false,
          msg: "Resubmit documents error..",
        };
      }
    });

    return {
      result: true,
      msg: "Resubmit documents successfully..",
    };
  } catch (err) {
    console.log(err);
    return {
      result: false,
      msg: "error.",
    };
  }
}

async function getDisbursementResubmit(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document where contract_number = $1 and type_collection = 'DIBURSEMENT'";
    const data = await poolWrite.query(sql, [contractNumber]);
    return data.rows;
  } catch (err) {
    return false;
  }
}

async function getDisbursementDocuments(kunnId) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document where kunn_contract_number = $1 and reference_table = 'kunn_disbursement_info'";
    const data = await poolWrite.query(sql, [kunnId]);
    return data.rows;
  } catch (err) {
    return false;
  }
}

async function getResubmitList(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const selectSql = "select * from loan_contract_document where waiting_resubmit = 1 and contract_number = $1";
    const result = await poolWrite.query(selectSql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows;
  } catch (err) {
    common.log(`get resubmit list error : ${err.message}`, contractNumber);
    return false;
  }
}

async function getResubmitListKunn(kunnNumber) {
  try {
    const poolWrite = global.poolWrite;
    const selectSql = "select * from loan_contract_document where waiting_resubmit = 1 and is_deleted = 0 and kunn_contract_number = $1";
    const result = await poolWrite.query(selectSql, [kunnNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows;
  } catch (err) {
    common.log(`get resubmit list error : ${err.message}`, kunnNumber);
    return false;
  }
}

async function getResubmitListV2(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const selectSql = "select * from loan_contract_document lcd left join loan_manual_decision lmd on lcd.doc_id  = lmd.doc_id where waiting_resubmit = 1 and lcd.contract_number = $1";
    const result = await poolWrite.query(selectSql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows;
  } catch (err) {
    common.log(`get resubmit list error : ${err.message}`, contractNumber);
    return false;
  }
}

async function getSubmitListAF3(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const selectSql = "select * from loan_contract_document lcd left join loan_manual_decision lmd on lcd.doc_id  = lmd.doc_id where waiting_resubmit = 1 and lcd.contract_number = $1";
    const result = await poolWrite.query(selectSql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows;
  } catch (err) {
    common.log(`get resubmit list error : ${err.message}`, contractNumber);
    return false;
  }
}

async function saveUploadedDocument(poolWrite, contractNumber, docList) {
  try {
    const promiseList = [];
    const sql = "update loan_contract_document set contract_number = $1,doc_type=$2,doc_group=$3,updated_date = now() where doc_id = $4";
    for (let i in docList) {
      const doc = docList[i];
      let docId = doc.doc_id || doc.docId;
      let docType = doc.doc_type || doc.docName;
      let docGroup = doc.docGroup;
      promiseList.push(poolWrite.query(sql, [contractNumber, docType, docGroup, docId]));
    }
    const resultList = await Promise.all(promiseList);
    for (let i in resultList) {
      let rs = resultList[i];
      if (rs.rowCount == 0) {
        common.log("Update document error", contractNumber);
        return false;
      }
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function saveUploadedDocumentKunn(poolWrite, kunnId, docList) {
  try {
    const promiseList = [];
    const sql = "update loan_contract_document set kunn_contract_number = $1,doc_type=$2,doc_group=$4,updated_date = now(),type_collection='KU' where doc_id = $3";
    for (let i in docList) {
      const doc = docList[i];
      let docId = doc.docId;
      let docType = doc.docName;
      let docGroup = doc.docGroup;
      // var docGroup = doc.docGroup
      promiseList.push(poolWrite.query(sql, [kunnId, docType, docId, docGroup]));
    }
    const resultList = await Promise.all(promiseList);
    for (let i in resultList) {
      let rs = resultList[i];
      if (rs.rowCount == 0) {
        common.log("Update document error", kunnId);
        return false;
      }
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function deleteDocumentsByContractNumberAndDocTypeAndPeriod(contractNumber, docType, period) {
  try {
    const poolWrite = global.poolWrite;

    const sql = "update loan_contract_document set is_deleted = 1 ,updated_date = now() where contract_number = $1 and doc_type = $2 and period = $3 and is_deleted = 0 RETURNING id";
    let rs = await poolWrite.query(sql, [contractNumber, docType, period]);
    if (rs.rowCount == 0) {
      common.log("Update document error", contractNumber);
      return false;
    }

    const updatedIds = rs.rows.map((row) => row.id);

    return updatedIds;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function saveUploadedDocumentKunnSme(poolWrite, kunnId, docList, typeCollection) {
  try {
    const promiseList = [];
    const sql = "update loan_contract_document set kunn_contract_number = $1,doc_type=$2,doc_group=$4,updated_date = now(),type_collection=$5 where doc_id = $3";
    for (let i in docList) {
      const doc = docList[i];
      let docId = doc.docId;
      let docType = doc.docName;
      let docGroup = doc.docGroup;
      // var docGroup = doc.docGroup
      promiseList.push(poolWrite.query(sql, [kunnId, docType, docId, docGroup, typeCollection]));
    }
    const resultList = await Promise.all(promiseList);
    for (let i in resultList) {
      let rs = resultList[i];
      if (rs.rowCount == 0) {
        common.log("Update document error", kunnId);
        return false;
      }
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function saveUploadedDocumentKOV(poolWrite, contractNumber, docList) {
  try {
    const promiseList = [];
    const sql = "update loan_contract_document set contract_number = $1,doc_type=$2,doc_group=$3,doc_name_vn=$5,doc_name_vn_detail=$6,updated_date = now() where doc_id = $4";
    for (const i in docList) {
      if (!docList[i].docId) {
        continue;
      }
      const doc = _.cloneDeep(docList[i]);
      const docId = doc.docId;
      const docType = doc.docName ?? doc.docType;
      const docGroup = doc.docGroup;
      const docNameVn = doc.bundleNameVi || null;
      let docNameVnDetail = null;
      const documentRs = await getValueCodeByCodeType("DOCUMENT");
      if (Array.isArray(documentRs) && documentRs.length > 0) {
        docNameVnDetail = documentRs.find((doc) => doc.code == docType)?.value;
      }
      promiseList.push(poolWrite.query(sql, [contractNumber, docType, docGroup, docId, docNameVn, docNameVnDetail]));
    }
    const resultList = await Promise.all(promiseList);
    for (const i in resultList) {
      let rs = resultList[i];
      if (rs.rowCount == 0) {
        common.log("Update document error", contractNumber);
        return false;
      }
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function saveUploadedDocumentBIZZ(poolWrite, contract_number, history_tag, docList) {
  try {
    const promiseList = [];
    const sql = "SELECT * FROM sp_handle_document_after_signed($1,$2,$3,$4,$5,$6,$7)";
    for (let i in docList) {
      const doc = docList[i];
      const docId = doc.doc_id;
      const docType = doc.doc_type;
      const docGroup = doc.docGroup;
      const docNameVn = doc.bundleNameVi || null;
      let docNameVnDetail = null;
      const documentRs = await getValueCodeByCodeType("DOCUMENT");
      if (Array.isArray(documentRs) && documentRs.length > 0) {
        docNameVnDetail = documentRs.find((doc) => doc.code == docType)?.value;
      }
      promiseList.push(poolWrite.query(sql, [contract_number, docType, docId, docNameVn, docNameVnDetail, history_tag, docGroup]));
    }
    const resultList = await Promise.all(promiseList);
    for (const i in resultList) {
      let rs = resultList[i];
      if (rs.rowCount == 0) {
        common.log("Update document error", contract_number);
        return false;
      }
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function insertSingleDocument(contractNumber, docType, docId, docGroup, fileKey, fileName, typeCollection) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "insert into loan_contract_document (contract_number,doc_type,doc_id,doc_group,file_key,file_name,type_collection) values ($1,$2,$3,$4,$5,$6,$7)";
    const rs = await poolWrite.query(sql, [contractNumber, docType, docId, docGroup, fileKey, fileName, typeCollection]);
    if (rs.rowCount == 0) {
      common.log(`save LCT error `, contractNumber);
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function updateUploadAfterSigned(contractNumber, docList) {
  try {
    const poolWrite = global.poolWrite;
    const promiseList = [];
    const sqlCheckIsResubmit = "select * from loan_contract_document where contract_number=$1 and waiting_resubmit=1";
    let sql = "update loan_contract_document set doc_type = $1,doc_group = $2,contract_number=$3,type_collection='DIBURSEMENT',is_resubmit=0 where doc_id=$4";
    const rs = await poolWrite.query(sqlCheckIsResubmit, [contractNumber]);
    if (rs.rowCount > 0) {
      sql = "update loan_contract_document set doc_type = $1,doc_group = $2,contract_number=$3,type_collection='DIBURSEMENT',is_resubmit=1 where doc_id=$4";
    }
    const sql2 = "update loan_contract_document set is_deleted=1,waiting_resubmit=0 where doc_type=$1 and contract_number=$2";
    for (let i in docList) {
      const doc = docList[i];
      await poolWrite.query(sql2, [doc.docName, contractNumber]);
      await poolWrite.query(sql, [doc.docName, doc.docGroup, contractNumber, doc.docId]);
    }
    return await Promise.all(promiseList);
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function isValidCallbackResubmitKOV(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document lcd where  waiting_resubmit = 1 and is_deleted = 0 and doc_group in ('KD_EXTERNAL_DOCUMENT','OTHER DOCUMENTS') and contract_number = $1;";
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (rs.rowCount != 0) {
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function isValidResubmitSS(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document lcd where  waiting_resubmit = 1 and is_deleted = 0 and contract_number = $1;";
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (rs.rowCount != 0) {
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getEKYCDoc(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document where doc_type in ('SPID','SNID','SPIC') and contract_number = $1";
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (parseInt(rs.rowCount) == 0) {
      common.log("Get ekyc doc error : empty doc", contractNumber);
      return false;
    }
    return rs.rows;
  } catch (err) {
    common.log(err.message, contractNumber);
    return false;
  }
}

async function getEKYCKunnDoc(kunnNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document where doc_type in ('PIC') and kunn_contract_number = $1";
    const rs = await poolWrite.query(sql, [kunnNumber]);
    if (parseInt(rs.rowCount) == 0) {
      common.log("Get ekyc kunn doc error : empty doc", kunnNumber);
      return false;
    }
    return rs.rows;
  } catch (err) {
    common.log(err.message, contractNumber);
    return false;
  }
}

async function getEKYCSmeDoc(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract_document where doc_type in ('SPIDLR','SPIDAR','SPLR','SPAR') and contract_number = $1";
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (parseInt(rs.rowCount) == 0) {
      common.log("Get ekyc sme doc error : empty doc", contractNumber);
      return false;
    }
    return rs.rows;
  } catch (err) {
    common.log(err.message, contractNumber);
    return false;
  }
}

async function validDocId(docId) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select count(id) from loan_contract_document where doc_id = $1";
    const countRs = await poolWrite.query(sql, [docId]);
    if (parseInt(countRs.rows[0].count) == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log(err.message, docId);
    return false;
  }
}

async function validDocIdAndType(docId, doc_type) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select count(id) from loan_contract_document where doc_id = $1 and doc_type = $2 and (contract_number IS NULL OR contract_number = '')";
    const countRs = await poolWrite.query(sql, [docId, doc_type]);
    if (parseInt(countRs.rows[0].count) == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log(err.message, docId);
    return false;
  }
}

async function validUsedDocId(docId) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select contract_number from loan_contract_document where doc_id = $1";
    const rs = await poolWrite.query(sql, [docId]);
    if (rs.rowCount == 0) {
      return false;
    } else if (rs.rows[0].contract_number != null) return false;
    return true;
  } catch (err) {
    common.log(err.message, docId);
    return false;
  }
}

function insertOtherDoc(contractNumber, numAdditiondoc, info = null) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "insert into loan_contract_document (contract_number,doc_type,doc_name,doc_group,created_by,type_collection,doc_id,doc_name_vn,doc_name_vn_detail) values ($1,$2,$3,$4,$5,$6,$7,'GIẤY TỜ KHÁC','GIẤY TỜ KHÁC')";
    for (const x of Array(numAdditiondoc).keys()) {
      const docType = additionDoc.docType + "" + (x + 1);
      const docName = additionDoc.docName + "" + (x + 1);
      let docGroup = additionDoc.docGroup;
      if (info) {
        if (info?.partnerCode == PARTNER_CODE.SMA) docGroup = `${info?.productCode}_${additionDoc.docGroupSma}`;
      }
      const docId = uuid.v4();
      poolWrite.query(sql, [contractNumber, docType, docName, docGroup, "system", "ADDITIONAL DOC", docId]);
    }
  } catch (err) {
    common.log(`insert other doc error : ${err.message}`, contractNumber);
    return false;
  }
}

async function getDocType(docID) {
  const sql = "select doc_type from loan_contract_document where doc_id = $1";
  const docRs = await global.poolWrite.query(sql, [docID]);
  if (docRs.rows.length != 0) {
    return docRs.rows[0].doc_type;
  } else {
    return "";
  }
}

async function getDocByContractNumberAndType(contractNumber, docType) {
  try {
    const sql = `select * from loan_contract_document 
    where contract_number = $1 and doc_type = $2 and (is_deleted = 0 or is_deleted is null) order by creation_time desc`;
    const docRs = await global.poolWrite.query(sql, [contractNumber, docType]);
    if (docRs.rows.length != 0) {
      return docRs.rows[0];
    }

    return null;
  } catch (error) {
    console.log(`getDocByContractNumberAndType kunnId: ${contractNumber}, docType: ${docType}, error ${error}`);
    return null;
  }
}

async function getDocByKunnAndType(kunnId, docType) {
  try {
    const sql = `select * from loan_contract_document 
    where kunn_contract_number = $1 and doc_type = $2 and (is_deleted = 0 or is_deleted is null) order by creation_time desc`;
    const docRs = await global.poolWrite.query(sql, [kunnId, docType]);
    if (docRs.rows.length != 0) {
      return docRs.rows[0];
    }

    return null;
  } catch (error) {
    console.log(`getDocByKunnAndType kunnId: ${kunnId}, docType: ${docType}, error ${error}`);
    return null;
  }
}

async function saveCheckedDoc(checkedList, role) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `update loan_contract_document set ${chekedDocByRoleField[role]} =1,is_resubmit = 0 where doc_id = ANY ($1)`;
    const docList = checkedList.map((x) => {
      return x.docId;
    });
    //console.log(docList)
    return await poolWrite.query(sql, [docList]);
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function saveCheckedDocV2(checkedList, role) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `update loan_contract_document set ${chekedDocByRoleField[role]} =1, waiting_resubmit = 1 where doc_id = $1`;
    checkedList.map((doc) => {
      poolWrite.query(sql, [doc.docId]);
    });
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getDocumentByContractNumber(contractNumber) {
  const sql = "select * from loan_contract_document where is_deleted = 0 and (contract_number = $1 or kunn_contract_number = $1)";
  const docRs = await global.poolWrite.query(sql, [contractNumber]);
  return docRs.rows;
}

async function getDocumentByContractNumberV2(contractNumber) {
  const kunnNumber = contractNumber;
  const contract_number = await getContractByKU(kunnNumber);
  const sql = "select * from loan_contract_document where is_deleted = 0 and contract_number = $1 and file_key is not null";
  const sql2 = `select * from loan_contract_document where is_deleted = 0 and kunn_contract_number = $1 and file_key is not null`;
  const docRs = await Promise.all([global.poolWrite.query(sql, [contractNumber]), global.poolWrite.query(sql, [contract_number]), global.poolWrite.query(sql2, [contractNumber])]);
  const finalRs = [...(docRs[0]?.rows || []), ...(docRs[1]?.rows || []), ...(docRs[2]?.rows || [])];
  return finalRs;
}

async function getAllDocumentByContractNumber(contractNumber) {
  try {
    const sql = "select * from loan_contract_document where contract_number = $1  and (is_deleted = 0 or is_deleted is null)";
    const docRs = await global.poolWrite.query(sql, [contractNumber]);
    return docRs.rows;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getAllLctDocument(contractNumber, isKunn = false) {
  let sql = "select * from loan_contract_document where contract_number = $1 and doc_type = 'LCT' order by creation_time limit 1";
  if (isKunn) sql = "select * from loan_contract_document where kunn_contract_number = $1 and doc_type = 'LCTKU' order by creation_time limit 1";
  const docRs = await global.poolWrite.query(sql, [contractNumber]);
  return docRs.rows[0];
}

async function validVatDoc(kunnNumer) {
  let sql = "select * from loan_contract_document where kunn_contract_number = $1 and doc_type = 'VAT' and is_deleted=0 limit 1";
  const docRs = await global.poolWrite.query(sql, [kunnNumer]);
  return docRs.rowCount;
}

async function getKunnDoc(kunnNumer) {
  let sql = "select * from loan_contract_document where kunn_contract_number = $1 and is_deleted=0";
  const docRs = await global.poolRead.query(sql, [kunnNumer]);
  return docRs.rows;
}

async function findByDocID(docId) {
  const sql = "select * from loan_contract_document where doc_id = $1";
  const rs = await global.poolRead.query(sql, [docId]);
  return rs?.rows[0];
}

async function findByDocIds(docId) {
  const sql = "select * from loan_contract_document where doc_id = ANY($1) and (is_deleted = 0 or is_deleted is null)";
  const rs = await global.poolRead.query(sql, [docId]);
  return rs?.rows || [];
}

async function getByDocType(contracNumber, docType) {
  const sql = "select * from loan_contract_document where contract_number = $1 and doc_type = $2 and (is_deleted = 0 or is_deleted is null)";
  const rs = await global.poolRead.query(sql, [contracNumber, docType]);
  return rs?.rows;
}

async function getByKUUNDocType(contracNumber, debt_contract_number, docType) {
  const sql = "select doc_type,file_key,file_name ,url  from loan_contract_document where contract_number = $1 and kunn_contract_number = $2 and doc_type = $3 and (is_deleted = 0 or is_deleted is null)";
  const rs = await global.poolRead.query(sql, [contracNumber, debt_contract_number, docType]);
  return rs?.rows;
}

async function saveUploadedDocumentSuperApp(poolWrite, contractNumber, docList) {
  try {
    const promiseList = [];
    const sql = "update loan_contract_document set contract_number = $1,doc_type=$2,doc_group=$3,updated_date = now(),is_prepare_kunn = 1 where doc_id = $4";
    for (let i in docList) {
      const doc = docList[i];
      let docId = doc.docId;
      let docType = doc.docName;
      let docGroup = doc.docGroup;
      promiseList.push(poolWrite.query(sql, [contractNumber, docType, docGroup, docId]));
    }
    const resultList = await Promise.all(promiseList);
    for (const i in resultList) {
      let rs = resultList[i];
      if (rs.rowCount == 0) {
        common.log("Update document error", contractNumber);
        return false;
      }
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getPrepareDocKunnByContractNumber(contractNumber) {
  const sql = "select * from loan_contract_document where is_deleted = 0 and is_prepare_kunn = 1 and (contract_number = $1 or kunn_contract_number = $1)";
  const docRs = await global.poolWrite.query(sql, [contractNumber]);
  return docRs.rows;
}

const insert = async ({ contractNumber, docType, docId, docGroup, fileKey, fileName, typeCollection, url, kunnContractNumber, fileSize, disbursementInfoId, period }) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = `insert into loan_contract_document (contract_number,doc_type,doc_id,doc_group,file_key,file_name,type_collection,
         url, kunn_contract_number,file_size, kunn_disbursement_info_id, period) values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10, $11, $12)`;
    const rs = await poolWrite.query(sql, [contractNumber, docType, docId, docGroup, fileKey, fileName, typeCollection, url, kunnContractNumber, fileSize, disbursementInfoId, period]);
    return rs?.rowCount == 0 ? false : true;
  } catch (err) {
    console.log(err);
    return false;
  }
};

async function insertSingleDocumentV2(contractNumber, docType, docId, docGroup, fileKey, fileName, typeCollection, url, kunnContractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "insert into loan_contract_document (contract_number,doc_type,doc_id,doc_group,file_key,file_name,type_collection, url, kunn_contract_number) values ($1,$2,$3,$4,$5,$6,$7, $8, $9)";
    const rs = await poolWrite.query(sql, [contractNumber, docType, docId, docGroup, fileKey, fileName, typeCollection, url, kunnContractNumber]);
    if (rs.rowCount == 0) {
      common.log(`save LCT error `, contractNumber);
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

const findOne = async ({ whereCondition, orderBy = {} }) => {
  try {
    const table = "loan_contract_document";
    const keys = Object.keys(whereCondition);
    const values = Object.values(whereCondition);

    const whereClauses = keys.map((key, i) => `${key} = $${i + 1}`).join(" AND ");

    const orderKeys = Object.keys(orderBy);
    const orderClauses = orderKeys.length > 0 ? "ORDER BY " + orderKeys.map((key) => `${key} ${orderBy[key]}`).join(", ") : "";

    const query = `
      SELECT *
      FROM ${table}
      WHERE ${whereClauses}
      ${orderClauses}
      LIMIT 1;
    `;

    const res = await global.poolWrite.query(query, values);
    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`FIND_ONE - ${table} error:`, e);
    return undefined;
  }
};

const findPresignUploaded = async (fileKeys) => {
  try {
    const sql = `select * from loan_contract_document where file_key = ANY($1) and contract_number is null`;
    const rs = await global.poolWrite.query(sql, [fileKeys]);
    return rs?.rows ?? [];
  } catch (error) {
    console.log(`findPresignUploaded error: ${error}`);
    return [];
  }
};

const findPresignUploadedByDocIds = async (docIds) => {
  try {
    const sql = `select * from loan_contract_document where doc_id = ANY($1) and contract_number is null`;
    const rs = await global.poolWrite.query(sql, [docIds]);
    return rs?.rows ?? [];
  } catch (error) {
    console.log(`findPresignUploadedByDocIds error: ${error}`);
    return [];
  }
};

async function insertLoanContractDocument({ contract_number, doc_type, doc_id, kunn_contract_number, owner_id, doc_group, url, file_key, file_name, created_by, type_collection, comment, doc_name, request_id, doc_name_vn, doc_name_vn_detail, period, signed_type }) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      INSERT INTO loan_contract_document (
        contract_number,
        doc_type,
        doc_id,
        kunn_contract_number,
        owner_id,
        doc_group,
        url,
        file_key,
        file_name,
        created_by,
        type_collection,
        comment,
        doc_name,
        request_id,
        doc_name_vn,
        doc_name_vn_detail,
        period,
        signed_type
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18
      )
      RETURNING *
    `;
    const params = [contract_number, doc_type, doc_id, kunn_contract_number ?? null, owner_id ?? null, doc_group ?? null, url ?? null, file_key, file_name ?? null, created_by ?? null, type_collection ?? null, comment ?? null, doc_name ?? null, request_id ?? null, doc_name_vn ?? null, doc_name_vn_detail ?? null, period ?? null, signed_type ?? null];
    const rs = await poolWrite.query(sql, params);
    if (rs.rowCount === 0) {
      common.log(`Insert loan_contract_document error for contract_number: ${contract_number}`);
      return false;
    }
    return rs.rows[0]; // Return the inserted row
  } catch (err) {
    console.log(err);
    return false;
  }
}

const updateSignedDocument = async (document) => {
  try {
    const { url, file_key, file_name, id } = document;
    const sql = `UPDATE loan_contract_document SET url = $1, file_key = $2, file_name = $3, updated_date = now() WHERE id = $4`;
    const rs = await global.poolWrite.query(sql, [url, file_key, file_name, id]);
    return rs?.rowCount > 0;
  } catch (error) {
    console.log(`updateSignedDocument error: ${error}`);
    return false;
  }
};

const updateCommentDocument = async (document) => {
  try {
    const { comment, id } = document;
    const sql = `UPDATE loan_contract_document SET comment = $1,updated_date = now() WHERE id = $2`;
    const rs = await global.poolWrite.query(sql, [comment, id]);
    return rs?.rowCount > 0;
  } catch (error) {
    console.log(`updateSignedDocument error: ${error}`);
    return false;
  }
};

async function findByKunnDocTypeFileKey({ kunn_contract_number, doc_type, file_key }) {
  try {
    const sql = `
      select * from loan_contract_document
      where kunn_contract_number = $1
        and doc_type = $2
        and file_key = $3
        and is_deleted = 0
      limit 1
    `;
    const poolWrite = global.poolWrite;
    const rs = await poolWrite.query(sql, [kunn_contract_number, doc_type, file_key]);
    return rs?.rows[0] ?? null;
  } catch (err) {
    console.log(err);
    return null;
  }
}

const insertHistory = async (document, status) => {
  try {
    const poolWrite = global.poolWrite;
    const { contract_number, doc_type, doc_id, url, file_key, file_name } = document;
    const sql = `insert into loan_contract_document_history (contract_number,doc_type,doc_id,url,file_key,file_name,status) values ($1,$2,$3,$4,$5,$6,$7)`;
    const rs = await poolWrite.query(sql, [contract_number, doc_type, doc_id, url, file_key, file_name, status]);
    return rs?.rowCount == 0 ? false : true;
  } catch (err) {
    console.log(err);
    return false;
  }
};

const getLatestDocumentHistory = async (contract_number, doc_type) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      SELECT *
      FROM loan_contract_document_history
      WHERE contract_number = $1 AND doc_type = $2
      ORDER BY creation_time DESC
      LIMIT 1
    `;
    const rs = await poolWrite.query(sql, [contract_number, doc_type]);
    return rs?.rows[0] ?? null;
  } catch (err) {
    console.log(err);
    return null;
  }
};

async function deleteLoanContractDocument({ id, docId, kunnId, contractNumber, updatedBy, docType }) {
  try {
    const poolWrite = global.poolWrite;
    let whereClauses = [];
    let params = [];
    let idx = 2;

    if (id) {
      whereClauses.push(`id = $${idx++}`);
      params.push(id);
    }
    if (docId) {
      whereClauses.push(`doc_id = $${idx++}`);
      params.push(docId);
    }
    if (kunnId) {
      whereClauses.push(`kunn_contract_number = $${idx++}`);
      params.push(kunnId);
    }
    if (contractNumber) {
      whereClauses.push(`contract_number = $${idx++}`);
      params.push(contractNumber);
    }

    if (docType) {
      whereClauses.push(`doc_type = $${idx++}`);
      params.push(docType);
    }

    if (whereClauses.length === 0) {
      throw new Error("No identifier provided for deleteLoanContractDocument");
    }

    // updatedBy is always the first param
    params.unshift(updatedBy);

    const sql = `
      UPDATE loan_contract_document
      SET is_deleted = 1, updated_date = now(), updated_by = $1
      WHERE ${whereClauses.join(" AND ")}
    `;

    const rs = await poolWrite.query(sql, params);
    return rs?.rowCount > 0;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function findDocumentByOwner({ kunnId, contractNumber, ownerId }) {
  try {
    const poolWrite = global.poolWrite;
    let whereClauses = [];
    let params = [];
    let idx = 1;

    if (kunnId) {
      whereClauses.push(`kunn_contract_number = $${idx++}`);
      params.push(kunnId);
    }
    if (contractNumber) {
      whereClauses.push(`contract_number = $${idx++}`);
      params.push(contractNumber);
    }
    if (ownerId) {
      whereClauses.push(`owner_id = $${idx++}`);
      params.push(ownerId);
    }

    if (whereClauses.length === 0) {
      throw new Error("No identifier provided for findDocumentByOwner");
    }

    const sql = `
      SELECT *
      FROM loan_contract_document
      WHERE is_deleted = 0 AND ${whereClauses.join(" AND ")}
    `;
    const rs = await poolWrite.query(sql, params);
    if (rs.rowCount === 0) {
      return null;
    }
    return rs.rows;
  } catch (err) {
    console.log(err);
    return null;
  }
}

async function getKunnDocNotByEVF(kunnNumer) {
  let sql = "select * from loan_contract_document where kunn_contract_number = $1 and is_deleted=0 and (owner_id is null or owner_id != $2)";
  const poolRead = global.poolRead;
  const docRs = await poolRead.query(sql, [kunnNumer, OWNER_ID.EVF]);
  return docRs.rows;
}

async function getKunnDocNotByEVFAndDocTypes(kunnNumer, docTypes) {
  let sql = "select * from loan_contract_document where kunn_contract_number = $1 and is_deleted=0 and (owner_id is null or owner_id != $2) and doc_type = ANY($3)";
  const poolRead = global.poolRead;
  const docRs = await poolRead.query(sql, [kunnNumer, OWNER_ID.EVF, docTypes]);
  return docRs.rows;
}

async function getDocumentsByKunnAndDocTypes(kunnContractNumber, docTypes) {
  try {
    if (!Array.isArray(docTypes) || docTypes.length === 0) {
      return [];
    }
    const sql = `
      select * from loan_contract_document
      where kunn_contract_number = $1
        and doc_type = ANY($2)
        and is_deleted = 0
    `;
    const db = global.poolWrite;
    const rs = await db.query(sql, [kunnContractNumber, docTypes]);
    return rs?.rows ?? [];
  } catch (err) {
    console.log(err);
    return [];
  }
}

async function getDocumentsByContractAndKunnAndDocTypes(contracNumber, kunnContractNumber, docTypes) {
  try {
    if (!Array.isArray(docTypes) || docTypes.length === 0) {
      return [];
    }
    const sql = `
      select * from loan_contract_document
      where 
        contract_number = $1
        and kunn_contract_number = $2
        and doc_type = ANY($3)
        and is_deleted = 0
    `;
    const db = global.poolWrite;
    const rs = await db.query(sql, [contracNumber, kunnContractNumber, docTypes]);
    return rs?.rows ?? [];
  } catch (err) {
    console.log(err);
    return [];
  }
}

async function findByContractAndTypes(contractNumber, docTypes) {
  const sql = "select * from loan_contract_document where is_deleted = 0 and contract_number = $1 and doc_type = ANY($2)";
  const docRs = await global.poolWrite.query(sql, [contractNumber, docTypes]);
  return docRs.rows;
}

async function checkAllDocumentsExist(contractNumber, docTypes) {
  const documents = await findByContractAndTypes(contractNumber, docTypes);
  return new Set(documents.map((doc) => doc.doc_type)).size === docTypes.length;
}

async function findByReferenceAndTypes(table, refId, docTypes) {
  const sql = "select * from loan_contract_document where is_deleted = 0 and reference_table = $1 and reference_id = $2 and doc_type = ANY($3)";
  const docRs = await global.poolWrite.query(sql, [table, refId, docTypes]);
  return docRs.rows;
}

async function findByContractDeletedAndTypes(contractNumber, docType) {
  const sql = "select * from loan_contract_document where is_deleted = 1 and contract_number = $1 and doc_type = $2 order by creation_time desc limit 1";
  const docRs = await global.poolWrite.query(sql, [contractNumber, docType]);
  return docRs.rows[0];
}

async function findOriginContractByDocType(contractNumber, docTypes) {
  // const sql = "SELECT DISTINCT ON (doc_type) * FROM loan_contract_document WHERE contract_number = $1 AND doc_type = ANY($2) ORDER BY doc_type, id ASC;";
  const sql = "SELECT DISTINCT ON (doc_type) doc_type,doc_id,creation_time,updated_date,is_deleted,doc_group,url,file_key,file_name,created_by,document_no FROM loan_contract_document WHERE contract_number = $1 AND doc_type = ANY($2) ORDER BY doc_type, creation_time ASC;";
  const docRs = await global.poolWrite.query(sql, [contractNumber, docTypes]);
  return docRs.rows;
}

const softDeleteByContractNumberAndDocType = async (contractNumber, docTypes) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = "update loan_contract_document set is_deleted = 1, updated_date = now() where contract_number = $1 and doc_type = ANY($2) and is_deleted = 0";
    const rs = await poolWrite.query(sql, [contractNumber, docTypes]);
    return rs.rowCount > 0;
  } catch (err) {
    console.log(err);
    return false;
  }
};


const softDeleteByContractNumberAndIds = async (contractNumber, Ids) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = "update loan_contract_document set is_deleted = 1, updated_date = now() where contract_number = $1 and id = ANY($2) and is_deleted = 0";
    const rs = await poolWrite.query(sql, [contractNumber, Ids]);
    return rs.rowCount > 0;
  } catch (err) {
    console.log(err);
    return false;
  }
};

const softDeleteByDebtContractNumberAndDocType = async (debtContractNumber, docTypes) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = "update loan_contract_document set is_deleted = 1, updated_date = now() where kunn_contract_number = $1 and doc_type = ANY($2) and is_deleted = 0";
    const rs = await poolWrite.query(sql, [debtContractNumber, docTypes]);
    return rs.rowCount > 0;
  } catch (err) {
    console.log(err);
    return false;
  }
};

const softDeleteByContractNumberAndDocId = async (contractNumber, docId) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = "update loan_contract_document set is_deleted = 1, updated_date = now() where contract_number = $1 and doc_id = $2 and is_deleted = 0";
    const rs = await poolWrite.query(sql, [contractNumber, docId]);
    return rs.rowCount > 0;
  } catch (err) {
    console.log(err);
    return false;
  }
};

async function findByKunnDocIdAndType({ kunnId, docId, docType }) {
  try {
    const sql = `
      select * from loan_contract_document
      where kunn_contract_number = $1
        and doc_id = $2
        and doc_type = $3
        and is_deleted = 0
      limit 1
    `;
    const poolWrite = global.poolWrite;
    const rs = await poolWrite.query(sql, [kunnId, docId, docType]);
    return rs?.rows[0] ?? null;
  } catch (err) {
    console.log(err);
    return null;
  }
}

async function saveKunnDocumentChecked({ kunnId, docType, docId, status, comment, updatedBy }) {
  try {
    const waitingResubmit = status ? 0 : 1;
    const isSsChecked = 1;
    const isChecked = status ? 1 : 0;
    const poolWrite = global.poolWrite;
    const sql = `
      UPDATE loan_contract_document
      SET
        waiting_resubmit = $1,
        comment = $2,
        updated_by = $3,
        is_ss_checked = $4,
        is_checked = $5,
        updated_date = now()
      WHERE kunn_contract_number = $6
        AND doc_type = $7
        AND doc_id = $8
        AND is_deleted = 0
      RETURNING *
    `;
    const params = [waitingResubmit, comment, updatedBy, isSsChecked, isChecked, kunnId, docType, docId];
    const rs = await poolWrite.query(sql, params);
    if (rs.rowCount === 0) {
      common.log(`saveLoanContractDocumentUpdate error for doc_id: ${docId}`);
      return false;
    }
    return rs.rows[0];
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getDocumentsByDocIdsWhereNoContract(docIds) {
  if (!Array.isArray(docIds) || docIds.length === 0) {
    return [];
  }
  const sql = `
    SELECT *
    FROM loan_contract_document
    WHERE doc_id = ANY($1)
      AND contract_number IS NULL
      AND kunn_contract_number IS NULL
      AND is_deleted = 0
  `;
  const poolWrite = global.poolWrite;
  const rs = await poolWrite.query(sql, [docIds]);
  return rs?.rows ?? [];
}

async function updateLoanContractNumberKunn({ docId, docType, contractNumber, kunnContractNumber, signedType = null }) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      UPDATE loan_contract_document
      SET contract_number = $1,
          kunn_contract_number = $2,
          updated_date = now(),
          signed_type = $5
      WHERE doc_id = $3
        AND doc_type = $4
        AND is_deleted = 0
        AND kunn_contract_number IS NULL
        AND contract_number IS NULL
      RETURNING *
    `;
    const params = [contractNumber, kunnContractNumber, docId, docType, signedType];
    const rs = await poolWrite.query(sql, params);
    if (rs.rowCount === 0) {
      common.log(`updateLoanContractNumber error for doc_id: ${docId}, doc_type: ${docType}`);
      return false;
    }
    return rs.rows[0];
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function deleteLoanContractNumberByDocIdAndDocType({ docId, docType }) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      UPDATE loan_contract_document
      SET is_deleted = 1, updated_date = now()
      WHERE doc_id = $1
        AND doc_type = $2
        AND is_deleted = 0
        AND contract_number IS NULL
        AND kunn_contract_number IS NULL
      RETURNING *
    `;
    const rs = await poolWrite.query(sql, [docId, docType]);
    if (rs.rowCount === 0) {
      common.log(`deleteLoanContractNumberByDocIdAndDocType error for doc_id: ${docId}, doc_type: ${docType}`);
      return false;
    }
    return rs.rows[0];
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function updateReference({ docId, docType, referenceId, referenceTable }) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      UPDATE loan_contract_document
      SET reference_id = $1,
          reference_table = $2,
          updated_date = now()
      WHERE doc_id = $3
        AND doc_type = $4
        AND is_deleted = 0
        AND kunn_contract_number IS NULL
        AND contract_number IS NULL
        AND reference_id IS NULL
        AND reference_table IS NULL
      RETURNING *
    `;
    const params = [referenceId, referenceTable, docId, docType];
    const rs = await poolWrite.query(sql, params);
    if (rs.rowCount === 0) {
      common.log(`updateDocumentReference error for doc_id: ${docId}, doc_type: ${docType}`);
      return false;
    }
    return rs.rows[0];
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function updateReferenceMultiple({ files, referenceId, referenceTable }) {
  try {
    if (files && Array.isArray(files) && files.length > 0) {
      const promises = [];
      for (const file of files) {
        promises.push(
          updateReference({
            docId: file.docId,
            docType: file.docType,
            referenceId: referenceId,
            referenceTable: referenceTable,
          })
        );
      }
      await Promise.all(promises);
    }
  } catch (err) {
    console.log(err);
  }
}

const saveRequestDocuments = async ({ contractNumber, docList, masterdataDocuments }) => {
  try {
    const promiseList = [];
    const sql = `
    update 
      loan_contract_document 
    set 
      contract_number = $1,
      doc_type = $2,
      doc_group = $3,
      doc_name_vn = $4,
      doc_name_vn_detail = $5,
      updated_date = now(),
      reference_table = $6,
      reference_id = $7
    where 
      doc_id = $8`;

    for (const i in docList) {
      if (!docList[i].docId) {
        continue;
      }
      const doc = _.cloneDeep(docList[i]);
      const docId = doc.docId;
      const docType = doc.docName ?? doc.docType;
      const docGroup = doc.docGroup;
      const docNameVn = doc.bundleNameVi || null;
      let docNameVnDetail = null;
      if (!masterdataDocuments || masterdataDocuments.length === 0) {
        masterdataDocuments = await getValueCodeByCodeType("DOCUMENT");
      }
      if (Array.isArray(masterdataDocuments) && masterdataDocuments.length > 0) {
        docNameVnDetail = masterdataDocuments.find((doc) => doc.code == docType)?.value;
      }
      promiseList.push(global.poolWrite.query(sql, [contractNumber, docType, docGroup, docNameVn, docNameVnDetail, doc.referenceTable || null, doc.referenceId || null, docId]));
    }

    const resultList = await Promise.all(promiseList);
    for (let idx = 0; idx < resultList.length; idx++) {
      const rs = resultList[idx];
      if (rs.rowCount === 0) {
        const doc = docList[idx];
        common.log(`Update document error`, { contractNumber, docId: doc?.docId || doc?.doc_id });
        return false;
      }
    }

    return true;
  } catch (err) {
    console.error(`saveRequestDocuments error: ${err.message}`, contractNumber);
    return false;
  }
};

const updateBundleGroup = async ({ docList, bundleInfo }) => {
  if (bundleInfo.length === 0 || docList.length === 0) {
    common.log("No bundle info or doc list provided for updateBundleGroup");
    return true;
  }
  const docDict = {};
  bundleInfo.map((bundle) => {
    let tmpDocList = bundle.docList;
    tmpDocList.forEach((doc) => {
      docDict[doc.docType] = bundle.bundleName;
      docDict[doc.bundleNameVi] = bundle.bundleNameVi;
    });
  });
  docList.map((doc) => {
    doc.docGroup = doc.hasOwnProperty("doc_type") ? docDict[doc.doc_type] : doc.hasOwnProperty("docType") ? docDict[doc.docType] : docDict[doc.docName];
    doc.bundleNameVi = docDict[doc.bundleNameVi];
  });

  const promiseList = [];
  const sql = `
    update 
      loan_contract_document 
    set 
      doc_group = $1,
      doc_name_vn = $2,
      updated_date = now()
    where 
      doc_id = $3`;

  for (const i in docList) {
    if (!docList[i].docId) {
      continue;
    }
    const doc = _.cloneDeep(docList[i]);
    const docId = doc.docId;
    const docGroup = doc.docGroup;
    const docNameVn = doc.bundleNameVi || null;

    promiseList.push(global.poolWrite.query(sql, [docGroup, docNameVn, docId]));
  }

  const resultList = await Promise.all(promiseList);
  for (let idx = 0; idx < resultList.length; idx++) {
    const rs = resultList[idx];
    if (rs.rowCount === 0) {
      const doc = docList[idx];
      common.log(`Update bundle error`, { contractNumber, docId: doc?.docId || doc?.doc_id });
      return false;
    }
  }
  return true;
};

async function findByContractDocTypeFileKey({ contract_number, doc_type, file_key }) {
  try {
    const sql = `
      select * from loan_contract_document
      where contract_number = $1
        and doc_type = $2
        and file_key = $3
        and is_deleted = 0
      limit 1
    `;
    const poolWrite = global.poolWrite;
    const rs = await poolWrite.query(sql, [contract_number, doc_type, file_key]);
    return rs?.rows[0] ?? null;
  } catch (err) {
    console.log(err);
    return null;
  }
}

async function deleteDocument(id) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      UPDATE loan_contract_document
      SET is_deleted = 1, updated_date = now()
      WHERE id = $1
        AND is_deleted = 0
      RETURNING *
    `;
    const rs = await poolWrite.query(sql, [id]);
    return rs?.rowCount > 0;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getDocumentByDocIdAndTypeWhereNoContract(docId, docType) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      SELECT *
      FROM loan_contract_document
      WHERE doc_id = $1
        AND doc_type = $2
        AND contract_number IS NULL
        AND kunn_contract_number IS NULL
        AND is_deleted = 0
      LIMIT 1
    `;
    const rs = await poolWrite.query(sql, [docId, docType]);
    return rs?.rows[0] ?? null;
  } catch (err) {
    console.log(err);
    return null;
  }
}

async function findLoanContractDocumentById(id) {
  try {
    const sql = "select * from loan_contract_document where id = $1 and is_deleted = 0";
    const rs = await global.poolWrite.query(sql, [id]);
    return rs?.rows[0] ?? null;
  } catch (err) {
    console.log(err);
    return null;
  }
}

async function update(id, data) {
  const fields = [];
  const values = [];
  let idx = 1;

  for (const [key, value] of Object.entries(data)) {
    fields.push(`${key} = $${idx++}`);
    values.push(value);
  }
  values.push(id);
  const sql = `
        UPDATE loan_contract_document
        SET ${fields.join(", ")}, updated_date = now()
        WHERE id = $${idx}
        RETURNING *;
    `;
  const pool = global.poolWrite;
  const result = await pool.query(sql, values);
  return result.rows[0];
}

module.exports = {
  getSubmitListAF3,
  resubmitDoc,
  saveUploadedDocument,
  saveUploadedDocumentKOV,
  insertSingleDocument,
  getEKYCDoc,
  validDocId,
  validDocIdAndType,
  validUsedDocId,
  getResubmitList,
  getResubmitListV2,
  isValidCallbackResubmitKOV,
  insertOtherDoc,
  updateUploadAfterSigned,
  getDisbursementResubmit,
  getDocType,
  saveUploadedDocumentKunn,
  saveCheckedDoc,
  saveCheckedDocV2,
  getDocumentByContractNumber,
  getAllDocumentByContractNumber,
  getEKYCSmeDoc,
  getAllLctDocument,
  isValidResubmitSS,
  validVatDoc,
  getKunnDoc,
  saveUploadedDocumentKunnSme,
  findByDocID,
  getDocumentByContractNumberV2,
  getResubmitListKunn,
  getEKYCKunnDoc,
  saveUploadedDocumentSuperApp,
  getPrepareDocKunnByContractNumber,
  insert,
  insertSingleDocumentV2,
  findOne,
  getDocByContractNumberAndType,
  deleteDocumentsByContractNumberAndDocTypeAndPeriod,
  getDocByKunnAndType,
  getByDocType,
  getByKUUNDocType,
  findPresignUploaded,
  findPresignUploadedByDocIds,
  insertLoanContractDocument,
  findByKunnDocTypeFileKey,
  updateSignedDocument,
  insertHistory,
  deleteLoanContractDocument,
  findDocumentByOwner,
  getKunnDocNotByEVF,
  getDocumentsByKunnAndDocTypes,
  getDocumentsByContractAndKunnAndDocTypes,
  findByContractAndTypes,
  getLatestDocumentHistory,
  saveUploadedDocumentBIZZ,
  softDeleteByContractNumberAndDocType,
  findByKunnDocIdAndType,
  saveKunnDocumentChecked,
  getDocumentsByDocIdsWhereNoContract,
  updateLoanContractNumberKunn,
  deleteLoanContractNumberByDocIdAndDocType,
  updateReference,
  updateReferenceMultiple,
  saveRequestDocuments,
  updateBundleGroup,
  softDeleteByContractNumberAndDocId,
  findByContractDocTypeFileKey,
  softDeleteByDebtContractNumberAndDocType,
  deleteDocument,
  findByContractDeletedAndTypes,
  findOriginContractByDocType,
  updateCommentDocument,
  findByReferenceAndTypes,
  getKunnDocNotByEVFAndDocTypes,
  getDocumentByDocIdAndTypeWhereNoContract,
  findLoanContractDocumentById,
  update,
  checkAllDocumentsExist,
  findByDocIds,
  softDeleteByContractNumberAndIds,
  getDisbursementDocuments
};
