const common = require("../utils/common")

async function inserEkycResult(contractNumber,serviceError,msgError) {
    try {
        const poolWrite = global.poolWrite
        const insertEkycSql = "insert into ekyc_check (contract_number,service_error,msg_error) values ($1,$2,$3)"
        const rs = await poolWrite.query(insertEkycSql,[contractNumber,serviceError,msgError])
        if(rs.rowCount == 0) {
            common.log(`save ekyc service error `,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save ekyc service error : ${err.message}`,contractNumber)
        return false
    }
}

async function getEkycError(contractNumber) {
    try{
        const poolWrite = global.poolWrite
        const getEkcySql = 'select * from ekyc_check where contract_number = $1'
        const rs = await poolWrite.query(getEkcySql,[contractNumber])
        if(rs.rowCount == 0) {
            //common.log(`get ekyc service error `,contractNumber)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`get ekyc service error : ${err.message}`,contractNumber)
        return false
    }
}
const getKycByContract = async function(contract_number){
	try{
        const poolRead = global.poolRead;
        let query = "select * from ekyc_check where contract_number = $1";
        let params = [contract_number];

        let result = await poolRead.query(query, params);
        if(result.rowCount > 0)
            return result.rows[0];

        return null;
	}catch(err){
		console.log(` ${err.message}`);
		console.log(err);
        return null;
	}
}
const updateEkycStatus = async function (poolWrite, input){
	let query = `update ekyc_check set act = $1, reason_code = $2, reject_message = $3, updated_by = $4, updated_on = now()
                where contract_number = $5`
    let params = [
        input.act,
        getReasonCode(input.reason_code),
        input.reject_mesage || null,
        input.updated_by || null,
        input.contract_number
    ];
    return await poolWrite.query(query, params);
}

const getReasonCode = (reasonCode) => {
    if(helper.isNullOrEmpty(reasonCode))
        return null;
    let index = reasonCode.indexOf(" : ");
    if(index > 0)
        return reasonCode.substr(0, index);
    return reasonCode;
}

module.exports = {
    updateEkycStatus,
    inserEkycResult,
    getEkycError,
    getKycByContract
}