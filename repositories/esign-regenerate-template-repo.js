
async function insert({reference_code, reference_table}) {
    const db = global.poolWrite;
    const query = `
        INSERT INTO esign_regenerate_template (reference_code, reference_table)
        VALUES ($1, $2)
        RETURNING *;
    `;
    const values = [
        reference_code,
        reference_table
    ];
    const result = await db.query(query, values);
    return result.rows[0];
}

async function updateGenerated(reference_code, reference_table) {
    const db = global.poolWrite;
    const query = `
        UPDATE esign_regenerate_template
        SET is_generated = true, 
            updated_at = NOW()
        WHERE reference_code = $1 AND reference_table = $2
        RETURNING *;
    `;
    const values = [
        reference_code,
        reference_table
    ];
    const result = await db.query(query, values);
    return result.rows[0];
}

module.exports = {
    insert,
    updateGenerated
};