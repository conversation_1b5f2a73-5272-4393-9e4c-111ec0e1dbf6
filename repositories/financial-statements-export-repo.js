const { convertCamelToSnake } = require("../utils/helper");
const { insertData, generateValues } = require("../utils/sqlHelper");

const columns = [
  "revenu_documents_id",
  "report_date",
  "report_year",
  "file_key",
  "contract_number",
  "accounting_regime",
  "accounting_template",
  "financial_year_end_date",
  "code",
  "is_audit",
  "template",
  "unit_of_measurement",
  "cust_id"
];
const TABLE_NAME = "financial_statements_export";
const LOG_PREFIX = `[DB][financial-statements-export-repo]`;
const save = async (data) => {
  try {
    data = convertCamelToSnake(data);
    return await insertData(TABLE_NAME, columns, generateValues(data, columns));
  } catch (error) {
    console.log(`${LOG_PREFIX} save ${JSON.stringify(data)}, error: ${error}}`);
  }
};

const findFinancialStatementsExportByRevenueDocumentIds = async (revenueDocumentIds) => {
  if (!revenueDocumentIds || !Array.isArray(revenueDocumentIds) || revenueDocumentIds.length === 0) {
    return [];
  }
  try {
    const poolWrite = global.poolWrite;
    const sql = `
            SELECT 
              * 
            FROM 
              financial_statements_export
            WHERE 
              is_deleted = 0 
              AND revenu_documents_id = ANY($1::int[])
        `;
    const result = await poolWrite.query(sql, [revenueDocumentIds]);
    return result?.rows ?? [];
  } catch (e) {
    console.error(e);
    return [];
  }
};

const softRemoveFinancialStatementsExport = async (financialStatementIds) => {
  if (!financialStatementIds || !Array.isArray(financialStatementIds) || financialStatementIds.length === 0) {
    return 0;
  }
  try {
    const poolWrite = global.poolWrite;
    const sql = `UPDATE financial_statements_export SET is_deleted = 1 WHERE id = ANY($1::int[])`;
    const result = await poolWrite.query(sql, [financialStatementIds]);
    return result.rowCount || 0;
  } catch (e) {
    console.error(e);
    return 0;
  }
};

module.exports = {
  save,
  findFinancialStatementsExportByRevenueDocumentIds,
  softRemoveFinancialStatementsExport
};
