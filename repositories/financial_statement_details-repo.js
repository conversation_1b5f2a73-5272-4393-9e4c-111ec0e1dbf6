const { convertCamelToSnake } = require("../utils/helper");
const { insertData, generateValues } = require("../utils/sqlHelper");

const columns = [
  "financial_statements_export_id",
  "num_of_first_year",
  "num_of_second_year",
  "code",
  "name",
  "note",
];
const TABLE_NAME = "financial_statement_details";
const LOG_PREFIX = `[DB][financial_statement_details-repo]`;
const save = async (data) => {
  try {
    data = convertCamelToSnake(data);
    return await insertData(TABLE_NAME, columns, generateValues(data, columns));
  } catch (error) {
    console.log(`${LOG_PREFIX} save ${JSON.stringify(data)}, error: ${error}}`);
  }
};

const getRevenue = async ({ contractNumber, }) => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(
      `SELECT fsd.*,
            fse2."template",
            fse2.report_year
      FROM financial_statement_details fsd
      LEFT JOIN financial_statements_export fse2 ON fsd.financial_statements_export_id = fse2.id
      WHERE financial_statements_export_id IN
          (SELECT id
          FROM financial_statements_export fse
          WHERE contract_number = $1
            AND "template" IN ('TT200_KQHDKD',
                                'TT133_HDKD_B02'))
        AND ((fse2."template" = 'TT200_KQHDKD'
              AND fsd.code = '10')
            OR (fse2."template" = 'TT133_HDKD_B02'
                AND fsd.code = '01'))
        AND fsd.is_deleted = 0 ;`,
      [contractNumber]
    );
    return result.rows[0] ?? {};
  } catch (error) {
    console.log(`${LOG_PREFIX} getRevenue ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};

const getFinancialStatementsExportByTemplate = async (contractNumber, template = "TT133_BCTC") => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(
      `select accounting_template ,"template" from financial_statements_export where contract_number = $1 and template LIKE '%' || $2 || '%'`, 
      [contractNumber, template]
    );
    return result.rows[0] ?? {};
  } catch (error) {
    console.log(`${LOG_PREFIX} getRevenue ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};

const getRevenueByTaxFinancialReport = async (contract_number, code) => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(
      `select  *  from financial_statement_details fsd 
        where financial_statements_export_id  in (
          select id from  financial_statements_export 
          where  contract_number = $1  and "template" in('TT133_HDKD_B02','TT200_KQHDKD')
        ) and code = ANY($2);
      `,
      [contract_number, code]
    );
    return result.rows ?? [];
  } catch (error) {
    console.log(`${LOG_PREFIX} getRevenue ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};

const softRemoveFinancialStatementDetails = async (financialStatementIds) => {
  if (!financialStatementIds || !Array.isArray(financialStatementIds) || financialStatementIds.length === 0) {
    return 0;
  }
  try {
    const poolWrite = global.poolWrite;
    const sql = `
    UPDATE 
      financial_statement_details 
    SET 
      is_deleted = 1 
    WHERE 
      financial_statements_export_id = ANY($1::int[])`;
    const result = await poolWrite.query(sql, [financialStatementIds]);
    return result.rowCount || 0;
  } catch (e) {
    console.error(e);
    return 0;
  }
};

const getTotalAssets = async ({ contractNumber, }) => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(
      `SELECT fsd.*,
            fse2."template",
            fse2.report_year
      FROM financial_statement_details fsd
      LEFT JOIN financial_statements_export fse2 ON fsd.financial_statements_export_id = fse2.id
      WHERE financial_statements_export_id IN
          (SELECT id
          FROM financial_statements_export fse
          WHERE contract_number = $1
            AND "template" IN ('TT200_CDKT',
                                'TT133_BCTC_B01B',
                                'TT133_BCTC_B01A',
                                'TT133_BCTC_B01'))
        AND ((fse2."template" = 'TT200_CDKT'
              AND fsd.code = '270')
            OR (fse2."template" = 'TT133_BCTC_B01B'
                AND fsd.code = '300')
            OR (fse2."template" = 'TT133_BCTC_B01A'
                AND fsd.code = '200')
            OR (fse2."template" = 'TT133_BCTC_B01'
                AND fsd.code = '200'))
        AND fsd.is_deleted = 0 ;`,
      [contractNumber]
    );
    return result.rows[0] ?? {};
  } catch (error) {
    console.log(`${LOG_PREFIX} getTotalAssets ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};


module.exports = {
  save,
  getRevenue,
  getFinancialStatementsExportByTemplate,
  getRevenueByTaxFinancialReport,
  softRemoveFinancialStatementDetails,
  getTotalAssets
};
