const common = require("../utils/common");

async function save({
  kunnId,
  disbursementInfoId,
  docType,
  mimeType,
  docName,
  docCode,
  docNumber,
  totalPaymentAmount,
  partnerUrl,
  url,
  fileKey
}) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `insert into kunn_disbursement_info_docs(kunn_id, disbursement_info_id, doc_type, mime_type, doc_name, doc_code, doc_number, total_payment_amount, partner_url, url,file_key) 
    values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11) returning *`;
    const insertRs = await poolWrite.query(sql, [
      kunnId,
      disbursementInfoId,
      docType,
      mimeType,
      docName,
      docCode,
      docNumber,
      totalPaymentAmount,
      partnerUrl,
      url,
      fileKey
    ]);
    if (insertRs.rowCount > 0) {
      return insertRs.rows?.[0];
    }
    return false;
  } catch (err) {
    common.log(err.message);
    return false;
  }
}
module.exports = {
  save,
};
