const { patchUpdate } = require("../utils/sqlHelper");
const common = require("../utils/common");
const updateColumns = [
  "verify_account_name",
  "verify_account_info",
  "updated_date",
];

async function save({
  kunnId,
  beneficiary,
  accountName,
  bankAccount,
  bankCode,
  bankBranchCode,
  amount,
  transferContent,
  bankName,
  verifyAccountName,
  verifyAccountInfo
}) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `insert into kunn_disbursement_info(kunn_id, beneficiary, account_name,
                bank_account, bank_code, bank_branch_code, amount, transfer_content, bank_name,verify_account_name,verify_account_info) 
                values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11) returning *`;
    const insertRs = await poolWrite.query(sql, [
      kunnId,
      beneficiary,
      accountName,
      bankAccount,
      bankCode,
      bankBranchCode,
      amount,
      transferContent,
      bankName,
      verifyAccountName,
      verifyAccountInfo
    ]);
    if (insertRs.rowCount > 0) {
      return insertRs.rows?.[0];
    }
    return false;
  } catch (err) {
    common.log(err.message);
    return false;
  }
}

const findByKunnId = async (kunnId) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = `select * from kunn_disbursement_info where kunn_id = $1 and (is_deleted is null or is_deleted = 0)`;
    const rs = await poolWrite.query(sql, [kunnId]);
    if (rs.rowCount > 0) {
      return rs.rows;
    }
    return [];
  } catch (error) {
    console.log(
      `[KUNN][DISBURSEMENT-INFO-REPO] findByKunnId ${kunnId} error ${error}`
    );
    return null;
  }
};

const update = async (id, values) => {
  try {
    return await patchUpdate({
      table: "kunn_disbursement_info",
      updateColumns,
      values,
      conditions: {
        id,
      },
    });
  } catch (error) {
    console.log(
      `[KUNN][DISBURSEMENT-INFO-REPO] update ${kunnId} error ${error}`
    );
    return null;
  }
};

module.exports = {
  save,
  findByKunnId,
  update,
};
