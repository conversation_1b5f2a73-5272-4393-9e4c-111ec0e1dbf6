const { isNullOrEmpty } = require("../utils/helper")

async function save(body) {
    try {
        if (!body.contractNumber || !body.field || !body.value) {
            return false
        }
        const poolWrite = global.poolWrite
        const sql = `INSERT INTO kunn_prepare_attribute (contract_number, field, value) SELECT $1, $2, $3 WHERE
            NOT EXISTS (
                SELECT field from kunn_prepare_attribute where contract_number = $4 and field = $5
            ) returning *;
        `
        const rsSave = await poolWrite.query(sql, [body.contractNumber, body.field, body.value, body.contractNumber, body.field])
        if (rsSave.rowCount > 0)
            return true
        return false
    } catch (error) {
        console.log(error)
        console.log("error when save kunn prepare attribute: " + error.message)
        return false
    }
}

async function update(body) {
    try {
        if (!body.contractNumber || !body.field || !body.value) {
            return false
        }
        const poolWrite = global.poolWrite
        const sql = `update kunn_prepare_attribute
            set value=$1, updated_date=$2 where contract_number=$3 and field=$4 returning *;
        `
        const rsUpdate = await poolWrite.query(sql, [body.value, new Date(), body.contractNumber, body.field])
        if (rsUpdate.rowCount > 0)
            return true
        return false
    } catch (error) {
        console.log(error)
        console.log("error when update kunn prepare attribute: " + error.message)
        return false
    }
}

async function getByContractNumber(contractNumber) {
    try {
        if (isNullOrEmpty(contractNumber))
            return false
        const poolRead = global.poolRead
        const sql = 'select * from kunn_prepare_attribute where contract_number = $1 returning *'
        const rs = await poolRead.query(sql, [contractNumber])
        if (rs.rowCount > 0)
            return rs.rows
        else
            return false
    } catch (error) {
        console.log(error)
        console.log("error when getByContractNumber: " + error.message)
        return false
    }
}

const getkunnPrepareAttribute = async function () {
    try {
        const sql = 'select distinct code, field, value from kunn_prepare_attribute'
        const rs = await global.poolRead.query(sql)
        return rs.rows
    } catch (error) {
        console.log(error)
        console.log("error when getkunnPrepareAttribute" + error.message)
        return false
    }

}

async function isMatchingOffer(contractNumber) {
    try {
        const obj = await getDataByContractNumber(contractNumber)
        if (parseFloat(obj.requestIrAmount) === parseFloat(obj.approveKunnIr)
            && parseFloat(obj.requestTenorAmount) === parseFloat(obj.approveKunnTenor)
            && parseFloat(obj.requestKunnAmount) === parseFloat(obj.approveKunnAmount))
            return true
        return false
    } catch (error) {
        console.log(error)
        console.log(`error when check matching kunn offer | contractNumber: ${contractNumber} | `, error.message)
        return false
    }
}

async function getDataByContractNumber(contractNumber) {
    try {
        const sql = `select * from kunn_prepare_attribute where contract_number = $1`
        const dataPrepareKunn = (await poolRead.query(sql, [contractNumber])).rows
        let obj = {}
        dataPrepareKunn.map(dp => {
            obj[dp.field] = dp.value
        })
        return obj
    } catch (error) {
        console.log(error)
        console.log(`error when getObjectDataByContractNumber | contractNumber: ${contractNumber} | `, error.message)
        return false
    }
}

module.exports = {
    save,
    update,
    getByContractNumber,
    getkunnPrepareAttribute,
    isMatchingOffer,
    getDataByContractNumber
}