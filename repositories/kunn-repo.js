const {kunnMapping} = require("./param-column-mapping")
const common = require("../utils/common");
const { PARTNER_CODE } = require("../const/definition");
const { KUNN_STATUS } = require("../const/caseStatus");

async function genKunnNumber(){
    try {
        const sql = "SELECT nextval('contract_generate_number')";
        const result = await global.poolWrite.query(sql)
        return result.rows[0].nextval
    }
    catch(err) {
        console.log(err)
        return false
    }
}

function generateSql(loanContractMapping,body) {
    let fieldQuery = "";
    let paramQuery = " values(";
    let params = []
    let paramIdx = 1
    for (let key of Object.keys(loanContractMapping)) {
        if(loanContractMapping[key] !== 'null' && loanContractMapping[key] !== "" && body[key] !== '' && body[key] != undefined) {
            fieldQuery += loanContractMapping[key] + ",";
            paramQuery += "$" + paramIdx + ","
            params.push(body[key]);
            paramIdx += 1
        }
    }
    fieldQuery = fieldQuery.slice(0,-1) + ")"
    paramQuery = paramQuery.slice(0,-1) + ")"
    const mappingQuery = fieldQuery + paramQuery
    return {
        mappingQuery,
        params
    }
}

async function insertKunn(body) {
    try {
        const poolWrite = global.poolWrite
        let basicQuery = "insert into kunn(" 
        const {mappingQuery,params} = generateSql(kunnMapping,body)
        const insertSql = basicQuery + mappingQuery
        const insertRs = await poolWrite.query(insertSql,params)
        if(insertRs.rowCount > 0) {
            return true
        }
        return false
    }
    catch(err) {
        common.log(err.message)
        return false
    }
}

async function updateKUStatus(kunnNumber,status) {
    try {
        const poolWrite = global.poolWrite
        const sql = "update kunn set status = $1,updated_date = now() where kunn_id = $2"
        const rs = await  poolWrite.query(sql,[status,kunnNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return true
    }
    catch(err) {
        console.log(err,kunnNumber)
        return false
    }
}

async function updateKUStatusV2(kunnNumber,status) {
    try {
        const poolWrite = global.poolWrite
        const sql = "update kunn set status = $1,updated_date = now() where kunn_id = $2"
        await  poolWrite.query(sql,[status,kunnNumber])
    }
    catch(err) {
        console.log(err,kunnNumber)
    }
}
async function updateKUNNWaitingResubmit(kunnNumber) {
    try {
        const status = KUNN_STATUS.WAITING_RESUBMIT;
        const poolWrite = global.poolWrite;
        const sql = `
            UPDATE kunn
            SET
                status = $1,
                updated_date = now(),
                waiting_resubmit_date = COALESCE(waiting_resubmit_date, now())
            WHERE kunn_id = $2
        `;
        await poolWrite.query(sql, [status, kunnNumber]);
    } catch (err) {
        console.log(err, kunnNumber);
    }
}

async function updateKUNNPassedTD1(kunnNumber) {
    try {
        const status = KUNN_STATUS.PASSED_TD1;
        const poolWrite = global.poolWrite;
        const sql = `
            UPDATE kunn
            SET
                status = $1,
                updated_date = now(),
                af1_approve_date = COALESCE(af1_approve_date, now())
            WHERE kunn_id = $2
        `;
        await poolWrite.query(sql, [status, kunnNumber]);
    } catch (err) {
        console.log(err, kunnNumber);
    }
}

async function checkIsKU(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = 'select 1 from kunn where kunn_id = $1'
        const rs = await poolWrite.query(sql,[contractNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return true
    }
    catch(err) {
        return false
    }
}

async function update(kunnId,values) {
    try {
        const poolWrite = global.poolWrite;
        const objKeys = Object.keys(values);
        const params = [kunnId,values[objKeys[0]]]
        let index = 2;
        let keyStr=` ${objKeys[0]} = $${index}`
        if(objKeys.length > 1)
        {
            for(let i=1;i<objKeys.length;i++)
            {
                index+=1;
                keyStr+=`, ${objKeys[i]}=$${index}`;
                params.push(values[objKeys[i]])
            }
        }
        
        const sql = `update kunn set ${keyStr},updated_date = now() where kunn_id = $1`
        const rs = await  poolWrite.query(sql, params)
        if(rs.rowCount == 0) {
            return false
        }
        return true
    }
    catch(err) {
        console.log(err,kunnId)
        return false
    }
}

async function getContractByKU(kunnNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select contract_number from kunn where kunn_id = $1"
        const rs = await poolWrite.query(sql,[kunnNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return rs.rows[0]?.contract_number
    }
    catch(err) {
        return false
    }
}

async function getKunnData(kunnNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select * from kunn where kunn_id = $1"
        const rs = await poolWrite.query(sql,[kunnNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        return false
    }   
}

async function getKunnDataByCustId(contractNumber,isMcApp = false) {
    try {
        const poolRead = global.poolRead
        let sql = `select k.contract_number, kunn_id, k.status, with_draw_amount, k.start_date, k.end_date from kunn k join loan_contract lc on 
                    k.contract_number = lc.contract_number where 1=1 and lc.cust_id = $1 `
        if (!isMcApp) {
            sql += ` and lc.status in ('ACTIVATED')`
        }
        const rs = await poolRead.query(sql,[contractNumber])
        if(rs.rowCount == 0) {
            return {}
        }
        return rs.rows
    }
    catch(err) {
        common.log(err)
        return {}
    }   
}

async function getKunnDataByContractNumber(contractNumber) {
    try {
        const poolRead = global.poolRead
        let sql = `select k.request_id, k.contract_number, kunn_id, k.status, with_draw_amount, k.start_date, k.end_date from kunn k join loan_contract lc on 
                    k.contract_number = lc.contract_number where 1=1 and lc.contract_number = $1 `
        const rs = await poolRead.query(sql,[contractNumber])
        if(rs.rowCount == 0) {
            return {}
        }
        return rs.rows
    }
    catch(err) {
        common.log(err)
        return {}
    }   
}

async function updateLmsType(lmsType,kunnNumber) {
    try {
        const poolRead = global.poolWrite
        const sql = `update kunn set lms_type = $1,updated_date=now() where kunn_id = $2`;
        const rs = await poolRead.query(sql,[lmsType,kunnNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return true
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function updateEffectiveDate(kunnNumber,startDate,endDate) {
    try {
        const poolWrite = global.poolWrite
        const sql = `update kunn set start_date=$1,end_date=$2 where kunn_id = $3`
        const rs = await poolWrite.query(sql,[startDate,endDate,kunnNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return true
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function getKunnByContractNumber(contractnumber){
    try {
        const poolWrite = global.poolWrite
        const sql = "select * from kunn where contract_number = $1 order by created_date asc"
        const data = await poolWrite.query(sql, [contractnumber])
        return data
    } catch (error) {
        console.log(error)
    }
}

async function updateKunnInfo({partnerCode, kunnId}) {
    try {
        const poolWrite = global.poolWrite
        const sql = `update kunn set partner_code = $1, updated_date=now() where kunn_id = $2`;
        const rs = await poolWrite.query(sql,[partnerCode, kunnId])
        if(rs.rowCount == 0) {
            return false
        }
        return true
    }
    catch(err) {
        console.log(`error when updateKunnInfo | ${kunnId} | ` + err?.message)
        console.log(err)
        return false
    }
}

async function getKunnSignedDisburByTime(time) {
    try {
        const poolRead = global.poolRead
        time = parseFloat(time)
        const sql = `
            select lc.phone_number1, k.* from kunn k join loan_contract lc on lc.contract_number = k.contract_number 
            where EXTRACT(EPOCH from now() - k.updated_date) >= $1
            and k.status = 'SIGNED_TO_BE_DISBURED' and phone_number1 is not null and k.partner_code = ANY ($2)
        `;
        const rs = await poolRead.query(sql, [time, [PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC]])
        if(rs.rowCount == 0) {
            return false
        }
        return rs.rows
    }
    catch(err) {
        console.log(`error when getKunnSignedDisburByTime` + err?.message)
        console.log(err)
        return false
    }
}

async function searchKunn({ partnerCodes, search, status, fromDate, toDate, page = 1, pageSize = 20, columns = [] }) {
    try {
        const poolRead = global.poolRead;
        let selectedColumns = columns.length > 0 ? columns.join(', ') : '*';
        let sql = `SELECT ${selectedColumns} FROM kunn WHERE 1=1`;
        const params = [];
        let idx = 1;

        let whereClause = '';

        if (partnerCodes && Array.isArray(partnerCodes) && partnerCodes.length > 0) {
            whereClause += ` AND partner_code = ANY($${idx++})`;
            params.push(partnerCodes);
        }

        if (search) {
            whereClause += ` AND (kunn_id = $${idx} OR contract_number = $${idx})`;
            params.push(search);
            idx++;
        }
        if (status) {
            whereClause += ` AND status = $${idx++}`;
            params.push(status);
        }
        if (fromDate) {
            whereClause += ` AND created_date >= $${idx++}`;
            params.push(fromDate);
        }
        if (toDate) {
            // Ensure created_date <= end of the provided date (23:59:59)
            whereClause += ` AND created_date <= $${idx++}`;
            const endOfDay = new Date(toDate);
            endOfDay.setHours(23, 59, 59, 999);
            params.push(endOfDay);
        }
        const countParams = [...params];

        // Paging
        const offset = (page - 1) * pageSize;
        sql += whereClause + ` ORDER BY created_date DESC LIMIT $${idx++} OFFSET $${idx++}`;
        params.push(pageSize, offset);

        // Count total records for pagination
        let countSql = `SELECT COUNT(*) FROM kunn WHERE 1=1 ` + whereClause;
        const countResult = await poolRead.query(countSql, countParams);
        const totalRecords = parseInt(countResult.rows[0].count, 10);
        const totalPages = Math.ceil(totalRecords / pageSize);

        const rs = await poolRead.query(sql, params);
        return {
            records: rs.rows,
            totalRecords: totalRecords,
            totalPages: totalPages,
            currentPage: page,
            pageSize: pageSize
        };
    } catch (err) {
        console.log('Error in searchKunn:', err);
        return {
            records: [],
            totalRecords: 0,
            totalPages: 0,
            currentPage: page,
            pageSize: pageSize
        };
    }
}

const getKunnsByStatusAndDate = async ({ status, columnName, days, partnerCode }) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      SELECT * FROM kunn
      WHERE status = $1
        AND ${columnName} IS NOT NULL
        AND now() - ${columnName} > ($2 * INTERVAL '1 day')
        AND partner_code = $3
    `;
    const result = await poolWrite.query(sql, [status, days, partnerCode]);
    return result.rows || [];
  } catch (err) {
    common.log("getKunnsByStatusAndDate error", err);
    return [];
  }
}

async function searchActiveKunnByCustId(custId) {
    try {
        const poolRead = global.poolRead;
        const sql = `
            SELECT k.*
            FROM kunn k 
            JOIN loan_contract lc ON k.contract_number = lc.contract_number 
            WHERE lc.cust_id = $1
            AND k.status = 'ACTIVATED'
            ORDER BY k.created_date DESC
        `;
        const rs = await poolRead.query(sql, [custId]);
        return rs.rows;
    } catch (err) {
        console.log('Error in searchActiveKunnByCustId:', err);
        return [];
    }
}

async function getKunnByIdAndContractNumber(kunnId, contractNumber) {
    try {
        const poolWrite = global.poolWrite;
        const sql = 'select * from kunn where kunn_id = $1 and contract_number = $2';
        const rs = await poolWrite.query(sql, [kunnId, contractNumber]);
        return rs.rows[0];
    } catch (err) {
        console.log('Error in getKunnByIdAndContractNumber:', err);
        return null;
    }
}

module.exports = {
    genKunnNumber,
    insertKunn,
    updateKUStatus,
    checkIsKU,
    getContractByKU,
    getKunnData,
    getKunnDataByCustId,
    getKunnDataByContractNumber,
    updateLmsType,
    updateEffectiveDate,
    getKunnByContractNumber,
    updateKUStatusV2,
    updateKunnInfo,
    getKunnSignedDisburByTime,
    update,
    searchKunn,
    getKunnsByStatusAndDate,
    searchActiveKunnByCustId,
    updateKUNNWaitingResubmit,
    updateKUNNPassedTD1,
    getKunnByIdAndContractNumber
}