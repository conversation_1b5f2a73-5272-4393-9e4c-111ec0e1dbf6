const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const { REF_TABLE } = require("../const/variables-const");

async function createLenderChangeRequest(data) {
    const {
        request_id,
        request_type,
        reference_code,
        reference_type,
        created_by,
        request_body,
        comment,
        parent_reference
    } = data;
    const db = global.poolWrite;
    const result = await db.query(
        `INSERT INTO lender_change_request
            (request_id, request_type, reference_code, reference_type, created_by, request_body, comment, parent_reference)
         VALUES ($1,$2,$3,$4,$5,$6, $7, $8)
         RETURNING *`,
        [
            request_id,
            request_type,
            reference_code,
            reference_type,
            created_by,
            request_body,
            comment,
            parent_reference || null
        ]
    );
    return result.rows[0];
}

async function getLenderChangeRequestById(id) {
    const db = global.poolWrite;
    const result = await db.query(
        'SELECT * FROM lender_change_request WHERE id = $1 and is_deleted = 0',
        [id]
    );
    return result.rows[0];
}

async function getLatestLenderChangeRequest({ request_type, reference_code, reference_type, status }) {
    const db = global.poolWrite;
    const conditions = [];
    const values = [];
    let idx = 1;

    if (!request_type && !reference_code && !reference_type) {
        throw new Error('At least one filter must be provided');
    }

    if (request_type) {
        conditions.push(`request_type = $${idx++}`);
        values.push(request_type);
    }
    if (reference_code) {
        conditions.push(`reference_code = $${idx++}`);
        values.push(reference_code);
    }
    if (reference_type) {
        conditions.push(`reference_type = $${idx++}`);
        values.push(reference_type);
    }

    if(Array.isArray(status)) {
        conditions.push(`status = ANY($${idx++})`);
        values.push(status);
    } else if (status) {
        conditions.push(`status = $${idx++}`);
        values.push(status);
    }
    let query = 'SELECT * FROM lender_change_request where is_deleted = 0 ';
    if (conditions.length > 0) {
        query += ' AND ' + conditions.join(' AND ');
    }
    query += ' ORDER BY created_at DESC LIMIT 1';
    const result = await db.query(query, values);
    return result.rows[0];
}

async function updateLenderChangeRequest(id, data) {
    const fields = [];
    const values = [];
    let idx = 1;
    for (const key in data) {
        fields.push(`${key} = $${idx++}`);
        values.push(data[key]);
    }
    if (fields.length === 0) return null;
    values.push(id);
    const db = global.poolWrite;
    const result = await db.query(
        `UPDATE lender_change_request SET updated_at = now(), ${fields.join(', ')} WHERE id = $${idx} RETURNING *`,
        values
    );
    return result.rows[0];
}

async function listLenderChangeRequests(filter = {}) {
    const db = global.poolWrite;
    let query = 'SELECT * FROM lender_change_request where is_deleted = 0';
    const conditions = [];
    const values = [];
    let idx = 1;
    for (const key in filter) {
        if (filter[key] !== undefined && filter[key] !== null) {
            conditions.push(`${key} = $${idx++}`);
            values.push(filter[key]);
        }
    }
    if (conditions.length > 0) {
        query += ' AND ' + conditions.join(' AND ');
    }
    query += ' ORDER BY created_at DESC';
    if (filter.limit) {
        query += ` LIMIT $${idx++}`;
        values.push(filter.limit);
    }
    const result = await db.query(query, values);
    return result.rows;
}


async function getLenderChangeRequestDetail(change_request_id) {
    const db = global.poolWrite;
    const result = await db.query(
        'SELECT * FROM lender_change_request_detail WHERE change_request_id = $1',
        [change_request_id]
    );
    return result?.rows ?? [];
}

async function getChangeRequestDetailByContract({contract_number, shareholderIds, repIds, managerIds, revenueIds, vatFormIds}) {
    const db = global.poolWrite;
    const whereClause = [];
    const params = [];
    if (contract_number) {
        whereClause.push(`(lcr.reference_code = $${params.length + 1} AND lcr.reference_type = $${params.length + 2})`);
        params.push(contract_number, REF_TABLE.LOAN_CONTRACT);
    }
    if (shareholderIds && shareholderIds.length > 0) {
        whereClause.push(`(lcr.reference_code = ANY($${params.length + 1}) AND lcr.reference_type = $${params.length + 2})`);
        params.push(shareholderIds, REF_TABLE.SHAREHOLDER);
    }
    if (repIds && repIds.length > 0) {
        whereClause.push(`(lcr.reference_code = ANY($${params.length + 1}) AND lcr.reference_type = $${params.length + 2})`);
        params.push(repIds, REF_TABLE.REPRESENTATION);
    }
    if (managerIds && managerIds.length > 0) {
        whereClause.push(`(lcr.reference_code = ANY($${params.length + 1}) AND lcr.reference_type = $${params.length + 2})`);
        params.push(managerIds, REF_TABLE.MANAGER);
    }
    if (revenueIds && revenueIds.length > 0) {
        whereClause.push(`(lcr.reference_code = ANY($${params.length + 1}) AND lcr.reference_type = $${params.length + 2})`);
        params.push(revenueIds, REF_TABLE.REVENUE);
    }
    if (vatFormIds && vatFormIds.length > 0) {
        whereClause.push(`(lcr.reference_code = ANY($${params.length + 1}) AND lcr.reference_type = $${params.length + 2})`);
        params.push(vatFormIds, REF_TABLE.VAT_FORM);
    }
    const result = await db.query(
        `SELECT lcrd.*, lcr.reference_code, lcr.reference_type FROM lender_change_request_detail lcrd
            JOIN lender_change_request lcr ON lcr.id = lcrd.change_request_id
            WHERE (${whereClause.join(' OR ')}) AND lcr.is_deleted = 0
            ORDER BY lcrd.created_at DESC`,
        params,
    );
    return result.rows;
}

async function insertChangeRequestDetail(detail) {
    const {
        change_request_id,
        key,
        old_value,
        new_value,
        is_resubmit,
        comment,
        status,
        editable,
        is_change
    } = detail;
    const db = global.poolWrite;
    const result = await db.query(
        `INSERT INTO lender_change_request_detail
            (change_request_id, "key", old_value, new_value, is_resubmit, "comment", status, editable, is_change)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
         RETURNING *`,
        [
            change_request_id,
            key,
            old_value,
            new_value,
            is_resubmit ?? false,
            comment,
            status ?? true,
            editable ?? false,
            is_change ?? false
        ]
    );
    return result.rows[0];
}

async function updateChangeRequestDetails(details) {
    if (!Array.isArray(details) || details.length === 0) return;

    for (const detail of details) {
        const {
            change_request_id,
            key,
            old_value,
            new_value,
            comment,
            status,
            editable,
            is_change,
            ref_table
        } = detail;
        const db = global.poolWrite;
        await db.query(
            `UPDATE lender_change_request_detail
             SET old_value = $1, new_value = $2, "comment" = $3, status = $4, editable = $5, is_change = $6
             WHERE change_request_id = $7 AND "key" = $8 AND ref_table = $9`,
            [old_value, new_value, comment, status ?? true, editable ?? false, is_change ?? false, change_request_id, key, ref_table]
        );
    }
}

async function updateChangeRequestDetailsById(details) {
  if (!Array.isArray(details) || details.length === 0) return;

  for (const detail of details) {
    const { id, change_request_id, old_value, new_value, comment, status, editable, is_change } = detail;
    const db = global.poolWrite;
    await db.query(
      `UPDATE lender_change_request_detail
             SET old_value = $1, new_value = $2, "comment" = $3, status = $4, editable = $5, is_change = $6
             WHERE change_request_id = $7 AND "id" = $8`,
      [old_value, new_value, comment, status ?? true, editable ?? false, is_change ?? false, change_request_id, id]
    );
  }
}

async function insertChangeRequestDetails(details) {
    if (!Array.isArray(details) || details.length === 0) return;

    const values = [];
    const params = [];

    details.forEach((item, idx) => {
        values.push(
            item.change_request_id,
            item.key,
            item.old_value,
            item.new_value,
            item.comment || null,
            item.status ?? true,
            item.editable ?? false,
            item.is_change ?? false,
            item.ref_table
        );
        const numOfColumns = 9; // change_request_id, key, old_value, new_value, comment, status, editable, is_change, ref_table
        const baseIdx = idx * numOfColumns;
        params.push(
        `($${baseIdx+1}, $${baseIdx+2}, $${baseIdx+3}, $${baseIdx+4}, $${baseIdx+5}, $${baseIdx+6}, $${baseIdx+7}, $${baseIdx+8}, $${baseIdx+9})`
        );
    });

    const query = `
        INSERT INTO lender_change_request_detail
        (change_request_id, "key", old_value, new_value, "comment", status, editable, is_change, ref_table)
        VALUES
        ${params.join(', ')}
    `;
    const db = global.poolWrite;
    await db.query(query, values);
}

async function getLatestLenderChangeRequestList(conditionList) {
    const db = global.poolWrite;
    if (!Array.isArray(conditionList) || conditionList.length === 0) {
        throw new Error('At least one filter group must be provided');
    }

    const allConditions = [];
    const values = [];
    let idx = 1;

    for (const condition of conditionList) {
        const subConditions = [];

        if (condition.request_type) {
            subConditions.push(`request_type = $${idx++}`);
            values.push(condition.request_type);
        }
        if (condition.reference_code) {
            subConditions.push(`reference_code = $${idx++}`);
            values.push(condition.reference_code);
        }
        if (condition.reference_type) {
            subConditions.push(`reference_type = $${idx++}`);
            values.push(condition.reference_type);
        }
        if (condition.status) {
            subConditions.push(`status = $${idx++}`);
            values.push(condition.status);
        }

        if (subConditions.length === 0) {
            throw new Error('Each condition group must have at least one field');
        }

        // Gói mỗi nhóm điều kiện AND vào dấu ngoặc
        allConditions.push(`(${subConditions.join(' AND ')})`);
    }

    let query = 'SELECT * FROM lender_change_request WHERE is_deleted = 0';
    if (allConditions.length > 0) {
        query += ' AND (' + allConditions.join(' OR ') + ')';
    }
    query += ' ORDER BY created_at DESC';

    const result = await db.query(query, values);
    return result.rows;
}

const updateStatusOfListChangeRequest = async (changeRequestIds, status) => {
    if (!Array.isArray(changeRequestIds) || changeRequestIds.length === 0) {
        return;
    }
    const db = global.poolWrite;
    const query = `
        UPDATE lender_change_request
        SET status = $1, updated_at = NOW()
        WHERE id = ANY($2)
        RETURNING *;
    `;
    const result = await db.query(query, [status, changeRequestIds]);
    return result.rows;
};

/**
 * reset lai status sau khi duyet change_request hoac resubmit lai data
 */
const updateStatusOfListChangeRequestDetails = async (changeRequestIds, status) => {
    if (!Array.isArray(changeRequestIds) || changeRequestIds.length === 0) {
        return;
    }
    const db = global.poolWrite;
    const query = `
        UPDATE lender_change_request_detail
        SET status = $1
        WHERE change_request_id = ANY($2)
        RETURNING *;
    `;
    const result = await db.query(query, [status, changeRequestIds]);
    return result.rows;
};

/**
 * check xem trong change details co change_request_id co bat ky cai nao status false hay khong
 */
const checkChangeRequestDetailsHasResubmit = async (changeRequestIds) => {
    const db = global.poolWrite;
    const query = `
        SELECT COUNT(*) as count
        FROM lender_change_request_detail
        WHERE change_request_id = ANY($1) AND status = false;
    `;
    const result = await db.query(query, [changeRequestIds]);
    return result.rows[0].count > 0;
};

/**
 *  check xem trong change details co change_request_id co bat ky cai nao is_change = true hay khong
 */
const checkChangeRequestDetailsHasChange = async (changeRequestIds) => {
    const db = global.poolWrite;
    const query = `
        SELECT COUNT(*) as count
        FROM lender_change_request_detail
        WHERE change_request_id = ANY($1) AND is_change = true;
    `;
    const result = await db.query(query, [changeRequestIds]);
    return result.rows[0].count > 0;
};

const checkChangeRequestDetailsHasSubInfor = async (changeRequestIds) => {
    const db = global.poolWrite;
    const query = `
        SELECT  * 
        FROM lender_change_request_detail
        WHERE change_request_id = $1 AND key like 'parent_%';;
    `;
    const result = await db.query(query, [changeRequestIds]);
    return result.rows[0];
};

async function getLatestChangeRequestsByParent(status, parentReference) {
    const db = global.poolWrite;
    const query = `
        SELECT DISTINCT ON (reference_code, reference_type, parent_reference)
            *
        FROM lender_change_request
        WHERE is_deleted = 0
        AND parent_reference = $2
        AND status = $1
        ORDER BY reference_code, reference_type, parent_reference, created_at DESC;
    `;

    try {
        const res = await db.query(query, [status, parentReference]);
        return res.rows;
    } catch (error) {
        console.error('Query error:', error);
        throw error;
    }
}

async function updateCancelLenderChangeRequest({ request_type, reference_code, reference_type, status }) {
    const db = global.poolWrite;
    const conditions = [];
    const values = [];
    let idx = 2;

    if (!request_type && !reference_code && !reference_type) {
        throw new Error('At least one filter must be provided');
    }

    if (request_type) {
        conditions.push(`request_type = $${idx++}`);
        values.push(request_type);
    }
    if (reference_code) {
        conditions.push(`reference_code = $${idx++}`);
        values.push(reference_code);
    }
    if (reference_type) {
        conditions.push(`reference_type = $${idx++}`);
        values.push(reference_type);
    }

    if (Array.isArray(status)) {
        conditions.push(`status = ANY($${idx++})`);
        values.push(status);
    } else if (status) {
        conditions.push(`status = $${idx++}`);
        values.push(status);
    }

    let query = 'UPDATE lender_change_request SET status = $1, updated_at = NOW() WHERE is_deleted = 0 ';
    query += ' AND ' + conditions.join(' AND ');
    
    values.unshift(LENDER_CHANGE_REQUEST_STATUS.CANCELED);

    const result = await db.query(query, values);
    return result.rowCount > 0;
}

module.exports = {
    createLenderChangeRequest,
    getLenderChangeRequestById,
    updateLenderChangeRequest,
    getLatestLenderChangeRequest,
    listLenderChangeRequests,
    getLenderChangeRequestDetail,
    insertChangeRequestDetail,
    insertChangeRequestDetails,
    getLatestLenderChangeRequestList,
    updateStatusOfListChangeRequest,
    updateStatusOfListChangeRequestDetails,
    getChangeRequestDetailByContract,
    updateChangeRequestDetails,
    updateChangeRequestDetailsById,
    getLatestChangeRequestsByParent,
    updateCancelLenderChangeRequest,
    checkChangeRequestDetailsHasChange,
    checkChangeRequestDetailsHasSubInfor,
    checkChangeRequestDetailsHasResubmit
};
