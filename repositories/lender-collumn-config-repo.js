
async function getLenderColumnConfig({ partnerCode, table = null, groupType = null }) {
    try {
        const poolWrite = global.poolWrite;
        let sql = "SELECT * FROM lender_column_config WHERE partner_code = $1";
        const params = [partnerCode];

        if (table) {
            sql += " and table_name = $" + (params.length + 1);
            params.push(table);
        }
        if (groupType) {
            sql += " and group_type = $" + (params.length + 1);
            params.push(groupType);
        }
        sql += " and is_deleted = 0 order by ord";

        const result = await poolWrite.query(sql, params);
        if (result.rowCount === 0) {
            return [];
        }
        return result.rows;
    } catch (err) {
        console.error("Error fetching lender column config:", err);
        return [];
    }
}

module.exports = {
    getLenderColumnConfig
};