async function save(body){
    try {
        if(!body.contractNumber || !body.field || !body.value) {
            return false
        }
        const poolWrite = global.poolWrite
        const sql = `INSERT INTO loan_attribute (contract_number, field, value) SELECT $1, $2, $3 WHERE
            NOT EXISTS (
                SELECT field from loan_attribute where contract_number = $4 and field = $5
            ) returning *;
        `
        const rsSave = await poolWrite.query(sql, [body.contractNumber, body.field, body.value, body.contractNumber, body.field])
        if(rsSave.rowCount > 0) 
            return true
        return false
    } catch (error) {
        console.log(error)
        console.log("error when save loan attribute: " + error.message)
        return false
    }
}

async function update(body){
    try {
        if(!body.contractNumber || !body.field || !body.value) {
            return false
        }
        const poolWrite = global.poolWrite
        const sql = `update loan_attribute
            set value=$1, updated_date=$2 where contract_number=$3 and field=$4 returning *;
        `
        const rsUpdate = await poolWrite.query(sql, [body.value, new Date(), body.contractNumber, body.field])
        if(rsUpdate.rowCount > 0) 
            return true
        return false
    } catch (error) {
        console.log(error)
        console.log("error when update loan attribute: " + error.message)
        return false
    }
}

async function getDataByContractNumber(contractNumber) {
    try {
        const sql = `select * from loan_attribute where contract_number = $1`
        const loanData = (await poolRead.query(sql, [contractNumber])).rows
        let obj = {}
        loanData.map(dp => {
            obj[dp.field] = dp.value
        })
        return obj
    } catch (error) {
        console.log(error)
        console.log(`error when getDataByContractNumber | contractNumber: ${contractNumber} | `, error.message)
        return false
    }
}

module.exports = {
    save,
    update,
    getDataByContractNumber
}