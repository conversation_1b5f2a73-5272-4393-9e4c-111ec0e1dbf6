const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");
const pgp = require("pg-promise")({
    capSQL: true,
});

const insert = async (contractNumber, branches, masterdataDocuments) => {
    if (!branches || branches?.length === 0) {
        return;
    }
    const poolWrite = global.poolWrite;
    const columnSet = new pgp.helpers.ColumnSet([
        "contract_number",
        "branch_name",
        "province",
        "district",
        "ward",
        "address",
        "status_owned",
        "num_of_staff",
        "new_province",
        "new_ward"
    ], { table: "loan_branch_address" });
    let insertData = [];
    branches.forEach((branchAddres) => {
        insertData.push({
            contract_number: contractNumber,
            branch_name: branchAddres.branchName,
            province: branchAddres.provinceCode,
            district: branchAddres.districtCode,
            ward: branchAddres.wardCode,
            address: branchAddres.detailAddress,
            status_owned: branchAddres.statusOwned || "",
            num_of_staff: branchAddres.numOfStaff || 0,
            new_province: branchAddres.newProvinceCode,
            new_ward: branchAddres.newWardCode,
        });
    });
    
    if (insertData?.length === 0) {
        return;
    }
    const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
    const result = await poolWrite
        .query(insertQuery)
        .then()
        .catch((error) => {
            console.log(error);
            common.log("INSERT - loan_branch_address: error", contractNumber);
        });
    if (result?.rowCount > 0) {
        //save docs
        let insertDocData = [];
        branches.forEach((e) => {
            if (e?.docs?.length > 0) {
                const branch = result?.rows?.filter((entity) => entity.branch_name == e.branchName);
                e.docs.forEach((doc) => {
                    insertDocData.push({
                        docId: doc.docId,
                        docType: doc.docType,
                        referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_BRANCH_ADDRESS,
                        referenceId: branch[0].id
                    });
                });
            }
        });
        const resultUpdateDocs = await documentRepo.saveRequestDocuments({
            contractNumber: contractNumber,
            docList: utils.snakeToCamel(insertDocData),
            masterdataDocuments
        });
        return resultUpdateDocs;
    }
};

module.exports = {
    insert
}