const { snakeToCamel } = require("../utils/helper");
const { find } = require("../utils/sqlHelper");
const pgp = require("pg-promise")({
  capSQL: true,
});

const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");

const columns = [
  "contract_number",
  "subject",
  "tax_id",
  "company_name",
  "full_name",
  "position",
  "dob",
  "id_number",
  "id_type",
  "issue_date",
  "issue_place",
  "phone_number",
  "email",
  "married_status",
  "per_province_code",
  "per_district_code",
  "per_ward_code",
  "per_detail_address",
  "partner_full_name",
  "partner_id_number",
  "updated_at",
  "created_by",
  "updated_by",
  "is_deleted",
  "partner_dob",
  "partner_phone_number",
  "partner_per_province_code",
  "partner_per_district_code",
  "partner_per_ward_code",
  "partner_per_detail_address",
  "partner_per_new_province_code",
  "partner_per_new_ward_code",
  "partner_cur_province_code",
  "partner_cur_district_code",
  "partner_cur_ward_code",
  "partner_cur_detail_address",
  "partner_cur_new_province_code",
  "partner_cur_new_ward_code",
  "cur_province_code",
  "cur_district_code",
  "cur_ward_code",
  "cur_detail_address",
  "business_province_code",
  "business_district_code",
  "business_ward_code",
  "business_detail_address",
  "entity_type",
  "authorization_doc_no",
  "business_phone_number",
  "business_email",
  "business_new_province_code",
  "business_new_ward_code",
  "per_new_province_code",
  "per_new_ward_code",
  "cur_new_province_code",
  "cur_new_ward_code",
  "partner_issue_date",
  "partner_issue_place",
  "partner_email",
  "other_id_number"
];

const TABLE_NAME = "loan_business_owner";

const findByContractNumber = async (contractNumber) => {
  return snakeToCamel(await find({
    table: TABLE_NAME,
    whereCondition: {
      contract_number: contractNumber,
    },
  }));
};

const insert = async (contractNumber, data, masterdataDocuments) => {
  if (!data) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(
    [
      "contract_number",
      "subject",
      "tax_id",
      "company_name",
      "full_name",
      "position",
      "dob",
      "id_number",
      "id_type",
      "issue_date",
      "issue_place",
      "phone_number",
      "email",
      "business_phone_number",
      "business_email",
      "married_status",
      "per_province_code",
      "per_district_code",
      "per_ward_code",
      "per_detail_address",
      "partner_full_name",
      "partner_id_number",
      "created_by",
      "is_deleted",
      "partner_dob",
      "partner_phone_number",
      "partner_per_province_code",
      "partner_per_district_code",
      "partner_per_ward_code",
      "partner_per_detail_address",
      "partner_cur_province_code",
      "partner_cur_district_code",
      "partner_cur_ward_code",
      "partner_cur_detail_address",
      "cur_province_code",
      "cur_district_code",
      "cur_ward_code",
      "cur_detail_address",
      "business_province_code",
      "business_district_code",
      "business_ward_code",
      "business_detail_address",
      "business_new_province_code",
      "business_new_ward_code",
      "per_new_province_code",
      "per_new_ward_code",
      "cur_new_province_code",
      "cur_new_ward_code",
      "entity_type",
      "authorization_doc_no"
    ],
    { table: TABLE_NAME }
  );
  let insertData = [];
  insertData.push({
    contract_number: contractNumber,
    subject: data.subject,
    tax_id: data.taxId,
    company_name: data.companyName,
    full_name: data.fullName,
    position: data.position,
    dob: data.dob,
    id_number: data.idNumber,
    id_type: data.idType,
    issue_date: data.issueDate,
    issue_place: data.issuePlace,
    phone_number: data.phoneNumber,
    email: data.email,
    business_phone_number: data.businessPhoneNumber,
    business_email: data.businessEmail,
    married_status: data.marriedStatus,
    per_province_code: data.perProvinceCode,
    per_district_code: data.perDistrictCode,
    per_ward_code: data.perWardCode,
    per_detail_address: data.perDetailAddress,
    partner_full_name: data.partnerFullName,
    partner_id_number: data.partnerIdNumber,
    created_by: data.createdBy,
    partner_dob: data.partnerDob,
    partner_phone_number: data.partnerPhoneNumber,
    partner_per_province_code: data.partnerPerProvinceCode,
    partner_per_district_code: data.partnerPerDistrictCode,
    partner_per_ward_code: data.partnerPerWardCode,
    partner_per_detail_address: data.partnerPerDetailAddress,
    partner_cur_province_code: data.partnerCurProvinceCode,
    partner_cur_district_code: data.partnerCurDistrictCode,
    partner_cur_ward_code: data.partnerCurWardCode,
    partner_cur_detail_address: data.partnerCurDetailAddress,
    cur_province_code: data.curProvinceCode,
    cur_district_code: data.curDistrictCode,
    cur_ward_code: data.curWardCode,
    cur_detail_address: data.curDetailAddress,
    business_province_code: data.businessProvinceCode,
    business_district_code: data.businessDistrictCode,
    business_ward_code: data.businessWardCode,
    business_detail_address: data.businessDetailAddress,
    business_new_province_code: data.businessNewProvinceCode,
    business_new_ward_code: data.businessNewWardCode,
    per_new_province_code: data.perNewProvinceCode,
    per_new_ward_code: data.perNewWardCode,
    cur_new_province_code: data.curNewProvinceCode,
    cur_new_ward_code: data.curNewWardCode,
    entity_type: data.entityType,
    authorization_doc_no: data.authorizationDocNo,
    is_deleted: 0
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *`;
  const result = await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log(`INSERT - ${TABLE_NAME}: error`, contractNumber);
    });
  if (result?.rowCount > 0) {
    //save docs
    let insertDocData = [];
    if (data?.docs?.length > 0) {
      const businessOwner = result?.rows?.filter(
        (entity) =>
          (entity.subject === 'INDIVIDUAL' && entity.id_number === data.id) ||
          (entity.subject === 'ORGANIZATION' && entity.tax_id === data.taxId)
      );
      data.docs.forEach((doc) => {
        insertDocData.push({
          docId: doc.docId,
          docType: doc.docType,
          referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_BUSINESS_OWNER,
          referenceId: businessOwner[0].id
        });
      });
    }
    const resultUpdateDocs = await documentRepo.saveRequestDocuments({
      contractNumber: contractNumber,
      docList: utils.snakeToCamel(insertDocData),
      masterdataDocuments
    });
    return resultUpdateDocs;
  }
};

const findDocsById = async (id) => {
  try {
    if (!id) return [];
    const poolWrite = global.poolWrite;
    const sql = `SELECT * FROM business_owner_documents WHERE business_owner_id = $1`;
    const result = await poolWrite.query(sql, [id]);
    return result.rows;
  } catch (error) {
    console.error("Error fetching business owner documents:", error);
    return [];
  }
};

function mapValuesByColumns(rawData, columns) {
  const result = {};
  for (const col of columns) {
    result[col] = col in rawData ? rawData[col] : null;
  }
  return result;
}

const insertV2 = async (contractNumber, data, masterdataDocuments) => {
  if (!data) {
    return;
  }
  const poolWrite = global.poolWrite;

  const columnSet = new pgp.helpers.ColumnSet(columns, { table: TABLE_NAME });

  data.contract_number = contractNumber;
  data.is_deleted = 0;
  // Map dữ liệu sang đúng column
  const insertData = [mapValuesByColumns(data, columns)];
  const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *`;
  const result = await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log(`INSERT - ${TABLE_NAME}: error`, contractNumber);
    });
  if (result?.rowCount > 0) {
    //save docs
    let insertDocData = [];
    if (data?.docs?.length > 0) {
      const businessOwner = result?.rows?.filter(
        (entity) =>
          (entity.subject === 'INDIVIDUAL' && entity.id_number === data.id_number) ||
          (entity.subject === 'ORGANIZATION' && entity.tax_id === data.tax_id)
      );
      data.docs.forEach((doc) => {
        insertDocData.push({
          docId: doc.doc_id,
          docType: doc.doc_type,
          referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_BUSINESS_OWNER,
          referenceId: businessOwner[0].id
        });
      });
    }
    const resultUpdateDocs = await documentRepo.saveRequestDocuments({
      contractNumber: contractNumber,
      docList: utils.snakeToCamel(insertDocData),
      masterdataDocuments
    });
    return resultUpdateDocs;
  }
};

module.exports = {
  columns,
  TABLE_NAME,
  findByContractNumber,
  insert,
  findDocsById,
  insertV2
};
