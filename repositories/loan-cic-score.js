const common = require("../utils/common");
const { isNullOrEmpty } = require("../utils/helper");
const pgp = require('pg-promise')({capSQL: true});

async function saveCicScore(poolWrite,objectValue,contractNumber,monthlyPaymentAtEc,cicInstallment) {
	let insertData = []
	const columnSet = new pgp.helpers.ColumnSet(['contract_number', 'score_type','score_value'], {table: 'loan_cic_score'});
    const legalDebt = objectValue.legalDebt
    const personDebt = objectValue.personDebt
    insertData.push({contract_number: contractNumber,score_type: 'cardDebt2', score_value: legalDebt.cardDebt2},
                    {contract_number: contractNumber,score_type: 'longTermInterest', score_value: legalDebt.longTermInterest},
                    {contract_number: contractNumber,score_type: 'longTermPrincipalAndInterest', score_value: legalDebt.longTermPrincipalAndInterest},
                    {contract_number: contractNumber,score_type: 'mediumTermInterest', score_value: legalDebt.mediumTermInterest},
                    {contract_number: contractNumber,score_type: 'mediumTermPrincipalAndInterest', score_value: legalDebt.mediumTermPrincipalAndInterest},
                    {contract_number: contractNumber,score_type: 'shortTermInterest', score_value: legalDebt.shortTermInterest},
                    {contract_number: contractNumber,score_type: 'shortTermPrincipalAndInterest', score_value: legalDebt.shortTermPrincipalAndInterest},
                    {contract_number: contractNumber,score_type: 'cardDebt', score_value: personDebt.cardDebt},
                    {contract_number: contractNumber,score_type: 'creditMediumTerm', score_value: personDebt.creditMediumTerm},
                    {contract_number: contractNumber,score_type: 'debtGroup', score_value: personDebt.debtGroup},
                    {contract_number: contractNumber,score_type: 'longTerm', score_value: personDebt.longTerm},
                    {contract_number: contractNumber,score_type: 'mortgageMediumTerm', score_value: personDebt.mortgageMediumTerm},
                    {contract_number: contractNumber,score_type: 'otherDebt1', score_value: personDebt.otherDebt1},
                    {contract_number: contractNumber,score_type: 'shortTerm', score_value: personDebt.shortTerm},
                    {contract_number: contractNumber,score_type: 'monthlyPaymentAtEc', score_value: monthlyPaymentAtEc},
                    {contract_number: contractNumber,score_type: 'cicInstallment', score_value: cicInstallment})

	const insertQuery = pgp.helpers.insert(insertData, columnSet)
	return await poolWrite.query(insertQuery).then().catch(error => common.log("INSERT - loan_cic_score: error","ERROR"))
}

async function getCicScore(poolRead,contractNumber) {
    try {
        if(contractNumber == undefined) return null
        let sql = "select * from loan_cic_score where 1=1 and contract_number = $1";
        const rs = await poolRead.query(sql,[contractNumber])
        return rs || null
    }
    catch(err) {
        common.log(`get data entry error : ${err.message}`)
    }
}

async function updateCicScore(poolWrite,contractNumber,data,monthlyPaymentAtEc,cicInstallment) {
    try {
        // console.log('contractNumber',contractNumber)
        // console.log('dataUpdate',data)
        let personDebt = data.personDebt
        let legalDebt = data.legalDebt
        const promiseList = []
        const sql = `UPDATE loan_cic_score
        SET score_value=$1, updated_date=now() where contract_number=$2 and score_type=$3;`
        promiseList.push(poolWrite.query(sql,[personDebt.shortTerm,contractNumber,'shortTerm']),
                    poolWrite.query(sql,[personDebt.mortgageMediumTerm,contractNumber,'mortgageMediumTerm']),
                    poolWrite.query(sql,[personDebt.creditMediumTerm,contractNumber,'creditMediumTerm']),
                    poolWrite.query(sql,[personDebt.longTerm,contractNumber,'longTerm']),
                    poolWrite.query(sql,[personDebt.cardDebt,contractNumber,'cardDebt']),
                    poolWrite.query(sql,[personDebt.otherDebt1,contractNumber,'otherDebt1']),
                    poolWrite.query(sql,[personDebt.debtGroup,contractNumber,'debtGroup']),
                    poolWrite.query(sql,[legalDebt.shortTermInterest,contractNumber,'shortTermInterest']),
                    poolWrite.query(sql,[legalDebt.mediumTermInterest,contractNumber,'mediumTermInterest']),
                    poolWrite.query(sql,[legalDebt.longTermInterest,contractNumber,'longTermInterest']),
                    poolWrite.query(sql,[legalDebt.shortTermPrincipalAndInterest,contractNumber,'shortTermPrincipalAndInterest']),
                    poolWrite.query(sql,[legalDebt.mediumTermPrincipalAndInterest,contractNumber,'mediumTermPrincipalAndInterest']),
                    poolWrite.query(sql,[legalDebt.cardDebt2,contractNumber,'cardDebt2']),
                    poolWrite.query(sql,[legalDebt.longTermPrincipalAndInterest,contractNumber,'longTermPrincipalAndInterest']),
                    poolWrite.query(sql,[monthlyPaymentAtEc,contractNumber,'monthlyPaymentAtEc']),
                    poolWrite.query(sql,[cicInstallment,contractNumber,'cicInstallment']))
        return await Promise.all(promiseList)
    } catch (error) {
        common.log(`update cic score error : ${error.message}`)
    }
}

async function getCicScoreV2(contractNumber) {
    try {
        if(isNullOrEmpty(contractNumber)) return null
        let sql = "select * from loan_cic_score where 1=1 and contract_number = $1 and is_delt = 0";
        const rs = (await global.poolRead.query(sql,[contractNumber])).rows
        let obj = {}
        rs.map(dp => {
            obj[dp.score_type] = dp.score_value
        })
        return obj
    }
    catch(err) {
        common.log(`get cic score error : ${err.message}`)
    }
}

module.exports = {
    saveCicScore,
    getCicScore,
    updateCicScore,
    getCicScoreV2
}

        