const common = require("../utils/common");
const { requestStatus } = require("../const/updateRequest");
const {isNullOrEmpty} = require("../utils/helper");
const dateHelper = require("../utils/dateHelper");
const pgp = require('pg-promise')({
  capSQL: true
});

async function getNotUpdateContractAndCount({limit, offset, search}) {
  try {
    const poolWrite = global.poolWrite;

    let index = 1;
    let arrParams = [];
    let countArrParams = [];

    let sql =
      "select lc.channel, lc.partner_code, lc.contract_number, 'MCC' as system_type, lc.contract_type, lc.sme_name, lc.registration_number, lc.tax_id, lc.id_number, lc.first_registration_date as registration_date, lc.tax_date, COALESCE(lcr.lcr, '[]') AS loan_customer_representations, COALESCE(lcm.lcm, '[]') AS loan_customer_managers " +
        "from loan_contract lc left join loan_contract_update_requests lcur on lc.contract_number = lcur.contract_number and lcur.is_deleted = 0 " +
        "left join lateral( " +
        " SELECT json_agg(lcr) AS lcr" +
        " FROM loan_customer_representations lcr " +
        " WHERE lc.contract_number = lcr.contract_number and is_deleted = 0) lcr on true " +
        "left join lateral( " +
        " SELECT json_agg(lcm) AS lcm" +
        " FROM loan_customer_managers lcm " +
        " WHERE lc.contract_number = lcm.contract_number and is_deleted = 0) lcm on true " +
        "where lcur.contract_number is null ";

    let countSql =
        "select count(lc.id) from loan_contract lc left join loan_contract_update_requests lcur on lc.contract_number = lcur.contract_number and lcur.is_deleted = 0 where lcur.contract_number is null ";

    if (search) {
      sql += ` and (lc.channel like $${index} 
       or lc.partner_code like $${index} 
       or lc.contract_number like $${index} 
       or lc.sme_name like $${index} 
       or lc.registration_number like $${index} 
       or lc.tax_id like $${index} 
       or lc.id_number like $${index}) `

      countSql += ` and (lc.channel like $${index} 
       or lc.partner_code like $${index}
       or lc.contract_number like $${index}
       or lc.sme_name like $${index}
       or lc.registration_number like $${index}
       or lc.tax_id like $${index}
       or lc.id_number like $${index}) `

      arrParams.push(`%${search}%`)
      countArrParams.push(`%${search}%`)
      index++;
    }

    sql += `ORDER BY lc.id DESC LIMIT $${index++} OFFSET $${index++}`
    arrParams.push(limit, offset);

    const [result, countResult] = await Promise.all([
      poolWrite.query(sql, arrParams),
      poolWrite.query(countSql, countArrParams),
    ])

    return {
      'dataList': result.rows,
      'totalItems': countResult.rows[0]?.count
    }
  } catch (err) {
    common.log("get loan contract not update error ", err);
    return {
      'dataList': [],
      'totalItems': 0
    }
  }
}

async function getUpdateRequestAndCount({status, search, limit, offset}) {
  try {
    const poolWrite = global.poolWrite;

    let index = 1;
    let arrParams = [];
    let countArrParams = [];

    let sql =
      "select lcur.id, lcur.contract_number, lcur.channel, 'MCC' as system_type, lc.contract_type, lcur.partner_code, lcur.sme_name, lcur.registration_number," +
        " TO_CHAR(lcur.registration_date, 'YYYY-MM-DD') as registration_date, lcur.tax_id, TO_CHAR(lcur.tax_date, 'YYYY-MM-DD') as tax_date, lcur.id_number, lcur.status, lcur.reject_reason, COALESCE(lcrur.lcrur, '[]') AS loan_customer_representations, COALESCE(lcmur.lcmur, '[]') AS loan_customer_managers from loan_contract_update_requests lcur " +
        "left join lateral( " +
        " SELECT json_agg(lcrur) AS lcrur" +
        " FROM loan_customer_representation_update_requests lcrur " +
        " WHERE lcur.id = lcrur.update_request_id and is_deleted = 0) lcrur on true " +
        "left join lateral( " +
        " SELECT json_agg(lcmur) AS lcmur" +
        " FROM loan_customer_manager_update_requests lcmur " +
        " WHERE lcur.id = lcmur.update_request_id and is_deleted = 0) lcmur on true " +
        " left join loan_contract lc on lc.contract_number = lcur.contract_number " +
        "where lcur.is_deleted = 0 ";

    let countSql =
        "select count(id) from loan_contract_update_requests lcur where is_deleted = 0 ";

    if (status && status !== 'all') {
      sql += `AND lcur.status = $${index} `
      countSql += `AND lcur.status = $${index} `
      arrParams.push(status)
      countArrParams.push(status)
      index++
    }

    if (search) {
      sql += ` and (lcur.channel like $${index} 
       or lcur.partner_code like $${index} 
       or lcur.contract_number like $${index} 
       or lcur.sme_name like $${index} 
       or lcur.registration_number like $${index} 
       or lcur.tax_id like $${index} 
       or lcur.id_number like $${index}) `

      countSql += ` and (lcur.channel like $${index} 
       or lcur.partner_code like $${index}
       or lcur.contract_number like $${index}
       or lcur.sme_name like $${index}
       or lcur.registration_number like $${index}
       or lcur.tax_id like $${index}
       or lcur.id_number like $${index}) `

      arrParams.push(`%${search}%`)
      countArrParams.push(`%${search}%`)
      index++;
    }

    sql += `ORDER BY lcur.id DESC LIMIT $${index++} OFFSET $${index++}`
    arrParams.push(limit, offset);

    const [result, countResult] = await Promise.all([
      poolWrite.query(sql, arrParams),
      poolWrite.query(countSql, countArrParams),
    ])

    return {
      'dataList': result.rows,
      'totalItems': countResult.rows[0]?.count
    }
  } catch (err) {
    common.log("get loan contract update request error ", err);
    return {
      'dataList': [],
      'totalItems': 0
    }
  }
}

async function getPendingUpdateRequestByContractNumber({contract_number}) {
  try {
    let sql =
      "select id, contract_number from loan_contract_update_requests where is_deleted = 0 and contract_number = $1 and status = any($2)";

    const result = await poolWrite.query(sql, [
        contract_number,
      [requestStatus.DRAFT,requestStatus.WAITING_APPROVE]
    ]);

    if (result.rows.length === 0) {
      return null
    }

    return result.rows[0];
  } catch (err) {
    common.log("get pending loan contract update request error ", err);
    return null
  }
}

async function findUpdateRequestById({id}) {
  try {
    let sql =
      "select id, contract_number, status, is_deleted from loan_contract_update_requests where id = $1";

    const result = await poolWrite.query(sql, [
      id,
    ]);

    if (result.rows.length === 0) {
      return null
    }

    return result.rows[0];
  } catch (err) {
    common.log("find loan contract update request by id error ", err);
    return null
  }
}

async function findUpdateRequestDetailById({id}) {
  try {
    let sql =
        "select lcur.contract_number, lcur.registration_number," +
        " TO_CHAR(lcur.registration_date, 'YYYY-MM-DD') as registration_date, lcur.tax_id, TO_CHAR(lcur.tax_date, 'YYYY-MM-DD') as tax_date, lcur.id_number, lcur.reject_reason, COALESCE(lcrur.lcrur, '[]') AS loan_customer_representations, COALESCE(lcmur.lcmur, '[]') AS loan_customer_managers from loan_contract_update_requests lcur " +
        "left join lateral( " +
        " SELECT json_agg(lcrur) AS lcrur" +
        " FROM loan_customer_representation_update_requests lcrur " +
        " WHERE lcur.id = lcrur.update_request_id and is_deleted = 0) lcrur on true " +
        "left join lateral( " +
        " SELECT json_agg(lcmur) AS lcmur" +
        " FROM loan_customer_manager_update_requests lcmur " +
        " WHERE lcur.id = lcmur.update_request_id and is_deleted = 0) lcmur on true " +
        "where lcur.is_deleted = 0 and id = $1 ";

    const result = await poolWrite.query(sql, [
      id,
    ]);

    if (result.rows.length === 0) {
      return null
    }

    return result.rows[0];
  } catch (err) {
    common.log("find loan contract update request detail by id error ", err);
    return null
  }
}

async function deleteUpdateRequestById({id, username}) {
  try {
    let sql =
        "update loan_contract_update_requests set is_deleted = 1, deleted_by = $2 where id = $1 and is_deleted = 0";

    let updateRs = await poolWrite.query(sql, [
      id, username
    ]);

    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("delete loan contract update request by id error ", err);
    return null
  }
}

async function deleteUpdateRequestRepresentationsByRequestId({update_request_id, username}) {
  try {
    let sql =
        "update loan_customer_representation_update_requests set is_deleted = 1, deleted_by = $2 where update_request_id = $1 and is_deleted = 0";

    let updateRs = await poolWrite.query(sql, [
      update_request_id, username
    ]);

    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("delete loan customer representation update request by id error ", err);
    return null
  }
}

async function deleteUpdateRequestManagersByRequestId({update_request_id, username}) {
  try {
    let sql =
        "update loan_customer_manager_update_requests set is_deleted = 1, deleted_by = $2 where update_request_id = $1 and is_deleted = 0";

    let updateRs = await poolWrite.query(sql, [
      update_request_id, username
    ]);

    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("delete loan customer manager update request by id error ", err);
    return null
  }
}

async function updateUpdateRequestById({id, data}) {
  try {
    const poolWrite = global.poolWrite
    const columnSet = new pgp.helpers.ColumnSet([
          'registration_number', 'registration_date',
          'tax_id', 'tax_date', 'id_number',
        ]
        , { table: 'loan_contract_update_requests' }
    );
    let updateData = {
      registration_number: data.registration_number,
      registration_date: data.registration_date,
      tax_id: data.tax_id,
      tax_date: data.tax_date,
      id_number: data.id_number,
    }

    const insertQuery = pgp.helpers.update(updateData, columnSet) + ` where id = $1;`

    let updateRs = await poolWrite.query(insertQuery, [id]);
    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("update loan contract update request error ", err);
    return null;
  }
}

async function requestUpdateRequestById({id, username}) {
  try {
    let sql =
        "update loan_contract_update_requests set status = $2, requested_by = $3 where id = $1";

    let updateRs = await poolWrite.query(sql, [
      id, requestStatus.WAITING_APPROVE, username
    ]);

    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("request loan contract update request by id error ", err);
    return null
  }
}

async function approveUpdateRequestById({id, username}) {
  try {
    let sql =
        "update loan_contract_update_requests set status = $2, approved_by = $3 where id = $1";

    let updateRs = await poolWrite.query(sql, [
      id, requestStatus.APPROVED, username
    ]);

    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("approve loan contract update request by id error ", err);
    return null
  }
}

async function rejectUpdateRequestById({id, username, reject_reason}) {
  try {
    let sql =
        "update loan_contract_update_requests set status = $2, rejected_by = $3, reject_reason = $4 where id = $1";

    let updateRs = await poolWrite.query(sql, [
      id, requestStatus.REJECTED, username, reject_reason
    ]);

    if (updateRs.rowCount == 0) {
      return false
    }
    return true
  } catch (err) {
    common.log("reject loan contract update request by id error ", err);
    return null
  }
}

async function createUpdateRequest({data}) {
  try {
    const poolWrite = global.poolWrite
    const columnSet = new pgp.helpers.ColumnSet([
          'contract_number', 'channel', 'partner_code',
          'sme_name', 'registration_number', 'registration_date',
          'tax_id', 'tax_date', 'id_number', 'requested_by', 'status'
        ]
        , { table: 'loan_contract_update_requests' }
    );
    let insertData = {
      contract_number: data.contract_number,
      channel: data.channel,
      partner_code: data.partner_code,
      sme_name: data.sme_name,
      registration_number: data.registration_number,
      registration_date: data.registration_date,
      tax_id: data.tax_id,
      tax_date: data.tax_date,
      id_number: data.id_number,
      status: data.status,
      requested_by: data.requested_by,
    }

    const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`

    return await poolWrite.query(insertQuery).then(result => result?.rows[0]).catch(error => {
      console.log(error)
      common.log("INSERT - createUpdateRequest: error", '')
    })
  } catch (err) {
    common.log("create loan contract update request error ", err);
    return null;
  }
}

async function createUpdateRequestRepresentations({updateRequestId, data}) {
  try {
    const poolWrite = global.poolWrite
    const columnSet = new pgp.helpers.ColumnSet([
          'update_request_id', 'registration_number', 'full_name',
          'position', 'dob', 'id_number', 'id_type',
          'issue_date', 'issue_place', 'phone_number',
          'email', 'created_by',
          'management_experience', 'per_province_code',
          'per_district_code', 'per_ward_code',
          'per_detail_address', 'gender'
        ]
        , { table: 'loan_customer_representation_update_requests' }
    );
    let insertData = []
    data.forEach(e => {
      insertData.push({
        update_request_id: updateRequestId,
        registration_number: e.registration_number,
        full_name: e.full_name,
        position: e.position,
        dob: e.dob,
        id_number: e.id_number,
        id_type: e.id_type,
        issue_date: e.issue_date,
        issue_place: e.issue_place,
        phone_number: e.phone_number,
        email: e.email,
        created_by: null,
        management_experience: e.management_experience,
        per_province_code: e?.per_province_code,
        per_district_code: e?.per_district_code,
        per_ward_code: e?.per_ward_code,
        per_detail_address: e?.per_detail_address,
        gender: e.gender
      })
    })
    const insertQuery = pgp.helpers.insert(insertData, columnSet)
    return await poolWrite.query(insertQuery).then().catch(error => {
      console.log(error)
      common.log("INSERT - createUpdateRequestRepresentations: error", '')
    })
  } catch (err) {
    common.log("create loan customer representation update request error ", err);
    return null;
  }
}

async function createUpdateRequestManagers({updateRequestId, data}) {
  try {
    const poolWrite = global.poolWrite
    const columnSet = new pgp.helpers.ColumnSet([
          'update_request_id', 'registration_number', 'full_name',
          'position', 'dob', 'id_number', 'id_type',
          'issue_date', 'issue_place', 'phone_number',
          'email', 'created_by',
          'management_experience', 'per_province_code',
          'per_district_code', 'per_ward_code',
          'per_detail_address', 'gender'
        ]
        , { table: 'loan_customer_manager_update_requests' }
    );
    let insertData = []
    data.forEach(e => {
      insertData.push({
        update_request_id: updateRequestId,
        registration_number: e.registration_number,
        full_name: e.full_name,
        position: e.position,
        dob: e.dob,
        id_number: e.id_number,
        id_type: e.id_type,
        issue_date: e.issue_date,
        issue_place: e.issue_place,
        phone_number: e.phone_number,
        email: e.email,
        created_by: null,
        management_experience: e.management_experience,
        per_province_code: e?.per_province_code,
        per_district_code: e?.per_district_code,
        per_ward_code: e?.per_ward_code,
        per_detail_address: e?.per_detail_address,
        gender: e.gender
      })
    })
    const insertQuery = pgp.helpers.insert(insertData, columnSet)
    return await poolWrite.query(insertQuery).then().catch(error => {
      console.log(error)
      common.log("INSERT - createUpdateRequestManagers: error", '')
    })
  } catch (err) {
    common.log("create loan customer manager update request error ", err);
    return null;
  }
}

module.exports = {
  getNotUpdateContractAndCount,
  getUpdateRequestAndCount,
  createUpdateRequest,
  getPendingUpdateRequestByContractNumber,
  createUpdateRequestRepresentations,
  createUpdateRequestManagers,
  findUpdateRequestById,
  findUpdateRequestDetailById,
  deleteUpdateRequestById,
  deleteUpdateRequestRepresentationsByRequestId,
  deleteUpdateRequestManagersByRequestId,
  approveUpdateRequestById,
  requestUpdateRequestById,
  rejectUpdateRequestById,
  updateUpdateRequestById,
};
