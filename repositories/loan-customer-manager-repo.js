const { snakeToCamel } = require("../utils/helper");
const { find } = require("../utils/sqlHelper");
const pgp = require("pg-promise")({
  capSQL: true,
});
const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");

const TABLE_NAME = "loan_customer_managers";

const columns = [
  "contract_number",
  "full_name",
  "position",
  "dob",
  "id_number",
  "id_type",
  "issue_date",
  "issue_place",
  "phone_number",
  "email",
  "created_by",
  "management_experience",
  "gender",
  "per_province_code",
  "per_district_code",
  "per_ward_code",
  "per_detail_address",
  "cur_province_code",
  "cur_district_code",
  "cur_ward_code",
  "cur_detail_address"
];

const findByContractNumber = async (contractNumber) => {
  return snakeToCamel(await find({
    table: TABLE_NAME,
    whereCondition: {
      contract_number: contractNumber,
    },
  }));
};

const insert = async (contractNumber, managers, masterdataDocuments) => {
  if (!managers || managers.length == 0) {
    return;
  }

  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(
    [
      "contract_number",
      "full_name",
      "position",
      "dob",
      "id_number",
      "id_type",
      "issue_date",
      "issue_place",
      "phone_number",
      "email",
      "created_by",
      "management_experience",
      "gender",
      "per_province_code",
      "per_district_code",
      "per_ward_code",
      "per_detail_address",
      "cur_province_code",
      "cur_district_code",
      "cur_ward_code",
      "cur_detail_address",
      "per_new_province_code",
      "per_new_ward_code",
      "cur_new_province_code",
      "cur_new_ward_code"
    ], { table: TABLE_NAME });
  let insertData = [];
  managers.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      full_name: e.fullName,
      position: e.position,
      dob: e.dob,
      id_number: e.id,
      id_type: e.identityType,
      issue_date: e.issueDate,
      issue_place: e.issuePlace,
      phone_number: e.phoneNumber,
      email: e.email,
      created_by: null,
      management_experience: e.managementExperience,
      per_province_code: null,
      per_district_code: null,
      per_ward_code: null,
      per_detail_address: e?.perAddress?.detail,
      cur_province_code: null,
      cur_district_code: null,
      cur_ward_code: null,
      cur_detail_address: e?.curAddress?.detail,
      gender: e.gender,
      per_new_province_code: e?.perAddress?.provinceCode,
      per_new_ward_code: e?.perAddress?.wardCode,
      cur_new_province_code: e?.curAddress?.provinceCode,
      cur_new_ward_code: e?.curAddress?.wardCode
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *`;
  const result = await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log(`INSERT - ${TABLE_NAME}: error`, contractNumber);
    });
  if (result?.rowCount > 0) {
    //save docs
    let insertDocData = [];
    managers.forEach((e) => {
      if (e?.docs?.length > 0) {
        const manager = result?.rows?.filter((entity) => entity.id_number == e.id);
        e.docs.forEach((doc) => {
          insertDocData.push({
            docId: doc.docId,
            docType: doc.docType,
            referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_MANAGERS,
            referenceId: manager[0].id
          });
        });
      }
    });

    const resultUpdateDocs = await documentRepo.saveRequestDocuments({
      contractNumber: contractNumber,
      docList: utils.snakeToCamel(insertDocData),
      masterdataDocuments
    });
    return resultUpdateDocs;
  }
};

module.exports = {
  TABLE_NAME,
  findByContractNumber,
  insert
};
