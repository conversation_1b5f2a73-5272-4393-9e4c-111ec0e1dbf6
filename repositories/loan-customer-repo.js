const common = require("../utils/common");

const columns = [
  "contract_number",
  "company_name",
  "registration_number",
  "address_on_license",
  "is_change",
  "business_license_url",
  "updated_at",
  "created_by",
  "updated_by",
  "is_deleted",
  "registration_date",
  "business_type",
  "phone_number",
  "tax_id",
  "province_on_license",
  "district_on_license",
  "ward_on_license",
  "detail_on_license",
  "old_info",
  "email",
  "sbv_sector",
  "new_province_on_license",
  "new_ward_on_license",
  "full_name",
  "id_number",
  "dob",
  "gender",
  "issue_date",
  "issue_place",
  "married_status",
  "partner_full_name",
  "partner_phone_number",
  "partner_id_number",
  "partner_per_province_code",
  "partner_per_ward_code",
  "partner_per_detail_address",
  "partner_cur_province_code",
  "partner_cur_ward_code",
  "partner_cur_detail_address",
  "partner_per_new_province_code",
  "partner_per_new_ward_code"
];

async function getLoanCustomer(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql =
      "select * from loan_customer where contract_number = $1 and ( is_deleted = 0 or is_deleted is null)";
    const result = await poolWrite.query(sql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loan contract info error ", contractNumber);
    return false;
  }
}

module.exports = {
  columns,
  getLoanCustomer,
};
