const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");
const pgp = require("pg-promise")({
  capSQL: true,
});

const columns = [
    "contract_number",
    "registration_number",
    "full_name",
    "position",
    "dob",
    "id_number",
    "id_type",
    "issue_date",
    "issue_place",
    "phone_number",
    "email",
    "updated_at",
    "created_by",
    "updated_by",
    "is_deleted",
    "management_experience",
    "per_province_code",
    "per_district_code",
    "per_ward_code",
    "per_detail_address",
    "per_new_province_code",
    "per_new_ward_code",
    "gender"
];

async function getRepresentationsByCustomer(contractNumber) {
    try {
        const poolWrite = global.poolWrite;
        const sql =
            "select * from loan_customer_representations where contract_number = $1 and is_deleted = 0 order by id";
        const result = await poolWrite.query(sql, [contractNumber]);
        if (result.rowCount === 0) {
            return [];
        }
        return result.rows;
    } catch (err) {
        common.log("get representations by customer error", contractNumber);
        return [];
    }
}

const insert = async (contractNumber, registrationNumber, representations, masterdataDocuments) => {
    const poolWrite = global.poolWrite;
    const columnSet = new pgp.helpers.ColumnSet(
        [
            "contract_number",
            "registration_number",
            "full_name",
            "position",
            "dob",
            "id_number",
            "id_type",
            "issue_date",
            "issue_place",
            "phone_number",
            "email",
            "created_by",
            "is_deleted",
            "management_experience",
            "per_province_code",
            "per_district_code",
            "per_ward_code",
            "per_detail_address",
            "gender",
            "per_new_province_code",
            "per_new_ward_code"
        ], 
        { table: "loan_customer_representations" }
    );
    let insertData = [];
    representations.forEach((e) => {
        insertData.push({
            contract_number: contractNumber,
            registration_number: registrationNumber,
            full_name: e.fullName,
            position: e.position,
            dob: e.dob,
            id_number: e.id,
            id_type: e.identityType,
            issue_date: e.issueDate,
            issue_place: e.issuePlace,
            phone_number: e.phoneNumber,
            email: e.email,
            created_by: null,
            is_deleted: 0,
            management_experience: e.managementExperience,
            per_province_code: null,
            per_district_code: null,
            per_ward_code: null,
            per_detail_address: e?.perAddress?.detail,
            gender: e.gender,
            updated_at: null,
            per_new_province_code: e?.perAddress?.provinceCode,
            per_new_ward_code: e?.perAddress?.wardCode,
        });
    });
    const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *`;
    const result = await poolWrite
        .query(insertQuery)
        .then()
        .catch((error) => {
            console.log(error);
            common.log("INSERT - loan_customer_representations: error", contractNumber);
        });
    if (result?.rowCount > 0) {
        //save docs
        let insertDocData = [];
        representations.forEach((e) => {
            if (e?.docs?.length > 0) {
                const rep = result?.rows?.filter((entity) => entity.id_number == e.id);
                e.docs.forEach((doc) => {
                    insertDocData.push({
                        docId: doc.docId,
                        docType: doc.docType,
                        referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_REPRESENTATIONS,
                        referenceId: rep[0].id
                    });
                });
            }
        });

        const resultUpdateDocs = await documentRepo.saveRequestDocuments({
            contractNumber: contractNumber,
            docList: utils.snakeToCamel(insertDocData),
            masterdataDocuments
        });
        return resultUpdateDocs;
    }
};

const findDocsByRepIds = async (ids) => {
    try {
        if (!ids || ids.length === 0) return [];
        const poolWrite = global.poolWrite;
        const sql = `SELECT * FROM loan_rep_documents WHERE loan_rep_id = ANY($1)`;
        const result = await poolWrite.query(sql, [ids]);
        return result.rows;
    } catch (error) {
        console.error("Error fetching representation documents:", error);
        return [];
    }
};

module.exports = {
    columns,
    getRepresentationsByCustomer,
    insert,
    findDocsByRepIds,
}