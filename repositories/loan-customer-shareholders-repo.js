const { snakeToCamel } = require("../utils/helper");
const { find } = require("../utils/sqlHelper");
const pgp = require("pg-promise")({
  capSQL: true,
});
const documentRepo = require("./document");
const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const utils = require("../utils/helper");

const TABLE_NAME = "loan_customer_shareholders";

const columns = [
  "contract_number",
  "subject",
  "company_name",
  "full_name",
  "tax_id",
  "position",
  "capital_contribution_ratio",
  "id_number",
  "id_type",
  "issue_date",
  "issue_place",
  "phone_number",
  "email",
  "per_province_code",
  "per_district_code",
  "per_ward_code",
  "per_detail_address",
  "cur_province_code",
  "cur_district_code",
  "cur_ward_code",
  "cur_detail_address",
  "gender",
  "dob",
  "business_province_code",
  "business_district_code",
  "business_ward_code",
  "business_detail_address",
  "per_new_province_code",
  "per_new_ward_code",
  "cur_new_province_code",
  "cur_new_ward_code",
  "business_new_province_code",
  "business_new_ward_code",
  "entity_type",
  "authorization_doc_no",
  "business_phone_number",
  "business_email"
];

const findByContractNumber = async (contractNumber) => {
  return snakeToCamel(await find({
    table: TABLE_NAME,
    whereCondition: {
      contract_number: contractNumber,
    },
    orderBy: { id: "asc" },
  }));
};

const insert = async (contractNumber, shareholders, masterdataDocuments) => {
  if (!shareholders || shareholders?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(
    columns, { table: TABLE_NAME }
  );
  let insertData = [];
  shareholders.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      subject: e.subject,
      company_name: e.companyName,
      full_name: e.fullName,
      tax_id: e.taxId,
      position: e.position,
      capital_contribution_ratio: e.capitalContributionRatio,
      id_number: e.id,
      id_type: e.identityType,
      issue_date: e.issueDate,
      issue_place: e.issuePlace,
      phone_number: e.phoneNumber,
      email: e.email,
      business_phone_number: e.businessPhoneNumber,
      business_email: e.businessEmail,
      per_province_code: null,
      per_district_code: null,
      per_ward_code: null,
      per_detail_address: e.perDetailAddress,
      cur_province_code: null,
      cur_district_code: null,
      cur_ward_code: null,
      cur_detail_address: e.curDetailAddress,
      business_province_code: null,
      business_district_code: null,
      business_ward_code: null,
      business_detail_address: e.businessDetailAddress,
      per_new_province_code: e.perProvinceCode,
      per_new_ward_code: e.perWardCode,
      cur_new_province_code: e.curProvinceCode,
      cur_new_ward_code: e.curWardCode,
      business_new_province_code: e.businessProvinceCode,
      business_new_ward_code: e.businessWardCode,
      entity_type: e.entity_type,
      authorization_doc_no: e.authorization_doc_number,
      gender: e.gender,
      dob: e.dob,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *`;
  const result = await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_customer_shareholders: error", contractNumber);
    });
  if (result?.rowCount > 0) {
    //save docs
    let insertDocData = [];
    shareholders.forEach((e) => {
      if (e?.docs?.length > 0) {
        const shareholder = result?.rows?.filter(
          (entity) =>
            (entity.subject === 'INDIVIDUAL' && entity.id_number === e.id) ||
            (entity.subject === 'ORGANIZATION' && entity.tax_id === e.taxId)
        );
        e.docs.forEach((doc) => {
          insertDocData.push({
            docId: doc.docId,
            docType: doc.docType,
            referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_SHAREHOLDERS,
            referenceId: shareholder[0].id
          });
        });
      }
    });
    const resultUpdateDocs = await documentRepo.saveRequestDocuments({
      contractNumber: contractNumber,
      docList: utils.snakeToCamel(insertDocData),
      masterdataDocuments
    });
    return resultUpdateDocs;
  }
};

const findDocsByShareholderIds = async (ids) => {
  try {
    if (!ids || ids.length === 0) return [];
    const poolWrite = global.poolWrite;
    const sql = `SELECT * FROM loan_shareholder_documents WHERE shareholder_id = ANY($1)`;
    const result = await poolWrite.query(sql, [ids]);
    return result.rows;
  } catch (error) {
    console.error("Error fetching shareholder documents:", error);
    return [];
  }
};

module.exports = {
  TABLE_NAME,
  columns,
  findByContractNumber,
  insert,
  findDocsByShareholderIds,
};
