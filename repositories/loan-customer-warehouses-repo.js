const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");
const pgp = require("pg-promise")({
    capSQL: true,
});

const insert = async (contractNumber, warehouses, masterdataDocuments) => {
    if (!warehouses || warehouses?.length === 0) {
        return;
    }
    const poolWrite = global.poolWrite;
    const columnSet = new pgp.helpers.ColumnSet([
        "contract_number",
        "warehouse_name",
        "province_code",
        "district_code",
        "ward_code",
        "detail_address",
        "new_province_code",
        "new_ward_code"
    ], { table: "loan_customer_warehouses" });
    let insertData = [];
    warehouses.forEach((e) => {
        insertData.push({
            contract_number: contractNumber,
            warehouse_name: e.warehouseName,
            province_code: e.provinceCode,
            district_code: e.districtCode,
            ward_code: e.wardCode,
            detail_address: e.detailAddress,
            new_province_code: e.newProvinceCode,
            new_ward_code: e.newWardCode,
        });
    });
    const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
    const result = await poolWrite
        .query(insertQuery)
        .then()
        .catch((error) => {
            console.log(error);
            common.log("INSERT - loan_customer_warehouses: error", contractNumber);
        });
    if (result?.rowCount > 0) {
        //save docs
        let insertDocData = [];
        warehouses.forEach((e) => {
            if (e?.docs?.length > 0) {
                console.log(`result: ${JSON.stringify(result)}`)
                console.log(` e: ${JSON.stringify( e)}`)
                const warehouse = result?.rows?.filter((entity) => entity.warehouse_name == e.warehouseName);
                if (warehouse?.length === 0) return;
                e.docs.forEach((doc) => {
                    insertDocData.push({
                        docId: doc.docId,
                        docType: doc.docType,
                        referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_WAREHOUSES,
                        referenceId: warehouse[0].id
                    });
                });
            }
        });
        const resultUpdateDocs = await documentRepo.saveRequestDocuments({
            contractNumber: contractNumber,
            docList: utils.snakeToCamel(insertDocData),
            masterdataDocuments
        });
        return resultUpdateDocs;
    }
};

module.exports = {
    insert
}