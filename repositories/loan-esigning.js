const common = require("../utils/common")

async function saveUnsignedContract(contractNumber,unsignedPath,unsignedKey) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_esigning (contract_number,status,unsigned_contract,unsigned_contract_key) values ($1,$2,$3,$4)"
        const saveUnsignedRs = await poolWrite.query(sql,[contractNumber,'NOT_SIGNED',unsignedPath,unsignedKey])
        if(saveUnsignedRs.rowCount == 0) {
            common.log(`save unsigned contract fail`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save unsigned contract fail : ${err.message}`,contractNumber)
        return false
    }
}

async function saveSignedContract( signedPath, contractNumber, status) {
    try {
        const poolWrite = global.poolWrite
        const sql = "update loan_esigning set contract_signed_path=$1,updated_date=now(),signing_date=now(),status=$2 where contract_number=$3"
        const rs = await poolWrite.query(sql, [signedPath, status || 'SIGNED', contractNumber])
        if(rs.rowCount == 0) {
            common.log(`save signed contract error `,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save signed contract error : ${err.message}`,contractNumber)
        return false
    }
}

async function getSingedContract(contractNumber) {
    try {
        const writePool = global.poolWrite
        const sql = 'select contract_signed_path from loan_esigning where contract_number = $1'
        const rs = await writePool.query(sql,[contractNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return rs.rows[0].contract_signed_path
    }
    catch(err) {
        common.log(`get signed contract err : ${err.message}`,contractNumber)
        return false
    }
}


const findOne = async (contractNumber) => {
    try {
        const writePool = global.poolWrite
        const sql = 'select * from loan_esigning where contract_number = $1'
        const rs = await writePool.query(sql, [contractNumber])
        return rs?.rows?.[0] ?? undefined
    } catch (err) {
        common.log(`loan_esigning findOne err : ${err.message}`, contractNumber)
        return false
    }
}

const findOneV2 = async ({ whereCondition, orderBy = {} }) => {
    try {
        const table = 'loan_esigning';
        const keys = Object.keys(whereCondition);
        const values = Object.values(whereCondition);

        const whereClauses = keys.map((key, i) => `${key} = $${i + 1}`).join(' AND ');

        const orderKeys = Object.keys(orderBy);
        const orderClauses = orderKeys.length > 0
            ? 'ORDER BY ' + orderKeys.map(key => `${key} ${orderBy[key]}`).join(', ')
            : '';

        const query = `
      SELECT *
      FROM ${table}
      WHERE ${whereClauses}
      ${orderClauses}
      LIMIT 1;
    `;

        const res = await global.poolWrite.query(query, values);
        return res?.rows[0] ?? {};
    } catch (e) {
        console.error(`FIND_ONE - ${table} error:`, e);
        return undefined;
    }
};

module.exports = {
    saveUnsignedContract,
    saveSignedContract,
    getSingedContract,
    findOne,
    findOneV2
}