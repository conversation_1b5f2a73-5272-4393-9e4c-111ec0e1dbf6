const { PARTNER_CODE } = require("../const/definition")
const masterdataService = require("../utils/masterdataService")

async function saveManualDecision(contractNumber,result_chk,assigne,role,taskId) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_manual_decision (contract_number,result_chk,created_date,assignee,role,step,task_id) values ($1,$2,now(),$3,$4,$4,$5)"
        return await poolWrite.query(sql,[contractNumber,result_chk,assigne,role,taskId])
    }
    catch(err) {
        console.log(err) 
        return false
    }
}

async function saveCaseWithReasonV2(taskId,contractNumber,userName,userRole,step,decision,listDecision) {
    try {
        const poolWrite = global.poolWrite
        let promiseQuery = []
        const sql = "insert into loan_manual_decision(task_id,contract_number,assignee,role,step,result_chk,doc_id,case_code,deviation_cmt,mistake_desc) values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)"
        
        const updateDocument = "update loan_contract_document set contract_number=$1 where doc_id=$2"
        listDecision.forEach(element => {
            const docId = element.docId
            const mistakeCode = element.mistakeCode
            const mistakeDes = element.mistakeDes
            const deviation_cmt = element.comment
            promiseQuery.push(poolWrite.query(sql,[taskId,contractNumber,userName,userRole,step,decision,docId,mistakeCode,deviation_cmt,mistakeDes]))
            promiseQuery.push(poolWrite.query(updateDocument,[contractNumber,docId]))
        })
        
        return await Promise.all(promiseQuery).then().catch(err => console.log(err))
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function saveCaseWithReason(taskId,contractNumber,userName,userRole,step,decision,listDecision) {
    try {
        const poolWrite = global.poolWrite
        let promiseQuery = []
        const sql = "insert into loan_manual_decision(task_id,contract_number,assignee,role,step,result_chk,doc_id,case_code,deviation_cmt,mistake_desc) values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)"
        
        const updateDocument = "update loan_contract_document set contract_number=$1 where doc_id=$2"
        listDecision.forEach(element => {
            const docId = element.docId
            const mistakeCode = element.mistakeCode
            const mistakeDes = element.mistakeDes
            const deviation_cmt = element.deviation_cmt
            promiseQuery.push(poolWrite.query(sql,[taskId,contractNumber,userName,userRole,step,decision,docId,mistakeCode,deviation_cmt,mistakeDes]))
            promiseQuery.push(poolWrite.query(updateDocument,[contractNumber,docId]))
        })
        
        return await Promise.all(promiseQuery).then().catch(err => console.log(err))
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function saveResubmit(taskId,contractNumber,userName,userRole,step,decision,listDecision) {
    try {
        const poolWrite = global.poolWrite
        // const poolRead = global.poolRead
        // let sqlTask = `select partner_code from task_aad_contract where task_id=$1;`
        // const taskData = await poolRead.query(sqlTask,[taskId])
        // const partnerCode = taskData.rows[0].partner_code;
        let promiseQuery = []
        const sql = "insert into loan_manual_decision(task_id,contract_number,assignee,role,step,result_chk,doc_id,case_code,deviation_cmt,mistake_desc) values($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)"
        const updateDocument = "update loan_contract_document set waiting_resubmit=1 where doc_id=$1"
        listDecision.forEach(async element => {
            const docId = element.docId
            const mistakeCode = element.mistakeCode
            let masterDataDesc = await masterdataService.getValueCode_v3(mistakeCode,'MISTAKE')
            if(!masterDataDesc) masterDataDesc = ''
            // const mistakeDes = partnerCode===PARTNER_CODE.MISA?element.comment:element.mistakeDes
            const mistakeDes = element.comment
            promiseQuery.push(poolWrite.query(sql,[taskId,contractNumber,userName,userRole,step,decision,docId,mistakeCode,mistakeDes,masterDataDesc]))
            promiseQuery.push(poolWrite.query(updateDocument,[docId]))
        })
        Promise.all(promiseQuery).then().catch(err => console.log(err))
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function saveDeviation(taskId,contractNumber,userName,userRole,step,decision,deviation) {
    try {
        // console.log('deviation',deviation)
        const poolWrite = global.poolWrite
        let promiseQuery = []
        const comment = deviation.comment
        const docList = deviation.documentList
        const updated_date = new Date()
        const sql = "insert into loan_manual_decision (task_id,contract_number,assignee,role,step,result_chk,doc_id,deviation_cmt) values($1,$2,$3,$4,$5,$6,$7,$8)"
        const updateDocument = "update loan_contract_document set contract_number=$1,updated_date=$2 where doc_id=$3"

        if(docList.length == 0) {
            promiseQuery.push(poolWrite.query(sql,[taskId,contractNumber,userName,userRole,step,decision,'',comment]))
        }
        else {
            docList.forEach(element => {
                const docId = element.docId
                promiseQuery.push(poolWrite.query(sql,[taskId,contractNumber,userName,userRole,step,decision,docId,comment]))
                promiseQuery.push(poolWrite.query(updateDocument,[contractNumber,updated_date,docId]))
            })
        }
        Promise.all(promiseQuery).then().catch(err => console.log(err))
    }
    catch(err) {
        console.log(err)
        return false
    }
}

module.exports = {
    saveManualDecision,
    saveCaseWithReason,
    saveResubmit,
    saveDeviation,
    saveCaseWithReasonV2
}