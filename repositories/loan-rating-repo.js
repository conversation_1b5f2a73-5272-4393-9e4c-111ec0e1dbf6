const columns = [
    "contract_number",
    "total_score",
    "rank",
    "max_limit_by_rank",
    "capital_needed_year_n_evnfc",
    "capital_needed_customer_declared",
    "approved_limit",
    "approved_interest_rate",
    "created_at",
    "updated_at",
    "created_by",
    "updated_by",
    "is_deleted",
    "long_term_working_capital",
    "max_limit_by_revenue",
    "decision",
    "description",
    "detail_data"
]

/**
 * get by contract_number
 */

const findByContractNumber = async (contractNumber) => {
    try {
        const poolWrite = global.poolWrite;
        const sql = "SELECT * FROM loan_rating WHERE contract_number = $1 AND (is_deleted = 0 OR is_deleted IS NULL)";
        const result = await poolWrite.query(sql, [contractNumber]);
        if (result.rowCount === 0) {
            return false;
        }
        return result.rows[0];
    } catch (err) {
        common.log("get loan rating error ", contractNumber);
        return false;
    }
}   

module.exports = {
    columns,
    findByContractNumber
}