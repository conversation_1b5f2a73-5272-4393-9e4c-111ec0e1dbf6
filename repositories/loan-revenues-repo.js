const { isNullOrEmpty, isNullOrEmptyV3 } = require("../utils/helper");
const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");
const pgp = require("pg-promise")({
  capSQL: true,
});

const columns = [

]

const findRevenueDocuments = async (contractNumber, isExport) => {
    try {
        const poolWrite = global.poolWrite
        const sql = `
        select rd.id as doc_id, *, lr.id from loan_revenues lr 
            inner join revenue_documents rd on lr.id = rd.loan_revenues_id 
        where lr.contract_number = $1 
            and rd.doc_type = 'SFSTD'
            ${isNullOrEmptyV3(isExport) ? '' : ' and rd.is_export = $2'}
        order by lr.year desc`;
        const params=[contractNumber]
        if(!isNullOrEmptyV3(isExport))
        {
            params.push(isExport ? true: false);
        }
        const result = await poolWrite.query(sql,params);
        return result?.rows ?? [];
    } catch (e) {
        console.error(e);
        return undefined;
    }
}

const updateFlagExport = async (id, isExport) => {
  try {
    const sql = `update revenue_documents set is_export = $1 where id = $2`;
    await poolWrite.query(sql, [isExport, id]);
    return true;
  } catch (error) {
    console.log(
      `[updateFlagExport] id ${id}, isExport: ${isExport}, error: ${error.message}`
    );
    return false;
  }
};

const findById = async (id) => {
    try {
        const poolWrite = global.poolWrite
        const sql = `select * from loan_revenues lr where id = $1 and is_deleted = 0`;
        const result = await poolWrite.query(sql,[id]);
        return result?.rows?.[0];
    } catch (e) {
        console.error(e);
        return undefined;
    }
}

const updateFinancialReportType = async (id, financialReportType) => {
    try {
        const sql = `update loan_revenues set financial_report_type = $1 where id = $2`;
        const result = await poolWrite.query(sql, [financialReportType, id]);
        if (result.rowCount === 0) {
            console.log(
                `[updateFinancialReportType] id ${id}, financialReportType: ${financialReportType}, error: update failed`
            );
            return false;
        }
        return true;
    } catch (error) {
        console.log(
            `[updateFinancialReportType] id ${id}, financialReportType: ${financialReportType}, error: ${error.message}`
        );
        return false;
    }
};

const insert = async (contractNumber, revenues, callback, masterdataDocuments) => {
    if (!revenues || revenues?.length === 0) {
        return;
    }
    const poolWrite = global.poolWrite;
    const columnSet = new pgp.helpers.ColumnSet(["contract_number", "year", "net_revenue", "total_assets", "financial_report_type"], { table: "loan_revenues" });
    let insertData = [];
    revenues.forEach((e) => {
        insertData.push({
            contract_number: contractNumber,
            year: e.year,
            net_revenue: e.netRevenue ? +e.netRevenue : null,
            total_assets: e.totalAssets ? +e.totalAssets : null,
            financial_report_type: e.financialReportType || null,
        });
    });
    const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
    const result = await poolWrite
        .query(insertQuery)
        .then()
        .catch((error) => {
            console.log(error);
            common.log("INSERT - loan_revenues: error", contractNumber);
        });
    if (result?.rowCount > 0) {
        //save docs
        const columnSet = new pgp.helpers.ColumnSet([
            "loan_revenues_id",
            "file_url",
            "file_type",
            "doc_type",
            "evf_file_url",
            "evf_doc_id"
        ], { table: "revenue_documents" });
        let insertData = [];
        let insertDocData = [];
        revenues.forEach((e) => {
            if (e?.financialReportDocs?.length > 0) {
                const loanRevenue = result?.rows?.filter((entity) => entity.year == e.year);
                e.financialReportDocs.forEach((doc) => {
                    insertData.push({
                        loan_revenues_id: loanRevenue[0].id,
                        file_url: doc.fileUrl,
                        file_type: doc.fileType,
                        doc_type: doc.docType,
                        evf_file_url: doc.evfFileUrl || null,
                        evf_doc_id: doc.docId || null
                    });
                    insertDocData.push({
                        docId: doc.docId,
                        docType: doc.docType,
                        referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_REVENUES,
                        referenceId: loanRevenue[0].id
                    });
                });
            }
        });
        const query = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
        await poolWrite
            .query(query)
            .then((results) => {
                if (callback) {
                    callback(snakeToCamel(results?.rows));
                }
            })
            .catch((error) => {
                console.log(error);
                common.log("INSERT - revenue_documents: error", contractNumber);
            });
        const resultUpdateDocs = await documentRepo.saveRequestDocuments({
            contractNumber: contractNumber,
            docList: utils.snakeToCamel(insertDocData),
            masterdataDocuments
        });
        return resultUpdateDocs;
    }
};

const findRevenueDocumentsByLoanRevenueIds = async (loanRevenueIds) => {
    if (!loanRevenueIds || !Array.isArray(loanRevenueIds) || loanRevenueIds.length === 0) {
        return [];
    }
    try {
        const poolWrite = global.poolWrite;
        const sql = `
            SELECT 
                * 
            FROM 
                revenue_documents
            WHERE 
                is_deleted = 0 
                AND loan_revenues_id = ANY($1::int[])
        `;
        const result = await poolWrite.query(sql, [loanRevenueIds]);
        return result?.rows ?? [];
    } catch (e) {
        console.error(e);
        return [];
    }
};

const softRemoveRevenueDocuments = async (revenueDocumentIds) => {
    if (!revenueDocumentIds || !Array.isArray(revenueDocumentIds) || revenueDocumentIds.length === 0) {
        return 0;
    }
    try {
        const poolWrite = global.poolWrite;
        const sql = `UPDATE revenue_documents SET is_deleted = 1 WHERE id = ANY($1::int[])`;
        const result = await poolWrite.query(sql, [revenueDocumentIds]);
        return result.rowCount || 0;
    } catch (e) {
        console.error(e);
        return 0;
    }
};

const softRemoveLoanRevenues = async (loanRevenueIds) => {
    if (!loanRevenueIds || !Array.isArray(loanRevenueIds) || loanRevenueIds.length === 0) {
        return 0;
    }
    try {
        const poolWrite = global.poolWrite;
        const sql = `UPDATE loan_revenues SET is_deleted = 1 WHERE id = ANY($1::int[])`;
        const result = await poolWrite.query(sql, [loanRevenueIds]);
        return result.rowCount || 0;
    } catch (e) {
        console.error(e);
        return 0;
    }
};

module.exports = {
    columns,
    findRevenueDocuments,
    updateFlagExport,
    findById,
    updateFinancialReportType,
    insert,
    findRevenueDocumentsByLoanRevenueIds,
    softRemoveRevenueDocuments,
    softRemoveLoanRevenues
}