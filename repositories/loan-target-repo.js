const common = require("../utils/common")
const moment = require('moment');

async function getFIByName() {
    try {
        const poolRead = global.poolRead
        const sql = "select distinct name_fi_code from loan_target lt order by lt.name_fi_code;"
        const resultFI = await poolRead.query(sql, [])
        if (resultFI.rowCount == 0) {
            common.log(`Not found data`)
            return null
        }
        return resultFI.rows
    }
    catch (err) {
        common.log(`getFIByName fail : ${err.message}`)
        return null
    }
}

async function getOptionsFI(nameFICode) {
    try {
        const poolRead = global.poolRead
        const sql = "select lt.recipe, lt.option_fi_code from loan_target lt where lt.name_fi_code = $1 order by lt.option_fi_code"
        const resultFI = await poolRead.query(sql, [nameFICode])
        if (resultFI.rowCount == 0) {
            common.log(`Not found data`, nameFICode)
            return null
        }
        return resultFI.rows
    }
    catch (err) {
        common.log(`getOptionsFI fail : ${err.message}`, nameTarget)
        return null
    }
}

async function selectFI(payLoad) {
    try {
        const poolRead = global.poolRead
        const sql = "select lt.criterion_score, lt.percentage from loan_target lt \
        where name_fi_code = $1 and option_fi_code = $2"
        const resultFI = await poolRead.query(sql, [payLoad.nameFiCode, payLoad.optionFiCode])
        if (resultFI.rowCount == 0) {
            common.log(`Not found data`, JSON.stringify(payLoad))
            return null
        }
        return resultFI.rows
    }
    catch (err) {
        common.log(`selectFI fail : ${err.message}`, JSON.stringify(payLoad))
        return null
    }
}

async function saveFI(payLoad) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_contract_target(contract_number, recipe, criterion_score, percentage, created_date, updated_date, score_customer, name_fi_code, option_fi_code, created_by) values($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)"
        await poolWrite.query(sql,
            [
                payLoad.contractNumber,
                payLoad.recipe,
                payLoad.criterionScore,
                payLoad.percentage,
                moment().format(),
                moment().format(),
                payLoad.scoreCustomer,
                payLoad.nameFiCode,
                payLoad.optionFiCode,
                payLoad.createdBy
            ]
        )
        return {}
    }
    catch (err) {
        common.log(`saveFI fail : ${err.message}`, JSON.stringify(payLoad))
    }
    return null
}

async function updateFI(payLoad) {
    try {
        const poolWrite = global.poolWrite
        const sql = "update loan_contract_target set recipe = $3, criterion_score = $4, percentage = $5, updated_date = $6, score_customer = $7, option_fi_code = $8 where contract_number = $1 and name_fi_code = $2"
        await poolWrite.query(sql,
            [
                payLoad.contractNumber,
                payLoad.nameFiCode,
                payLoad.recipe,
                payLoad.criterionScore,
                payLoad.percentage,
                moment().format(),
                payLoad.scoreCustomer,
                payLoad.optionFiCode
            ]
        )
    }
    catch (err) {
        common.log(`updateFI fail : ${err.message}`, nameTarget)
    }
}

async function searchFI(payLoad) {
    try {
        const poolRead = global.poolRead
        const sql = "select * from loan_contract_target \
        where contract_number = $1 and name_fi_code = $2"
        let resultFI = await poolRead.query(sql,
            [
                payLoad.contractNumber,
                payLoad.nameFiCode
            ]
        )
        if (resultFI.rowCount == 0) {
            common.log(`Not found data`, JSON.stringify(payLoad))
            return null
        }
        return resultFI.rows
    }
    catch (err) {
        common.log(`searchFI fail : ${err.message}`, JSON.stringify(payLoad))
        return null
    }
}

async function getFIByContractNumber(contractNumber) {
    try {
        const poolRead = global.poolRead
        const sql = "select recipe, criterion_score, \
        percentage, score_customer, name_fi_code, option_fi_code \
        from loan_contract_target where contract_number = $1 order by created_date"
        let resultFI = await poolRead.query(sql,
            [
                contractNumber
            ]
        )
        if (resultFI.rowCount == 0) {
            common.log(`Not found data`, contractNumber)
            return null
        }
        return resultFI.rows
    }
    catch (err) {
        common.log(`getFIByContractNumber fail : ${err.message}`, contractNumber)
        return null
    }
}

module.exports = {
    getFIByName,
    getOptionsFI,
    selectFI,
    saveFI,
    updateFI,
    searchFI,
    getFIByContractNumber
}