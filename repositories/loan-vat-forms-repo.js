const { isNullOrEmpty, isNullOrEmptyV3 } = require("../utils/helper");
const { DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const documentRepo = require("./document");
const utils = require("../utils/helper");
const pgp = require("pg-promise")({
    capSQL: true,
});

const insert = async (contractNumber, vatForms, masterdataDocuments) => {
    if (!vatForms || vatForms?.length === 0) {
        return;
    }
    const poolWrite = global.poolWrite;
    const columnSet = new pgp.helpers.ColumnSet(["contract_number", "period", "name"], { table: "loan_vat_forms" });
    let insertData = [];
    vatForms.forEach((e) => {
        if (!e.period) {
            return;
        }
        insertData.push({
            contract_number: contractNumber,
            period: e.period,
            name: e.name,
        });
    });
    if (insertData?.length === 0) {
        return;
    }
    const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
    const result = await poolWrite
        .query(insertQuery)
        .then()
        .catch((error) => {
            console.log(error);
            common.log("INSERT - loan_vat_forms: error", contractNumber);
        });
    if (result?.rowCount > 0) {
        //save docs
        const columnSet = new pgp.helpers.ColumnSet(["loan_vat_forms_id", "file_url", "file_type", "doc_type"], { table: "vat_forms_documents" });
        let insertData = [];
        let insertDocData = [];
        vatForms.forEach((e) => {
            if (e?.docs?.length > 0) {
                const vatForm = result?.rows?.filter((entity) => entity.period == e.period);
                e.docs.forEach((doc) => {
                    insertData.push({
                        loan_vat_forms_id: vatForm[0].id,
                        file_url: doc.fileUrl,
                        file_type: doc.fileType,
                        doc_type: doc.docType,
                    });

                    insertDocData.push({
                        docId: doc.docId,
                        docType: doc.docType,
                        referenceTable: DOCUMENT_REFERENCE_TABLE.LOAN_VAT_FORMS,
                        referenceId: vatForm[0].id
                    });
                });
            }
        });
        if (insertData?.length === 0) {
            return;
        }
        const query = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
        await poolWrite
            .query(query)
            .then()
            .catch((error) => {
                console.log(error);
                common.log("INSERT - vat_forms_documents: error", contractNumber);
            });
        const resultUpdateDocs = await documentRepo.saveRequestDocuments({
            contractNumber: contractNumber,
            docList: utils.snakeToCamel(insertDocData),
            masterdataDocuments
        });
        return resultUpdateDocs;
    }
};

const findByContractNumber = async (contractNumber) => {
    if (isNullOrEmpty(contractNumber)) {
        return [];
    }
    const poolRead = global.poolRead;
    const query = `SELECT * FROM loan_vat_forms WHERE contract_number = $1`;
    const result = await poolRead.query(query, [contractNumber]);
    if (result?.rowCount > 0) {
        return result.rows;
    }
    return [];
};

const findByContractNumberWithDocs = async (contractNumber) => {
    if (isNullOrEmpty(contractNumber)) {
        return [];
    }
    const poolRead = global.poolRead;
    const query = `SELECT * FROM loan_vat_forms lvf, vat_forms_documents vfd WHERE lvf.contract_number = $1 AND lvf.id = vfd.loan_vat_forms_id`;
    const result = await poolRead.query(query, [contractNumber]);
    if (result?.rowCount > 0) {
        return result.rows;
    }
    return [];
};

module.exports = {
    insert,
    findByContractNumber,
    findByContractNumberWithDocs,
}