const common = require("../utils/common");

const columns = [
  "request_id",
  "tax_id",
  "company_name",
  "partner_code",
  "representation_full_name",
  "representation_id",
  "contract_creator_name",
  "contract_creator_phone_number",
  "anchor_tax_number",
  "platform_usage_time",
  "anchor_transaction_time",
  "updated_at",
  "is_deleted",
  "contract_number",
  "anchor_name",
  "customer_name",
  "id_number",
  "nfc_value",
  "phone_number"
];

async function getLoanAf1(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql =
      "select * from loans_af1 where contract_number = $1 and ( is_deleted = 0 or is_deleted is null)";
    const result = await poolWrite.query(sql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loanAf1 error ", contractNumber);
    return false;
  }
}

module.exports = {
  columns,
  getLoanAf1
};
