const { snakeToCamel } = require('../utils/helper')

async function validRequestId(requestId) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select count(id) from request_log where request_id = $1"
        const result = await poolWrite.query(sql,[requestId])
        if(parseInt(result.rows[0].count) > 0) {
            return false
        }
        return true
    }
    catch(err) {
        console.log(`valid request error : ${err.message}`)
        return false
    }
}

async function getLastRequestId(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select request_id from request_log where contract_number = $1 order by created_date desc limit 1"
        const rs = await poolWrite.query(sql,[contractNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return rs.rows[0].request_id
    }
    catch(err) {
        console.log(`get last request Id error : ${err.message}`,contractNumber)
        return false
    }
}

async function updateCallback(contractNumber,callbackBody) {
    try {
        const poolWrite = global.poolWrite
        const requestId = await getLastRequestId(contractNumber)
        const updateSql = 'update request_log set request_callback = $1 where request_id = $2'
        const rs = await poolWrite.query(updateSql,[callbackBody,requestId])
        if(rs.rowCount == 0) {
            console.log(`update callback error`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        console.log(`update callback error : ${err?.message}`,contractNumber)
        return false
    }
}

async function saveCallback(contractNumber,callbackBody,callbackRespone) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into callback_log (contract_number,callback_body,callback_response) values ($1,$2,$3)"
        const rs = await poolWrite.query(sql,[contractNumber,callbackBody,callbackRespone])
        if(rs.rowCount == 0) {
            console.log(`save callback error`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        console.log(`save callback error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveStepLog(contractNumber,serviceName,stepName,body,response, url) {
    try {
      console.log('save step log');
        const poolWrite = global.poolWrite
        const insertSql = 'insert into step_check_log (contract_number,service_name,step,body_input,body_output, url) values ($1,$2,$3,$4,$5,$6)'
        await poolWrite.query(insertSql,[contractNumber,serviceName,stepName,body,response,url])
        return true
    }
    catch(err) {
        console.log(`save step log error : ${err.message}`,contractNumber)
    }
}

async function saveEkycCheck(contractNumber,serviceError,messageError) {
    try {
        const poolWrite = global.poolWrite
        const insertSql = 'insert into ekyc_check (contract_number,service_error,msg_error) values ($1,$2,$3)'
        const rs = await poolWrite.query(insertSql,[contractNumber,serviceError,messageError])
        if(rs.rowCount == 0) {
            console.log(`save ekyc step error`,contractNumber)
        }
        return true
    }
    catch(err) {
        console.log(`save ekyc message error : ${err.message}`,contractNumber)
        return false
    }
}

function saveErrorLog(params,errorLogs) {
    try {
        const poolWrite = global.poolWrite
        const sql = 'insert into error_log (param,error_logs) values ($1,$2)'
        poolWrite.query(sql,[params,errorLogs])
    }
    catch(err) {
        console.log(`save error log error : ${err.message}`)
        return false
    }
}

async function saveWorkflow(stage,status,contractNumber,userName='system',kunnNumber=undefined, statusDesc = undefined) {
    try {
        const poolWrite = global.poolWrite
        let params;
        let sql = "insert into case_status_log (contract_number,stage,status_code,status_desc,created_user) values ($1,$2,$3,$4,$5)"
        params = [contractNumber,stage,status,statusDesc || status,userName]
        if(kunnNumber !== undefined ) {
            sql = "insert into case_status_log (contract_number,stage,status_code,status_desc,created_user,kunn_number) values ($1,$2,$3,$4,$5,$6)"
            params = [contractNumber,stage,status,statusDesc || status,userName,kunnNumber]
        }
        await poolWrite.query(sql,params)
        return true
    }
    catch(err) {
        console.log(`save case status error :${err.message} , ${contractNumber}`)
        return false
    }
}
const insertLog = async({contractNumber,requestId,requestType,reqBody,resBody, url})=>{
    try {
        const poolWrite = global.poolWrite
        let params;
        let sql = "insert into request_log (contract_number,request_id,request_type,request_body,request_response,url) values ($1,$2,$3,$4,$5,$6)"
        params = [contractNumber,requestId,requestType,reqBody,resBody,url]
        await poolWrite.query(sql,params)
        return true
    }
    catch(err) {
        console.log(`insertLog error :${err.message} , ${contractNumber}`)
        return false
    }
}

async function updateWorkflowStatus(id, status) {
    try {
        const poolWrite = global.poolWrite
        const updatedDate = new Date();
        let params;
        let sql = "update  case_status_log set status=$2, updated_date = $3 where id = $1"
        params = [id,status, updatedDate]
        await poolWrite.query(sql,params)
        return true
    }
    catch(err) {
        console.log(`save case status error :${err.message} , ${contractNumber}`)
        return false
    }
}

const findKunnWorkflowByStage = async (kunnNumber, stage) => {
  try {
    const poolRead = global.poolRead;
    const sql =
      "select * from case_status_log where stage = $1 and kunn_number=$2 order by id desc limit 1";
    const resultFI = await poolRead.query(sql, [stage, kunnNumber]);
    if (resultFI.rowCount == 0) {
      console.log(`Not found data`);
      return null;
    }
    return snakeToCamel(resultFI.rows?.[0]);
  } catch (err) {
    console.log(`findKunnWorkflowByStage fail : ${err.message}`);
    return null;
  }
};

module.exports = {
    validRequestId,
    updateCallback,
    saveCallback,
    saveStepLog,
    saveEkycCheck,
    saveErrorLog,
    saveWorkflow,
    insertLog,
    updateWorkflowStatus,
    findKunnWorkflowByStage
}