async function insert({
  requestId,
  contractNumber,
  nfcRawData,
  frontIdCardUrl,
  backIdCardUrl,
  ocrData,
  nfcIssueDate,
  exists,
}) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `INSERT INTO nfc_data (
                    request_id, contract_number, raw_data, front_id_card_url, back_id_card_url,
                    ocr_id_number, ocr_name, ocr_dob, ocr_gender, ocr_address,
                    ocr_issue_date, ocr_issue_place, ocr_native_place, ocr_mrz,
                    ocr_expire_date, nfc_issue_date_cache, exists
                ) VALUES (
                    $1, $2, $3, $4, $5,
                    $6, $7, $8, $9, $10,
                    $11, $12, $13, $14,
                    $15, $16, $17
                )`;
    const insertRs = await poolWrite.query(sql, [
      requestId,
      contractNumber,
      nfcRawData,
      frontIdCardUrl,
      backIdCardUrl,
      ocrData.id,
      ocrData.name,
      ocrData.dob,
      ocrData.gender,
      ocrData.address,
      ocrData.issueDate,
      ocrData.issuePlace,
      ocrData.nativePlace,
      ocrData.mrz,
      ocrData.expireDate,
      nfcIssueDate,
      exists,
    ]);
    if (insertRs.rowCount > 0) {
      return true;
    }
    return false;
  } catch (error) {
    console.log(`insert nfcdata requestId ${requestId} error ${error}`);
    return false;
  }
}

const findByRequestId = async(requestId)=>{
    const sql = "select * from nfc_data where request_id = $1 order by id desc";
    const rs =  await global.poolWrite.query(sql,[requestId]);
    return rs?.rows?.[0]
}

module.exports = {
    insert,
    findByRequestId
};
