const {caseStatusCode,STATUS} = require("../const/caseStatus")
const {saveStatusAsync,getStatusV2} = require("../utils/helper")
const common = require("../utils/common")
const {roleCode} = require("../const/definition")
const moduleName = 'repositories/offer.js'
const pgp = require('pg-promise')({
    capSQL: true 
});
const kunnPrepareAttributeRepo = require('../repositories/kunn-prepare-attribute-repo')

async function selectOffer(poolWrite,offerId,contractNumber,userName='',userRole='') {
    try {
        let sql = "update loan_offer_selection set is_selected = 1,updated_date = now(),user_acted=$3,role_acted=$4 where id = $1 and contract_number = $2"
        const sql2 = "update loan_offer_selection set is_selected=0,updated_date=now(),user_acted=$3,role_acted=$4 where id !=$1 and contract_number=$2"
        const updateRs = await Promise.all([poolWrite.query(sql,[parseInt(offerId),contractNumber,userName,userRole]),
                                            poolWrite.query(sql2,[parseInt(offerId),contractNumber,userName,userRole]),
                                            updateSelectedOffer(contractNumber,parseInt(offerId))])
        if(updateRs[0].rowCount == 0) {
            return {
                result : false,
                msg : "Invalid offer_id"
            }
        }
        else {
            await saveStatusAsync(poolWrite,contractNumber,caseStatusCode.KH13)
            return {
                result : true,
                msg : "select offer successfully."
            }
        }
    }
    catch(err) {
        return {
            result : false,
            msg : "error."
        }
    }
}

async function getDataToCalculateOffer(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const getDataSql = "select lc.request_int_rate ,lc.request_amt ,lc.request_tenor,lc.time_duration,lms.risk_grade,lc.empl_type,lc.birth_date,lc.partner_code,lc.product_code ,lms.estimated_revenue,lms.net_income from loan_contract lc left join loan_main_score lms on lc.contract_number = lms.contract_number where lms.contract_number = $1;"
        const data = await poolWrite.query(getDataSql,[contractNumber])
        if(data.rowCount == 0) {
            common.log(`get data for compute offer error`,contractNumber)
            return false
        }
        return data.rows[0]
    }
    catch(err) {
        common.log(`get data for offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveDEScore(contractNumber,data) {
    try {
        const poolWrite = global.poolWrite
        const updateScore = 'insert into loan_main_score(di_before_ce,net_income,risk_grade,scoring,contract_number) values ($1,$2,$3,$4,$5)'
        const updateRs = await poolWrite.query(updateScore,[parseInt(data.annuity),parseInt(data.annuity),parseInt(data.riskGrade),parseInt(data.internalScore),contractNumber])
        if(updateRs.rowCount == 0) {
            common.log(`save DE score error`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save DE score error : ${err.message}`,contractNumber)
        return false
    }
}

async function updateDEScore(contractNumber,data) {
    try {
        const poolWrite = global.poolWrite
        const updateScore = 'update loan_main_score set di_before_ce=$1,net_income=$2,risk_grade=$3,scoring=$4 where contract_number=$5'
        const updateRs = await poolWrite.query(updateScore,[parseInt(data.annuity),parseInt(data.annuity),parseInt(data.riskGrade),parseInt(data.internalScore),contractNumber])
        if(updateRs.rowCount == 0) {
            common.log(`update DE score error`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save DE score error : ${err.message}`,contractNumber)
        return false
    } 
}

async function updateScore2(contractNumber,data) {
    try {
        const poolWrite = global.poolWrite
        const updateScore = 'update loan_main_score set average_turnover = $1,estimated_revenue = $2 where contract_number = $3'
        const updateRs = await poolWrite.query(updateScore,[data.averageTurnover,data.estimatedRevenue,contractNumber])
        if(updateRs.rowCount == 0) {
            common.log(`save net income , revenue error`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save de turn over, net income score error : ${err.message}`,contractNumber)
        return false
    }
}

async function updateMainScore(contractNumber,column,value) {
    try {
        const poolWrite = global.poolWrite
        const updateSql = `update loan_main_score set ${column} =$1,updated_date=now() where contract_number = $2`
        poolWrite.query(updateSql,[value,contractNumber])
    }
    catch(err) {
        common.log(`${moduleName} : update loan main score error : ${err.message}`,contractNumber)
        return false
    }
}

async function updatePCBScore(pcb1,pcb2,pcbDebtGroup,contractNumber) {
    try {
	    const sql = "update loan_main_score set pcb_score1 = $1,pcb_score2 = $2,pcb_debt_group=$3,updated_date=now() where contract_number = $4"
	    return await poolWrite.query(sql,[pcb1,pcb2,pcbDebtGroup,contractNumber])
    }
    catch(err) {
        common.log(`${moduleName} | updatePCBScore error : ${err.message}`,contractNumber)
        return false
    }
}

async function updateSelectedOffer(contractNumber,approvalOfferId) {
    try {
        const poolWrite = global.poolWrite
        const updateOfferSql = 'update loan_contract lc set approval_amt  = los.offer_amt ,approval_tenor = los.tenor ,approval_int_rate = los.int_rate,approval_date =now(),updated_date = now() from loan_offer_selection los where lc.contract_number =$1 and los.id = $2 ;'
        const updateRs = await poolWrite.query(updateOfferSql,[contractNumber,approvalOfferId])
        if(updateRs.rowCount == 0) {
            common.log(`update approval offer error`,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`update approval offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveOffer(offers,contractData,contractNumber) {
    try {
        const promiseList = []
        const poolWrite = global.poolWrite
        const saveOfferSql = 'insert into loan_offer_selection(contract_number,offer_amt,int_rate,tenor,instal_amt,offer_type,product_code,request_amt,request_tenor) values ($1,$2,$3,$4,$5,$6,$7,$8,$9)'
        for(let idx in offers) {
            let offer = offers[idx]
            promiseList.push(poolWrite.query(saveOfferSql,[contractNumber,offer.requestedAmount,parseFloat(offer.ir),offer.tenor,offer.annuity,offer.offerType,offer.productCode,contractData.request_amt,contractData.request_tenor]))
        }
        return await Promise.all(promiseList)
    }
    catch(err) {
        common.log(`save offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveOfferV2(contractNumber,offerAmt,offerRate,offerTenor,offerType,productCode,requestAmt,requestTenor) {
    try {
        const poolWrite = global.poolWrite
        const saveOfferSql = 'insert into loan_offer_selection(contract_number,offer_amt,int_rate,tenor,offer_type,product_code,request_amt,request_tenor) values ($1,$2,$3,$4,$5,$6,$7,$8)'
        return await poolWrite.query(saveOfferSql,[contractNumber,offerAmt,offerRate,offerTenor,offerType,productCode,requestAmt,requestTenor])
    }
    catch(err) {
        common.log(`save offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveOfferV3(contractNumber,offerAmt,offerTenor,annuity) {
    try {
        const poolWrite = global.poolWrite
        const saveOfferSql = "insert into loan_offer_selection(contract_number,offer_amt,int_rate,tenor,offer_type,product_code,request_amt,request_tenor,instal_amt) select CAST($1 AS VARCHAR),round(cast($2 as numeric)),request_int_rate ,$3,'STANDARD',product_code ,request_amt,request_tenor,$4 from loan_contract where contract_number =$1;"
        return await poolWrite.query(saveOfferSql,[contractNumber,offerAmt,offerTenor,annuity])
    }
    catch(err) {
        common.log(`save offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveOfferV4(contractNumber,offerAmt,offerTenor,annuity,createdBy,createdRole) {
    try {
        const poolWrite = global.poolWrite
        const saveOfferSql = "insert into loan_offer_selection(contract_number,offer_amt,int_rate,tenor,offer_type,product_code,request_amt,request_tenor,instal_amt,created_role,user_acted) select CAST($1 AS VARCHAR),cast($2 as numeric),request_int_rate ,$3,'STANDARD',product_code ,request_amt,request_tenor,$4,$5,$6 from loan_contract where contract_number =$1 returning *;"
        return await poolWrite.query(saveOfferSql,[contractNumber,offerAmt,offerTenor,annuity,createdRole,createdBy])
    }
    catch(err) {
        common.log(`save offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveOfferSme(contractNumber,offerAmt,int_rate,offerTenor,annuity,createdBy,createdRole) {
    try {
        const poolWrite = global.poolWrite
        const saveOfferSql = "insert into loan_offer_selection(contract_number,offer_amt,int_rate,tenor,offer_type,product_code,request_amt,request_tenor,instal_amt,created_role,user_acted) select CAST($1 AS VARCHAR),cast($2 as numeric),$3,$4,'STANDARD',product_code ,request_amt,request_tenor,$5,$6,$7 from loan_contract where contract_number =$1 returning *;"
        return await poolWrite.query(saveOfferSql,[contractNumber,offerAmt,int_rate,offerTenor,annuity,createdRole,createdBy])
    }
    catch(err) {
        common.log(`save offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function deleteOfferByContract(conractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "update loan_offer_selection set is_delt=1 where contract_number =$1"
        return await poolWrite.query(sql,[contractNumber])
    }
    catch(err) {
        return false
    }
}

async function saveKUOffer(contractNumber,kunnNumber,offerAmt,offerRate,offerTenor,offerType,productCode,requestAmt,requestTenor) {
    try {
        const poolWrite = global.poolWrite
        const saveKUOfferSql = 'insert into loan_offer_selection(contract_number,kunn_id,offer_amt,int_rate,tenor,offer_type,product_code,request_amt,request_tenor) values ($1,$2,$3,$4,$5,$6,$7,$8,$9) RETURNING *'
        return await poolWrite.query(saveKUOfferSql,[contractNumber,kunnNumber,offerAmt,offerRate,offerTenor,offerType,productCode,requestAmt,requestTenor])
    }
    catch(err) {
        common.log(`save KUNN offer error : ${err.message}`,contractNumber)
        return false
    }
}
async function saveKUOfferV2(data) {
    try {
        const poolWrite = global.poolWrite
        const saveKUOfferSql = 'insert into loan_offer_selection(contract_number,kunn_id,offer_amt,int_rate,tenor,offer_type,product_code,request_amt,request_tenor,is_selected) values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10) RETURNING *'
        return await poolWrite.query(saveKUOfferSql,[data.contractNumber,data.kunnNumber,data.offerAmt,data.offerRate,data.offerTenor,data.offerType,data.productCode,data.requestAmt,data.requestTenor,1])
    }
    catch(err) {
        common.log(`save KUNN offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveScoreDetail(contractNumber,params,scoreType) {
    try {
        const poolWrite = global.poolWrite
        const insertData = []
        const columnSet = new pgp.helpers.ColumnSet(['contract_number', 'input_param_name','input_param_value','param_group'], {table: 'loan_score_detail'});

        params.forEach(element => {
            insertData.push({
                contract_number : contractNumber,
                input_param_name : element.code,
                input_param_value : element.value,
                param_group : scoreType
            })
        })
        const insertQuery = pgp.helpers.insert(insertData, columnSet)
        return await poolWrite.query(insertQuery).then().catch(error => common.log("INSERT - loan_score_detail: error","ERROR"))
    }catch(err) {
        return false
    }
}

async function saveVPLScore(contractNumber,offer1,averageIncome,totalTurnover,maxAnnuity) {
    try {
        const poolWrite = global.poolWrite
        const saveVPLScore = 'insert into vpl_score (contract_number,offer_1,average_income,total_turn_over,max_annuity) values ($1,$2,$3,$4,$5)'
        const rs = await poolWrite.query(saveVPLScore,[contractNumber,parseInt(offer1),parseInt(averageIncome),parseInt(totalTurnover),parseInt(maxAnnuity)])
        if(rs.rowCount == 0) {
            common.log(`save vpl score error `,contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`save vpl score error : ${err.message}`,contractNumber)
    }
}

async function getVPLSCore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const getVPLScore = 'select * from vpl_score where contract_number = $1'
        const rs = await poolWrite.query(getVPLScore,[contractNumber])
        if(rs.rowCount == 0) {
            common.log(`get vpl score error `,contractNumber)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`save vpl score error : ${err.message}`,contractNumber)
        return false
    }
}

async function getMainScore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const getMainScore = 'select * from loan_main_score where contract_number = $1'
        const rs = await poolWrite.query(getMainScore,[contractNumber])
        if(rs.rowCount == 0) {
            common.log(`get loan main score error `,contractNumber)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`save loan main score error : ${err.message}`,contractNumber)
        return false
    }
}

async function getSelectedOffer(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const selectedOffer = "select * from loan_offer_selection where is_selected = 1 and contract_number = $1"
        const rs = await poolWrite.query(selectedOffer,[contractNumber])
        if(rs.rowCount == 0) {
            common.log(`get selected offer error`,contractNumber)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`get selected offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function getSelectedOfferByKunn(kunnId) {
    try {
        const poolWrite = global.poolWrite
        const selectedOffer = "select * from loan_offer_selection where is_selected = 1 and kunn_id = $1"
        const rs = await poolWrite.query(selectedOffer,[kunnId])
        if(rs.rowCount == 0) {
            common.log(`get selected offer kunn ${kunnId} error`,kunnId)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`get selected offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function offerConfig(poolWrite) {
    try {
        const sql = "select param_name,param_value,param_dtype from kov_offer_config ;"
        const rs = await poolWrite.query(sql)
        if(rs.rowCount == 0) {
            return false
        }
        const offerConfigDict = {}
        let paramValue;
        rs.rows.map(row => {
            if(offerConfigDict.hasOwnProperty(row.param_name))
            paramValue = row.param_value;
            if(row.param_dtype == 'float') {
                paramValue = parseFloat(row.param_value)
            }
            else if(row.param_dtype == 'int') {
                paramValue = parseInt(row.param_value)
            }
            offerConfigDict[row.param_name] = paramValue
        })
        return offerConfigDict
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function createLoanScore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_score(contract_number) values ($1)"
        return await poolWrite.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`create loan score error : ${err.message}`,contractNumber)
        return false
    }
}

async function createLoanMainScore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_main_score(contract_number) values ($1)"
        return await poolWrite.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`create loan score error : ${err.message}`,contractNumber)
        return false
    }
}

async function createKOVLoanScore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into kov_offer_score(contract_number) values ($1)"
        return await poolWrite.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`create kov_offer_score error : ${err.message}`,contractNumber)
        return false
    }
}

function saveOfferKov(contractNumber,netIncomeOffer,vongquayvonOffer,timeDurationOffer) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into kov_offer_score (contract_number,net_income_offer,vong_quay_von_offer,time_duration_offer) values ($1,$2,$3,$4)"
        poolWrite.query(sql,[contractNumber,netIncomeOffer,vongquayvonOffer,timeDurationOffer])
    }
    catch(err) {
        return false
    } 
}

async function getOfferList(contractNumber,role=undefined) {
    try {
        const poolWrite = global.poolWrite
        let sql = "select id,offer_amt,int_rate,tenor,request_amt,is_delt,created_user,created_role from loan_offer_selection where contract_number = $1 and is_delt= 0"
		if(role == roleCode.CL) {
			sql = "select id,offer_amt,int_rate,tenor,request_amt,is_delt,created_user,created_role from loan_offer_selection where is_delt= 0 and contract_number = $1 and created_role in ('MC','CE')"
		}
        else if(role == roleCode.customer) {
            sql = "select id,offer_amt,int_rate,tenor,request_amt,is_delt,created_user,created_role from loan_offer_selection where is_delt= 0 and contract_number = $1 and is_selected=1"
        }
        const result = await poolWrite.query(sql,[contractNumber])
        return result.rows
    }
    catch(err) {
        return false
    }
}

async function getCicScore(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select * from loan_cic_score where is_delt = 0 and contract_number = $1"
        const rs = await poolWrite.query(sql,[contractNumber])
        if(rs.rowCount == 0) {
            return false
        }
        return rs.rows
    }
    catch(err) {
        return false
    }
}

async function isMatchingOffer(contractNumber, isSuperAppWithDraw = false) {
	try {
        const sql = "select id,request_amt,offer_amt,tenor,request_tenor from loan_offer_selection where is_selected=1 and contract_number=$1"
        const result = await global.poolWrite.query(sql,[contractNumber])
        const offer = result.rows[0]
        if (isSuperAppWithDraw) {
            Promise.all([
                kunnPrepareAttributeRepo.save({contractNumber, field: "approveKunnAmount", value: offer?.offer_amt}),
                kunnPrepareAttributeRepo.save({contractNumber, field: "approveKunnIr", value: offer?.int_rate}),
                kunnPrepareAttributeRepo.save({contractNumber, field: "approveKunnTenor", value: offer?.tenor})
            ])
        }
        if(offer.request_tenor == null) {
            if(parseFloat(offer.request_amt) == parseFloat(offer.offer_amt)) {
                return offer.id
            }
            else {
                return false
            }
        }
        else {
            if((parseFloat(offer.request_amt) == parseFloat(offer.offer_amt)) && (parseFloat(offer.tenor) == parseFloat(offer.request_tenor)) ) {
                return offer.id
            }
            else {
                return false
            }
        }
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function proposeOffer(contractNumber,offerId,userName,userRole) {
    try {
        const poolWrite = global.poolWrite
        const currentDate = new Date()
        let sql = "update loan_offer_selection set is_delt=0, updated_date=$1,user_acted=$2,role_acted=$3,created_user=$4,created_role=$5 where id =$6"
        let sql2 = "update loan_offer_selection set is_delt=1,updated_date=$1,user_acted=$2,role_acted=$3 where id !=$4 and contract_number=$5 and created_role =$6 ";
        if(userRole != 'CE') {
            return await Promise.all([poolWrite.query(sql,[currentDate,userName,userRole,userName,userRole,offerId]),poolWrite.query(sql2,[currentDate,userName,userRole,offerId,contractNumber,userRole])])
        }
        else {
            sql = "update loan_offer_selection set is_delt=0,is_selected=1, updated_date=$1,user_acted=$2,role_acted=$3,created_user=$4,created_role=$5 where id =$6"
            sql2 = "update loan_offer_selection set is_delt=1,updated_date=$1,user_acted=$2,role_acted=$3 where id !=$4 and contract_number=$5 and created_role not in ('MC','RP')";
            return await Promise.all([poolWrite.query(sql,[currentDate,userName,userRole,userName,userRole,offerId]),poolWrite.query(sql2,[currentDate,userName,userRole,offerId,contractNumber])])
        }
    }
    catch(err) {
        console.log(err)
    }
}

async function getOfferBeforeDI(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const selectedOffer = "select * from loan_offer_selection where contract_number = $1 limit 1"
        const rs = await poolWrite.query(selectedOffer,[contractNumber])
        if(rs.rowCount == 0) {
            common.log(`get selected offer error`,contractNumber)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`get selected offer error : ${err.message}`,contractNumber)
        return false
    }
}

async function validateOffer(contractNumber) {
	try {
        const sql = "select id,request_amt,offer_amt,tenor,request_tenor from loan_offer_selection where is_selected=1 and contract_number=$1"
        const result = await global.poolWrite.query(sql,[contractNumber])
        const offer = result.rows[0]
        if (parseInt(offer.offer_amt) <= 0) 
            return false;
        else 
            return true;
    }
    catch(err) {
        console.log(err)
    }
}
async function getOfferById(offerId) {
    try {
        const poolWrite = global.poolWrite
        const selectedOffer = "select * from loan_offer_selection where id=$1"
        const rs = await poolWrite.query(selectedOffer,[offerId])
        if(rs.rowCount == 0) {
            common.log(`get getOfferById error`,offerId)
            return false
        }
        return rs.rows[0]
    }
    catch(err) {
        common.log(`get getOfferById error : ${err.message}`, offerId)
        return false
    }
}

async function getOfferListSma(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select id,offer_amt,int_rate,tenor,request_amt,is_delt,created_user,created_role from loan_offer_selection where is_delt= 0 and contract_number = $1 and is_selected=1 and kunn_id is null"
        const result = await poolWrite.query(sql,[contractNumber])
        return result.rows
    }
    catch(err) {
        return false
    }
}


module.exports = {
    getOfferById,
    selectOffer,
    saveDEScore,
    saveOffer,
    saveOfferV2,
    saveOfferV3,
    saveOfferV4,
    saveOfferSme,
    saveScoreDetail,
    saveVPLScore,
    saveOfferKov,
    updateScore2,
    updateSelectedOffer,
    updateMainScore,
    updatePCBScore,
    updateDEScore,
    getDataToCalculateOffer,
    getVPLSCore,
    getMainScore,
    getSelectedOffer,
    getOfferList,
    offerConfig,
    createLoanScore,
    createLoanMainScore,
    createKOVLoanScore,
    saveKUOffer,
    getCicScore,
    deleteOfferByContract,
    isMatchingOffer,
    proposeOffer,
    getOfferBeforeDI,
    saveKUOfferV2,
    validateOffer,
    getOfferListSma,
    getSelectedOfferByKunn
}
