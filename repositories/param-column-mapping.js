const loanContractMapping = {
    'id': 'id',
    'contract_number': 'contract_number',
    'channel': 'channel',
    'contract_type': 'contract_type',
    'product_id': 'product_id',
    'request_amt': 'request_amt',
    'request_tenor': 'request_tenor',
    'request_int_rate': 'request_int_rate',
    'approval_amt': 'approval_amt',
    'approval_tenor': 'approval_tenor',
    'approval_int_rate': 'approval_int_rate',
    'status': 'status',
    'pstatus': 'pstatus',
    'loan_purpose': 'loan_purpose',
    'risk_chk_result': 'risk_chk_result',
    'risk_chk_reason': 'risk_chk_reason',
    'rejection_reason': 'rejection_reason',
    'cust_full_name': 'cust_full_name',
    'id_number': 'id_number',
    'id_type': 'id_type',
    'id_issue_dt': 'id_issue_dt',
    'id_issue_place': 'id_issue_place',
    'address_cur': 'address_cur',
    'province_cur': 'province_cur',
    'district_cur': 'district_cur',
    'ward_cur': 'ward_cur',
    'village_cur': 'village_cur',
    'address_per': 'address_per',
    'province_per': 'province_per',
    'district_per': 'district_per',
    'ward_per': 'ward_per',
    'village_per': 'village_per',
    'user_created_id': 'user_created_id',
    'type': 'type',
    'birth_date': 'birth_date',
    'education': 'education',
    'married_status': 'married_status',
    'gender': 'gender',
    'house_type': 'house_type',
    'num_of_dependants': 'num_of_dependants',
    'salary_method': 'salary_method',
    'salary_frequency': 'salary_frequency',
    'salary_payment_day': 'salary_payment_day',
    'income_proof_flag': 'income_proof_flag',
    'income_proof_amount': 'income_proof_amount',
    'empl_type': 'empl_type',
    'is_delete': 'is_delete',
    'job_type': 'job_type',
    'job_title': 'job_title',
    'empl_ctrct_type': 'empl_ctrct_type',
    'empl_ctrct_duration': 'empl_ctrct_duration',
    'empl_name': 'empl_name',
    'empl_ctrct_from_year': 'empl_ctrct_from_year',
    'empl_ctrct_to_year': 'empl_ctrct_to_year',
    'empl_address': 'empl_address',
    'empl_country': 'empl_country',
    'empl_province': 'empl_province',
    'empl_district': 'empl_district',
    'empl_ward': 'empl_ward',
    'house_type_other_value': 'house_type_other_value',
    'current_address_years': 'current_address_years',
    'other_contact_type': 'other_contact_type',
    'other_contact_value': 'other_contact_value',
    'reference_type_1': 'reference_type_1',
    'reference_name_1': 'reference_name_1',
    'reference_phone_1': 'reference_phone_1',
    'reference_id_number_1': 'reference_id_number_1',
    'reference_type_2': 'reference_type_2',
    'reference_name_2': 'reference_name_2',
    'reference_phone_2': 'reference_phone_2',
    'reference_id_number_2': 'reference_id_number_2',
    'monthly_income': 'monthly_income',
    'monthly_income_n': 'monthly_income_n',
    'monthly_income_n1': 'monthly_income_n1',
    'monthly_income_n2': 'monthly_income_n2',
    'monthly_income_n3': 'monthly_income_n3',
    'other_income': 'other_income',
    'm_household_expenses': 'm_household_expenses',
    'phone_verified_tick_box': 'phone_verified_tick_box',
    'mailling_address': 'mailling_address',
    'phone_number': 'phone_number',
    'phone_number1': 'phone_number1',
    'phone_number2': 'phone_number2',
    'phone_number3': 'phone_number3',
    'company_phone_number': 'company_phone_number',
    'bank_account': 'bank_account',
    'bank_account_owner': 'bank_account_owner',
    'bank_name': 'bank_name',
    'bank_city': 'bank_city',
    'bank_branch': 'bank_branch',
    'bank_code': 'bank_code',
    'email': 'email',
    'created_date': 'created_date',
    'updated_date': 'updated_date',
    'owner_id': 'owner_id',
    'request_id': 'request_id',
    'partner_code': 'partner_code',
    'score_matching': 'score_matching',
    'type_matching': 'type_matching',
    'disbursement_channel': 'disbursement_channel',
    'contract_file_name': 'contract_file_name',
    'same_permanent': 'same_permanent',
    'doc_list_id': 'doc_list_id',
    'telco_score': 'telco_score',
    'consumption_index': 'consumption_index',
    'connection_rate': 'connection_rate',
    'customer_id': 'customer_id',
    'bill_day': 'bill_day',
    'product_code': 'product_code',
    'kiot_use_month': 'kiot_use_month',
    'type_trading': 'type_trading',
    'account_trading': 'account_trading',
    'business_legal': 'business_legal',
    'turnover_12m': 'turnover_12m',
    'transaction12m': 'transaction12m',
    'active_month': 'active_month',
    'sector_industry': 'sector_industry',
    'business_type': 'business_type',
    'capital_need': 'capital_need',
    'self_financing': 'self_financing',
    'other_capital': 'other_capital',
    'funding_from_ec': 'funding_from_ec',
    'repayment_method' :'repayment_method',
    'repayment_sources': 'repayment_sources',
    'fixed_asset': 'fixed_asset',
    'plan_asset': 'plan_asset',
    'number_business_asset': 'number_business_asset',
    'time_using_asset': 'time_using_asset',
    'approval_date': 'approval_date',
    'income_frequency': 'income_frequency',
    'income_method': 'income_method',
    'tax_id': 'tax_id',
    'empl': 'empl',
    'employment_contract_term': 'employment_contract_term',
    'income_date': 'income_date',
    'marriage_mate_id': 'marriage_mate_id',
    'cust_id': 'cust_id',
    'offer_id': 'offer_id',
    'other_id_number': 'other_id_number',
    'other_issue_place': 'other_issue_place',
    'other_issue_date': 'other_issue_date',
    'merchant_contract_number': 'merchant_contract_number',
    'representati_merchant_contract': 'representati_merchant_contract',
    'start_date_on_merchant': 'start_date_on_merchant',
    'end_date_on_merchant': 'end_date_on_merchant',
    'status_desc': 'status_desc',
    'number_of_staffs': 'number_of_staffs',
    'total_stock': 'total_stock',
    'revenue_of_seller': 'revenue_of_seller',
    'capital_turnover': 'capital_turnover',
    'latest_tax_payment': 'latest_tax_payment',
    'bussiness_duration': 'bussiness_duration',
    'workplace_name': 'workplace_name',
    'workplace_province': 'workplace_province',
    'workplace_district': 'workplace_district',
    'workplace_ward': 'workplace_ward',
    'workplace_address': 'workplace_address',
    'disbursement_method': 'disbursement_method',
    'time_duration': 'time_duration',
    'turnover_amount': 'turnover_amount',
    'month_fee': 'month_fee',
    'third_party_cust_id': 'third_party_cust_id',
    'first_registration_date': 'first_registration_date',
    'zalo_phone_number': 'zalo_phone_number',
    'merchant_account': 'merchant_account',
    'name_of_app': 'name_of_app',
    'name_of_tool': 'name_of_tool',
    'name_of_app_duration': 'name_of_app_duration',
    'name_of_tool_duration': 'name_of_tool_duration',
    'sme_name': 'sme_name',
    'sme_tax_id': 'sme_tax_id',
    'registration_number': 'registration_number',
    'sme_phone_number': 'sme_phone_number',
    'sme_email': 'sme_email',
    'ecommerce': 'ecommerce',
    'ecommerce_duration': 'ecommerce_duration',
    'sector_industry1': 'sector_industry1',
    'sector_industry2': 'sector_industry2',
    'sector_industry3': 'sector_industry3',
    'branch_tax_id': 'branch_tax_id',
    'sme_manager_name': 'sme_manager_name',
    'sme_manager_id': 'sme_manager_id',
    'sme_manager_address_per': 'sme_manager_address_per',
    'sme_manager_address_cur': 'sme_manager_address_cur',
    'sme_representation_name': 'sme_representation_name',
    'sme_representation_dob': 'sme_representation_dob',
    'sme_representation_gender': 'sme_representation_gender',
    'sme_representation_position': 'sme_representation_position',
    'sme_representation_id': 'sme_representation_id',
    'sme_representation_issue_date': 'sme_representation_issue_date',
    'sme_representation_issue_place': 'sme_representation_issue_place',
    'sme_representation_other_id': 'sme_representation_other_id',
    'sme_representation_address_cur': 'sme_representation_address_cur',
    'sme_representation_province_cur': 'sme_representation_province_cur',
    'sme_representation_district_cur': 'sme_representation_district_cur',
    'sme_representation_ward_cur': 'sme_representation_ward_cur',
    'sme_representation_village_cur': 'sme_representation_village_cur',
    'sme_representation_address_per': 'sme_representation_address_per',
    'sme_representation_province_per': 'sme_representation_province_per',
    'sme_representation_district_per': 'sme_representation_district_per',
    'sme_representation_ward_per': 'sme_representation_ward_per',
    'sme_representation_village_per': 'sme_representation_village_per',
    'sme_representation_phone_number': 'sme_representation_phone_number',
    'sme_representation_email': 'sme_representation_email',
    'accountant_status': 'accountant_status',
    'accountant_name': 'accountant_name',
    'accountant_gender': 'accountant_gender',
    'accountant_phone_number': 'accountant_phone_number',
    'accountant_email': 'accountant_email',
    'authorized_name': 'authorized_name',
    'authorized_dob': 'authorized_dob',
    'authorized_gender': 'authorized_gender',
    'authorized_position': 'authorized_position',
    'authorized_id': 'authorized_id',
    'authorized_issue_date': 'authorized_issue_date',
    'authorized_issue_place': 'authorized_issue_place',
    'authorized_other_id': 'authorized_other_id',
    'authorized_address_cur': 'authorized_address_cur',
    'authorized_province_cur': 'authorized_province_cur',
    'authorized_district_cur': 'authorized_district_cur',
    'authorized_ward_cur': 'authorized_ward_cur',
    'authorized_village_cur': 'authorized_village_cur',
    'authorized_address_per': 'authorized_address_per',
    'authorized_province_per': 'authorized_province_per',
    'authorized_district_per': 'authorized_district_per',
    'authorized_ward_per': 'authorized_ward_per',
    'authorized_village_per': 'authorized_village_per',
    'authorized_phone_number': 'authorized_phone_number',
    'authorization_letter_number': 'authorization_letter_number',
    'authorization_letter_singed_date': 'authorization_letter_singed_date',
    'socal_insurance_staff_number': 'socal_insurance_staff_number',
    'total_capital_last_year': 'total_capital_last_year',
    'total_turnover_next_year': 'total_turnover_next_year',
    'request_tenor_kunn': 'request_tenor_kunn',
    'bank_account2': 'bank_account2',
    'bank_account_owner2': 'bank_account_owner2',
    'bank_name2': 'bank_name2',
    'bank_branch2': 'bank_branch2',
    'bank_code2': 'bank_code2',
    'ware_house_address': 'ware_house_address',
    'ware_house_province': 'ware_house_province',
    'ware_house_district': 'ware_house_district',
    'ware_house_ward': 'ware_house_ward',
    'ware_house_village': 'ware_house_village',
    'enterprise_type': 'enterprise_type',
    'profit_after_tax': 'profit_after_tax',
    'average_depreciation': 'average_depreciation',
    'total_profit': 'total_profit',
    'sme_headquarters_province': 'sme_headquarters_province',
    'sme_headquarters_district': 'sme_headquarters_district',
    'sme_headquarters_ward': 'sme_headquarters_ward',
    'sme_headquarters_address': 'sme_headquarters_address',
    'contract_creator_name': 'contract_creator_name',
    'contract_creator_position': 'contract_creator_position',
    'contract_creator_phone_number': 'contract_creator_phone_number',
    'contract_creator_email': 'contract_creator_email',
    'contract_creator_province': 'contract_creator_province',
    'contract_creator_district': 'contract_creator_district',
    'contract_creator_ward': 'contract_creator_ward',
    'contract_creator_address': 'contract_creator_address',
    'is_authorization_sign': 'is_authorization_sign',
    'lms_type': 'lms_type',
    'sme_employment_type1_code': 'sme_employment_type1_code',
    'sme_employment_type4_code': 'sme_employment_type4_code',
    'referral_name': 'referral_name',
    'referral_source': 'referral_source',
    'owner_equity': 'owner_equity',
    'loans_other_financial_institutions': 'loans_other_financial_institutions',
    'charter_capital': 'charter_capital',
    'conditional_business_industry': 'conditional_business_industry',
    'principal_payment': 'principal_payment',
    'interest_payment': 'interest_payment',
    'cost_next_year': 'cost_next_year',
    'pre_tax_profit_next_year': 'pre_tax_profit_next_year',
    'misa_loan_code': 'misa_loan_code',
    'tax_date': 'tax_date',
    // 'partner_full_name': 'partner_full_name',
    // 'partner_id_number': 'partner_id_number',
    // 'partner_birth_date': 'partner_birth_date',
    // 'partner_issue_date': 'partner_issue_date',
    // 'partner_issue_place': 'partner_issue_place',
    // 'partner_phone_number': 'partner_phone_number',
    // 'business_expenses': 'business_expenses',
    // 'avg_turnover': 'avg_turnover',
    // 'turnover_3m': 'turnover_3m',
    // 'turnover_6m': 'turnover_6m',
    'business_data': 'business_data',
    "current_task" : 'current_task',
    "business_operating_time" : 'business_operating_time',
    "platform_usage_time" : 'platform_usage_time',
    "anchor_transaction_time" : 'anchor_transaction_time',
    "anchor_tax_number" : 'anchor_tax_number',
    "anchor_contract_number_date" : 'anchor_contract_number_date',
    "anchor_contract_number" : 'anchor_contract_number',
    "last_3_month_sales_anchor": 'last_3_month_sales_anchor',
    "company_website": 'company_website',
    "loan_purpose_detail": 'loan_purpose_detail',
    "registration_cert_issue_place": 'registration_cert_issue_place',
    "registration_cert_issue_date": 'registration_cert_issue_date',
    "registration_cert_no": 'registration_cert_no',
    "total_cost_over_next_year": 'total_cost_over_next_year',
    "anchor_name": 'anchor_name',
    "old_contract_number": "old_contract_number",
    "nfc_value": "nfc_value",
    "workflow_code": "workflow_code",
    "sme_headquarters_new_province": "sme_headquarters_new_province",
    "sme_headquarters_new_ward": "sme_headquarters_new_ward",
    "address_on_license": "address_on_license",
    "workplace_new_province_code": "workplace_new_province_code",
    "workplace_new_ward_code": "workplace_new_ward_code",
    "per_new_province_code": "per_new_province_code",
    "per_new_ward_code": "per_new_ward_code",
    "cur_new_province_code": "cur_new_province_code",
    "cur_new_ward_code": "cur_new_ward_code"
}

const kunnMapping = {
    'kunn_id': 'kunn_id',
    'status': 'status',
    'created_date': 'created_date',
    'updated_date': 'updated_date',
    'end_date': 'end_date',
    'contract_number': 'contract_number',
    'esign_contract': 'esign_contract',
    'esigned_contract': 'esigned_contract',
    'available_amount': 'available_amount',
    'request_id': 'request_id',
    'partner_code': 'partner_code',
    'customer_name': 'customer_name',
    'identity_card_id': 'identity_card_id',
    'method': 'method',
    'with_draw_purpose': 'with_draw_purpose',
    'with_draw_purpose2': 'with_draw_purpose2',
    'with_draw_purpose3': 'with_draw_purpose3',
    'with_draw_purpose4': 'with_draw_purpose4',
    'beneficiary_name': 'beneficiary_name',
    'bank_code': 'bank_code',
    'bank_branch_code': 'bank_branch_code',
    'bank_account': 'bank_account',
    'with_draw_amount': 'with_draw_amount',
    'with_draw_tenor': 'with_draw_tenor',
    'tenor': 'tenor',
    'ir': 'ir',
    'date_approval': 'date_approval',
    'bill_day': 'bill_day',
    'kunn_code': 'kunn_code',
    'capital_need': 'capital_need',
    'self_financing': 'self_financing',
    'other_capital': 'other_capital',
    'funding_from_ec': 'funding_from_ec',
    'repayment_source': 'repayment_source',
    'fixed_asset': 'fixed_asset',
    'plan_asset': 'plan_asset',
    'number_bussiness_asset': 'number_bussiness_asset',
    'beneficiary_name2': 'beneficiary_name2',
    'bank_code2': 'bank_code2',
    'bank_branch_code2': 'bank_branch_code2',
    'bank_account2': 'bank_account2',
    'beneficiary_name3': 'beneficiary_name3',
    'bank_code3': 'bank_code3',
    'bank_branch_code3': 'bank_branch_code3',
    'bank_account3': 'bank_account3',
    'beneficiary_name4': 'beneficiary_name4',
    'bank_code4': 'bank_code4',
    'bank_branch_code4': 'bank_branch_code4',
    'bank_account4': 'bank_account4',
    'disbursed_amount': 'disbursed_amount',
    'disbursed_amount2': 'disbursed_amount2',
    'disbursed_amount3': 'disbursed_amount3',
    'disbursed_amount4': 'disbursed_amount4',
    'content': 'content',
    'content2': 'content2',
    'content3': 'content3',
    'content4': 'content4',
    'acronym_sme_name': 'acronym_sme_name',
    'step':'step',
    'api_version': 'api_version',
    'start_date': 'start_date',
    'repayment_method' :'repayment_method',
    'agree_insurance'  : 'agree_insurance',
    'total_receivable_amount' : 'total_receivable_amount',
    'resubmitted_at': 'resubmitted_at',
    'rejection_reason': 'rejection_reason',
    'cancelled_by': 'cancelled_by',
    'cancelled_at': 'cancelled_at'
}

module.exports = {
    loanContractMapping,
    kunnMapping
}