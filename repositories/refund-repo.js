const common = require("../utils/common")
const pgp = require('pg-promise')({ capSQL: true });
const { REFUND_STATUS, REFUND_REQUEST_STATUS } = require("../const/variables-const");

let cleanDate = null;

const insert = async (data) => {
    const {
        contract_number,
        amount,
        start_date,
        end_date,
        lpi,
        int_amount,
        fee_amount,
        invoice_value,
        refund_value,
        created_by,
        cust_id,
        paid_amount,
    } = data;
    const db = global.poolWrite;

    const result = await db.query(
        `INSERT INTO refunds
            (contract_number, amount, start_date, end_date, lpi, int_amount, fee_amount, invoice_value, refund_value, status, created_by, cust_id, paid_amount)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
         RETURNING *`,
        [
            contract_number,
            amount,
            start_date,
            end_date,
            lpi,
            int_amount,
            fee_amount,
            invoice_value,
            refund_value,
            REFUND_STATUS.NEW,
            created_by,
            cust_id,
            paid_amount,
        ]
    );
    return result.rows[0];
}

const search = async ({ contractNumber, custId, page, size, status, startDate, endDate }) => {
    const db = global.poolWrite;
    const whereClause = [];
    const params = [];
    if (contractNumber) {
        whereClause.push(`r.contract_number = $${params.length + 1}`);
        params.push(contractNumber);
    }
    if (custId) {
        whereClause.push(`r.cust_id = $${params.length + 1}`);
        params.push(custId);
    }
    if (status) {
        whereClause.push(`r.status = $${params.length + 1}`);
        params.push(status);
    }
    if (startDate) {
        whereClause.push(`r.created_at >= $${params.length + 1}`);
        params.push(startDate);
    }
    if (endDate) {
        whereClause.push(`r.created_at <= $${params.length + 1}`);
        params.push(endDate);
    }
    whereClause.push("r.is_deleted = 0");
    const sql = "SELECT k.contract_number as parent_contract_number, r.* FROM refunds r LEFT JOIN kunn k ON k.kunn_id = r.contract_number WHERE " + whereClause.join(" AND ") + ` ORDER BY r.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(size, (page - 1) * size);
    const counterSql = "SELECT COUNT(*) FROM refunds r WHERE " + whereClause.join(" AND ");
    const counterResult = await db.query(counterSql, params.slice(0, -2));
    const result = await db.query(sql, params);
    return {
        items: result.rows || [],
        page: page || 1,
        size: size || 10,
        total: parseInt(counterResult.rows[0].count),
        totalPage: Math.ceil(parseInt(counterResult.rows[0].count, 10) / (size || 10)),
    };
}

const findById = async (id) => {
    const db = global.poolWrite;
    const sql = "SELECT * FROM refunds WHERE id = $1 AND is_deleted = 0";
    const result = await db.query(sql, [id]);
    if (result.rowCount === 0) {
        return null;
    }
    return result.rows[0];
}

const update = async (id, { status, action, message }) => {
    const db = global.poolWrite;
    const fields = [];
    const params = [];
    let idx = 1;

    if (status !== undefined && status !== null) {
        fields.push(`status = $${idx++}`);
        params.push(status);
    }
    if (action !== undefined && action !== null) {
        fields.push(`action = $${idx++}`);
        params.push(action);
    }
    if (message !== undefined && message !== null) {
        fields.push(`message = $${idx++}`);
        params.push(message);
    }
    if (fields.length === 0) {
        throw new Error("No fields to update");
    }
    params.push(id);
    const sql = `UPDATE refunds SET ${fields.join(", ")} WHERE id = $${idx} RETURNING *`;
    const result = await db.query(sql, params);
    if (result.rowCount === 0) {
        throw new Error("Refund not found or already deleted");
    }
    return result.rows[0];
}

const createRefundRequest = async (data) => {
    const { refund_id, beneficiary_name, bank_code, bank_account, bank_name, branch_code, transfer_bank_code, transfer_bank_name, transfer_account, note } = data;
    const db = global.poolWrite;
    const sql = `INSERT INTO refund_requests
        (refund_id, beneficiary_name, bank_code, bank_account, bank_name, branch_code, transfer_bank_code, transfer_bank_name, transfer_account, note, action_date, status)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
        RETURNING *`;
    const result = await db.query(sql, [refund_id, beneficiary_name, bank_code, bank_account, bank_name, branch_code, transfer_bank_code, transfer_bank_name, transfer_account, note, new Date(), REFUND_REQUEST_STATUS.NEW]);
    return result.rows[0];
}

const findRequestById = async (id) => {
    const db = global.poolWrite;
    const sql = "SELECT * FROM refund_requests WHERE id = $1 AND is_deleted = 0";
    const result = await db.query(sql, [id]);
    if (result.rowCount === 0) {
        return null;
    }
    return result.rows[0];
}

const findActiveRequestByKunn = async (contract_number) => {
    const db = global.poolWrite;
    const sql = "SELECT r.*, rr.* FROM refund_requests rr JOIN refunds r ON rr.refund_id = r.id WHERE r.contract_number = $1 AND rr.status = $2 AND rr.is_deleted = 0";
    const result = await db.query(sql, [contract_number, REFUND_REQUEST_STATUS.APPROVED]);
    if (result.rowCount === 0) {
        return null;
    }
    return result.rows[0];
}

const rejectRequest = async (id, reason) => {
    const db = global.poolWrite;
    const sql = "UPDATE refund_requests SET status = $1, reason = $2 WHERE id = $3 RETURNING *";
    const result = await db.query(sql, [REFUND_REQUEST_STATUS.REJECTED, reason, id]);
    if (result.rowCount === 0) {
        throw new Error("Refund request not found or already deleted");
    }
    const refundId = result.rows[0].refund_id;
    await db.query("UPDATE refunds SET status = $1 WHERE id = $2", [REFUND_STATUS.NEW, refundId]);
    return result.rows[0];
}

const approveRequest = async (id) => {
    const db = global.poolWrite;
    const sql = "UPDATE refund_requests SET status = $1 WHERE id = $2 RETURNING *";
    const result = await db.query(sql, [REFUND_REQUEST_STATUS.APPROVED, id]);
    await db.query("UPDATE refund_requests SET status = $1 WHERE refund_id = $2 AND status = $3 AND id != $4",
        [REFUND_REQUEST_STATUS.CANCELLED, result.rows[0].refund_id, REFUND_REQUEST_STATUS.NEW, id],
    );
    if (result.rowCount === 0) {
        throw new Error("Refund request not found or already deleted");
    }
    const refundId = result.rows[0].refund_id;
    await db.query("UPDATE refunds SET status = $1 WHERE id = $2", [REFUND_STATUS.SUCCESS, refundId]);
    return result.rows[0];
}

const updateRequestDisbursementStatus = async (id, status) => {
    const db = global.poolWrite;
    const sql = "UPDATE refund_requests SET disbursement_status = $1 WHERE id = $2 RETURNING *";
    const result = await db.query(sql, [status, id]);
    if (result.rowCount === 0) {
        throw new Error("Refund request not found or already deleted");
    }
    return result.rows[0];
}

const searchRequest = async ({ contractNumber, custId, page, size, status, startDate, endDate, refundId }) => {
    size = size || 10;
    page = page || 1;
    const db = global.poolWrite;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    if (!cleanDate || cleanDate < currentDate) {
        cleanDate = currentDate;
        await db.query("UPDATE refund_requests SET status = $1 WHERE action_date < $2 AND status = $3", [REFUND_REQUEST_STATUS.CANCELLED, cleanDate, REFUND_REQUEST_STATUS.NEW]);
    }
    const whereClause = [];
    const params = [];
    if (refundId) {
        whereClause.push(`r.id = $${params.length + 1}`);
        params.push(refundId);
    }
    if (contractNumber) {
        whereClause.push(`r.contract_number = $${params.length + 1}`);
        params.push(contractNumber);
    }
    if (custId) {
        whereClause.push(`r.cust_id = $${params.length + 1}`);
        params.push(custId);
    }
    if (status) {
        whereClause.push(`rr.status = $${params.length + 1}`);
        params.push(status);
    }
    if (startDate) {
        whereClause.push(`rr.action_date >= $${params.length + 1}`);
        params.push(startDate);
    }
    if (endDate) {
        whereClause.push(`rr.action_date <= $${params.length + 1}`);
        params.push(endDate);
    }
    whereClause.push("rr.is_deleted = 0");
    const sql = "SELECT r.*, rr.* FROM refund_requests rr JOIN refunds r ON rr.refund_id = r.id WHERE " + whereClause.join(" AND ") + ` ORDER BY rr.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(size, (page - 1) * size);
    const counterSql = "SELECT COUNT(*) FROM refund_requests rr JOIN refunds r ON rr.refund_id = r.id WHERE " + whereClause.join(" AND ");
    const counterResult = await db.query(counterSql, params.slice(0, -2));
    const result = await db.query(sql, params);
    return {
        items: result.rows || [],
        page: page || 1,
        size: size || 10,
        total: parseInt(counterResult.rows[0].count),
        totalPage: Math.ceil(parseInt(counterResult.rows[0].count, 10) / (size || 10)),
    };
}

module.exports = {
    insert,
    search,
    findById,
    update,
    createRefundRequest,
    findRequestById,
    rejectRequest,
    approveRequest,
    searchRequest,
    findActiveRequestByKunn,
    updateRequestDisbursementStatus,
};