const common = require("../utils/common")
const pgp = require('pg-promise')({capSQL: true});

async function saveVdsTurnOver(contractNumber,data) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_turnover_transaction (contract_number,month_of_info,info_type,value_of_month) values ($1,$2,$3,$4)"
        let i = 1;
        data.forEach(element => {
            const month = i
            Promise.all([
                poolWrite.query(sql,[contractNumber,month,'transaction',element.transactions]),
                poolWrite.query(sql,[contractNumber,month,'DT_PINCODE_VT_N',element.DT_PINCODE_VT_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_TT_VT_N',element.DT_TT_VT_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_GD_VINA_N',element.DT_GD_VINA_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_GD_MOBI_N',element.DT_GD_MOBI_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_GD_BEELINE_N',element.DT_GD_BEELINE_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_GD_VNMOBILE_N',element.DT_GD_VNMOBILE_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_GAME_RECHARGE_N',element.DT_GAME_RECHARGE_N]),
                poolWrite.query(sql,[contractNumber,month,'DT_GAME_KHAC_N',element.DT_GAME_KHAC_N]),
                poolWrite.query(sql,[contractNumber,month,'TOTAL_CP',element.TOTAL_CP]),
                poolWrite.query(sql,[contractNumber,month,'TOTAL_TURNOVER',(element.DT_PINCODE_VT_N + element.DT_GD_VINA_N + element.DT_GD_MOBI_N + element.DT_GD_BEELINE_N + element.DT_GD_VNMOBILE_N + element.DT_GAME_RECHARGE_N + element.DT_GAME_KHAC_N + element.DT_TT_VT_N)]),
            ])
            i += 1
        });
        return true
    }
    catch(err) {
        common.log(`save turn over error : ${err.message}`,contractNumber)
        return false
    }
}

async function getTurnOver(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select * from loan_turnover_transaction where contract_number = $1"
        const data = await poolWrite.query(sql,[contractNumber])
        // console.log('data',data.rowCount)
        if (data.rowCount == 0) {
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        const dataDict = {}
        for(let i in data.rows) {
            const row = data.rows[i]
            if(!dataDict.hasOwnProperty(row.month_of_info)) {
                dataDict[row.month_of_info] = {}
            }
            dataDict[row.month_of_info][row.info_type] = parseFloat(row.value_of_month)
        }
        // console.log('dataDict',dataDict)
        return dataDict
    }
    catch(err) {
        console.log(err)
        common.log(`get turn over error : ${err.message}`,contractNumber)
        return false
    }
}

async function getTurnOverTransaction(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select info_type,month_of_info , value_of_month from loan_turnover_transaction where contract_number = $1 and info_type in ('turnover','transaction','mod_turnover','net_turnover','sme_percent','expenses')"
        const data = await poolWrite.query(sql,[contractNumber])
        if (data.rowCount == 0) {
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        const dataDict = {}
        for(let i in data.rows) {
            const row = data.rows[i]
            if(!dataDict.hasOwnProperty(row.month_of_info)) {
                dataDict[row.month_of_info] = {}
            }
            dataDict[row.month_of_info][row.info_type] = parseFloat(row.value_of_month)
        }
        return dataDict
    }
    catch(err) {
        console.log(err)
        common.log(`get turn over error : ${err.message}`,contractNumber)
        return false
    }
}

async function duplicateRealTurnoverKov(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_turnover_transaction (contract_number,info_type,month_of_info,value_of_month) select contract_number ,'real_turn_over',month_of_info ,value_of_month from loan_turnover_transaction where contract_number =$1 and info_type ='turnover';"
        return await poolWrite.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`duplicateRealTurnoverKov error : ${err.message}`,contractNumber)
    }
}

async function insertOtherTurnover(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "insert into loan_turnover_transaction (contract_number,info_type,month_of_info,value_of_month) \
        select * from (\
        select contract_number ,'net_turnover',month_of_info ,value_of_month from loan_turnover_transaction where contract_number =$1 and info_type ='turnover'\
        union\
        select contract_number ,'mod_turnover',month_of_info ,value_of_month from loan_turnover_transaction where contract_number =$1 and info_type ='turnover'\
        union\
        select contract_number,'sme_percent',month_of_info,1 from loan_turnover_transaction where contract_number =$1 and info_type ='turnover') a;"
        return await poolWrite.query(sql,[contractNumber])
    }
    catch(err) {
        common.log(`insert other turnover error : ${err.message}`,contractNumber)
        return false
    }
}

async function getTurnOverV2(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select month_of_info , value_of_month from loan_turnover_transaction where contract_number = $1 and info_type ='turnover';"
        const data = await poolWrite.query(sql,[contractNumber])
        if (data.rowCount == 0) {
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        return data.rows
    }
    catch(err) {
        common.log(`get turn over error : ${err.message}`,contractNumber)
        return false
    }
}
async function getRealTurnover(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select month_of_info , value_of_month from loan_turnover_transaction where contract_number = $1 and (info_type ='real_turn_over' or info_type = 'TOTAL_CP');"
        const data = await poolWrite.query(sql,[contractNumber])
        if (data.rowCount == 0) {
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        return data.rows
    }
    catch(err) {
        common.log(`get turn over error : ${err.message}`,contractNumber)
        return false
    }
}
async function getRealTurnoverV2(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select month_of_info , value_of_month from loan_turnover_transaction where contract_number = $1 and (info_type ='net_turnover' or info_type = 'TOTAL_CP');"
        const data = await poolWrite.query(sql,[contractNumber])
        if (data.rowCount == 0) {
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        const dataDict = {}
        for(let i in data.rows) {
            const row = data.rows[i]
            if(!dataDict.hasOwnProperty(row.month_of_info)) {
                dataDict[row.month_of_info] = {}
            }
            dataDict[row.month_of_info][row.info_type] = parseFloat(row.value_of_month)
        }
        return dataDict
        // return data.rows
    }
    catch(err) {
        common.log(`get turn over error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveTurnOrTrans(poolWrite,objectValue,infoType,contractNumber) {
	let insertData = []
	const columnSet = new pgp.helpers.ColumnSet(['contract_number', 'info_type','month_of_info','value_of_month'], {table: 'loan_turnover_transaction'});
	objectValue.forEach(element => {
		insertData.push({
			contract_number : contractNumber,
			info_type : infoType,
			month_of_info : element.month,
			value_of_month : element.amount
		})
	})

	const insertQuery = pgp.helpers.insert(insertData, columnSet)
	return await poolWrite.query(insertQuery).then().catch(error => common.log("INSERT - loan_turnover_transaction: error","ERROR"))
}

async function updateRealTurnover(contractNumber,realTurnover) {
    try {
        const poolWrite = global.poolWrite
        const promiseList = []
        for(let i in realTurnover) {
            const monthTurnover = realTurnover[i]
            const sql = "update loan_turnover_transaction set value_of_month=$1 where info_type = 'real_revenue'  and month_of_info = $2 and value_of_month = $3"
            promiseList.push(poolWrite.query(sql,[monthTurnover.amount,monthTurnover.month,contractNumber]))   
        }
        return await Promise.all(promiseList)
    }
    catch(err) {
        common.log(`updateRealTurnover error : ${err.message}`)
        return false
    }
}

async function updateRevenue(contractNumber,revenue) {
    try {
        const poolWrite = global.poolWrite
        for (let i in revenue) {
            for (let key in revenue[i]) {
                const value = revenue[i][key]
                const monthInfo = parseInt(revenue[i].month)
                const sql = 'update loan_turnover_transaction set value_of_month = $1,updated_date = now() where info_type = $2 and month_of_info = $3 and contract_number = $4'
                await poolWrite.query(sql,[value,key,monthInfo,contractNumber])
            }
        }
        return true
    }
    catch(err) {
        console.log(err)
        common.log(`save error `,contractNumber)
        return false
    }

}

async function getNetTurnover(contractNumber) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select info_type, month_of_info as month, round(value_of_month) as amount from loan_turnover_transaction where contract_number = $1 and info_type ='net_turnover' order by month_of_info"
        const data = await poolWrite.query(sql,[contractNumber])
        if (data.rowCount == 0) {   
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        return data.rows
    }
    catch(err) {
        common.log(`get turn over error : ${err.message}`,contractNumber)
        return false
    }
}

async function saveAfterLoanTurnover(objectValue,contractNumber) {
    const poolWrite = global.poolWrite
	let insertData = []
	const columnSet = new pgp.helpers.ColumnSet(['contract_number', 'info_type','month_of_info','value_of_month','created_date'], {table: 'loan_turnover_transaction'});
	objectValue.forEach(element => {
        for(let index in element.monthData) {
            insertData.push({
                contract_number : contractNumber,
                info_type : element.monthData[index].infoType,
                month_of_info : element.month,
                value_of_month : element.monthData[index].value,
                created_date : element.createdDate
            })
        }
		
	})
	const insertQuery = pgp.helpers.insert(insertData, columnSet)
	return await poolWrite.query(insertQuery).then().catch(error => common.log("INSERT - loan_turnover_transaction: error","ERROR"))
}

async function getTurnoverByInfoType(contractNumber,infoType) {
    try {
        const poolWrite = global.poolWrite
        const sql = "select * from loan_turnover_transaction where contract_number = $1 and info_type = ANY($2)"
        const data = await poolWrite.query(sql,[contractNumber,infoType])
        if (data.rowCount == 0) {
            common.log(`empty turn over data`,contractNumber)
            return false
        }
        const dataDict = {}
        for(let i in data.rows) {
            const row = data.rows[i]
            if(!dataDict.hasOwnProperty(row.month_of_info)) {
                dataDict[row.month_of_info] = {}
                dataDict[row.month_of_info].month = row.month_of_info
                dataDict[row.month_of_info].createdDate = row.created_date
            }
            dataDict[row.month_of_info][row.info_type] = parseFloat(row.value_of_month)
        }
        return dataDict
    }
    catch(err) {
        console.log(err)
        return false
    }
}

module.exports = {
    saveVdsTurnOver,
    getTurnOver,
    getTurnOverV2,
    duplicateRealTurnoverKov,
    getRealTurnover,
    getRealTurnoverV2,
    saveTurnOrTrans,
    updateRealTurnover,
    getTurnOverTransaction,
    insertOtherTurnover,
    updateRevenue,
    getNetTurnover,
    saveAfterLoanTurnover,
    getTurnoverByInfoType
}
