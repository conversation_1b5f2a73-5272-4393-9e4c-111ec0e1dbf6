const { findOne } = require("../utils/sqlHelper")

const getByContractAndLocation = async(contractNumber, location) => {
    return await(findOne(
        {
            table: "ui_review",
            whereCondition:{
                contract_number: contractNumber,
                param_location: location
            },
            orderBy: "id DESC"
        }
    ))
}

const findByContract = async (contractNumber) => {
    try {
        const sql = "select * from ui_review where contract_number = $1"
        const rs = await poolRead.query(sql, [contractNumber])
        if (rs.rowCount == 0) {
            return []
        }  
        return rs.rows
    } catch (error) {
        console.log(`findByContract contractNumber: ${contractNumber} error: ${error.message}`);
        return []
    }
}

const insert = async({contractNumber, paramLocation, comment, step}) => {
    try {
        const sql = "insert into ui_review(contract_number, param_location, comment, step) values($1, $2, $3, $4)"
        const rs = await poolWrite.query(sql, [contractNumber, paramLocation, comment, step])
        if (rs.rowCount == 0) {
            console.log(`[UI-REVIEW-REPO]insert contractNumber: ${contractNumber}, paramLocation ${paramLocation} error: insert fail`);
            return false
        }
        return true;
    } catch (error) {
        console.log(`[UI-REVIEW-REPO]insert contractNumber: ${contractNumber}, paramLocation ${paramLocation} error: ${error.message}`);
        return false
    }
}

module.exports = {
    getByContractAndLocation,
    findByContract,
    insert
}