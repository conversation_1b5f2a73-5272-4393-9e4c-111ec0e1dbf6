const express = require('express');
const router = express.Router();
const {kovA1, MisaA1, FinvA1} = require("../A1_BASE/a1-app-form-service")
const {MisaBasicValidate} = require("../utils/validator/misa-validator")
const {KOVBasicValidate} = require("../utils/validator/kov-validator");
const { authenticateOauth2V03WithToken, authenticateOauth2V04WithToken } = require('../utils/aaaService');
const { FinvAf1Validate } = require('../utils/validator/finv-validator');
const { decryptRequestFinv, encryptResponseFinv } = require('../utils/middleware');
// const {VSKCreateKunnValidate} = require("../utils/validator/vsk-validator")

router.post('/kov/check-a1', authenticateOauth2V03WithToken, KOVBasicValidate, (req, res) => {
    const kova1 = new kovA1(req, res)
    kova1.createLoan()
})

router.post('/misa/check-a1', authenticateOauth2V03WithToken, MisaBasicValidate, (req, res) => {
    const misaA1 = new MisaA1(req,res)
    misaA1.createLoan()
})

// router.post(
//   "/finv/check-a1",
//   FinvAf1Validate,
//   (req, res) => {
//     const finvA1 = new FinvA1(req, res);
//     finvA1.createLoan();
//   }
// );

module.exports = router
