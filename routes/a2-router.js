const express = require('express');
const router = express.Router();
const {kovA2, MisaA2, finvA2, genFileResult} = require("../A2_BASE/a2-app-form-service");
const { authenticateOauth2V03WithToken, authenticateOauth2V04WithToken } = require('../utils/aaaService');
const {KOVFullLoanValidate} = require("../utils/validator/kov-validator")
const {MisaFullloanValidate} = require("../utils/validator/misa-validator")
const {FinvFullloanValidate, finvA3Validate} = require("../utils/validator/finv-validator");
const { encryptResponseFinv ,decryptRequestFinv} = require('../utils/middleware');

router.post('/kov/check-a2', authenticateOauth2V03WithToken, KOVFullLoanValidate, (req, res) => {
    const kova2 = new kovA2(req, res)
    kova2.a2Receive()
})

router.post('/misa/check-a2', authenticateOauth2V03WithToken, MisaFullloanValidate, (req, res) => {
    const misa2 = new MisaA2(req,res)
    misa2.a2Receive()
})

// router.post(
//   "/finv/check-a2",
//   authenticateOauth2V04WithToken,
//   decryptRequestFinv,
//   FinvFullloanValidate,
//   encryptResponseFinv,
//   (req, res) => {
//     const finv2 = new finvA2(req, res);
//     finv2.a2Receive();
//   }
// );

// router.post(
//   "/finv/check-a3",
//   authenticateOauth2V04WithToken,
//   decryptRequestFinv,
//   finvA3Validate,
//   encryptResponseFinv,
//   (req, res) => {
//     const finv2 = new finvA2(req, res);
//     finv2.a3Receive();
//   }
// );

// router.post(
//     "/doc/gen-file-result",
//     (req, res) => {
//         genFileResult(req,res)
//     }
//   );
  

module.exports = router
