const express = require("express");
const router = express.Router();
const { handleCallbackCicDetailsSme, checkConsent, handleCallbackCic, handleCallbackCicIndividual } = require("../services/anti-fraud");
const { throwBadReqError } = require("../utils/helper");
const { MISA_ERROR_CODE } = require("../const/response-const");
const { handleResponseError } = require("../base/response");
const { authenticate, authenticateOauth2V04WithToken } = require("../utils/aaaService");
const { encryptResponseFinv } = require("../utils/middleware");
const { goNextStep } = require("../services/workflow-continue");
const loggingRepo = require("../repositories/logging-repo");
const { SERVICE_NAME, TASK_FLOW } = require("../const/definition");

router.post("/cic/sme/results", async (req, res) => {
  try {
    const {
      requestId,
      contractNumber,
      status,
      decision,
      cicData,
      contractType,
    } = req.body;
    if (!requestId || !contractNumber || !status || !decision)
      return res.status(400).json({
        code: -1,
        msg: `Missing field`,
      });

    await handleCallbackCicDetailsSme({
      requestId,
      contractNumber,
      status,
      decision,
      cicData,
      contractType,
    });
    return res.status(200).json({
      code: 1,
      msg: `contract ${contractNumber} update successfully.`,
    });
  } catch (err) {
    return res.status(500).json({
      code: -1,
      msg: `Get contract detail error ${err.message}`,
    });
  }
});

router.get("/check-consent",authenticateOauth2V04WithToken,encryptResponseFinv, async (req, res) => {
  try {
    const { phoneNumber, phoneTelco } = req.query;
    if (!phoneNumber) {
      throwBadReqError(
        "query.phoneNumber",
        "Thiếu thông tin số điện thoại",
        MISA_ERROR_CODE.E400
      );
    }

    const data = await checkConsent({phoneNumber,phoneTelco});
    return res.json(data);
  } catch (error) {
    return handleResponseError(res, error);
  }
});


router.post("/cic/result", async (req, res) => {
  try {
    const {
      requestId,
      contractNumber,
      decisionCode,
      rjSubCode,
      reasonCode,
      groupReason,
      contractRenewDate
    } = req.body;
    if (!contractNumber || !decisionCode)
    {
      throwBadReqError(
        "body",
        "Thiếu thông tin",
        MISA_ERROR_CODE.E400
      );
    }

    loggingRepo.saveStepLog(
      contractNumber,
      SERVICE_NAME.ANTI_FRAUD,
      TASK_FLOW.CALLBACK_CIC,
      req.body,
      {},
      req?.url || "/cic/result"
    );

    await handleCallbackCic({
      requestId,
      contractNumber,
      decisionCode,
      rjSubCode,
      reasonCode,
      groupReason,
      contractRenewDate
    });
    return res.status(200).json({
      code: 0,
      msg: `contract ${contractNumber} update successfully.`,
    });
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/cic/result-withdraw", async (req, res) => {
  try {
    const payload = req.body;
    const {
      contractNumber,
      decisionCode,
    } = payload;
    if (!contractNumber || !decisionCode)
    {
      throwBadReqError(
        "body",
        "Thiếu thông tin",
        MISA_ERROR_CODE.E400
      );
    }

    await handleCallbackCicIndividual(payload);
    return res.status(200).json({
      code: 1,
      msg: `contract ${contractNumber} update successfully.`,
    });
  } catch (err) {
    return res.status(500).json({
      code: -1,
      msg: `Get contract detail error ${err.message}`,
    });
  }
});

module.exports = router;
