const express = require('express');
const router = express.Router();
const { CONTRACT_TYPE } = require("../const/definition");
const productService = require("../utils/productService");
const common = require("../utils/common");
const constant = require('../const/definition');
const { McAppBasicValidate, McAppFullloanValidate } = require('../utils/validator/mc-app-validator');
const { McAppA1 } = require('../A1_BASE/a1-app-form-service');
const { McAppA2 } = require('../A2_BASE/a2-app-form-service');
const { STATUS } = require('../const/caseStatus');
const { getAllDocumentByContractNumber } = require('../repositories/document');
const { isNullOrEmpty } = require('../utils/helper');

router.get('/product/info', async (req, res) => {
    try {
        let productCode = constant.PRODUCT_CODE.MCTAX_HMTD;
        // const contractType = req.query.contractType
        // if(contractType==CONTRACT_TYPE.CREDIT_LINE){
        //     productCode = "MCBAS_HMTD"
        // }

        const productInfo = await productService.getProductInfoV2(productCode)
        return res.status(200).json({
            code: 1,
            msg: "get product list sucessfully.",
            data: {
                productCode: productCode,
                minTenor: parseFloat(productInfo.productVar[0].tenorFrm),
                maxTenor: parseFloat(productInfo.productVar[0].tenorTo),
                tenorStep: parseFloat(productInfo.productVar[0].tenorStep),
                minAmt: parseFloat(productInfo.productVar[0].minAmt),
                maxAmt: parseFloat(productInfo.productVar[0].maxAmt),
                amountStep: parseFloat(productInfo.productVar[0].amountStep),
                rate: parseFloat(productInfo.productVar[0].intRate / 100)
            }
        })
    }
    catch (err) {
        return common.responseErrorPublic(res);
    }
});

router.post('/basic/check', McAppBasicValidate, (req, res) => {
    const mcAppA1 = new McAppA1(req, res);
    mcAppA1.createLoan();
});

router.post('/full-loan/check', McAppFullloanValidate, async (req, res) => {
    const mcAppA1 = new McAppA1(req, res);
    const rsA1 = await mcAppA1.createLoan();
    if (rsA1?.message == STATUS.ELIGIBLE) {
        req.body.contractNumber = rsA1.contractNumber;
        const mcAppA2 = new McAppA2(req, res);
        mcAppA2.a2Receive();
    } else {
        return res.status(200).json(rsA1);
    }
});

router.get('/get-docs-by-contract-number', async (req, res) => {
    const {contractNumber} = req.query
    if(isNullOrEmpty(contractNumber))
        return res.status(404).json({
            code: 1,
            message: 'Missing contract number',
            data: []
        });
    const docs = await getAllDocumentByContractNumber(contractNumber);
    if (docs?.length > 0) {
        return res.status(200).json({
            code: 0,
            message: 'get data successfully',
            data: docs,
        });
    } else {
        return res.status(200).json({
            code: 0,
            message: 'get data successfully',
            data: []
        });
    }
});

module.exports = router