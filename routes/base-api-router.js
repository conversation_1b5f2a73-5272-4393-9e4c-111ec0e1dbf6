const express = require('express');
const loanContractRepo = require("../repositories/loan-contract-repo")
const documentRepo = require("../repositories/document")
const crmService = require("../services/crm-service")
const crmHelper = require("../utils/crmService")
const lmsService = require("../services/lms-service")
const apiValidator = require("../utils/validator/base-api-validator")
const productService = require("../utils/productService")
const loggingService = require("../utils/loggingService")
const loggingRepo = require("../repositories/logging-repo")
const {isValidResubmit} = require("../utils/validator/logic-validator")
const offerRepo = require("../repositories/offer")
const masterDataService = require("../app")
const contractGw = require("../services/contract-service")
const {MANUAL_TASK_CODE, roleCode,BUNDLE_STAGE, CONTRACT_TYPE, PARTNER_CODE, TURNOVER_TYPE, CHANNEL, SERVICE_NAME} = require("../const/definition")
const {STATUS,caseStatusCode,MAPPING_CALLBACK_STATUS,MAPPING_STATUS_CLIENT_CODE, CALLBACK_STAUS, ActionAuditCaseStatus, KUNN_STATUS} = require("../const/caseStatus")
const aadService = require("../utils/aadService")
const router = express.Router();
const common = require("../utils/common")
const _  = require("underscore")
const callbackService = require("../services/callback-service")
const { getPlace, getValueCode_v3, getValueCode} = require('../utils/masterdataService')
const dateHelper = require("../utils/dateHelper")
const smsService = require("../utils/smsService")
const utils = require("../utils/helper")
const kunnRepo = require('../repositories/kunn-repo');
const camelcaseKeys = require('camelcase-keys');
const {routing} = require("../services/workflow-service")
const {CRM} = require("../const/service-response-const")
const {saveAfterLoanTurnover} = require("../repositories/turnover-repo")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const loanAttributeRepo = require("../repositories/loan-atribute-repo")
const kunnPrepareAttributeRepo = require("../repositories/kunn-prepare-attribute-repo");
const { createKunnWithoutLimit } = require('../super_app/application-form-service');
const { genKunn } = require('../KUNN/update_status');
const sqlHelper = require("../utils/sqlHelper")
const actionAuditService = require("../services/action-audit");
const { STEP, KUNN_API_VERSION } = require('../const/variables-const');
const { callbackKunnCancelApi } = require('../apis/misa-api');

router.get("/caseDetail/contractDetail",async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber
        // const contractType = req.query.contractType
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        if(!contractData) {
            return res.status(404).json({
                code : -1,
                msg : "Invalid contract_number"
            })
        }
        const response = {
            "identity_card": contractData.id_number,
            "date_of_birth": contractData.birth_date,
            "customer_name": contractData.cust_full_name,
            "entry_date": contractData.created_date,
            "tem_address": "",
            "permanent_address": "",
            "channel": contractData.channel,
            "partner_code": contractData.partner_code
        }
        return res.status(200).json({
            code : 1,
            msg : "get contract detail successfully.",
            data : response
        })
    }
    catch(err) {
        return res.status(500).json({
            code : -1,
            msg : `Get contract detail error ${err.message}`
        })
    }
})

router.post("/dedup/update",async(req,res) => {
    try {
        const data = req.body;
        const contract_number = data.contract_number||data.contractNumber;
        const cust_id = data.cust_id;
        const act = parseInt(data.act);
        let isKovCredit = false;
        let contractData = await loanContractRepo.getPartnerCodeAndProductCode(contract_number)
        const loanData = await loanContractRepo.getLoanContract(contract_number)
        const partnerCode = contractData.partner_code
        if(loanData.partner_code === PARTNER_CODE.KOV && loanData.contract_type === CONTRACT_TYPE.CREDIT_LINE) isKovCredit = true;
        let workflowBody = {
            contract_number : contract_number,
            partner_code : partnerCode,
            product_code : contractData.product_code
        }
        //call wf cũ cho kov creditline
        const currentTaskCode = 'MC_APPROVE_DED';
        let flowData = {
            contractNumber: contract_number,
            dob: moment(loanData.birth_date).format('DD-MM-yyyy'),
            issueDate: moment(loanData.id_issue_dt).format('DD-MM-yyyy'),
            gender: loanData.gender || 'F',
            phoneNumber: loanData.phone_number1,
            custName: loanData.cust_full_name,
            idNumber: loanData.id_number,
            partnerCode: PARTNER_CODE.KOV,
            email: loanData.email || '',
            bankAccount: loanData.bank_account || '',
            loanAmount: loanData.request_amt || 0,
            current_task_code: currentTaskCode
        };
        
        let datas = {
			"contractNumber" : contract_number,
		    "phoneNumber": loanData.phone_number1,
		    "fullName": loanData.cust_full_name,
		    "idNumber": loanData.id_number,
		    "idIssueDt": moment(loanData.id_issue_dt).format('DD-MM-yyyy'),
		    "birthDate": moment(loanData.birth_date).format('DD-MM-yyyy'),
            contractType : CONTRACT_TYPE.CREDIT_LINE,
            dob: moment(loanData.birth_date).format('DD-MM-yyyy'),
            issueDate: moment(loanData.id_issue_dt).format('DD-MM-yyyy'),
            gender: loanData.gender || 'F',
            custName: loanData.cust_full_name,
            partnerCode: PARTNER_CODE.KOV,
            email: loanData.email || '',
            bankAccount: loanData.bank_account || '',
            loanAmount: loanData.request_amt || 0,
            current_task_code: currentTaskCode
		}
        flowData.data = datas
        //console.log('bodyApproveDed',JSON.stringify(flowData))
        let lb = req.config.basic.wfMcCredit[req.config.env];
        const envType = req.config.data.env.wf_uri;
        let wf_lb = lb
        if(envType=='local') {
            wf_lb = "http://localhost:1001"
        }
        let workflowUri = req.config.data.workflow.uri;
        let workflowUrl = wf_lb + workflowUri;
        if(act === CRM.MANUAL_DEDUP.MERGED || act === CRM.MANUAL_DEDUP.NEW){
            await loanContractRepo.updateCustId(cust_id,contract_number)
            await crmHelper.createCustomerService(contract_number)
            if(!isKovCredit){
                workflowBody.currentTask = MANUAL_TASK_CODE.DEDUP_MANUAL.DEDUP_APPROVE
                routing(workflowBody)
            }else{
                common.postAPI(workflowUrl,flowData).then()
                .catch(err =>{
                    console.log(err)
                    common.log("CALL WORKFLOW : error","ERROR")
                })
            }
            return res.status(200).json({
                code : 1,
                msg : "Create dedup successfully."
            })
        }
        else if(act === CRM.MANUAL_DEDUP.CANCELLED){
            if(contractData.partner_code == PARTNER_CODE.SMA) 
                callbackService.callbackPartner(contract_number, partnerCode, CALLBACK_STAUS.CANCELLED)
            loanContractRepo.updateContractStatus(STATUS.CANCELLED, contract_number)
            workflowBody.currentTask = MANUAL_TASK_CODE.DEDUP_MANUAL.DEDUP_CANCEL
            routing(workflowBody)
            return res.status(200).json({
                code : 1,
                msg : "Cancel dedup successfully."
            })
        } else if (act === CRM.MANUAL_DEDUP.FRAUD) {
            if(partnerCode == PARTNER_CODE.SMA)
                callbackService.callbackPartner(contract_number, partnerCode, CALLBACK_STAUS.REJECTED)
            loanContractRepo.updateContractStatus(STATUS.REFUSED, contract_number)
            workflowBody.currentTask = MANUAL_TASK_CODE.DEDUP_MANUAL.DEDUP_CANCEL
            routing(workflowBody)
            return res.status(200).json({
                code : 1,
                msg : "Fraud dedup successfully."
            })
        }
    }
    catch(err){
        console.log(err)
        common.log(`get bundle error : ${err.message} `,req.query.contractNumber)
        return res.status(500).json({
            code : 1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/contract/update-status",apiValidator.validLMSCallback,lmsService.updateStatus)

router.get("/bundle/bundle-by-contract",apiValidator.validRequireContract,async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber
        const contractData = await loanContractRepo.getPartnerCodeAndProductCode(contractNumber)
        const productCode = contractData.product_code
        const productBundle = await productService.getBundleV3(global.config,productCode,'SIGNING')
        // const bundleList = productBundle.data
        // const docList = []
        // for(let idx in bundleList) {
        //     let bundleObj = bundleList[idx]
        //     for (let idx2 in bundleObj.docList) {
        //         docList.push(bundleObj.docList[idx2].docType)
        //     }
        // }
        return res.status(200).json(productBundle)
    }
    catch(err) {
        console.log(err)
        common.log(`get bundle error : ${err.message} `,req.query.contractNumber)
        return res.status(500).json({
            code : 1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

router.get("/bundle/resubmit-bundle",apiValidator.validRequireContract,async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber
        const resubmitList = await documentRepo.getResubmitListV2(contractNumber)
        const resultObj = {}
        const resultList = []
        const loanContractData = await loanContractRepo.getLoanContract(contractNumber);
        const productCode = loanContractData?.product_code;
        const bundleList = await productService.getBundleV3(req.config,productCode);
        let docList = [];
        let docTypeList = [];
        (bundleList?.data ?? []).forEach(x => {
            docList.push(...(x?.docList || []));
        });
        docList.forEach(x => {
            docTypeList.push(x.docType);
        })
        for(let idx in resubmitList) {
            let doc = resubmitList[idx]
            if(!resultObj.hasOwnProperty(doc.doc_group)) {
                resultObj[doc.doc_group] = []
            }
            resultObj[doc.doc_group].push({
                docType : doc.doc_type,
                resubmitReason : doc.deviation_cmt ? `${doc.mistake_desc} : ${doc.deviation_cmt}` : doc.mistake_desc
            })
        }
       
        for(let key in resultObj) {
            resultList.push({
                docGroup : key,
                docList : resultObj[key]
            })
        }

        (resultList ?? []).forEach(x => {
                (x.docList ?? []).forEach(y => {
                    // console.log({x,y})
                    y.nameVN = docTypeList.includes(y.docType) ? docList.find(m => m.docType == y.docType)?.docVnName : "Chứng từ khác"
                })
        })
        
        return res.status(200).json({
            code : 1,
            msg : "get resubmit list successfully.",
            data : resultList
        })
    }
    catch(err) {
        console.log(err)
        common.log(`get bundle resubmit error : ${err.message} `,req.query.contractNumber)
        return res.status(500).json({
            code : 1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

router.get("/offer/get",apiValidator.validRequireContract, async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber
        const loanAttributeData = await loanAttributeRepo.getDataByContractNumber(contractNumber)
        let isSuperApp = false
        if (loanAttributeData) {
            isSuperApp = loanAttributeData?.isWithdrawRequest === 'T' ? true : false
        }
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        let prdCode
        if(contractData!=false){
            prdCode = contractData.product_code
        } 
        let offerRs = await offerRepo.getOfferList(contractNumber,roleCode.customer)
        if(isSuperApp) {
            offerRs = await offerRepo.getOfferListSma(contractNumber)
        }
        
        const offerList = []
        for(let idx in offerRs) {
            let offer = offerRs[idx]
            offerList.push({
                offerId : offer.id,
                offerAmount : parseInt(offer.offer_amt),
                offerRate : parseFloat(offer.int_rate),
                offerTenor : parseInt(offer.tenor),
                monthlyInstallment : prdCode=='MCBAS_HMTD'?0:await productService.getMonthlyInstallment(offer.offer_amt,offer.int_rate,offer.tenor)
            })
        }
        return res.status(200).json({
            code : 1,
            msg : "get offer list successfully.",
            data : offerList
        })
    }
    catch(err) {
        console.log(err)
        common.log(`get bundle resubmit error : ${err.message} `,req.query.contractNumber)
        return res.status(500).json({
            code : 1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/bundle/resubmit",apiValidator.validResubmitDoc,async (req,res) => {
    try {
        const poolWrite = global.poolWrite
        const {contractNumber,requestId,listDocResubmit} = req.body
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        await aadService.completedTaskByRole(contractNumber,roleCode.SS)
        const waitingResubmitList = await documentRepo.getResubmitList(contractNumber)
        const validResubmit = isValidResubmit(waitingResubmitList,listDocResubmit)
        if(!waitingResubmitList || !validResubmit) {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : "Invalid list_doc_resubmit",
                data : {
                    contractNumber,
                    requestId
                }
            })
        }
        
        const bundleInfo = await productService.getBundle(global.config,contractData.product_code)
		const newDocList = productService.mapBundleGroup(listDocResubmit,bundleInfo.data)
        
        const resubmitRs = await documentRepo.resubmitDoc(contractNumber,newDocList)
        
        if(resubmitRs.result) {

			const resubmitRole = await loanContractRepo.getLastStepResubmit(contractNumber)
			if(!resubmitRole) {
				common.log(`INVALID SALE SUPPORT RESUBMIT `,contractNumber)
			}
            let status;
			switch(resubmitRole) {
				case roleCode.CP:
					status = STATUS.IN_CP_QUEUE;
					break;
				case roleCode.CE :
					status = STATUS.IN_CE_QUEUE;
					break;
				default:
					status = `IN_${resubmitRole}_QUEUE`
					break;
			}
			await aadService.pushTaskMcV2(resubmitRole,contractNumber,contractData.contract_type,status)
			await loanContractRepo.updateContractStatus(status,contractNumber)
            
            const responseBody = {
                code : 'SUCCESS',
                msg : resubmitRs.msg,
                data : {
                    contractNumber,
                    requestId
                }
            }
            await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contractNumber,requestId,"")
            return res.status(200).json(responseBody)
        }else {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : resubmitRs.msg,
                data : {
                    contractNumber,
                    requestId
                }
            })
        }
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : -1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/offer/select",apiValidator.validSelectOffer,async(req,res) => {
    try {
        const poolWrite = global.poolWrite
        const {requestId,contractNumber,selectedOfferId} = req.body
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        if(contractData.status !== caseStatusCode.KH11 && contractData.status !== STATUS.ACCEPTED_WITH_OFFERS && contractData.status !== STATUS.MC_APPROVED) {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : "Invalid contractNumber",
                data : {
                    requestId,
                    contractNumber
                }
            })
        }
        const selectOfferRs = await offerRepo.selectOffer(poolWrite,selectedOfferId,contractNumber)
        if(selectOfferRs.result) {
            const responseBody = {
                code : 'RECEIVED',
                msg : 'The offer selection has been received and is being processed.',
                data : {
                    requestId,
                    contractNumber
                }
            }
            await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contractNumber,requestId,"")
            await loanContractRepo.updateContractStatus(STATUS.SELECTED_OFFER,contractNumber)
            // const isGenContractReview = await loanContractRepo.checkGenerateContractReview(contractNumber);
            // if(!utils.isNullOrEmpty(contractData?.root_contract_number)){
            //     const oldContractNumber = contractData?.root_contract_number;
            //     console.log({contractData})
            //     Promise.all([
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'province_cur',contractData?.province_cur),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'district_cur',contractData?.district_cur),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'ward_cur',contractData?.ward_cur),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'address_cur',contractData?.address_cur),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'province_per',contractData?.province_per),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'district_per',contractData?.district_per),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'ward_per',contractData?.ward_per),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'address_per',contractData?.address_per),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'id_number',contractData?.id_number),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'issue_date',contractData?.issue_date),
            //         loanContractRepo.updateFieldLoanContract(oldContractNumber,'issue_place',contractData?.issue_place)
            //     ])
            // }
            // if(isGenContractReview){
            //     contractGw.generateContract(contractNumber,contractData.partner_code)
            // }
            // else{
            //     loanContractRepo.updateContractStatus(STATUS.ACTIVATED,contractNumber)
            // }
            await contractGw.generateContract(contractNumber,contractData.partner_code,true)
            return res.status(200).json(responseBody)
        }
        else {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : selectOfferRs.msg,
                data : {
                    requestId,
                    contractNumber
                }
            })
        }
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : 'SERVER_ERROR',
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

router.get("/caseDetail/contract-detailv2",async(req,res) => {
    try {
        const {custId,contractNumber} = req.query
        const contractData = await loanContractRepo.getLoanContractV2(custId,contractNumber)
        if(!contractData) {
            return res.status(404).json({
                code : -1,
                msg : "Invalid custId"
            })
        }
        let res1 = [] 
        contractData.forEach(element => {
            const e = {
                "customer_name": element.cust_full_name,
                "identity_card": element.id_number,
                "contract_number": element.contract_number,
                "cust_id": element.third_party_cust_id,
                "status": MAPPING_STATUS_CLIENT_CODE.hasOwnProperty(element.status) ? MAPPING_STATUS_CLIENT_CODE[element.status] : element.status ,
                "approval_amt": element.approval_amt ? parseInt(element.approval_amt) : element.approval_amt,
                "approval_date": element.approval_date,
                "approval_int_rate": element.approval_int_rate,
                "payment_due_date": null,
                "monthly_payment_amount": null
            }
            res1.push(e)
        });
        return res.status(200).json({
            code : 1,
            msg : "get contract detail successfully.",
            data : res1
        })
    }
    catch(err) {
        return res.status(500).json({
            code : -1,
            msg : `Get contract detail error ${err.message}`
        })
    }
})

router.put('/contract/cancel',apiValidator.validRequireContractBody,async(req,res) => {
    try {
        const {contractNumber,comment,user} = req.body
        await aadService.cancelTaskByContract(contractNumber)
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        await loanContractRepo.updateContractStatus(STATUS.CANCELLED,contractNumber)
        await loggingRepo.saveWorkflow('CANCELLED',comment,contractNumber,user)
        await callbackService.callbackPartner(contractNumber,contractData.partner_code,CALLBACK_STAUS.CANCELLED)
        await crmHelper.removeContract(global.config,contractNumber)
        return res.status(200).json({
            code : 1,
            msg : `CANCEL contract ${contractNumber} Success.`
        })
    }
    catch(err) {
        common.responseErrorInternal(res,err)
    }
})

router.post('/signed/document/submit',async(req,res) => {
    try {
        const {contractNumber,docList} = req.body
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        await documentRepo.updateUploadAfterSigned(contractNumber,docList)
        await aadService.pushTaskMcV2(roleCode.CP,contractNumber,contractData.contract_type,STATUS.IN_CP_POST_QUEUE)
        await loanContractRepo.updateContractStatus(STATUS.IN_CP_POST_QUEUE,contractNumber)
        return res.status(200).json({
            code : 1,
            msg : "submit doc successfully."
        })
    }
    catch(err) {
        console.log(err);
        common.responseErrorInternal(res,err)
    }
})


router.get('/signed/document/resubmit/get',async(req, res) => {
    try {
        let pl = req.query
        const contractNumber = pl.contractNumber
        if (!pl || !pl.contractNumber) return res.status(400).json({ msg: 'invalid input' })

        let loanContractObj = await loanContractRepo.getLoanContract(pl.contractNumber);

        if (!loanContractObj) return res.status(200).json({ code: 2, msg: `Contract number ${pl.contractNumber} not exists` })

        if (![STATUS.CP_RESUBMIT,STATUS.SS_RESUBMIT].includes(loanContractObj.status)) return res.status(200).json({ code: 2, msg: `Contract number ${pl.contractNumber} status does not ` })

        let productBundleData = await productService.getBundle(global.config, loanContractObj.product_code, BUNDLE_STAGE.DIBURSEMENT)
        const bundleList = productBundleData.data
        const bundleData = await documentRepo.getDisbursementResubmit(contractNumber)
        
        bundleList.forEach(element => {
            element.docList.forEach(element => {
                let obj = _.where(bundleData,{doc_type: element.docType})[0] || {}
                
                element.mistakeDes = ""
                element.cmt = ""
                element.isResubmit = obj.waiting_resubmit || 0
            });
            element.docGroup = element.bundleName
        });
        res.status(200).json({
            code: 1,
            msg: 'Get docs resubmit successfully.',
            data: bundleList
        })
    } 
    catch(err) {
        console.log(err)
        common.responseErrorInternal(res,err)
    }
})

router.get('/product/info',async(req,res) => {
    try {
        let productCode = "MCBAS_ALL"
        const contractType = req.query.contractType
        if(contractType==CONTRACT_TYPE.CREDIT_LINE){
            productCode = "MCBAS_HMTD"
        }
        
        const productInfo = await productService.getProductInfoV2(productCode)
        return res.status(200).json({
            code : 1,
            msg : "get product list sucessfully.",
            data : {
                productCode : productCode,
                minTenor : parseFloat(productInfo.productVar[0].tenorFrm),
                maxTenor : parseFloat(productInfo.productVar[0].tenorTo),
                tenorStep : parseFloat(productInfo.productVar[0].tenorStep),
                minAmt : parseFloat(productInfo.productVar[0].minAmt),
                maxAmt : parseFloat(productInfo.productVar[0].maxAmt),
                amountStep : parseFloat(productInfo.productVar[0].amountStep),
                rate : parseFloat(productInfo.productVar[0].intRate / 100)
            }
        })
    }
    catch(err) {
        return common.responseErrorPublic(res)
    }

})

router.get('/caseDetail/contractDetailV2', async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber;
        const requestId = req.query.requestId;
        let contractData = await loanContractRepo.getLoanContract(contractNumber)
        if(requestId != undefined) {
            contractData = await loanContractRepo.getLoanContractByRequestId(requestId)
        }
        if(!contractData) {
            return res.status(404).json({
                code : -1,
                msg : "Invalid contract_number"
            })
        }
        const masterData = await Promise.all([
            getValueCode_v3(contractData.id_issue_place,"ISSUE_PLACE_VN"),
            getValueCode_v3(contractData.ward_cur,"WARD"),
            getValueCode_v3(contractData.district_cur,'DISTRICT'),
            getValueCode_v3(contractData.province_cur,'PROVINCE'),
            getValueCode_v3(contractData.ward_per,"WARD"),
            getValueCode_v3(contractData.district_per,'DISTRICT'),
            getValueCode_v3(contractData.province_per,'PROVINCE'),
            getValueCode(req,contractData.reference_type_1,"FONCTION_INTERLOCUTEUR"),
            getValueCode(req,contractData.reference_type_2,"FONCTION_INTERLOCUTEUR"),
            getValueCode(req,contractData.empl_type, "EMPLOYMENT_TYPE")
        ])
        const response = {
            contract_number: contractData.contract_number,
            cust_id: contractData.cust_id,
            customer_name: contractData.cust_full_name,
            id_number: contractData.id_number,
            phone_number: contractData.phone_number1 || '',
            email: contractData.email || '',
            date_of_birth: dateHelper.formatDate(contractData.birth_date, "YYYY-MM-DD") || '',
            issue_date: dateHelper.formatDate(contractData.id_issue_dt, "YYYY-MM-DD") || '',
            gender: contractData.gender,
            issue_place: masterData[0] || '',
            reference_1: masterData[7],
            relative_reference_name_1: contractData.reference_name_1,
            relative_reference_phone_1: contractData.reference_phone_1,
            reference_2: masterData[8],
            relative_reference_name_2: contractData.reference_name_2,
            relative_reference_phone_2: contractData.reference_phone_2,
            employment_type: masterData[9],
            company_name: contractData.empl_name || contractData.workplace_name || '',
            company_address: contractData.empl_address || contractData.workplace_address || '',
            monthly_income: contractData.monthly_income,
            other_income: contractData.other_income,
            status: contractData.status,
            monthly_expenses: contractData.month_fee,
            tem_province: masterData[3],
            tem_district: masterData[2],
            tem_ward: masterData[1],
            tem_detail_address: contractData.address_cur,
            permanent_province: masterData[6],
            permanent_district: masterData[5],
            permanent_ward: masterData[4],
            permanent_detail_address: contractData.address_per,
            requestLoan: contractData.request_amt,
            requestTenor: contractData.request_tenor,
            grantedLoan: contractData.approval_amt || contractData.request_amt,
            grantedTenor: contractData.approval_tenor || contractData.request_tenor
        }

        return res.status(200).json({
            code : 1,
            msg : "get contract detail successfully.",
            data : response
        })
    }
    catch(err) {
        return res.status(500).json({
            code : -1,
            msg : `Get contract detail error ${err.message}`
        })
    }
})

router.get("/caseDetail/contractDetailV3",async(req,res) => {//for kunn get info customer by kunnId
    try {
        const contractNumber = req.query.contractNumber
        // const contractType = req.query.contractType
        let contractData;
        if(req.query.type!=undefined && req.query.type!='' && req.query.type=='webview'){
            contractData = await loanContractRepo.getLoanContract(contractNumber)
        }
        else{
            const kunnData = await kunnRepo.getKunnData(contractNumber)
            contractData = await loanContractRepo.getLoanContract(kunnData.contract_number)
        }
        
        if(!contractData) {
            return res.status(404).json({
                code : -1,
                msg : "Invalid kunn_number"
            })
        }
        const response = {
            "identity_card": contractData.id_number,
            "date_of_birth": contractData.birth_date,
            "customer_name": contractData.cust_full_name,
            "entry_date": contractData.created_date,
            "tem_address": "",
            "permanent_address": "",
            "channel": contractData.channel,
            "partner_code": contractData.partner_code
        }
        return res.status(200).json({
            code : 1,
            msg : "get contract detail successfully.",
            data : response
        })
    }
    catch(err) {
        return res.status(500).json({
            code : -1,
            msg : `Get contract detail error ${err.message}`
        })
    }
})

router.get('/caseDetail/contractDetailSme', async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber;
        let contractData = await loanContractRepo.getLoanContractByPartner(contractNumber, PARTNER_CODE.MISA)
        const requestId = req.query.requestId;
        if(requestId != undefined) {
            contractData = await loanContractRepo.getLoanContractByRequestId(requestId)
        }
        if(!contractData) {
            return res.status(404).json({
                code : -1,
                msg : "Invalid contract_number"
            })
        }

        await getDataPlace(req, contractData)

        return res.status(200).json({
            code : 1,
            msg : "Get contract detail successfully.",
            data : contractData
        })
    }
    catch(err) {
        return res.status(500).json({
            code : -1,
            msg : `Get contract detail error ${err.message}`
        })
    }
})

router.get('/caseDetail/civilData', async(req,res) => {
    try {
        const custId = req.query.custId;
        let contractData = await loanContractRepo.getLoanContractByCustId(custId)
        if(!contractData) {
            return res.status(200).json({
                code : -1,
                msg : `Not found loan of custId: ${custId}`
            })
        }

        await getDataPlace(req, contractData)

        return res.status(200).json({
            code : 1,
            msg : "Get contract detail successfully.",
            data : camelcaseKeys(contractData)
        })
    }
    catch(err) {
        return res.status(500).json({
            code : -1,
            msg : `Get contract detail error ${err.message}`
        })
    }
})

async function getBranchAddress(poolRead,contractNumber,req) {
    const sql = "select * from loan_branch_address where contract_number = $1"
    const result = await poolRead.query(sql,[contractNumber])
    const branchResult = []
    for (let i in result.rows) {
        const element = result.rows[i]
        const data = await Promise.all([getPlace(req,element.province,'provinces'),getPlace(req,element.district, 'districts'),getPlace(req,element.ward,"wards")])
        const temp = {}
        temp.id = element.id
        temp.branchName = element.branch_name
        temp.street = element.address
        temp.provinceCode = element.province
        temp.districtCode = element.district
        temp.wardCode = element.ward
        temp.province = data[0]
        temp.district = data[1]
        temp.ward = data[2]
        branchResult.push(temp)
    }   
    return branchResult
}

async function getDataPlace(req, contractData){
    const dataPlace = await Promise.all(
        [
            getPlace(req, contractData.sme_headquarters_province, 'provinces'),//0
            getPlace(req, contractData.sme_headquarters_district, 'districts'),//1
            getPlace(req, contractData.sme_headquarters_ward, 'wards'),//2
            getPlace(req, contractData.sme_representation_province_cur, 'provinces'),//3
            getPlace(req, contractData.sme_representation_district_cur, 'districts'),//4
            getPlace(req, contractData.sme_representation_ward_cur, 'wards'),//5
            getPlace(req, contractData.sme_representation_province_per, 'provinces'),//6
            getPlace(req, contractData.sme_representation_district_per, 'districts'),//7
            getPlace(req, contractData.sme_representation_ward_per, 'wards'),//8
            getPlace(req, contractData.authorized_province_cur, 'provinces'),//9
            getPlace(req, contractData.authorized_district_cur, 'districts'),//10
            getPlace(req, contractData.authorized_ward_cur, 'wards'),//11
            getPlace(req, contractData.authorized_province_per, 'provinces'),//12
            getPlace(req, contractData.authorized_district_per, 'districts'),//13
            getPlace(req, contractData.authorized_ward_per, 'wards'),//14
            getPlace(req, contractData.contract_creator_province, 'provinces'),//15
            getPlace(req, contractData.contract_creator_district, 'districts'),//16
            getPlace(req, contractData.contract_creator_ward, 'wards'),//17
            getPlace(req, contractData.ware_house_province, 'provinces'),//18
            getPlace(req, contractData.ware_house_district, 'districts'),//19
            getPlace(req, contractData.ware_house_ward, 'wards'),//20
            getBranchAddress(req.poolRead, contractData.contract_number, req),
        ]
    )

    const productData = await productService.getProductInfoV2(contractData.product_code)
    contractData.product_name = productData ? productData.prdctName : null
    contractData.sme_headquarters_province = dataPlace[0]
    contractData.sme_headquarters_district = dataPlace[1]
    contractData.sme_headquarters_ward = dataPlace[2]
    contractData.sme_representation_province_cur = dataPlace[3]
    contractData.sme_representation_district_cur = dataPlace[4]
    contractData.sme_representation_ward_cur = dataPlace[5]
    contractData.sme_representation_province_per = dataPlace[6]
    contractData.sme_representation_district_per = dataPlace[7]
    contractData.sme_representation_ward_per = dataPlace[8]
    contractData.authorized_province_cur = dataPlace[9]
    contractData.authorized_district_cur = dataPlace[10]
    contractData.authorized_ward_cur = dataPlace[11]
    contractData.authorized_province_per = dataPlace[12]
    contractData.authorized_district_per = dataPlace[13]
    contractData.authorized_ward_per = dataPlace[14]
    contractData.contract_creator_province = dataPlace[15]
    contractData.contract_creator_district = dataPlace[16]
    contractData.contract_creator_ward = dataPlace[17]
    contractData.ware_house_province = dataPlace[18]
    contractData.ware_house_district = dataPlace[19]
    contractData.ware_house_ward = dataPlace[20]
    contractData.branchList = dataPlace[21]

    return contractData
}

router.post('/after-loan-info/insert', apiValidator.validAfterLoan, async(req,res) => {
    try {
        const {contractNumber,data} = req.body
        const insertRs = await saveAfterLoanTurnover(data,contractNumber)
        
        if(insertRs.rowCount == 0) {
            return res.status(200).json({
                code : 0,
                msg : "insert turnover after loan not success ."
            })
        }

        return res.status(200).json({
            code : 1,
            msg : "insert turnover after loan success."
        })
    }
    catch(err) {
        console.log(err)
        common.responseErrorPublic(res)
    }
})

router.post('/attribute/update', async (req, res) => {
    try {
        const formData = req.body
        loanAttributeRepo.save(formData)
        return res.status(200).json({code: 0, message: "update info success"})
    } catch (error) {
        console.log("error when check create request: " + error?.message)
        return res.status(500).json({code: 1, message: "server error"})
    }
})

router.patch('/easy-ui/contracts/:contractNumber/cancel', apiValidator.validateCancelContract, async (req, res) => {
    try {
        const { contractNumber } = req.params;
        const loanContract = await loanContractRepo.getLoanContract(contractNumber);
        const {
            isKunn,
            kunnNumber,
            reason,
            createdBy
        } = req.body

        if (isKunn) {
            //cancel kunn
            const kunn = await kunnRepo.getKunnData(kunnNumber);
            await Promise.all([
              aadService.cancelTaskByContract(contractNumber),
              sqlHelper.patchUpdate({
                table: `kunn`,
                columns: ["status", "updated_date", "manual_actions"],
                values: sqlHelper.generateValues(
                  {
                    status: KUNN_STATUS.CANCELLED,
                    updated_date: new Date(),
                    manual_actions: JSON.stringify({
                      user: createdBy,
                      reason: reason ?? null,
                    }),
                  },
                  ["status", "updated_date", "manual_actions"]
                ),
                conditions: {
                  kunn_id: kunnNumber,
                },
              }),
              loggingRepo.saveWorkflow(
                STATUS.CANCELLED,
                reason,
                contractNumber,
                createdBy,
                kunnNumber
              ),
              actionAuditService.saveCaseHistoryActionAudit(
                kunnNumber,
                ActionAuditCaseStatus.UI_CANCEL.STEP_CODE,
                ActionAuditCaseStatus.UI_CANCEL.ACTION.CANCEL,
                kunnNumber,
                createdBy,
                reason
              ),
              kunn.api_version == KUNN_API_VERSION.V2
                ? callbackKunnCancelApi(kunnNumber, {
                    contractNumber,
                    debtContractNumber: kunnNumber,
                    debtContractStatus: 10,
                  })
                : callbackService.callbackPartner(
                    contractNumber,
                    loanContract.partner_code,
                    CALLBACK_STAUS.CANCEL_KUNN,
                    undefined,
                    undefined,
                    kunnNumber
                  ),
              loggingRepo.saveStepLog(
                kunnNumber,
                SERVICE_NAME.MC_LOS,
                STEP.CANCEL_LOAN,
                req.body,
                null
              ),
            ]);
            return res.status(200).json({
                code: 0,
                msg: `CANCEL KUNN ${kunnNumber} Success.`
            });
        }

        //cancel hạn mức
        await Promise.all([
            aadService.cancelTaskByContract(contractNumber),
            sqlHelper.patchUpdate({
                table: `loan_contract`,
                columns: ['status', 'updated_date', 'manual_actions'],
                values: sqlHelper.generateValues({
                    status: STATUS.CANCELLED,
                    updated_date: new Date(),
                    manual_actions: JSON.stringify({
                        user: createdBy,
                        reason: reason ?? null
                    })
                }, ['status', 'updated_date', 'manual_actions']),
                conditions: {
                    contract_number: contractNumber
                }
            }),
            loggingRepo.saveWorkflow(STATUS.CANCELLED, reason, contractNumber, createdBy),
            actionAuditService.saveCaseHistoryActionAudit(
                contractNumber,
                ActionAuditCaseStatus.UI_CANCEL.STEP_CODE,
                ActionAuditCaseStatus.UI_CANCEL.ACTION.CANCEL,
                contractNumber,
                createdBy,
                reason
            ),
            callbackService.callbackPartner(contractNumber, loanContract.partner_code, CALLBACK_STAUS.CANCELLED)
        ])
        crmHelper.removeContract(global.config, contractNumber)
        return res.status(200).json({
            code: 0,
            msg: `CANCEL contract ${contractNumber} Success.`
        });
    } catch (e) {
        console.log(e);
        common.responseErrorInternal(res, err);
    }
})

module.exports = router