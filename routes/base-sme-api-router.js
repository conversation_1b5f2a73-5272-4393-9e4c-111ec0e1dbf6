const express = require('express');
const loanContractRepo = require("../repositories/loan-contract-repo")
const documentRepo = require("../repositories/document")
const apiValidator = require("../utils/validator/base-api-validator")
const productService = require("../utils/productService")
const loggingService = require("../utils/loggingService")
const { isValidResubmit } = require("../utils/validator/logic-validator")
const contractGw = require("../services/contract-service")
const { MANUAL_TASK_CODE, roleCode, BUNDLE_STAGE, CONTRACT_TYPE, PARTNER_CODE, DOC_TYPE, DATE_FORMAT } = require("../const/definition")
const { STATUS, caseStatusCode, MAPPING_CALLBACK_STATUS, MAPPING_STATUS_CLIENT_CODE, CALLBACK_STAUS } = require("../const/caseStatus")
const aadService = require("../utils/aadService")
const router = express.Router();
const common = require("../utils/common")
const utils = require("../utils/helper")
const kunnRepo = require('../repositories/kunn-repo');
const { authenticateOauth2V03WithToken } = require('../utils/aaaService');
const ultis = require("../utils/helper")
const {checkContract} = require("../contract/get-contract-info")
const s3Service = require("../upload_document/s3-service")
const FormData = require('form-data');
const { serviceEndpoint } = require('../const/config');
const { saveDataEntry } = require('../repositories/data-entry-repo');
const { MisaReceiveInfoValidate } = require('../utils/validator/misa-validator');
const moment = require('moment-timezone');
const { getValueCode_v3 } = require('../utils/masterdataService');
moment().tz('Asia/Ho_Chi_Minh').format()
const offerRepo = require("../repositories/offer")

router.get("/bundle/resubmit-bundle", authenticateOauth2V03WithToken, apiValidator.validRequireContract, async (req, res) => {
    try {
        const contractNumber = req.query.contractNumber
        const resubmitList = await documentRepo.getResubmitListV2(contractNumber)
        const resultObj = {}
        const resultList = []

        for (let idx in resubmitList) {
            let doc = resubmitList[idx]
            if (!resultObj.hasOwnProperty(doc.doc_group)) {
                resultObj[doc.doc_group] = []
            }
            resultObj[doc.doc_group].push({
                docType: doc.doc_type,
                resubmitReason: doc.deviation_cmt ? `${doc.mistake_desc} : ${doc.deviation_cmt}` : doc.mistake_desc
            })
        }

        for (let key in resultObj) {
            resultList.push({
                docGroup: key,
                docList: resultObj[key]
            })
        }

        return res.status(200).json({
            code: 1,
            msg: "get resubmit list successfully.",
            data: resultList
        })
    }
    catch (err) {
        console.log(err)
        common.log(`get bundle resubmit error : ${err.message} `, req.query.contractNumber)
        return res.status(500).json({
            code: 1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.get("/offer/get", authenticateOauth2V03WithToken, apiValidator.validRequireContract, async (req, res) => {
    try {
        const contractNumber = req.query.contractNumber
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        let prdCode
        if (contractData != false) {
            prdCode = contractData.product_code
        }
        const offerRs = await offerRepo.getOfferList(contractNumber, roleCode.customer)
        const offerList = []
        for (let idx in offerRs) {
            let offer = offerRs[idx]
            offerList.push({
                offerId: offer.id,
                offerAmount: parseInt(offer.offer_amt),
                offerRate: parseFloat(offer.int_rate),
                offerTenor: parseInt(offer.tenor),
                monthlyInstallment: prdCode == 'MCBAS_HMTD' ? 0 : await productService.getMonthlyInstallment(offer.offer_amt, offer.int_rate, offer.tenor)
            })
        }
        return res.status(200).json({
            code: 1,
            msg: "get offer list successfully.",
            data: offerList
        })
    }
    catch (err) {
        console.log(err)
        common.log(`get bundle resubmit error : ${err.message} `, req.query.contractNumber)
        return res.status(500).json({
            code: 1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/bundle/resubmit", authenticateOauth2V03WithToken, apiValidator.validResubmitDocSme, async (req, res) => {
    try {
        req.body.requestId = utils.genRequestId(PARTNER_CODE.MISA);
        const poolWrite = global.poolWrite
        const { contractNumber, requestId, listDocResubmit } = req.body
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        await aadService.completedTaskByRole(contractNumber, roleCode.SS)
        const waitingResubmitList = await documentRepo.getResubmitList(contractNumber)
        const validResubmit = isValidResubmit(waitingResubmitList, listDocResubmit)
        // console.log(waitingResubmitList)
        // console.log(validResubmit)
        if (!waitingResubmitList || !validResubmit) {
            return res.status(400).json({
                code: 'INVALID_REQUEST',
                msg: "Invalid list_doc_resubmit",
                data: {
                    contractNumber,
                    requestId
                }
            })
        }
        const LCTDocIdIndex = listDocResubmit.findIndex(item => item.docType == DOC_TYPE.LCT)
        if(LCTDocIdIndex > -1) {
            const fileKey = (await documentRepo.findByDocID(listDocResubmit[LCTDocIdIndex].docId)).file_key
            const dataCheckSign = new FormData();
            
            const checkSignUrl = global.config.basic['bss-esigning-service'][global.config.env] + serviceEndpoint.ESIGN.checkValidDigitalSign;
            let bufferFileCheckSign = (await s3Service.downloadFile(global.config.data,fileKey)).Body;
            dataCheckSign.append('file',bufferFileCheckSign)
            
            const rsCheck = await common.postApiV2(checkSignUrl, dataCheckSign, dataCheckSign.getHeaders());
            if (rsCheck.data.code !== 0) {
                return res.status(400).json({
                    code: 'INVALID_REQUEST',
                    msg: "Chữ ký số không hợp lệ.",
                    data: {
                        contractNumber,
                        requestId
                    }
                })
            }
        }

        const bundleInfo = await productService.getBundle(global.config, contractData.product_code)
        const newDocList = productService.mapBundleGroup(listDocResubmit, bundleInfo.data)
        const signData = await checkContract(req.poolRead,contractNumber);
        let flagSme = false;
        let statusSign = 'sign';
        if(signData.rowCount!=0){
            statusSign = signData?.rows[0].status;
            flagSme = true;
        }
        const resubmitRs = await documentRepo.resubmitDoc(contractNumber, newDocList,flagSme)
        if (resubmitRs.result) {

            const resubmitRole = await loanContractRepo.getLastStepResubmit(contractNumber)
            if (!resubmitRole) {
                common.log(`INVALID SALE SUPPORT RESUBMIT `, contractNumber)
            }

            if(statusSign==='SME PARTNER SIGN IN PROGRESS'){
                await aadService.pushTaskMcV2(resubmitRole,contractNumber,contractData.contract_type,STATUS.IN_CP_POST_QUEUE)
			    await loanContractRepo.updateContractStatus(STATUS.IN_CP_POST_QUEUE,contractNumber)
            }else{
                let status;
                switch(resubmitRole) {
                    case roleCode.CP:
                        status = STATUS.IN_CP_QUEUE;
                        break;
                    case roleCode.CE :
                        status = STATUS.IN_CE_QUEUE;
                        break;
                    default:
                        status = `IN_${roleCode}_QUEUE`
                        break;
                }
                await aadService.pushTaskMcV2(resubmitRole, contractNumber, contractData.contract_type,status)
                await loanContractRepo.updateContractStatus(status,contractNumber)
            }
            
            const responseBody = {
                code: 'SUCCESS',
                msg: resubmitRs.msg,
                data: {
                    contractNumber,
                    requestId
                }
            }
            await loggingService.saveRequestV2(poolWrite, req.body, responseBody, contractNumber, requestId, "")
            return res.status(200).json(responseBody)
        } else {
            return res.status(400).json({
                code: 'INVALID_REQUEST',
                msg: resubmitRs.msg,
                data: {
                    contractNumber,
                    requestId
                }
            })
        }
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: -1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/offer/select", authenticateOauth2V03WithToken, apiValidator.validSelectOfferSME, async (req, res) => {
    try {
        const poolWrite = global.poolWrite
        const { contractNumber, selectedOfferId } = req.body
        const currentTimestamp = new Date().getTime()
        const requestId = PARTNER_CODE.MISA + currentTimestamp
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        if (contractData.status !== caseStatusCode.KH11 && contractData.status !== STATUS.ACCEPTED_WITH_OFFERS && contractData.status !== STATUS.MC_APPROVED) {
            return res.status(400).json({
                code: 'INVALID_REQUEST',
                msg: "Invalid contractNumber",
                data: {
                    requestId,
                    contractNumber
                }
            })
        }
        const selectOfferRs = await offerRepo.selectOffer(poolWrite, selectedOfferId, contractNumber)
        if (selectOfferRs.result) {
            const responseBody = {
                code: 'RECEIVED',
                msg: 'The offer selection has been received and is being processed.',
                data: {
                    requestId,
                    contractNumber
                }
            }
            await loggingService.saveRequestV2(poolWrite, req.body, responseBody, contractNumber, requestId, "")
            await loanContractRepo.updateContractStatus(STATUS.SELECTED_OFFER, contractNumber)
            contractGw.generateContract(contractNumber, contractData.partner_code)
            return res.status(200).json(responseBody)
        }
        else {
            return res.status(400).json({
                code: 'INVALID_REQUEST',
                msg: selectOfferRs.msg,
                data: {
                    requestId,
                    contractNumber
                }
            })
        }
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: 'SERVER_ERROR',
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.get('/smeInfo', async (req, res) => {
    try {
        const contractNumber = req.query.contractNumber
        const loanData = await loanContractRepo.getLoanContract(contractNumber);
        const masterData = await Promise.all([
            getValueCode_v3(loanData?.sme_headquarters_ward,"WARD"),
            getValueCode_v3(loanData?.sme_headquarters_district,'DISTRICT'),
            getValueCode_v3(loanData?.sme_headquarters_province,'PROVINCE'),
        ])
        const dataPart = {
            smeName : loanData?.sme_name||'',
            registrationNumber : loanData?.registration_number||'',
            registrationDate : loanData?.first_registration_date||'',
            phoneNumber : loanData?.sme_phone_number||'',
            address : masterData[0]!==false?`${loanData?.sme_headquarters_address}, ${masterData[0]}, ${masterData[1]}, ${masterData[2]}`:''
        }
        const contractData = await loanContractRepo.getRemainInfo(contractNumber)
        contractData.bank_account_owner = contractData.cust_full_name ? ultis.nonAccentVietnamese(contractData.cust_full_name) : ''
        if (!contractNumber) return res.status(400).json({ code: 1, msg: "Invalid contractNumber" })
        if (!contractData) return res.status(400).json({ code: 1, msg: "Get remain info error" })
        return res.status(200).json({ code: 0, msg: "Get remain info success", data: {...contractData,...dataPart} })
    }
    catch (err) {
        common.log(`get smeInfo error : ${err?.message} `, req.query.contractNumber)
        return res.status(500).json({
            code: 1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/uploadDocAfterDisbur", async (req, res) => {
    try {
        const poolWrite = global.poolWrite;
        const { kunnNumber, listDoc } = req.body;
        const kunnData = await kunnRepo.getKunnData(kunnNumber);
        if (kunnData.status !== STATUS.ACTIVATED) {
            return res.status(400).json({
                code: 'INVALID_REQUEST',
                msg: "Invalid kunnNumber",
                data: {
                    kunnNumber
                }
            })
        }
        const contractNumber = await kunnRepo.getContractByKU(kunnNumber)
        const loanContractData = await loanContractRepo.getLoanContract(contractNumber)
        const contractType = loanContractData?.contract_type
        const type = contractType===CONTRACT_TYPE.CASH_LOAN?'VM':'HM'
        const kunnCode = 'KU_SME_MISA_'+type+'_STANDARD';
        const bundleInfo = await productService.getBundle(global.config, kunnCode, undefined, true);
        const documentlist = productService.mapBundleGroupKOV(listDoc, bundleInfo.data);
        await documentRepo.saveUploadedDocumentKunnSme(poolWrite, kunnNumber, documentlist, null);
        if(contractType===CONTRACT_TYPE.CASH_LOAN){
            const updateSql = `update loan_contract_document set type_collection='HM' where doc_group not like '%AFTER DISBURSEMENT%' and type_collection is null and kunn_contract_number=$1`;
            await poolWrite.query(updateSql, [kunnNumber])
        }
        //aadService.pushTaskKU('CP', kunnNumber, contractType, false, true);
        await aadService.pushTaskMcV2(roleCode.CP,kunnNumber,contractType,STATUS.IN_CP_DIS_QUEUE)
        await loanContractRepo.updateKUStatus(STATUS.IN_CP_DIS_QUEUE,kunnNumber)
        return res.status(200).json({
            code: 'SUCCESS',
            msg: "upload doc after disbursment success",
            data: {
                kunnNumber
            }
        })
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: 'SERVER_ERROR',
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/jobAfterDisbursement", async (req, res) => {
    try {
        const poolWrite = global.poolWrite;
        const sql = `update kunn set status = 'EARLY_COLLECTION' where kunn_id in (
            select kunn_id  from kunn k where partner_code = 'MIS' and status != 'ACTIVATED_WITH_DIS_DOCS' and 
            (SELECT extract
                (day from 
                    ((select now())-date_approval))>30);
        )`;
        await poolWrite.query(sql);
        return res.status(200).json({
            code: 'SUCCESS',
            msg: 'UPDATE STATUS KUNN TO EARLY_COLLECTION SUCCESS'
        })
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: 'SERVER_ERROR',
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/external/receiveInfoSme", MisaReceiveInfoValidate, async (req, res) => {
    try {
        const poolWrite = global.poolWrite;
        const {turnover, profit, contractNumber } = req.body;
        if(contractNumber===undefined){
            return res.status(400).json({
                code: 'INVALID_REQUEST',
                msg: "EC not found SME or found than one SME"
            })
        }
        for (const t of turnover) { t.info_type = 'sme_turnover';}
        for (const p of profit) { p.info_type = 'sme_profit';}
        const datas = [...turnover,...profit];
        saveDataEntry(poolWrite,contractNumber,undefined,datas);
        return res.status(200).json({
            code: 'SUCCESS',
            msg: "EC received data from MISA success",
            contractNumber
        })
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: 'SERVER_ERROR',
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

module.exports = router