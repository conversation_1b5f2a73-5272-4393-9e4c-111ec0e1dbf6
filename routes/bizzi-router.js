const express = require("express");
const { handleResponseError } = require("../base/response.js");
const authService = require("../utils/aaaService.js");
const bizziService = require("../services/bizzi-service.js");
const bizziKunn = require("../KUNN/bizzi-kunn.js");
const bizziKunnStatus = require("../KUNN/bizzi-kunn-status");
const bizziValidator = require("../utils/validator/bizzi-validator.js");
const { masterDataSchema, presignUploadDocSchema } = require("../KUNN/schema/bizzi-schema");
const { verifySignedRequestFromPartner, injectPartnerHeaders } = require("../utils/middleware.js");
const { PARTNER_CODE } = require("../const/definition.js");

const router = express.Router();

// router.use(authService.authenticateOauth2V04WithToken);

router.post("/v1/bizz/af1/submit", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.af1Submit(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/af2/submit", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.af2Submit(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/af2/resubmit", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.af2ReSubmit(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/af2/delete-loan", async (req, res) => {
  try {
    const { contract_number } = req.body;
    if (!contract_number) {
      return res.status(400).json({ message: "Missing contract_number" });
    }

    const tables = ["loan_customer", "loan_business_owner", "loan_customer_representations", "loan_customer_shareholders", "loan_customer_warehouses", "loan_customer_partners", "loan_contract"];

    for (const table of tables) {
      await global.poolWrite.query(`DELETE FROM ${table} WHERE contract_number = $1`, [contract_number]);
    }

    const response = {
      contract_number: contract_number,
      message: "deleted successfully.",
    };
    return res.json(response);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/manual-workflow", async (req, res) => {
  try {
    bizziService.manualWorkflow(req, res);
    return res.json({
      message: "Manual workflow successfully.",
    });
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/af3/submit", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.af3Submit(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/af3/resubmit", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    req.body.is_af3_re_submit = true;
    const result = await bizziService.af3Submit(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

/*** API CHO GIAI NGAN */
router.post("/v1/bizz/credit-limit/:contract_number/kunns", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const { contract_number } = req.params;
    console.log(`Requesting disbursement for contract number: ${contract_number}`);
    const result = await bizziKunn.requestDisbursement(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/credit-limit/:contract_number/kunns/:debt_contract_number/resubmit", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziKunn.resubmitDisbursement(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/v1/bizz/credit-limit/:contract_number/kunns/:debt_contract_number/status", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const { contract_number, debt_contract_number } = req.params;
    const result = await bizziKunnStatus.getKunnStatus({ contract_number, debt_contract_number });
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post(
  "/v1/bizz/document/presigned-s3",
  (req, res, next) => bizziValidator.validate(req, res, presignUploadDocSchema, next),
  verifySignedRequestFromPartner,
  authService.authenticateOauth2V04WithToken,
  async (req, res) => {
    try {
      const result = await bizziService.getPresignedUploadDocumentUrl(req, res);
      return res.json(result);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/v1/bizz/masterdata",
  injectPartnerHeaders(PARTNER_CODE.BIZZ),
  authService.authenticateOauth2V04WithToken,
  (req, res, next) => bizziValidator.validate(req, res, masterDataSchema, next),
  async (req, res) => {
    try {
      const result = await bizziService.getMasterdata(req, res);
      return res.json(result);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post("/v1/bizz/credit-limit/:contract_number/documents", bizziValidator.validateGetPresignDocumentRequest, verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.getMultiplePresignedDocumentUrl(req, res);

    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/v1/bizz/credit-limit/:contract_number/status", injectPartnerHeaders(PARTNER_CODE.BIZZ), verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.getContractStatus(req, res);

    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/v1/bizz/credit-limit/:contract_number/kunns/:debt_contract_number/documents", bizziValidator.validateGetKUUNPresignDocumentRequest, injectPartnerHeaders(PARTNER_CODE.BIZZ), verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const result = await bizziService.getKUUNPresignedDocumentUrl(req, res);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/credit-limit/:contract_number/kunns/:debt_contract_number/documents", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const body = req.body;
    const result = await bizziKunn.submitKunnSignedDocument(body);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/v1/bizz/credit-limit/:contract_number/kunns/:debt_contract_number/resubmit-documents", verifySignedRequestFromPartner, authService.authenticateOauth2V04WithToken, async (req, res) => {
  try {
    const body = req.body;
    const result = await bizziKunn.reSubmitKunnSignedDocument(body);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

const { getDataForTemplateBTTHDTD, getDataForTemplateBTTCNKPT, getDataForTemplateBTTKCBB, getDataForTemplateBTTQDCV } = require("../services/creditlimit-service.js");
router.post("/test", async (req, res) => {
  try {
    const { contract_number } = req.body;
    // req.params = contract_number;
    //["BTTHDTD", "BTTCNKPT", "BTTKCBB", "BTTQDCV"]
    let result = [];
    result.push(await getDataForTemplateBTTHDTD(contract_number, undefined));
    result.push(await getDataForTemplateBTTCNKPT(contract_number, undefined));
    result.push(await getDataForTemplateBTTKCBB(contract_number, undefined));
    result.push(await getDataForTemplateBTTQDCV(contract_number, undefined));
    // contractGw.generateAF3ContractFile(contract_number);
    console.log("Data for BTT getContractStatus:", JSON.stringify(result));
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

module.exports = router;
