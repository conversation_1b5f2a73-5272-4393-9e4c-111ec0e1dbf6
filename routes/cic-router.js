const express = require("express");
const router = express.Router();
const { handleResponseError } = require("../base/response.js");
const { exportCicReportData, exportCicReportDataListContracts } = require("../services/cic-report-service.js");
const { uploadFinancialReportInternal } = require("../services/sme-misa-v2.js");
const {
  misaUploadFinancialReportInternalValidate,
} = require("../utils/validator/misa-validator.js");

router.post("/cic/reports", async (req, res) => {
  try {
    const data = await exportCicReportData(req.body);
    return res.json(data);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/cic/reports/sync", async (req, res) => {
  try {
    const data = await exportCicReportDataListContracts(req.body);
    return res.json(data);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post(
  "/cic/reports/upload",
  misaUploadFinancialReportInternalValidate,
  async (req, res) => {
    try {
      const data = await uploadFinancialReportInternal(req.body);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

module.exports = router;