const express = require('express');
const router = express.Router();
const loanContractRepo = require("../repositories/loan-contract-repo");
const { STATUS, CALLBACK_STAUS } = require("../const/caseStatus");
const callbackService = require("../services/callback-service");
const crmService = require("../utils/crmService");
const aadService = require("../utils/aadService");
const helper = require("../utils/helper");
const smsService = require("../utils/smsService");
const { formatDate } = require('../utils/dateHelper');
const moment = require('moment-timezone');
const { PARTNER_CODE } = require('../const/definition');
const { SuccessResponse, ServerErrorResponse, BadRequestResponse } = require('../base/response');
const { cancelContract } = require('../services/contract-service');
const kunnRepo = require("../repositories/kunn-repo");
const { cancelKunn } = require('../services/kunn-service');
moment().tz('Asia/Ho_Chi_Minh').format();

router.post("/autoCancel", async (req, res) => {
    try {
        const config = req.config
        const eligibleDay = parseInt(config.data.autoCancel.eligibleDay)
        const acceptedWithOfferDay = parseInt(config.data.autoCancel.acceptedWithOfferDay)
        const resubmitDay = parseInt(config.data.autoCancel.resubmitDay)
        const waitingToBeSignedLoanDay = parseInt(config.data.autoCancel.waitingToBeSignedLoanDay)
        const waitingToBeSignedKunnDay = parseInt(config.data.autoCancel.waitingToBeSignedKunnDay)
        const eligibleSmeDay = parseInt(config.data.autoCancel.eligibleSmeDay)
        
        const poolWrite = global.poolWrite;
        const sql = `select contract_number from loan_contract lc where lc.status in ('ELIGIBLE','KH05') and lc.partner_code!='MIS' and (select extract (day from ((select now())-updated_date))>=${eligibleDay});`;
        const sql2 = `select contract_number from loan_contract lc where lc.status = 'ACCEPTED_WITH_OFFERS' and (select extract (day from ((select now())-updated_date))>=${acceptedWithOfferDay});`;
        const sql3 = `select contract_number from loan_contract lc where lc.status like '%RESUBMIT%' and (select extract (day from ((select now())-updated_date))>=${resubmitDay});`;
        const sql4 = `select contract_number from loan_contract lc where lc.status = 'WAITING_TO_BE_SIGNED' and (select extract (day from ((select now())-updated_date))>=${waitingToBeSignedLoanDay});`;
        const sql5 = `select kunn_id  from kunn k where k.status = 'WAITING_TO_BE_SIGNED' and (select extract (day from ((select now())-updated_date))>=${waitingToBeSignedKunnDay});`;
        const sql6 = `select contract_number from loan_contract lc where lc.status = 'ELIGIBLE' and lc.partner_code = 'MIS' and (select extract (day from ((select now())-updated_date))>=${eligibleSmeDay});`;

        const data = await Promise.all([poolWrite.query(sql), poolWrite.query(sql2), poolWrite.query(sql3), poolWrite.query(sql4), poolWrite.query(sql5), poolWrite.query(sql6)]);

        let contractNumberList = [];
        const kunnNumberList = [];
        data[0].rows.map(x => { contractNumberList.push(x.contract_number) })
        data[1].rows.map(x => { contractNumberList.push(x.contract_number) })
        data[2].rows.map(x => { contractNumberList.push(x.contract_number) })
        data[3].rows.map(x => { contractNumberList.push(x.contract_number) })
        data[4].rows.map(x => { kunnNumberList.push(x.kunn_id) })
        data[5].rows.map(x => { contractNumberList.push(x.contract_number) })
        // console.log({contractNumberList})
        // console.log({kunnNumberList})
        let promiseList = [];
        for await (const contractNumber of contractNumberList) {
            let partnerCode = await loanContractRepo.getPartnerCode(contractNumber);
            promiseList.push(loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber),
                callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.CANCELLED),
                crmService.removeContract(global.config, contractNumber),
                aadService.cancelTaskByContract(contractNumber)
            );
        }
        for await (const kunnNumber of kunnNumberList) {
            promiseList.push(helper.saveKUStatus(poolWrite, kunnNumber, STATUS.CANCELLED));
        }
        
        const promiseListFinal = await helper.sliceIntoChunks(promiseList,5);
        for await (const plf of promiseListFinal) {
            Promise.all(plf);
        }

        return res.status(200).json({
            code: 1,
            msg: 'JOB CANCEL SUCCESS'
        })
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: -1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/reviewHM", async (req, res) => {
    try {
        const config = req.config;
        const smsUrl = config.data.smsService.sendSMS
        const isEnable = config.data.smsService.useSMS
        let msg = config.data.smsService.reviewHmMsg;
        const poolWrite = global.poolWrite;

        const sql = `select contract_number, approval_tenor, approval_date, phone_number1 as phone from loan_contract lc where lc.status = 'ACTIVATED' and partner_code != 'MIS' and 
        contract_type = 'CREDITLINE' and (select extract(year from AGE(now()::date, approval_date::date)) * 12 + extract(month from AGE(now()::date, approval_date::date)))=11`;
        const sql2 = `select contract_number, approval_tenor, approval_date, sme_representation_phone_number as phone from loan_contract lc where lc.status = 'ACTIVATED' and partner_code = 'MIS' and 
        contract_type = 'CREDITLINE' and (select extract(year from AGE(now()::date, approval_date::date)) * 12 + extract(month from AGE(now()::date, approval_date::date)))=11`;

        const data = await Promise.all([poolWrite.query(sql), poolWrite.query(sql2)]);
        let dt1 = data[0].rows;
        // let dt2 = data[1].rows;
        let datas = [];
        console.log(helper.calNextCycleDate2(11, formatDate('2021-09-17T08:20:20.000Z','YYYY-MM-DD')))
        dt1 = dt1.filter(item => 
            item.approval_tenor>=12 && formatDate(moment(new Date()),'YYYY-MM-DD')===helper.calNextCycleDate2(11, formatDate(item.approval_date,'YYYY-MM-DD')));
        for (const i in dt1) {
            datas.push(dt1[i])
        }
        // for (const i in dt2) {
        //     datas.push(dt2[i])
        // }
        // console.log({contractNumberList})
        // console.log({phoneList})
        let promiseList = [];
        for await (const data of datas) {
            if (data.phone !== undefined) {
                if (isEnable) {
                    msg = msg.replace("contractNumber", data.contract_number)
                    promiseList.push(smsService.sendSMS(data.contract_number, msg, smsUrl, data.phone, false))
                }
            }
        }
        const promiseListFinal = await helper.sliceIntoChunks(promiseList,5);
        for await (const plf of promiseListFinal) {
            Promise.all(plf);
        }

        return res.status(200).json({
            code: 1,
            msg: 'JOB SEND SMS REVIEW SUCCESS'
        })
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: -1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/closeHM", async (req, res) => {
    try {
        const poolWrite = global.poolWrite;
        const sql = `select contract_number from loan_contract lc where lc.status = 'ACTIVATED' and 
        contract_type = 'CREDITLINE' and (select extract(year from AGE(now()::date, approval_date::date)) * 12 + extract(month from AGE(now()::date, approval_date::date)))=12`;
        const sql2 = `select contract_number from loan_contract lc where lc.status = 'ACTIVATED' and 
        contract_type = 'CREDITLINE' and (select extract(year from AGE(now()::date, approval_date::date)) * 12 + extract(month from AGE(now()::date, approval_date::date)))=13`;

        const data = await Promise.all([poolWrite.query(sql), poolWrite.query(sql2)]);
        let contractNumberList = [...data[0].rows,...data[1].rows];
        let promiseList = [];
        for await (const contractNumber of contractNumberList) {
            promiseList.push(loanContractRepo.updateContractStatus(STATUS.CLOSED,contractNumber?.contract_number))
        }

        const promiseListFinal = await helper.sliceIntoChunks(promiseList,5);
        for await (const plf of promiseListFinal) {
            Promise.all(plf);
        }

        return res.status(200).json({
            code: 1,
            msg: 'JOB CLOSE HM SUCCESS'
        })
    }
    catch (err) {
        console.log(err)
        return res.status(500).json({
            code: -1,
            msg: "INTERNAL SERVER ERROR"
        })
    }
})

router.post("/credit-limits/cancel-timeouts", async (req, res) => {
    try {
        const {
            status,
            days,
            partnerCode,
            comment
        } = req.body;
        const loans = await loanContractRepo.getContractsByStatusAndApprovalDate({
            status,
            days,
            partnerCode: partnerCode ?? PARTNER_CODE.BIZZ,
        });

        const now = moment().format('YYYY-MM-DD HH:mm:ss');

        if (!loans || loans.length === 0) {
            console.log(`${now} | cancelCreditLimitTimeout | ${JSON.stringify(req.body)} | No contracts found for the given criteria`);
            return res.status(200).json(
                new SuccessResponse([], `${now} - No contracts found for the given criteria`)
            );
        }

        let cancelPromises = [];
        let effectiveContractNumbers = [];
        for (const loan of loans) {
            try {
                //handle cancel
                cancelPromises.push(cancelContract({
                    contractNumber: loan.contract_number,
                    comment: comment ?? 'Cancelled due to timeout',
                    cancelledBy: 'system',
                    loanContract: loan
                }));
                effectiveContractNumbers.push(loan.contract_number);
            } catch (error) {
                console.error(`Error cancelling contract ${loan.contract_number}:`, error);
            }
        }
        Promise.all(cancelPromises).then(() => {
            console.log(`${now} | cancelCreditLimitTimeout | ${JSON.stringify(req.body)} | Successfully cancelled ${cancelPromises.length} contracts`)
        }).catch((error) => {
            console.error(`${now} | cancelCreditLimitTimeout | ${JSON.stringify(req.body)} | Error cancelling contracts:`, error);
        });
        if (cancelPromises.length === 0) {
            return res.status(200).json(
                new SuccessResponse([], `${now} - No contracts found for the given criteria`)
            );
        }

        return res.status(200).json(
            new SuccessResponse({
                effectiveContractNumbers: effectiveContractNumbers ?? []
            }, `${now} - Processing ${cancelPromises.length} contracts`)
        );
    } catch (e) {
        console.error(e);
        return res.status(500).json(
            new ServerErrorResponse(`Error processing cancelCreditLimitTimeout: ${e.message}`, e)
        );
    }
})

router.post("/kunns/cancel-timeouts", async (req, res) => {
    try {
        const {
            status,
            days,
            partnerCode,
            comment,
            columnName
        } = req.body;

        const validColumnNames = [
            'updated_date',
            'created_date',
            'date_approval',
            'approve_signing_date',
            'waiting_resubmit_date',
            'af1_approve_date'
        ];
        const now = moment().format('YYYY-MM-DD HH:mm:ss');
        if(!columnName || !validColumnNames.includes(columnName)) {
            let msg = `Invalid column name, expected one of ${validColumnNames.join(', ')}`;
            console.log(`${now} | cancelKunnTimeout | ${JSON.stringify(req.body)} | ${msg}`);
            return res.status(400).json(
                new BadRequestResponse([], msg)
            );
        }
        const kunns = await kunnRepo.getKunnsByStatusAndDate({
            status,
            days,
            partnerCode: partnerCode ?? PARTNER_CODE.BIZZ,
            columnName
        });

        if (!kunns || kunns.length === 0) {
            let msg = `No kunns found for the given criteria`;
            console.log(`${now} | cancelKunnTimeout | ${JSON.stringify(req.body)} | ${msg}`);
            return res.status(200).json(
                new SuccessResponse([], `${now} - ${msg}`)
            );
        }

        let cancelPromises = [];
        let effectiveKunns = [];
        for (const kunn of kunns) {
            try {
                //handle cancel
                cancelPromises.push(cancelKunn({
                    contractNumber: kunn.contract_number,
                    kunnId: kunn.kunn_id,
                    comment: comment ?? 'Cancelled due to timeout',
                    cancelledBy: 'system',
                    kunn: kunn
                }));
                effectiveKunns.push(kunn.kunn_id);
            } catch (error) {
                console.error(`Error cancelling kunn ${kunn.contract_number}:`, error);
            }
        }
        Promise.all(cancelPromises).then(() => {
            console.log(`${now} | cancelKunnTimeout | ${JSON.stringify(req.body)} | Successfully cancelled ${cancelPromises.length} kunns`)
        }).catch((error) => {
            console.error(`${now} | cancelKunnTimeout | ${JSON.stringify(req.body)} | Error cancelling kunns:`, error);
        });
        if (cancelPromises.length === 0) {
            return res.status(200).json(
                new SuccessResponse([], `${now} - No kunns found for the given criteria`)
            );
        }

        return res.status(200).json(
            new SuccessResponse({
                effectiveKunns: effectiveKunns ?? []
            }, `${now} - Processing ${cancelPromises.length} kunns`)
        );
    } catch (e) {
        console.error(e);
        return res.status(500).json(
            new ServerErrorResponse(`Error processing cancelKunnTimeout: ${e.message}`, e)
        );
    }
})

module.exports = router;