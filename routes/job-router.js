const express = require('express');
const router = express.Router();
const contractService = require('../services/contract-service');
const loanContractService = require('../services/loan-contract-service');

router.post('/v1/bizz/job/credit/signing/regenerate-template', (req, res) => {
    contractService.generateBizziCreditLimitTemplateJobNextDay()
    res.json({ message: 'Success'});
});

router.post('/v1/bizz/job/kunn/signing/regenerate-template', (req, res) => {
    contractService.generateBizziKunnTemplateJobNextDay()
    res.json({ message: 'Success'});
});

router.post('/v1/job/contract/expired', (req, res) => {
    loanContractService.setExpiredContract(req.body.endDate, req.body.partnerCodes)
        .then(() => res.json({ message: 'Success' }))
        .catch(err => res.status(500).json({ error: err.message }));
});

router.post('/v1/finv/job/credit/signing/regenerate-template', (req, res) => {
    contractService.generateFinvCreditLimitTemplateJobNextDay();
    res.json({ message: 'Success'});
});

router.post('/v1/finv/job/kunn/signing/regenerate-template', (req, res) => {
    contractService.generateFinvKunnTemplateJobNextDay();
    res.json({ message: 'Success'});
});

module.exports = router;