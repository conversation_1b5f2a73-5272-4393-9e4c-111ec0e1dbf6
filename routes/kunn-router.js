const express = require('express');
const router = express.Router();
const { vskKUNN, misaKUNN, MCAppKUNN } = require("../KUNN_V2/app-form-service")
const { VSKCreateKunnValidate } = require("../utils/validator/vsk-validator")
const { MisaCreateKunnValidate, docsSupplementationValidation, addSignedFileValidation } = require("../utils/validator/misa-validator");
const { getLoanContract } = require('../repositories/loan-contract-repo');
const { CONTRACT_TYPE, PRODUCT_CODE, PARTNER_CODE } = require('../const/definition');
const { McAppCreateKunnValidate, McAppGetLoanContractValidate, McAppGetKunnValidate } = require('../utils/validator/mc-app-validator');
const kunnService = require('../services/kunn-service')
const bizziService = require('../services/bizzi-service');
const { handleResponseError } = require('../base/response');

router.post('/vsk/create-request', VSKCreateKunnValidate, (req, res) => {
  const vskKU = new vskKUNN(req, res, 'MASBAS_KU')
  vskKU.createDiburRequest()
})

router.post('/misa/create-request', MisaCreateKunnValidate, async (req, res) => {
  const contractNumber = req.body.contractNumber;
  const loanContract = await getLoanContract(contractNumber);
  // if (loanContract?.status != STATUS.ACTIVATED) {
  //   return this.res.status(400).json({
  //     code : 0,
  //     msg : `contractNumber ${contractNumber} not found or invalid.`
  //   })
  // }
  // if (!loanContractService.isContractLockActive(loanContract)) {
  //   return this.res.status(400).json({
  //     code : 0,
  //     msg : `contractNumber ${contractNumber} was locked.`
  //   })
  // }
  const contractType = loanContract.contract_type === CONTRACT_TYPE.CREDIT_LINE ? 'HM' : 'VM';
  let kunnCode = `KU_SME_MISA_${contractType}_STANDARD`;
  const productCodeV2 = [
    'SME_MISA_HM_SILVER',
    'SME_MISA_HM_GOLD',
    'SME_MISA_HM_DIAMOND'
  ];
  if (productCodeV2.includes(loanContract.product_code)) {
    kunnCode = `KU_` + loanContract.product_code;
  }
  // req.body.tenor = loanContract?.approval_tenor_kunn;
  // if(contractType==='VM') req.body.tenor = loanContract?.approval_tenor;
  const misaKU = new misaKUNN(req, res, kunnCode);
  misaKU.createDiburRequest();
})

router.post('/mc-app/create-request', McAppCreateKunnValidate, async (req, res) => {
  const partnerCode = req.body.partner_code;
  let kunnCode = PRODUCT_CODE.KU_MCTAX_HMTD;
  if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL].includes(partnerCode)) {
    kunnCode = PRODUCT_CODE.KIOT_VIET_KU;
  }
  const mcAppKunn = new MCAppKUNN(req, res, kunnCode);
  mcAppKunn.createDiburRequest();
})

//test viết tạm phần hợp đồng vào kunn lấy thông tin
router.get('/mc-app/get-loan', McAppGetLoanContractValidate, async (req, res) => {
  const contractNumber = req.query.contractNumber;
  await getLoanContract(contractNumber);
})

//test chi tiết hợp đồng khế ước lấy thông tin
router.get('/mc-app/get-kunn', McAppGetKunnValidate, async (req, res) => {
  const contractNumber = req.query.contractNumber;
  await getLoanContract(contractNumber);
})

router.post('/sme/docs-supplementation', docsSupplementationValidation, async (req, res) => {
  kunnService.documentsSupplementation(req, res)
})

router.post('/sme/supplement-signed-file', addSignedFileValidation, async (req, res) => {
  kunnService.supplementSignedFile(req, res)
})


router.post("/refunds", async (req, res) => {
  try {
    const { body } = req;
    const result = await bizziService.createRefund(body);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/refunds", async (req, res) => {
  try {
    const result = await bizziService.findRefunds(req.query);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/refunds/requests", async (req, res) => {
  try {
    const result = await bizziService.findRefundRequests(req.query);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/refunds/callback", async (req, res) => {
  try {
    const result = await bizziService.handleRefundCallback(req.body);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/refunds/:id/bank-info", async (req, res) => {
  try {
    const { id } = req.params;
    const result = await bizziService.getBankInfoByRefund(id);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/refunds/:id/list-kunn", async (req, res) => {
  try {
    const { id } = req.params;
    const result = await bizziService.getListKunnByRefund(id);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/refunds/:id/transfer", async (req, res) => {
  try {
    const result = await bizziService.transferCase({ refund_id: req.params.id, ...req.body });
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/refunds/:id/refund", async (req, res) => {
  try {
    const result = await bizziService.refundMoney({ refund_id: req.params.id, ...req.body });
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.get("/refunds/:id/history", async (req, res) => {
  try {
    const result = await bizziService.getHistoryByRefund(req.params.id);
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/refunds/requests/:id/approve", async (req, res) => {
  try {
    const result = await bizziService.approveRefund({ request_id: req.params.id, ...req.body });
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

router.post("/refunds/requests/:id/reject", async (req, res) => {
  try {
    const result = await bizziService.rejectRefund({ request_id: req.params.id, ...req.body });
    return res.json(result);
  } catch (error) {
    return handleResponseError(res, error);
  }
});

module.exports = router
