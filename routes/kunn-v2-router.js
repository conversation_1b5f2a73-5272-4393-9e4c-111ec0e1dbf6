const express = require("express");
const {
  decryptRequestMisa,
  encryptResponseMisa,
} = require("../utils/middleware.js");
const router = express.Router();
const {
  misaCreateKunnValidateV2,
  misaSignKunnValidate,
  misaCancelKunnValidate,
  misaGetBankInfoValidate,
} = require("../utils/validator/misa-validator");
const kunnFromService = require("../KUNN_V2/misa-kunn-form-service");
const smeService = require("../services/sme-misa-v2");
const { handleResponseError } = require("../base/response.js");
const { throwBadReqError } = require("../utils/helper.js");
const { MISA_ERROR_CODE } = require("../const/response-const.js");
const misaController = require("../controllers/misaController.js");

router.get(
  "/misa/kunn/gen-ttrv",
  async (req, res) => {
    try {
      const data = await kunnFromService.genKunnTtrv(req.query?.kunnId);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/misa/kunn/gen-ltt",
  async (req, res) => {
    try {
      const data = await kunnFromService.genKunnLtt(req.query?.kunnId);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/misa/kunn",
  decryptRequestMisa,
  misaCreateKunnValidateV2,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const context = { req };
      const data = await kunnFromService.createKunn(req.body, context);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/misa/kunn/sign",
  decryptRequestMisa,
  misaSignKunnValidate,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const context = { req };
      const data = await smeService.signKunnContract(req.body, context);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/misa/kunn/sign/test",
  decryptRequestMisa,
  misaSignKunnValidate,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const context = { req };
      const data = await smeService.signKunnContract(req.body, context, true);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/misa/kunn/:debtContractNumber/pre-sign",
  decryptRequestMisa,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const { debtContractNumber } = req.params;
      if (!debtContractNumber) {
        throwBadReqError(
          "debtContractNumber",
          "MISSING FIELD debtContractNumber",
          MISA_ERROR_CODE.E400
        );
      }
      const data = await misaController.getKunnPresignInfoController(debtContractNumber);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/misa/kunn/cancel",
  decryptRequestMisa,
  misaCancelKunnValidate,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const data = await smeService.cancelKunn(req.body);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/misa/credit-limit/details",
  decryptRequestMisa,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const contractNumber = req.query?.contractNumber;
      const { availableAmount, lastestLoanAmount } = await misaController.calculateAvailableAmountByContractNumber(contractNumber);
      const data = await smeService.getKunnInstallment(contractNumber, { availableAmount, lastestLoanAmount });
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.post(
  "/misa/kunn/test",
  decryptRequestMisa,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const data = await smeService.createKunnDebt(req.body.debtContractNumber);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/misa/credit-limit",
  decryptRequestMisa,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const { contractNumber } = req.query;
      if (!contractNumber)
      {
        throwBadReqError(
          "contractNumber",
          "MISSING FIELD contractNumber",
          MISA_ERROR_CODE.E400
        );
      }
      const data = await misaController.getCreditLimit(contractNumber);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

router.get(
  "/misa/bank-info",
  decryptRequestMisa,
  misaGetBankInfoValidate,
  encryptResponseMisa,
  async (req, res) => {
    try {
      const data = await smeService.getBankInfo(req.query);
      return res.json(data);
    } catch (error) {
      return handleResponseError(res, error);
    }
  }
);

module.exports = router;
