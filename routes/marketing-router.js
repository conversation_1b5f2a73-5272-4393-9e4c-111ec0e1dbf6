const express = require('express');
const router = express.Router();
const marketingService = require("../services/marketing-service");

router.get("/marketing/get-kunn", async (req, res) => {
    const time = req.query.time
    const rs = await marketingService.getKunnSignedDisburByTime(time)
    return res.status(rs.statusCode).json(rs)
})

router.get("/marketing/get-not-yet-decided", async (req, res) => {
    const time = req.query.time
    const rs = await marketingService.getNotYetDecidedLoanByTime(time)
    return res.status(rs.statusCode).json(rs)
})

router.get("/marketing/get-cancelled-ekyc-loan", async (req, res) => {
    const rs = await marketingService.getCancelledEkycLoan()
    return res.status(rs.statusCode).json(rs)
})

module.exports = router;