const express = require('express');
const router = express.Router();
const {VSKReviewHmValidate} = require("../utils/validator/vsk-validator")
const {KOVReviewHmValidate} = require("../utils/validator/kov-validator")
const {VskReview,KovReview} = require("../REVIEW_BASE/review-app-form-service")

router.post('/vsk',VSKReviewHmValidate,(req,res) => {
    const vskrv = new VskReview(req,res)
    vskrv.reviewLoanContract()
})

router.post('/kov',KOVReviewHmValidate,(req,res) => {
    const kovrv = new KovReview(req,res)
    kovrv.reviewLoanContract()
})

module.exports = router