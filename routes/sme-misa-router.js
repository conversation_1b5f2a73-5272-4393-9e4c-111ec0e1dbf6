const express = require('express');
const router = express.Router();
const misaValidator = require("../utils/validator/misa-validator")
const {genPresignedUrlForSme} = require("../upload_document/s3-service")
const { authenticateOauth2V03WithToken } = require("../utils/aaaService")
const {MisaA1, MisaAf1, MisaTc1} = require("../A1_BASE/a1-app-form-service");
const {MisaBasicValidate} = require("../utils/validator/misa-validator")
const {MisaA2, MisaAf2, MisaTc2} = require("../A2_BASE/a2-app-form-service");
const {MisaFullloanValidate} = require("../utils/validator/misa-validator")
const {genRequestId} = require("../utils/helper");
const { decryptRequestMisa, encryptResponseMisa } = require('../utils/middleware');
const { Response, handleResponseError } = require('../base/response');
const { ERROR_CODE, MisaStep } = require('../const/definition');
const misaController = require('../controllers/misaController');
const downloadContract = require("../contract/download_contract");
const loggingRepo = require("../repositories/logging-repo");

router.post('/v1/misa/checkA1', authenticateOauth2V03WithToken, MisaBasicValidate, (req, res) => {
    const misaA1 = new MisaA1(req,res)
    misaA1.createLoan()
})
router.post('/v1/misa/checkA2', authenticateOauth2V03WithToken, MisaFullloanValidate, (req, res) => {
    const misa2 = new MisaA2(req,res)
    misa2.a2Receive()
})
router.post('/v1/misa/document/presignedS3', authenticateOauth2V03WithToken, (req, res, next) => {
    req.query.partnerCode='MIS';
    req.query.requestId=genRequestId(req.query.partnerCode);
    return next();
},misaValidator.MisaGetPresignedValidate,genPresignedUrlForSme)
// router.post('/select-offers', authenticate_oauth2,vplValidator.VPLSelectOfferValidate,vdsService.selectOffer)

// router.post('/resubmit-docs', authenticate_oauth2,vplValidator.VPLResubmitDoc,vdsService.resubmitDoc)


router.post('/v2/misa/af1', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    const misaA1 = new MisaAf1(req, res)
    misaA1.processAf1()
})

router.post('/v2/misa/af2', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    const misa2 = new MisaAf2(req, res)
    misa2.processAf2()
})

router.post('/v2/misa/manual/re-check-af2', authenticateOauth2V03WithToken, (req, res) => {
    const misa2 = new MisaAf2(req, res)
    loggingRepo.saveWorkflow(MisaStep.AF2, `RE-CHECK_AF2`, req?.body?.contractNumber, 'system').then(() => {
        misa2.checkAntiFraud(req?.body?.contractNumber);
    }).catch((e) => {
        console.error(`ERROR | misa/manual/re-check-af2 | save workflow status error | ${req?.body?.contractNumber}`, e)
    })
    return res.status(200).json(new Response(
        ERROR_CODE.SUCCESS,
        `re-check-af2 success`,
        {
            contractNumber: req?.body?.contractNumber
        }
    ));
})

router.post('/v2/misa/loan-evaluation-report', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    misaController.exportLoanEvaluationReportController(req, res);
})

router.post('/v2/misa/signature/evf', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    misaController.fillEvfSignatureController(req, res);
})

router.post('/v2/misa/manual/cic-callback', authenticateOauth2V03WithToken, (req, res) => {
    misaController.manualCallbackCic(req, res);
})

router.post('/v2/misa/test', authenticateOauth2V03WithToken, (req, res) => {
    misaController.test(req, res);
})

router.post('/v2/misa/re-gen-loan-evaluation-report', authenticateOauth2V03WithToken, (req, res) => {
    misaController.reGenLoanEvaluationReportController(req, res);
})

router.get("/v2/misa/file", authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    downloadContract.downloadFileForSmePartner(req, res);
});
router.get("/v2/misa/kunn/file", authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    downloadContract.downloadFileKunnForSmePartner(req, res);
});

router.put('/v2/misa/loan-status', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    misaController.updateStatus(req, res);
})

router.post('/v2/misa/re-signature/evf', (req, res) => {
    misaController.refillEvfSignatureController(req, res);
})

router.post('/v2/misa/download-misa-file', (req, res) => {
    misaController.downloadMisaFile(req, res);
})

router.put('/v2/misa/manual/doc-group', (req, res) => {
    misaController.manualUpdateDocGroup(req, res);
})

router.post('/v2/misa/download-misa-file-test', (req, res) => {
    misaController.downloadMisaFileTest(req, res);
})

router.post(
  "/v2/misa/upload-financial-report",
  decryptRequestMisa,
  misaValidator.misaGetFinancialReportValidate,
  encryptResponseMisa,
  (req, res) => {
    misaController.uploadFinancialReport(req, res);
  }
);

router.post(
    "/v2/misa/upload-document-report",
    decryptRequestMisa,
    misaValidator.misaGetDocumentReportValidate,
    encryptResponseMisa,
    (req, res) => {
        misaController.uploadDocumentReport(req, res);
    }
);

router.get(
    "/v2/misa/get-installments",
    decryptRequestMisa,
    encryptResponseMisa,
    (req, res) => {
        misaController.getInstallments(req, res);
    }
);

router.post('/v2/misa/financial-docs/download', (req, res) => {
    misaController.downloadFinancialDocuments(req, res);
})

router.post('/v2/misa/refinancing/af1', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    const misa1 = new MisaTc1(req, res)
    misa1.processTc1()
})
router.post('/v2/misa/refinancing/af2', authenticateOauth2V03WithToken, decryptRequestMisa, encryptResponseMisa, (req, res) => {
    const misa2 = new MisaTc2(req, res)
    misa2.processTc2()
})

router.get('/v1/misa/check-report-data', async (req, res) => {
    const {contractNumber} = req.query;
    if (!contractNumber) {
        return res.status(400).json(new Response(
            ERROR_CODE.INVALID_REQUEST,
            'Missing contractNumber',
            null
        ));
    }
    try {
        const result = await misaController.getDataBctdMisaByContractNumber(contractNumber);
        return res.status(200).json(new Response(
            ERROR_CODE.SUCCESS,
            'Success',
            result
        ));
    } catch (error) {
        return res.status(500).json(new Response(
            ERROR_CODE.SERVER_ERROR,
            'Internal Server Error',
            null
        ));
    }   
})

module.exports = router