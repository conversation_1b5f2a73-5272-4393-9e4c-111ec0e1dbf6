const express = require('express');
const router = express.Router();
const a1Service = require("../a1_application/a1-recieve-service")
const a2Service = require("../a2_application/a2-recieve-service")
const splValidator = require("../utils/validator/spl-validator")
const {CONTRACT_TYPE,PARTNER_CODE} = require("../const/definition")
const  {mcCreditLineBasicInfo} = require("../services/app-form-service")

router.post('/basic-info',splValidator.splBasicValidate,(req,res) => {
    req.body.contract_type = CONTRACT_TYPE.CREDIT_LINE
    req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE
    const spl = new mcCreditLineBasicInfo(req,res,PARTNER_CODE.SPL)
    spl.recieveBasic()
})

router.post('/fullloan',a2Service.a2_recieve)

module.exports = router