const express = require('express');
const router = express.Router();
const { <PERSON>loanValidate, SubmitLoanLimitValidate, CreateKunnValidate, SubmitIncreaseLoanLimitValidate, createWithDrawRequestCheck } = require('../utils/validator/super-app-validator');
const { SuperAppA1 } = require('../A1_BASE/a1-app-form-service');
const { SuperAppA2 } = require('../A2_BASE/a2-app-form-service');
const superAppService = require('../super_app/application-form-service');
const { STATUS } = require('../const/caseStatus');
const { RESPONSE_CODE, PRODUCT_CODE, PARTNER_CODE, SMA_PAYMENT_METHOD } = require('../const/definition');
const { SuperAppKUNN } = require('../KUNN_V2/app-form-service');
const { getLoanContract } = require('../repositories/loan-contract-repo');
const { RESPONSE_MSG, RES_STT_CODE } = require('../const/response-const');
const productService = require('../utils/productService');
const { isNullOrEmpty, getPartnerCode } = require('../utils/helper');
const { updateKunnInfo, getKunnData } = require('../repositories/kunn-repo');

router.post('/submit-loan-limit', SubmitLoanLimitValidate, async (req, res) => {
    try {
        const rs = await superAppService.submitLoanLimit(req)
        return res.status(rs.statusCode).json(rs)
    } catch (error) {
        console.log("error when submit-loan-limit: " + error.message)
        return res.status(RES_STT_CODE.SERVER_ERROR).json({
            code: RESPONSE_CODE.SERVER_ERROR,
            message: RESPONSE_MSG.INTERNAL_SERVER_ERROR
        })
    }
})

// router.post('/full-loan/check', FullloanValidate, async (req, res) => {
//     try {
//         const af1 = new SuperAppA1(req, res)
//         const contractNumber = req.body.contractNumber
//         const loanData = await getLoanContract(contractNumber)
//         const status = loanData?.status
//         if (status == STATUS.LOAN_LIMIT_PASSED) {
//             const rsAf1 = await af1.createLoan()
//             if (rsAf1?.code == RESPONSE_CODE.RECIEVED) {
//                 req.body.contractNumber = contractNumber
//                 const af2 = new SuperAppA2(req, res)
//                 const rsAf2 = await af2.a2Receive()
//                 return res.status(200).json(rsAf2)
//             } else {
//                 return res.status(200).json(rsAf1)
//             }
//         } else {
//             return res.status(200).json({
//                 code: RESPONSE_CODE.INVALID_REQUEST,
//                 message: 'The contractNumber is not valid.',
//                 contractNumber
//             })
//         }
//     } catch (error) {
//         console.log(error)
//         console.log(`error when check full loan | ${contractNumber} | ${error}`)
//     }
// })

router.post('/create-request', CreateKunnValidate, async (req,res) => {
    try {
        const kunnCode = req.body.kunnCode
        const productData = await productService.getProductInfoV2(kunnCode)
        if (productData) {
            const ir = productData?.rate?.find(val => val?.rateType == "OFFER_NORMAL_RATE")?.intRateVal / 100
            req.body.ir = ir
        }
        if(req.body?.paymentMethod == SMA_PAYMENT_METHOD[1]){
            req.body.billDay = new Date().getDate()
        }
        const partnerCode = await getPartnerCode(req.poolRead,req.body.contractNumber)
        req.body.partner_code = PARTNER_CODE.SMA
        if(partnerCode != PARTNER_CODE.SMA)
            req.body.partner_code = PARTNER_CODE.SMASYNC
        const kunn = new SuperAppKUNN(req,res,kunnCode)
        kunn.createDiburRequest()
    } catch (error) {
        console.log('error when create kunn: ', error?.message)
    }
})

router.post('/submit-increase-loan-limit', SubmitIncreaseLoanLimitValidate, async (req, res) => {
    try {
        const rs = await superAppService.submitIncreaseLoanLimit(req)
        return res.status(rs.statusCode).json(rs)
    } catch (error) {
        console.log("error when submit-increase-loan-limit: " + error.message)
        return res.status(RES_STT_CODE.SERVER_ERROR).json({
            code: RESPONSE_CODE.SERVER_ERROR,
            message: RESPONSE_MSG.INTERNAL_SERVER_ERROR
        })
    }
})

// router.post('/basic/check', McAppBasicValidate, (req, res) => {
//     const af1 = new SuperAppA1(req, res)
//     af1.createLoan();
// })

router.post('/full-loan/check', FullloanValidate, async (req, res) => {
    try {
        const af1 = new SuperAppA1(req, res);
        const rsA1 = await af1.createLoan();
        if (rsA1?.message == STATUS.ELIGIBLE) {
            req.body.contractNumber = rsA1.contractNumber;
            const af2 = new SuperAppA2(req, res);
            af2.a2Receive();
        } else {
            return res.status(200).json(rsA1);
        }
    } catch (error) {
        console.log("error when check full loan: " + error?.message)
    }
});

router.post('/create-request/check', createWithDrawRequestCheck, async (req, res) => {
    try {
        const formData = req.body
        const rs = await superAppService.calculateOfferWithDrawRequest(formData)
        return res.status(rs.statusCode).json(rs)
    } catch (error) {
        console.log("error when check create request: " + error?.message)
    }
})

router.post('/kunn/update', async (req, res) => {
    try {
        const pl = req.body
        if(!isNullOrEmpty(pl.partnerCode) && !isNullOrEmpty(pl.kunnId)) {
            const kunnData = await getKunnData(pl.kunnId)
            if(!kunnData)
                return res.status(400).json({
                    code: 1,
                    message: "kunnId was not found"
                })
            const rs = await updateKunnInfo(pl)
            if(!rs){
                return res.status(400).json({
                    code: 1,
                    message: "Update kunn fail"
                })
            } 
            return res.status(400).json({
                code: 0,
                message: "Update kunn success"
            })
        } else {
            return res.status(400).json({
                code: 1,
                message: "Input is invalid"
            })
        }
    } catch (error) {
        console.log("error when update kunnInfo: " + error?.message)
    }
})
router.post('/ocr/bussiness-certification/callback', async (req, res) => {
    try {
        const pl = req.body
        console.log('request body callback ocr bussiness certification:', JSON.stringify(pl))
        return res.status(200).json({
            code: 0,
            message: "Success"
        })

    } catch (error) {
        console.log("error when update kunnInfo: " + error?.message)
        return res.status(500).json({
            code: 99,
            message: "System error!"
        })
    }
})

module.exports = router