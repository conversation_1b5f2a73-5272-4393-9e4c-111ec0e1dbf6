const express = require('express');
const loanContractRepo = require("../repositories/loan-contract-repo")
const invoiceRepo = require("../repositories/invoice-repo")
const uiService = require("../services/ui-service")
const offerService = require("../services/offer-service")
const cmtService = require("../UI_API/getComment-service")
const router = express.Router();
const apiValidator = require("../utils/validator/base-api-validator")
const {CONTRACT_TYPE} = require("../const/definition")
const kycService = require("../services/kyc-service");
const { addPaging } = require('../utils/middleware');
const { validator, responseError } = require("../utils/validator/validate");
const Joi = require('joi');
const { STATUS } = require('../const/caseStatus');
const { genUploadPresignedUrl, genViewOrDownloadPresignedUrl } = require('../upload_document/s3-service');

router.get("/vpl/turn-over",uiService.getVdsTurnOver)

router.get("/vtp/turn-over",uiService.getVTPTurnover)

router.get("/kov/turn-over",uiService.getKOVTurnover)

router.get("/getDIInfo",uiService.getDIScore)

router.post("/score",uiService.saveChecklistScore)

router.get("/getOfferList",uiService.getOfferList)

router.post("/computeDI",offerService.calculateDIV2)

router.post("/di/computeOffer",offerService.calculateOffer)

router.get("/di/info",offerService.getOfferScore)

router.post("/validateOffer",offerService.validateOffer) 

router.get("/case/summary-info",apiValidator.validRequireContract, uiService.getCaseSummary)

router.get("/contact/allContact",uiService.getAllContact)

router.get("/comment/mc",cmtService.getMCcmt)

router.get("/kunn/get",uiService.getKunnDataByCustId)

router.get("/kunn/get",uiService.getKunnDataByKunnId)

router.get("/kunn/getByContractNumber",uiService.getKunnDataByContractNumber)

router.get("/after-loan/turn-trans/get",uiService.getTurnoverAfterLoan)

router.get('/caseAction/getKyc', kycService.getKYCInfo)

router.post('/ekyc/update', kycService.updateStatus)

router.post('/kyc/edit', kycService.editKyc)

// // api lay danh sách full thong tin de tham dinh 
// router.get('/contracts/review/info', uiService.getReviewInfo)

router.get('/review/contracts',addPaging, uiService.getReviewContract)
router.get('/review/contracts/details', uiService.getReviewContractDetails)

router.post('/review/contracts/comments',(req, res, next)=>{
    const schema = Joi.object({
        contractNumber: Joi.string().required(),
        comment: Joi.string().required(),
        paramLocation: Joi.string().required(),
        createdBy: Joi.string().allow(null).allow(""),
        userName: Joi.string().allow(null).allow(""),
    });
    
    const { error } = schema.validate(req.body, { abortEarly: false });
    if (error) {
        return res.status(400).json({
            code: -1,
            message: "Validation error",
            details: error.details.map(err => err.message)
        });
    }
    next();
}, uiService.createReviewContractComments)

router.post('/review/contracts/update-status', (req, res, next)=>{
    const schema = Joi.object({
        contractNumber: Joi.string().required(),
        status: Joi.string().valid(STATUS.CP_RESUBMIT, STATUS.REFUSED,STATUS.APPROVED),
        createdBy: Joi.string().required(),
        userName: Joi.string().allow(null).allow(""),
    });
    
    const { error } = schema.validate(req.body, { abortEarly: false });
    if (error) {
        return res.status(400).json({
            code: -1,
            message: "Validation error",
            details: error.details.map(err => err.message)
        });
    }
    next();
},uiService.updateReviewContractStatus)

router.post('/upload/presigned-url', genUploadPresignedUrl);
router.get('/view/presigned-url', genViewOrDownloadPresignedUrl);

router.get('/kunn/invoices', async (req, res) => {
    try {
        const { contract_number } = req.query;
        if (!contract_number) {
            return res.status(400).json({ code: -1, message: "contract_number is required" });
        }
        const result = await invoiceRepo.getInvoicesByKunn(contract_number);
        return res.status(200).json(result);
    } catch (error) {
        console.error("Error fetching invoices:", error);
        return res.status(500).json({ code: -1, message: "Internal server error" });
    }
});

module.exports = router;