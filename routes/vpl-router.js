const express = require('express');
const router = express.Router();
const vdsService = require("../services/vds-service")
const vplValidator = require("../utils/validator/vpl-validator")
const {basicInfo} = require("../a1_application/a1-recieve-service")
const {fullLoan} = require("../a2_application/a2-recieve-service")
const {genPresignedUrl} = require("../upload_document/s3-service")
const { authenticateOauth2V03WithToken } = require("../utils/aaaService");
const { CONTRACT_TYPE } = require('../const/definition');

router.get('/check-status', authenticateOauth2V03WithToken,vdsService.checkStatus)

router.post('/select-offers', authenticateOauth2V03WithToken,vplValidator.VPLSelectOfferValidate,vdsService.selectOffer)

router.post('/resubmit-docs', authenticateOauth2V03WithToken,vplValidator.VPLResubmitDoc,vdsService.resubmitDoc)

router.post('/s3-presigned',authenticateOauth2V03WithToken,vplValidator.VPLGetPresignedValidate,genPresignedUrl)

router.post('/eligible',authenticateOauth2V03WithToken,vplValidator.VPLEligibleValidate,(req,res,next) => {
    req.body.contract_type = CONTRACT_TYPE.CASH_LOAN
    req.body.lms_type = CONTRACT_TYPE.CASH_LOAN
    next()
},basicInfo)

router.post('/full-loan',authenticateOauth2V03WithToken,vplValidator.VPLFullloanValidate,fullLoan)

module.exports = router