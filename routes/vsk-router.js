const express = require('express');
const router = express.Router();
const vskValidator = require("../utils/validator/vsk-validator")
const {basicInfo} = require("../a1_application/a1-recieve-service")
const {fullLoan} = require("../a2_application/a2-recieve-service")
const {genPresignedUrl} = require("../upload_document/s3-service")
const vdsService = require("../services/vds-service")
const dataEntry = require("../services/data-entry-service")
const vskOffer = require("../offer/VSK-offer")
const authenService = require("../utils/aaaService")
const loanContractRepo = require("../repositories/loan-contract-repo")
const helper = require("../utils/helper");
const common = require('../utils/common')
const { CONTRACT_TYPE } = require('../const/definition');

router.post('/basic/check', (req,res,next) => {
    req.body.partnerCode = 'VSK'
    if(req.body.contractType == CONTRACT_TYPE.CREDIT_LINE){
        req.body.tenor = 36
        req.body.lms_type = CONTRACT_TYPE.CREDIT_LINE
    }
    else {
        req.body.lms_type = CONTRACT_TYPE.CASH_LOAN
    }
    return next()
},vskValidator.VSKBasicValidate,basicInfo)

router.post('/s3/presigned',vskValidator.VSKGetPresignedValidate,genPresignedUrl)
router.post('/fullloan/check',vskValidator.VSKFullloanValidate,fullLoan)
router.get('/dataentry/getoffer',vskOffer.computeVSKOffer)
router.post('/resubmit-docs', vskValidator.VSKResubmitDoc,vdsService.resubmitDoc)

router.post('/dataentry',dataEntry.saveDataEntry)
// router.put('/dataentry',dataEntry.updateDataEntry)
router.get('/dataentry',dataEntry.getDataEntry)
router.get('/dataentry/history',dataEntry.getHistory)
router.get('/dataentry/mistakeCE',dataEntry.getMistakeInfo)
router.get('/remain-info',async(req,res) => {
    try {
        const contractNumber = req.query.contractNumber
        const contractData = await loanContractRepo.getRemainInfo(contractNumber)
        contractData.bank_account_owner = contractData.cust_full_name ? helper.nonAccentVietnamese(contractData.cust_full_name) : ''
        if(!contractNumber) return res.status(400).json({code: 1,msg: "Invalid contractNumber"})
        if(!contractData) return res.status(400).json({code: 1,msg: "Get remain info error"})
        return res.status(200).json({code: 0, msg: "Get remain info success", data: contractData})
    }
    catch(err) {
        console.log(err)
        common.log(`get bundle error : ${err.message} `,req.query.contractNumber)
        return res.status(500).json({
            code : 1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
})

module.exports = router