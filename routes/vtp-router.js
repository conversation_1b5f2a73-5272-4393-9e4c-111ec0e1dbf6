const express = require('express');
const router = express.Router();
const vtpValidator = require("../utils/validator/vtp-validator")
const baseApiValidator = require("../utils/validator/base-api-validator")
const {basicInfoCOD} = require("../a1_application/a1-recieve-service")
const {fullLoan} = require("../a2_application/a2-recieve-service")
const {genPresignedUrl} = require("../upload_document/s3-service")
const codService = require("../services/cod-service")

router.post('/basic/check', vtpValidator.VTPBasicValidate,basicInfoCOD)

router.post('/s3/presigned',vtpValidator.VTPGetPresignedValidate,genPresignedUrl)

router.post('/fullloan/check',vtpValidator.VTPFullloanValidate,fullLoan)

router.get('/product/info',baseApiValidator.validRequireContract,codService.getProductInfo)

module.exports = router