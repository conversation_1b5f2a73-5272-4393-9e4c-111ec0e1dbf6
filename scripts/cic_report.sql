-- public.financial_statements_export definition

-- Drop table

-- DROP TABLE public.financial_statements_export;

CREATE TABLE financial_statements_export (
	id serial4 NOT NULL,
	loan_revenues_id int4 NOT NULL,
	report_date date NULL,
	ccy varchar(10) NULL,
	report_year varchar NULL,
	is_audit varchar(10) NULL,
	financial_year_end_date varchar(20) NULL,
	file_key varchar(255) NULL,
	unit_of_measurement varchar(20) NULL,
	contract_number varchar(20) NULL,
	accounting_regime varchar(50) NULL,
	accounting_template varchar(50) NULL,
	created_by varchar(20) DEFAULT 'system'::character varying NULL,
	updated_by varchar(20) DEFAULT 'system'::character varying NULL,
	created_date timestamp DEFAULT now() NOT NULL,
	updated_date timestamp DEFAULT now() NOT NULL,
	is_deleted int2 DEFAULT 0 NOT NULL,
	code varchar NULL
);

-- public.financial_statement_details definition

-- Drop table

-- DROP TABLE public.financial_statement_details;

CREATE TABLE financial_statement_details (
	id serial4 NOT NULL,
	created_at timestamp DEFAULT now() NOT NULL,
	updated_at timestamp DEFAULT now() NOT NULL,
	financial_statements_export_id int4 NULL,
	code varchar(50) NULL,
	"name" varchar(500) NULL,
	note varchar(255) NULL,
	num_of_first_year float8 NULL,
	num_of_second_year float8 NULL,
	created_by varchar(20) DEFAULT 'system'::character varying NULL,
	updated_by varchar(20) DEFAULT 'system'::character varying NULL,
	"isBold" bool DEFAULT false NOT NULL,
	is_deleted int2 DEFAULT 0 NULL,
	CONSTRAINT "PK_671f95f050eb43dd47a2aa89cea" PRIMARY KEY (id)
);