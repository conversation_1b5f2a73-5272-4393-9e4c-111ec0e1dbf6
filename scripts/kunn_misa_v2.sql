ALTER TABLE request_log ADD "url" varchar NULL;


ALTER TABLE loan_contract_document ADD file_size int8 NULL;
ALTER TABLE loan_contract_document ADD disbursement_info_id int4 NULL;

ALTER TABLE kunn ADD step varchar NULL;

ALTER TABLE kunn ADD expired_date varchar NULL;

ALTER TABLE kunn ADD loan_effect_time varchar NULL;

ALTER TABLE kunn ADD api_version varchar DEFAULT 'v1' NULL;

ALTER TABLE kunn ADD disbursement_date timestamp NULL;

ALTER TABLE step_check_log ADD "url" varchar NULL;


------------configuration-----------------
INSERT INTO "configuration"
( "service", param_group, param_name, value, data_type, is_deleted)
VALUES( 'los', 'partnerCallback', 'misaResultKunnActUrl', 'https://testonlinelendingpartner.misa.vn/lending/api/partner-profile/api/v1/EVNDebtContractLps/file-contract', '', 0);


INSERT INTO "configuration"
( "service", param_group, param_name, value, data_type, is_deleted)
VALUES( 'los', 'lms', 'cancelDebt', '/lms-mc/v1/debt-ack-contract/cancel', '', 0);

INSERT INTO "configuration"
( "service", param_group, param_name, value, data_type, is_deleted)
VALUES( 'los', 'lms', 'installment', '/lms-mc/v1/installment/get-by-contract-limit', '', 0);

INSERT INTO "configuration"
(service, param_group, param_name, value, data_type, is_deleted)
VALUES( 'los', 'partnerCallback', 'misaUpdateLimitApi', 'https://testonlinelendingpartner.misa.vn/lending/api/partner-profile/api/v1/EVNLoanProfileLps/tracking-info', '', 0);
---
INSERT INTO "configuration"
(service, param_group, param_name, value, data_type, is_deleted)
VALUES('los', 'disbursementService', 'checkAccountInfoApi', '/api/disbursementServices/v1/internal/accounts/info', '', 0);

INSERT INTO "configuration"
( service, param_group, param_name, value, data_type, is_deleted)
VALUES( 'los', 'partnerCallback', 'misaCancelUrl', 'https://testonlinelendingpartner.misa.vn/lending/api/partner-profile/api/v1/EVNDebtContractLps', '', 0);
-- kunn_disbursement_info definition





-- Drop table

-- DROP TABLE kunn_disbursement_info;

CREATE TABLE kunn_disbursement_info (
	id serial4 NOT NULL,
	kunn_id varchar NOT NULL,
	beneficiary varchar NULL,
	account_name varchar NULL,
	bank_account varchar NULL,
	bank_code varchar NULL,
	bank_branch_code varchar NULL,
	amount numeric NULL,
	transfer_content varchar NULL,
	is_deleted int4 DEFAULT 0 NULL,
	created_date timestamp DEFAULT now() NULL,
	updated_date timestamp DEFAULT now() NULL,
	bank_name varchar NULL,
	verify_account_name varchar NULL,
	verify_account_info varchar NULL
);
CREATE INDEX kunn_disbursement_info_kunn_id_idx ON kunn_disbursement_info USING btree (kunn_id);
