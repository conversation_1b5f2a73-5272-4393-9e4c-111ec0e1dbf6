-- DROP FUNCTION public.sp_handle_document_after_signed(varchar, varchar, varchar, bpchar, bpchar, text);

CREATE OR REPLACE FUNCTION public.sp_handle_document_after_signed(p_new_contract_number character varying, p_new_doc_type character varying, p_new_doc_id character varying, p_new_doc_name_vn character, p_new_doc_name_vn_detail character, p_stepid text)
 RETURNS TABLE(doc_id character varying, contract_number character varying, doc_type character varying)
 LANGUAGE plpgsql
AS $function$
DECLARE
    old_rec RECORD;
BEGIN
    BEGIN
        SELECT lcd.doc_id, lcd.contract_number, lcd.doc_type, lcd.creation_time, lcd.updated_date, lcd.url, lcd.file_key, lcd.file_name, lcd.is_deleted, lcd.document_no, lcd.doc_group
        INTO old_rec
        FROM loan_contract_document lcd
        WHERE lcd.contract_number = p_new_contract_number
          AND lcd.doc_type = p_new_doc_type
          AND lcd.doc_id <> p_new_doc_id
          AND lcd.is_deleted <> 1 
        ORDER BY lcd.creation_time DESC
        LIMIT 1;
    

        UPDATE loan_contract_document 
        SET  contract_number = p_new_contract_number,
             doc_type = p_new_doc_type,
             doc_name_vn = p_new_doc_name_vn,
             doc_name_vn_detail = p_new_doc_name_vn_detail,
             updated_date = now(),
             document_no = old_rec.document_no,
             doc_group  = old_rec.doc_group
        WHERE loan_contract_document.doc_id = p_new_doc_id;
     

        --IF old_rec IS NOT NULL THEN 
        
        INSERT INTO loan_contract_document_history (
            contract_number,
            doc_type,
            doc_id,
            creation_time,
            updated_date,
            url,
            file_key,
            file_name,
            step
        ) VALUES (
            old_rec.contract_number,
            old_rec.doc_type,
            old_rec.doc_id,
            old_rec.creation_time,
            old_rec.updated_date,
            old_rec.url,
            old_rec.file_key,
            old_rec.file_name,
            p_stepid
        );
       /*
        UPDATE loan_contract_document lcd_toupdate
        SET document_no = old_rec.document_no,
            doc_group = old_rec.doc_group
        WHERE lcd_toupdate.doc_id = p_new_doc_id; 
       */
        UPDATE loan_contract_document lcd
        SET   is_deleted = 1,
              updated_date = now()
        WHERE lcd.doc_id = old_rec.doc_id; 
        --END IF;
     
    EXCEPTION WHEN OTHERS THEN
    	RAISE NOTICE 'Error in sp_handle_document_after_signed: %', SQLERRM;
        INSERT INTO error_log (param, error_logs, created_date)
        VALUES (p_new_contract_number, 'Error in sp_handle_document_after_signed: ' || SQLERRM, now());
       	RETURN;
    END;

    RETURN QUERY
    SELECT lcd.doc_id, lcd.contract_number, lcd.doc_type
    FROM loan_contract_document lcd
    WHERE lcd.doc_id = p_new_doc_id;
END;
$function$
;
