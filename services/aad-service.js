const { pushTaskMcV2} = require("../utils/aadService")
const {STATUS} = require("../const/caseStatus")
const {roleCode, PARTNER_CODE} = require("../const/definition")
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")
const callbackSerrvice = require("../services/callback-service")

async function pushTaskCheckDoc(contractNumber,role) {
    try {
        let status;
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        if(role == roleCode.CP) {
            status = STATUS.IN_CP_QUEUE
        }
        if(role == roleCode.CE) {
            status = STATUS.IN_CE_QUEUE
        }
        if(role == roleCode.DE) {
            status = STATUS.IN_DE_QUEUE
        }
        if(role == roleCode.SS) {
            status = STATUS.IN_SS_QUEUE
        }
        await pushTaskMcV2(role,contractNumber,contractData.contract_type,status)
        await loanContractRepo.updateContractStatus(status,contractNumber)
        return true
    }
    catch(err){
        console.log("push task err :",err.message )
        console.log(err)
        return false
    }
}

async function pushTaskCheckDocKU(kunnNumber,role) {
    try {
        let status;
        const contractNumber = await kunnRepo.getContractByKU(kunnNumber)
        const loanContractData = await loanContractRepo.getLoanContract(contractNumber)
        const contractType = loanContractData?.contract_type
        // const partnerCode = loanContractData?.partner_code
        
        if(role == roleCode.CP) {
            status = STATUS.IN_CP_QUEUE
        }
        if(role == roleCode.CE) {
            status = STATUS.IN_CE_QUEUE
        }
        if(role == roleCode.SS) {
            status = STATUS.IN_SS_QUEUE
        }
        await pushTaskMcV2(role,kunnNumber,contractType,status)
        await kunnRepo.updateKUStatus(kunnNumber,status)
        return true
    }
    catch(err){
        console.log("push task err :",err.message )
        console.log(err)
        return false
    }
}

module.exports = {
    pushTaskCheckDoc,
    pushTaskCheckDocKU
}