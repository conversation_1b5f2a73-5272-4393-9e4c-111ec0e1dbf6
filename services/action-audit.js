const helper = require('../utils/helper')
const common = require('../utils/common')
const { CORE } = require('../const/variables-const')
const moment = require('moment-timezone');
const timezone = "Asia/Ho_Chi_Minh";

const LMS_DATE = moment().tz(timezone).format("YYYY-MM-DD HH:mm:ss");

const saveCaseHistoryActionAudit = async function (contract_number, step_code, action_code, title, user, value) {
  let body;
  try {
    const config = global.config;

    let now = LMS_DATE;
    let url = global.config.basic.actionAudit[config.env] + `/action-audit/v1/status-history`;

    body = {
      contractNumber: contract_number,
      systemType: "STATUS_CASE_ECL",
      stepCode: step_code,
      actionCode: action_code,
      entryDate: now,
      updateStatusDate: now,
      title: title || "",
      involveParty: user || "system",
      value: value
    }

    const result = await common.postAPI(url, body);
    console.log(`Save case status: `, JSON.stringify(result));
  } catch (err) {
    console.log(`Save case status error: ${err.message}`);
    console.log(err);
  }
}

module.exports = {
  saveCaseHistoryActionAudit
}