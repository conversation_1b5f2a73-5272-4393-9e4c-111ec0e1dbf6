const {
  cicDeta<PERSON><PERSON><PERSON>,
  eligible<PERSON>pi,
  eligibleApiV2,
  s37<PERSON>pi,
  checkConsent<PERSON><PERSON>,
  checkFull<PERSON>oan<PERSON><PERSON>,
  eligibleApiCash,
  ocrIdCard<PERSON>pi,
  checkNfcApi,
  checkEkyc<PERSON>pi,
  checkCicB11t<PERSON>pi,
  checkCicB11tInvidual<PERSON>pi,
  getRenewDate<PERSON><PERSON>,
  checkEligible<PERSON>tatus<PERSON><PERSON>,
  checkBlacklist<PERSON><PERSON>,
  checkCicB11SmeApi,
  checkWhitelistFullLoanApi,
  checkWhitelistHmFullLoanApi,
  checkModelApi,
  checkModelHmApi,
  checkWhitelistAf1Api,
  checkModelFinvApi,
  checkContractProgressApi,
  checkContractActiveApi
} = require("../apis/anti-fraud.api");
const {
  ELIGIBLE_STATUS,
  DECISION_V02_RES_DECISION,
  CIC_DETAILS_STATUS,
} = require("../const/status-const");
const sqlHelper = require("../utils/sqlHelper");
const loanCicLogRepo = require("../repositories/loan-cic-log-repo");
const {
  CIC_STEP_CHECK,
  CONTRACT_TYPE,
  PARTNER_CODE,
  MisaStep,
  LOS_TYPE,
  TASK_FLOW,
  DOC_TYPE,
  LOCK_STATUS,
  SERVICE_NAME,
  PRODUCT_CODE,
  KUNN_WORKFLOW,
} = require("../const/definition");
const smeMisaService = require("../services/sme-misa-v2");
const loanContractRepo = require("../repositories/loan-contract-repo");
const { STATUS, KUNN_STATUS, CALLBACK_STAUS } = require("../const/caseStatus");
const { CIC_CONTRACT_TYPE } = require("../const/cusType");
const { genMisaKunnTTRV } = require("../utils/misa-file-handler");
const loggingRepo = require("../repositories/logging-repo");
const nfcDataRepo = require("../repositories/nfc-data-repo");
const loanContractDocumentRepo = require("../repositories/document");
const loanRatingRepo = require("../repositories/loan-rating-repo");
const loanRevenuesRepo = require("../repositories/loan-revenues-repo");
const loanContractService = require("../services/loan-contract-service");
const kunnRepo = require("../repositories/kunn-repo");
const uuid = require("uuid");
const productService = require("../utils/productService");
const { getRepresentationsByCustomer } = require("../repositories/loan-customer-representations-repo");
const actionAuditService = require("./action-audit");

const {
  throwBadReqError,
  throwServerError,
  snakeToCamel,
  convertCamelToSnake,
} = require("../utils/helper");
const common = require("../utils/common");
const moment = require("moment-timezone");
const { getValueCode, getPlace, getValueCodeMasterdataV2 } = require("../utils/masterdataService");
const documentRepo = require("../repositories/document");
const {
  callbackReject,
  callbackAprroved,
  callbackCancel,
} = require("../utils/callbackService");
const { MISA_ERROR_CODE } = require("../const/response-const");
const { callbackPartner, callbackFinv } = require("./callback-service");
const { CHECK_NFC_STEP } = require("../const/step-const");
const _ = require("lodash");
const { getFinancialReportDataFromFile } = require("./cic-report-service");
const financialStatementDetailsRepo = require("../repositories/financial_statement_details-repo");
const { CHECK_CIC_DETAILS_SUBJECT } = require("../const/variables-const");
const { goNextStep } = require("./workflow-continue");
const crmService = require("../utils/crmService");
const { processCallbackWaitingCicKunn } = require("../KUNN/bizzi-kunn");
const utils = require("../utils/helper");
const { CASE_STATUS } = require("../const/code-const");
const workflowRouter = require("../services/workflow-router");
const { promises } = require("form-data");

/**
 * 
 * @param {*} legalRepresentative : nguoi dai dien phap luat, cau truc 
 *    {
        customerName,
        idNumber,
        otherIdNumber,
        issueDate,
        issuePlace,
        dateOfBirth,
        gender,
        phoneNumber,
      }
 * @param {*} otherPersons : danh sach cac the nhan khac, cau truc 
      {
        customerName,
        idNumber,
        otherIdNumber,
        issueDate,
        issuePlace,
        dateOfBirth,
        gender,
        phoneNumber,
      }
 * @returns eligible result
 */
const checkEligibleSmeV2 = async ({
  requestId,
  legalRepresentative,
  otherPersons,
  productType,
  registrationNumber,
  taxCode,
  companyName,
  registrationDate,
  productCode,
  caseCreationTime,
  partnerCode,
  channel,
  contractNumber,
  companyType,
  contractType,
  otherEnterprises,
  options = {},
}) => {
  try {
    const payload = {
      requestId,
      legalRepresentative,
      otherPersons,
      productType,
      registrationNumber,
      taxCode,
      companyName,
      registrationDate,
      productCode,
      caseCreationTime,
      partnerCode,
      channel,
      contractNumber,
      companyType,
      contractType,
      otherEnterprises,
      options: options
    };
    //call eligible
    const eligibleRes = await eligibleApiV2(payload);
    // let status;
    if (eligibleRes?.code === 0) {
      // if (ELIGIBLE_STATUS.ELIGIBLE === eligibleRes?.data?.decision) {
      //   status = ELIGIBLE_STATUS.ELIGIBLE;
      //   //to do: handle eligible here
      // } else {
      //   status = ELIGIBLE_STATUS.NOT_ELIGIBLE;
      //   //to do: handle not eligible here
      // }
      return eligibleRes?.data;
    }

    throw Error(
      `Call check eligible requestId ${requestId} error ${eligibleRes?.message}`
    );
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkEligibleSme] error ${error}`);
    //to do error
    throw error;
  }
};

/**
 * @param {*} contractNumber: so hop dong
 * @param {*} custId: ma khach hang
 * @param {*} persons: danh sách cá nhân check
 * @param {*} enterprises: thông các tin doanh nghiệp
 * @return ket qua check s37
 */

const checkS37Sme = async ({
  contractNumber,
  custId,
  persons,
  partnerCode,
  enterprises,
  stepCheck,
}) => {
  try {
    const payload = {
      contractNumber,
      custId,
      persons,
      enterprises,
      stepCheck,
      partnerCode,
    };
    //call eligible
    await loggingRepo.saveWorkflow(
      MisaStep.CHECK_CIC,
      "START_CHECKING",
      contractNumber,
      "system"
    );
    const cicRes = await s37Api(payload);
    let status;
    if (cicRes?.code === 0) {
      await loggingRepo.saveWorkflow(
        MisaStep.CHECK_CIC,
        status,
        contractNumber,
        "system"
      );
      return cicRes?.data;
    }

    throw new Error(
      `Call checkS37Sme contractNumber ${contractNumber} error ${cicRes?.message}`
    );
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkS37Sme] error ${error}`);
    //to do error
    return null;
  }
};

/**
 * @param {*} contractNumber: so hop dong
 * @param {*} custId: ma khach hang
 * @param {*} contractType: looại hợp đồng hạn mức hay kunn , value [LIMIT, KUNN]
 * @param {*} persons: danh sách cá nhân check
 * @param {*} enterprises: danh sách thông tin doanh nghiệp
 * @return ket qua check r11a/s10
 */

const checkCicDetailsSme = async ({
  contractNumber,
  custId,
  persons,
  enterprises,
  contractType,
  taxCode,
  stepCheck,
  partnerCode,
}) => {
  try {
    const payload = {
      contractNumber,
      custId,
      persons,
      enterprises,
      contractType,
      taxCode,
      stepCheck,
      partnerCode
    };
    //call api
    const cicRes = await cicDetailsApi(payload);
    if (cicRes?.code === 0) {
      if (contractType === CIC_CONTRACT_TYPE.KUNN) {
        return cicRes.data;
      }
      if (
        [CIC_DETAILS_STATUS.RECEIVED, CIC_DETAILS_STATUS.PROCESSING].includes(
          cicRes?.data?.status
        )
      ) {
        //to do: handle eligible here
        //wait callback from anti_fraud
        await loanContractRepo.updateContractStatus(
          STATUS.WAITING_CIC_RESULT,
          contractNumber
        );
        await loggingRepo.saveWorkflow(
          MisaStep.CHECK_CIC_DETAIL,
          STATUS.WAITING_CIC_RESULT,
          contractNumber,
          "system"
        );
        actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_DETAIL_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL_SME.ACTION.WAIT_CIC, contractNumber);
        return false;
      } else {
        //to do: lay ket qua check
        const { decision } = cicRes.data || {};
        //decision in list value [A,B,C,D] -> group kh
        //logic receive result
        if (!decision) {
          actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_DETAIL_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL_SME.ACTION.ERROR, contractNumber);
          console.log(
            `${contractNumber} | checkCicDetailsSme error: cannot get decision from response`
          );
          return false;
        }
        let loanStatus = ["A", "B", "C"].includes(decision)
          ? STATUS.APPROVED
          : STATUS.BAD_DEBT;
        if (partnerCode === PARTNER_CODE.BIZZ) {
          loanStatus = ["A", "B"].includes(decision)
            ? STATUS.APPROVED
            : STATUS.REFUSED;
        }
        await loanContractRepo.updateContractStatus(loanStatus, contractNumber);
        await loggingRepo.saveWorkflow(
          TASK_FLOW.CHECK_CIC_DETAIL_SME,
          loanStatus,
          contractNumber,
          "system"
        );
        const loan = await sqlHelper.findOne({
          table: "loan_contract",
          whereCondition: {
            contract_number: contractNumber,
          },
        });
        if (loan?.id) {
          if (
            loan.partner_code === PARTNER_CODE.MISA &&
            loan?.contract_type === CONTRACT_TYPE.CREDIT_LINE
          ) {
            await smeMisaService.callbackCicDetailToMisa(contractNumber);
          }
          if (loan.partner_code === PARTNER_CODE.BIZZ
            && loan?.contract_type === CONTRACT_TYPE.CREDIT_LINE) {
            if (loanStatus === STATUS.APPROVED) {
              actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_DETAIL_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL_SME.ACTION.ELIGIBLE, contractNumber);
              return true;
            } else {
              await loanContractRepo.updateLoanStatusV2({
                status: decision,
                contractNumber,
                rejectionReason: `ranking: ${decision}`,
                cancelledBy: 'ANTI_FRAUD',
                cancelledAt: new Date()
              })
              if (partnerCode === PARTNER_CODE.BIZZ) {
                await Promise.all([
                  callbackPartner(
                    contractNumber,
                    partnerCode ?? loan.partner_code,
                    CALLBACK_STAUS.REJECTED
                  ),
                  crmService.rejectContract(global.config, contractNumber)
                ])
              }
              actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_DETAIL_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL_SME.ACTION.NOT_ELIGIBLE, contractNumber);
              return false;
            }
          }
        }
      }
    }
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_DETAIL_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL_SME.ACTION.ERROR, contractNumber);
    console.log(
      `Call checkCicDetailsSme custId ${custId} error ${cicRes?.message}`
    );
    return false;
  } catch (error) {
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_DETAIL_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL_SME.ACTION.ERROR, contractNumber);
    console.log(`[ANTI_FRAUD][checkCicDetailsSme] error ${error}`);
    return false
  }
};
/**
 * xu ly callback ket qua tu anti_fraud check cic details
 * @param {*} contractNumber
 * @param {*} requestId: request call
 * @param {*} status: trang thai -> FINISH
 * @param {*} decision: decision in list value [A,B,C,D] -> group kh
 * @return ...
 */
const handleCallbackCicDetailsSme = async ({ requestId, contractNumber, contractType, status, decision, cicData }) => {
  try {
    //todo: handle here
    let step, loan;

    // set step value
    loan = await sqlHelper.findOne({
      table: "loan_contract",
      whereCondition: {
        contract_number: contractNumber,
      },
    });

    switch (true) {
      case contractType == CIC_CONTRACT_TYPE.KUNN:
        step = CIC_STEP_CHECK.KUNN;
        break;
      case loanContractService.isRefinance(loan) && loan.partner_code === PARTNER_CODE.MISA:
        step = CIC_STEP_CHECK.TC2_DETAIL; // for MISA TC2
        break;
      default:
        step = CIC_STEP_CHECK.AF2_DETAIL; // for AF2
    }

    // save response
    await sqlHelper.patchUpdate({
      table: "loan_cic_log",
      columns: loanCicLogRepo.columns,
      values: sqlHelper.generateValues(
        {
          response_payload: {
            requestId,
            contractNumber,
            status,
            decision,
            cicData,
          },
        },
        loanCicLogRepo.columns
      ),
      conditions: {
        contract_number: contractNumber,
        step,
      },
    });

    //logic receive result
    //decision in list value [A,B,C,D] -> group kh
    //logic receive result
    if (!["A", "B", "C", "D"].includes(decision)) {
      throw new Error(`${contractNumber} | decision ${decision} invalid`);
    }
    let loanStatus = ["A", "B", "C"].includes(decision) ? STATUS.APPROVED : STATUS.REFUSED;
    if (contractType !== CIC_CONTRACT_TYPE.KUNN) {
      if (loan?.id) {
        if (loan.status === STATUS.CANCELLED) {
          return;
        }
        if (loan.partner_code === PARTNER_CODE.BIZZ) {
          loanStatus = ["A", "B"].includes(decision) ? STATUS.APPROVED : STATUS.REFUSED;
        }
        if (loan.partner_code === PARTNER_CODE.BZHM) {
          loanStatus = ["A", "B"].includes(decision) ? STATUS.APPROVED : STATUS.REFUSED;
        }
        // update loan status
        await loanContractRepo.updateContractStatus(loanStatus, contractNumber);
        await loggingRepo.saveWorkflow(MisaStep.CHECK_CIC_DETAIL, loanStatus, contractNumber, "system");
        if (loan.partner_code === PARTNER_CODE.MISA && loan?.contract_type === CONTRACT_TYPE.CREDIT_LINE) {
          if (loanContractService.isRefinance(loan)) {
            // for MISA TC2
            if (loanStatus != STATUS.APPROVED) {
              // not Pass (NOT_ELIGIBLE)
              await loanContractRepo.updateContractLockStatus(LOCK_STATUS.ACTIVE, loan.old_contract_number); // unlock old contract
            }
            await smeMisaService.callbackRefinanceCicDetailToMisa(contractNumber);
          } else {
            // for MISA AF2
            smeMisaService.callbackCicDetailToMisa(contractNumber);
          }
        }
        
        if (loan.partner_code === PARTNER_CODE.BIZZ && loan?.contract_type === CONTRACT_TYPE.CREDIT_LINE) {
          if (loanStatus === STATUS.APPROVED) {
            goNextStep(contractNumber);
          } else {
            await loanContractRepo.updateLoanStatusV2({
              status: loanStatus,
              contractNumber,
              rejectionReason: `ranking: ${decision}`,
              cancelledBy: "ANTI_FRAUD",
              cancelledAt: new Date(),
            });
            if (loan.partner_code === PARTNER_CODE.BIZZ) {
              await Promise.all([callbackPartner(contractNumber, loan.partner_code, CALLBACK_STAUS.REJECTED), crmService.rejectContract(global.config, contractNumber)]);
            }
          }
        }

        if (loan.partner_code === PARTNER_CODE.BZHM && loan?.contract_type === CONTRACT_TYPE.CREDIT_LINE) {
          if (loanStatus === STATUS.APPROVED) {
            goNextStep(contractNumber);
          } else {
            await loanContractRepo.updateLoanStatusV2({
              status: loanStatus,
              contractNumber,
              rejectionReason: `ranking: ${decision}`,
              cancelledBy: "ANTI_FRAUD",
              cancelledAt: new Date(),
            });
            if (loan.partner_code === PARTNER_CODE.BZHM) {
              await Promise.all([callbackPartner(contractNumber, loan.partner_code, CALLBACK_STAUS.REJECTED), crmService.rejectContract(global.config, contractNumber)]);
            }
          }
        }
      }
      return;
    }

    if (contractType === CIC_CONTRACT_TYPE.KUNN) {
      const kunnData = await kunnRepo.getKunnData(contractNumber);
      // Check whether partner_code is either BIZZ or BZHM
      if (
        kunnData &&
        [PARTNER_CODE.BIZZ, PARTNER_CODE.BZHM].includes(kunnData.partner_code) &&
        kunnData.status === KUNN_STATUS.WAITING_CIC_RESULT
      ) {
        const body = {
          requestId,
          contractNumber,
          contractType,
          status,
          decision,
          cicData,
        };
        await processCallbackWaitingCicKunn({ kunnId: kunnData.kunn_id, decision, body });
        return;
      }
    }

    //handle kunn
    const kunn = await sqlHelper.findOne({
      table: "kunn",
      whereCondition: {
        kunn_id: contractNumber,
      },
    });
    if (!kunn?.kunn_id) {
      throw new Error(`error | kunn ${contractNumber} not found`);
    }
    if (kunn.status === KUNN_STATUS.CANCELLED) {
      return;
    }
    const loanOfKunn = await loanContractRepo.getLoanContract(kunn?.contract_number);
    const { availableAmount, lastestLoanAmount } = await loanContractService.calculateAvailableAmount(loanOfKunn);
    smeMisaService.handleCallbackCicKunn({
      requestId,
      kunnId: contractNumber,
      contractType,
      status,
      decision,
      cicData,
      callbackAmount: { availableAmount, lastestLoanAmount }
    });
    genMisaKunnTTRV(contractNumber, { availableAmount, lastestLoanAmount });
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][handleCallbackCicDetailsSme] requestId: ${requestId} contractNumber: ${contractNumber},
       status: ${status}, decision: ${decision} error ${error}`
    );
    //to do error
    throw error;
  }
};

const checkEligibleSme = async ({
  requestId,
  customerName,
  idNumber,
  otherIdNumber,
  issueDate,
  issuePlace,
  dateOfBirth,
  gender,
  phoneNumber,
  productType,
  registrationNumber,
  taxCode,
  companyName,
  registrationDate,
  productCode,
  caseCreationTime,
  partnerCode,
  channel,
  contractNumber,
  companyType,
}) => {
  try {
    const payload = {
      requestId,
      customerName,
      idNumber,
      otherIdNumber,
      issueDate,
      issuePlace,
      dateOfBirth,
      gender,
      phoneNumber,
      productType,
      registrationNumber,
      taxCode,
      companyName,
      registrationDate,
      productCode,
      caseCreationTime,
      partnerCode,
      channel,
      contractNumber,
      companyType,
    };
    //call eligible
    const eligibleRes = await eligibleApi(payload);
    // let status;
    if (eligibleRes?.code === 0) {
      // if (ELIGIBLE_STATUS.ELIGIBLE === eligibleRes?.data?.decision) {
      //   status = ELIGIBLE_STATUS.ELIGIBLE;
      //   //to do: handle eligible here
      // } else {
      //   status = ELIGIBLE_STATUS.NOT_ELIGIBLE;
      //   //to do: handle not eligible here
      // }
      return eligibleRes?.data;
    }

    throw Error(
      `Call check eligible requestId ${requestId} error ${eligibleRes?.message}`
    );
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkEligibleSme] error ${error}`);
    //to do error
    throw error;
  }
};

const checkEligibleApiIndividual = async (contractNumber) => {
  try {
    const contractData = await loanContractRepo.getLoanContract(contractNumber);
    if (!contractData) {
      common.log(`get data for check dedup error`, contractNumber);
      return false;
    }

    let requestId = contractData.request_id;

    const [frontIdDoc, backIdDoc, selfieDoc] = await Promise.all([
      documentRepo.getDocByContractNumberAndType(contractNumber, "SPIDLR"),
      documentRepo.getDocByContractNumberAndType(contractNumber, "SPIDLR"),
      documentRepo.getDocByContractNumberAndType(contractNumber, "PIC"),
    ]);

    const payload = {
      request_id: requestId,
      partner_code: contractData.partner_code,
      customer_name: contractData.cust_full_name,
      phone_number: contractData.phone_number1,
      date_of_birth: contractData.birth_date
        ? moment(contractData.birth_date).format("yyyy-MM-DD")
        : null,
      contract_number: contractNumber,
      id_number: contractData.id_number,
      issue_date: contractData.id_issue_dt
        ? moment(contractData.id_issue_dt).format("yyyy-MM-DD")
        : null,
      issue_place: contractData.id_issue_place,
      tem_province: "",
      gender: contractData.gender == "M" ? "M" : "F",
      product_type: contractData.contract_type,
      loan_amount: contractData.request_amt,
      loan_tenor: contractData.request_tenor,
      channel: contractData.channel,
      product_code: contractData.product_code,
      email: contractData.email,
      employment_type: contractData.empl_type,
      sale_channel: 'FINV',
      dsa_agent_code: null,
      case_creation_time: null,
      is_insert: true,
      scan_data: null,
      s3_front_id_card_url: frontIdDoc?.file_key,
      s3_back_id_card_url: backIdDoc?.file_key,
      s3_selfie_image_url: selfieDoc?.file_key,
      reference_persons: [
        {
          customerName: contractData?.reference_name_1,
          idNumber: contractData?.reference_id_number_1,
        },
        {
          customerName: contractData?.reference_name_2,
          idNumber: contractData?.reference_id_number_2,
        },
      ],
    };
    //call eligible
    const eligibleRes = await eligibleApiCash(payload);
    if (ELIGIBLE_STATUS.ELIGIBLE === eligibleRes?.code) {
      await loanContractRepo.updateContractStatus(
        STATUS.ELIGIBLE,
        contractNumber
      );
      return true;
    } else {
      await loanContractRepo.updateContractStatus(
        STATUS.NOT_ELIGIBLE,
        contractNumber
      );
      crmService.rejectContract(global.config, contractNumber);
      if (contractData.partner_code === PARTNER_CODE.FINV) {
        await callbackFinv(contractNumber)
      }

      //todo: handle callback partner
      // await callbackReject(global.poolWrite, global.config, contractNumber);
      // await loanContractRepo.updateLoanContract({
      //   contract_number: contractNumber,
      //   status: STATUS.NOT_ELIGIBLE,
      //   updated_date: new Date(),
      //   current_task: TASK_FLOW.END_TASK
      // });
    }
    return false;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkEligibleSme] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkFullLoan = async (contractNumber) => {
  try {
    const contractData = await loanContractRepo.getLoanContract(contractNumber);
    if (!contractData) {
      common.log(`get data for check dedup error`, contractNumber);
      return false;
    }
 
    let requestId = contractData.request_id;

    let req = { config: global.config };
    const masterData = await Promise.all([
      getPlace(req, contractData.province_cur, "provinces"),
      getPlace(req, contractData.district_cur, "districts"),
      getPlace(req, contractData.ward_cur, "wards"),
      getPlace(req, contractData.province_per, "provinces"),
      getPlace(req, contractData.district_per, "districts"),
      getPlace(req, contractData.ward_per, "wards"),
    ]);

    const [frontIdDoc, backIdDoc, selfieDoc] = await Promise.all([
      documentRepo.getDocByContractNumberAndType(this.contractNumber, "SPIDLR"),
      documentRepo.getDocByContractNumberAndType(this.contractNumber, "SPIDLR"),
      documentRepo.getDocByContractNumberAndType(this.contractNumber, "PIC"),
    ]);
    const caseCreationTime = new Date();
    const contractFrom = caseCreationTime.getFullYear();
    caseCreationTime.setMonth(
      caseCreationTime.getMonth() + Number(contractData.request_tenor || 0)
    );
    const contractTo = caseCreationTime.getFullYear();
    const payload = {
      requestId,
      partnerCode: contractData.partner_code,
      channel: contractData.channel,
      contractNumber,
      contractNumberParent: "",
      slAdvanceContractType: "HM",
      custId: contractData?.cust_id,
      losType: LOS_TYPE,
      productType: contractData?.contract_type,
      riskGroup: null,
      productCode: contractData?.product_code,
      productId: contractData?.product_id,
      insuranceCode: null,
      customerName: contractData?.cust_full_name,
      gender: contractData.gender,
      dateOfBirth: contractData.birth_date
        ? moment(contractData.birth_date).format("yyyy-MM-DD")
        : null,
      idNumber: contractData.id_number,
      issueDate: contractData.id_issue_dt
        ? moment(contractData.id_issue_dt).format("yyyy-MM-DD")
        : null,
      issuePlace: contractData.id_issue_place,
      otherIdNumber: contractData.other_id_number,
      otherIdDateOfBirth: null,
      otherIdIssueDate: null,
      otherIdIssuePlace: null,
      phoneNumber: contractData.phone_number1,
      phoneTelco: contractData.phone_number1,
      email: contractData.email,
      disbursementMethod: contractData.disbursement_method,
      accountNumber: contractData.bank_account,
      bankCode: contractData.bank_code,
      bankName: contractData.bank_name,
      bankBranchCode: contractData.bank_branch,
      bankBranchName: null,
      beneficiaryName: contractData.bank_account_owner,

      temCountry: null,
      temProvince: masterData[0],
      temProvinceCode: contractData.province_cur,
      temDistrict: masterData[1],
      temDistrictCode: contractData.district_cur,
      temWard: masterData[2],
      temWardCode: contractData.ward_cur,
      temDetailAddress: contractData.address_cur,

      permanentCountry: null,
      permanentProvince: masterData[3],
      permanentProvinceCode: contractData.province_per,
      permanentDistrict: masterData[4],
      permanentDistrictCode: contractData.district_per,
      permanentWard: masterData[5],
      permanentWardCode: contractData.ward_per,
      permanentDetailAddress: contractData.address_per,

      nationality: "VN",
      reference1Type: contractData.reference_type_1,
      reference2Type: contractData.reference_type_2,
      relativeReferenceName1: contractData.reference_name_1,
      relativeReferenceName2: contractData.reference_name_2,
      relativeReferencePhone1: contractData.reference_phone_1,
      relativeReferencePhone2: contractData.reference_phone_2,

      otherContact: null,
      contactDetail: null,
      occupation: null,
      monthlyIncome: Number(contractData.monthly_income || 0),
      otherIncome: Number(contractData.other_income || 0),
      monthlyExpenses: 0,
      companyName: contractData.workplace_name,
      companyAddress: contractData.workplace_address,
      companyCountry: "VN",
      companyProvince: contractData.workplace_province,
      companyDistrict: contractData.workplace_district,
      companyWard: contractData.workplace_ward,
      contractFrom: contractFrom,
      contractTo: contractTo,
      jobType: contractData.job_type,
      employmentType: contractData.empl_type,
      employmentContractType: contractData.empl_ctrct_type,
      marriedStatus: contractData.married_status,
      houseType: contractData.house_type,
      otherHouseType: null,
      numOfDependents: contractData.num_of_dependants || 0,
      yearsOfStay: null,
      incomeProof: contractData.income_proof_amount || 0,
      salaryFrequency: contractData.salary_frequency,
      salaryPaymentDate: contractData.salary_payment_day,
      taxId: contractData.tax_id,
      loanPurpose: contractData.loan_purpose,
      loanAmount: contractData.request_amt,
      loanTenor: contractData.request_tenor,

      s3IdCardUrl: null,
      s3FrontIdCardUrl: frontIdDoc?.file_key,
      s3BackIdCardUrl: backIdDoc?.file_key,
      s3SelfieImageUrl: selfieDoc?.file_key,
      s3VerifyIncomeImageUrls: [],

      phoneNumber2: null,
      phoneNumber3: null,
      education: null,
      custType: null,
      sameCities: null,
      countryPer: null,
      villageCur: null,
      villagePer: null,
      requestIntRate: Number(contractData.request_int_rate || 0),
      requestInstalAmt: null,
      staffNumber: contractData.number_of_staffs,
      insuranceType: null,
      companyCode: null,
      companyTaxId: contractData.sme_tax_id,
      deviceId: null,
      deviceModel: null,
      deviceBrand: null,
      deviceOs: null,
      latitude: null,
      longitude: null,
      scanData: null,
      referencePersons: [
        {
          customerName: contractData.reference_name_1,
          idNumber: contractData.reference_id_number_1,
        },
        {
          customerName: contractData.reference_name_2,
          idNumber: contractData.reference_id_number_2,
        },
      ],
      step: TASK_FLOW.CHECK_FULL_LOAN_INDIVIDUAL
    };
    const res = await checkFullLoanApi(payload);

    const {
      data
    } = res || {};
    if (DECISION_V02_RES_DECISION.APPROVE === data?.decision) {
      // approve
      await loanContractRepo.updateContractStatus(
        STATUS.ELIGIBLE,
        contractNumber
      );
      return true;
    }
    if (DECISION_V02_RES_DECISION.WAIT_CIC === data?.decision) {
      // wait cic
      await loanContractRepo.updateContractStatus(
        STATUS.WAITING_CIC_RESULT,
        contractNumber
      );
      return false;
    } else {
      // reject
      await loanContractRepo.updateContractStatus(
        STATUS.NOT_ELIGIBLE,
        contractNumber
      );
      crmService.rejectContract(global.config, contractNumber);
      callbackPartner(contractNumber, PARTNER_CODE.FINV, CALLBACK_STAUS.NOT_ELIGIBLE);
      return false;
    }
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkFullLoan] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkConsent = async ({ phoneNumber, phoneTelco }) => {
  try {
    const res = await checkConsentApi({ phoneNumber, phoneTelco });
    if (!res) {
      throwServerError("cannot get res consent");
    }
    return {
      code: 0,
      message: "success",
      data: res,
    };
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkConsent] phoneNumber :${phoneNumber}, error ${error}`
    );
    //to do error
    throw error;
  }
};

const handleCallbackCic = async ({
  requestId,
  contractNumber,
  decisionCode,
  rjSubCode,
  reasonCode,
  groupReason,
  contractRenewDate,
}) => {
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (loan?.status != STATUS.WAITING_CIC_RESULT) {
      throwBadReqError(
        "body.contractNumber",
        "ContractNumber invalid or status not WAITING_CIC_RESULT",
        MISA_ERROR_CODE.E400
      );
    }
    switch (decisionCode) {
      case DECISION_V02_RES_DECISION.APPROVE: {
        await loanContractRepo.updateContractStatus(
          STATUS.APPROVED,
          contractNumber
        );

        goNextStep(contractNumber);
        break;
      }
      case DECISION_V02_RES_DECISION.REJECT: {
        await loanContractRepo.updateLoanContract({
          contract_number: contractNumber,
          status: STATUS.REFUSED,
          rejection_reason: `${rjSubCode}|${reasonCode}|${groupReason}|${contractRenewDate}`,
          updated_date: new Date()
        });
        crmService.rejectContract(global.config, contractNumber);
        callbackPartner(contractNumber, loan.partner_code, CALLBACK_STAUS.REJECTED);
        // await callbackReject(global.poolWrite, global.config, contractNumber);
        break;
      }
      case DECISION_V02_RES_DECISION.CANCEL: {
        await loanContractRepo.updateLoanContract({
          contract_number: contractNumber,
          status: STATUS.CANCELLED,
          rejection_reason: `${rjSubCode}|${reasonCode}|${groupReason}|${contractRenewDate}`,
          updated_date: new Date()
        });
        crmService.removeContract(global.config, contractNumber);
        callbackPartner(contractNumber, loan.partner_code, CALLBACK_STAUS.CANCELLED);
        // await callbackCancel(global.poolWrite, global.config, contractNumber);
        break;
      }
    }
    return loan;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][handleCallbackCic] contractNumber :${contractNumber}, decisionCode ${decisionCode}, error ${error?.message}`
    );
    throw error;
  }
};

const ocrIdCard = async (contractData, step) => {
  const docTypes = Object.values(DOC_TYPE.ID_CARD);
  const documents = await loanContractDocumentRepo.findByContractAndTypes(contractData.contract_number, docTypes);
  const { frontIdCardFileKey, backIdCardFileKey } = documents.reduce((o, el) => {
    if (el.doc_type === DOC_TYPE.ID_CARD.FRONT) {
      o.frontIdCardFileKey = el.file_key;
    } else if (el.doc_type === DOC_TYPE.ID_CARD.BACK) {
      o.backIdCardFileKey = el.file_key;
    }
    return o;
  }, {
    frontIdCardFileKey: '',
    backIdCardFileKey: '',
  });
  const result = await ocrIdCardApi({
    contractNumber: contractData.contract_number,
    partnerCode: contractData.partner_code,
    s3FrontIdCardUrl: frontIdCardFileKey,
    s3BackIdCardUrl: backIdCardFileKey,
    requestId: contractData.request_id,
    step,
  });
  if (result) {
    await nfcDataRepo.insert({
      requestId: contractData.request_id,
      contractNumber: contractData.contract_number,
      nfcRawData: contractData.nfc_value,
      frontIdCardUrl: frontIdCardFileKey,
      backIdCardUrl: backIdCardFileKey,
      ocrData: utils.snakeToCamel(result),
    })
  } else {
    await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE, contractData.contract_number);
    await Promise.all([
      callbackPartner(contractData.contract_number, contractData.partner_code, CALLBACK_STAUS.NOT_ELIGIBLE),
      crmService.rejectContract(global.config, contractData.contract_number),
    ])
  }
  return !!result;
};

const checkNfc = async (
  contractNumber,
  isCheckC06 = false,
  step = CHECK_NFC_STEP.AF2,
  isWorkflow = true
) => {
  let status = STATUS.ELIGIBLE;
  try {
    const loan = snakeToCamel(
      await loanContractRepo.getLoanContract(contractNumber)
    );
    const nfcCacheData = snakeToCamel(
      await nfcDataRepo.findByRequestId(loan.requestId)
    );
    if (!nfcCacheData) {
      throw Error(`cannot get nfc data cache`);
    }

    const selfieDoc =
      await loanContractDocumentRepo.getDocByContractNumberAndType(
        contractNumber,
        DOC_TYPE.SELFIE
      );

    const nfcData = JSON.parse(nfcCacheData.rawData);
    nfcData.aa_result = nfcData.aAResult;
    nfcData.eacca_result = nfcData.eACCAResult;
    delete nfcData.aAResult;
    delete nfcData.eACCAResult;

    const ocrData = {
      id_number: nfcCacheData.ocrIdNumber,
      name: nfcCacheData.ocrName,
      dob: nfcCacheData.ocrDob || "",
      gender: nfcCacheData.ocrGender || "",
      address: nfcCacheData.ocrAddress || "",
      issue_date: nfcCacheData.ocrIssueDate || "",
      issue_place: nfcCacheData.ocrIssuePlace || "",
      native_place: nfcCacheData.ocrNativePlace || "",
      mrz: nfcCacheData.ocrMrz || "",
      expire_date: nfcCacheData.ocrExpireDate || "",
    };

    const rs = await checkNfcApi({
      request_id: loan.requestId,
      contract_number: loan.contractNumber,
      cust_id: loan.custId || "",
      partner_code: loan.partnerCode,
      id_number: loan.idNumber,
      s3_selfie_url: selfieDoc?.file_key || "",
      nfc_data: nfcData,
      ocr_data: ocrData,
      is_check_c06: isCheckC06,
      step,
      s3_front_id_card_url: nfcCacheData.frontIdCardUrl,
      s3_back_id_card_url: nfcCacheData.backIdCardUrl,
    });
    if (!rs) throw Error("check nfc fail");
    if (isWorkflow) {
      const { decision, reason } = rs;
      if (decision != ELIGIBLE_STATUS.ELIGIBLE) {
        status = STATUS.NOT_ELIGIBLE;
        await loanContractRepo.updateLoanContract({
          contract_number: loan.contractNumber,
          status: STATUS.NOT_ELIGIBLE,
          rejection_reason: `nfc_${reason}`,
          updated_date: new Date(),
        });
        //todo : add callback
        if (loan.partnerCode === PARTNER_CODE.FINV) {
          await callbackFinv(contractNumber)
        }
        return false;
      }
      else if (isCheckC06) {
        status = STATUS.SUBMIT_AF3;
        await loanContractRepo.updateLoanContract({
          contract_number: loan.contractNumber,
          status: STATUS.SUBMIT_AF3,
          updated_date: new Date(),
        });
      }
      return true;
    }
    return rs;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkNfc error ${contractNumber} error ${error}`);
    if (isWorkflow) {
      return false;
    } else {
      throw error;
    }
  } finally {
    await loggingRepo.saveWorkflow(
      MisaStep.CHECK_NFC,
      status,
      contractNumber,
      "system"
    );
  }
};

const checkNfcFinv = async (
  contractNumber,
  isCheckC06 = false,
  step = CHECK_NFC_STEP.AF2,
  isWorkflow = true
) => {
  let status = STATUS.ELIGIBLE;
  try {
    const loan = snakeToCamel(
      await loanContractRepo.getLoanContract(contractNumber)
    );
    const nfcCacheData = snakeToCamel(
      await nfcDataRepo.findByRequestId(loan.requestId)
    );
    if (!nfcCacheData) {
      throw Error(`cannot get nfc data cache`);
    }

    const selfieDoc =
      await loanContractDocumentRepo.getDocByContractNumberAndType(
        contractNumber,
        DOC_TYPE.SELFIE
      );

    const nfcData = JSON.parse(nfcCacheData.rawData);
    nfcData.aa_result = nfcData.aAResult;
    nfcData.eacca_result = nfcData.eACCAResult;
    delete nfcData.aAResult;
    delete nfcData.eACCAResult;

    const ocrData = {
      id_number: nfcCacheData.ocrIdNumber,
      name: nfcCacheData.ocrName,
      dob: nfcCacheData.ocrDob || "",
      gender: nfcCacheData.ocrGender || "",
      address: nfcCacheData.ocrAddress || "",
      issue_date: nfcCacheData.ocrIssueDate || "",
      issue_place: nfcCacheData.ocrIssuePlace || "",
      native_place: nfcCacheData.ocrNativePlace || "",
      mrz: nfcCacheData.ocrMrz || "",
      expire_date: nfcCacheData.ocrExpireDate || "",
    };

    const rs = await checkNfcApi({
      request_id: loan.requestId,
      contract_number: loan.contractNumber,
      cust_id: loan.custId || "",
      partner_code: loan.partnerCode,
      id_number: loan.idNumber,
      s3_selfie_url: selfieDoc?.file_key || "",
      nfc_data: nfcData,
      ocr_data: ocrData,
      is_check_c06: isCheckC06,
      step,
      s3_front_id_card_url: nfcCacheData.frontIdCardUrl,
      s3_back_id_card_url: nfcCacheData.backIdCardUrl,
    });
    if (!rs) throw Error("check nfc fail");
    if (isWorkflow) {
      const { decision, reason } = rs;
      if (decision != ELIGIBLE_STATUS.ELIGIBLE) {
        status = STATUS.NOT_ELIGIBLE;
        await loanContractRepo.updateLoanContract({
          contract_number: loan.contractNumber,
          status: STATUS.NOT_ELIGIBLE,
          rejection_reason: `nfc_${reason}`,
          updated_date: new Date(),
        });
        crmService.rejectContract(global.config, contractNumber);
        //todo : add callback
        callbackPartner(contractNumber, loan.partnerCode, CALLBACK_STAUS.NOT_ELIGIBLE)
        return false;
      }
      return true;
    }
    return rs;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkNfc error ${contractNumber} error ${error}`);
    if (isWorkflow) {
      return false;
    } else {
      throw error;
    }
  } finally {
    await loggingRepo.saveWorkflow(
      MisaStep.CHECK_NFC,
      status,
      contractNumber,
      "system"
    );
  }
};

const checkEKYC = async (contractData, step) => {
  try {
    const loan = snakeToCamel(contractData);
    const nfcCacheData = snakeToCamel(
      await nfcDataRepo.findByRequestId(loan.requestId)
    );
    if (!nfcCacheData) {
      throw Error(`cannot get nfc data cache`);
    }
    const documents = await loanContractDocumentRepo.findByContractAndTypes(
      contractData.contract_number,
      [DOC_TYPE.SELFIE, DOC_TYPE.NFC]
    );
    const { selfiePath, nfcPath } = documents.reduce((o, el) => {
      if (el.doc_type === DOC_TYPE.SELFIE) {
        o.selfiePath = el.file_key;
      } else if (el.doc_type === DOC_TYPE.NFC) {
        o.nfcPath = el.file_key;
      }
      return o;
    }, {
      selfiePath: '',
      nfcPath: '',
    });
    const rs = await checkEkycApi({
      requestId: loan.requestId,
      contractNumber: loan.contractNumber,
      partnerCode: loan.partnerCode,
      selfiePath,
      idCardFrontPath: nfcCacheData.frontIdCardUrl,
      idCardBackPath: nfcCacheData.backIdCardUrl,
      nfcPath,
      step,
      idNumber: loan.idNumber,
      custFullName: loan.custFullName
    });
    if (!rs || rs.code !== "PASS_EKYC") {
      await loanContractRepo.updateLoanContract({
        contract_number: loan.contractNumber,
        status: STATUS.NOT_ELIGIBLE,
        updated_date: new Date(),
      });
      // TODO: add callback
      await Promise.all([
        callbackPartner(loan.contractNumber, loan.partnerCode, CALLBACK_STAUS.NOT_ELIGIBLE),
        crmService.rejectContract(global.config, loan.contractNumber),
      ])
      return false;
    }
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkEKYC error ${contractData.contract_number} error ${error}`);
    return false;
  }
};


/**
 * @param {*} contractNumber: so hop dong
 * @param {*} custId: ma khach hang
 * @param {*} persons: danh sách cá nhân check
 * @param {*} enterprises: thông các tin doanh nghiệp
 * @return ket qua check s37
 */

const checkCicB11t = async ({
  contractNumber,
  custId,
  persons,
  partnerCode,
  stepCheck,
}) => {
  try {
    const payload = {
      contractNumber,
      custId,
      persons,
      stepCheck,
      partnerCode,
    };
    //call eligible
    await loggingRepo.saveWorkflow(
      MisaStep.CHECK_CIC,
      "START_CHECKING",
      contractNumber,
      "system"
    );
    const cicRes = await checkCicB11tApi(payload);
    let status;
    if (cicRes?.code === 0) {
      await loggingRepo.saveWorkflow(
        MisaStep.CHECK_CIC,
        status,
        contractNumber,
        "system"
      );
      return cicRes?.data;
    }

    throw new Error(
      `Call checkCicB11t contractNumber ${contractNumber} error ${cicRes?.message}`
    );
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkCicB11t] error ${error}`);
    //to do error
    return null;
  }
};

/**
 * @param {*} contractNumber: so hop dong
 * @param {*} persons: danh sách cá nhân check
 * @return ket qua check s37
 */

const checkCicB11tInvidual = async ({
  contract_number: contractNumber,
  cust_full_name: fullname,
  id_number: identityCard,
  partner_code: partnerCode,
}) => {
  try {
    const persons = [
      {
        id_number: identityCard,
        other_id_number: "",
        full_name: fullname,
        address: "",
      }
    ]

    const payload = {
      contractNumber,
      persons,
      partnerCode,
    };

    const cicRes = await checkCicB11tInvidualApi(payload);
    if (cicRes?.code === 0) {
      const { decision } = cicRes.data;
      await loanContractRepo.updateContractStatus(
        decision,
        contractNumber
      )
      if(decision !== STATUS.ELIGIBLE){
        await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.NOT_ELIGIBLE)
      }
      return decision === STATUS.ELIGIBLE;
    }


    throw new Error(
      `Call checkCicB11t contractNumber ${contractNumber} error ${cicRes?.message}`
    );
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkCicB11t] error ${error}`);
    return null;
  }
};

async function checkContractStatus(payload, step_name) {
  try {
    const { data } = await checkEligibleStatusApi(payload, step_name);
    if (data?.code === 1) {
      return { success: true, data: data };
    }
    return { success: false, data: data };
  } catch (error) {
    return { success: false, data: { message: error.message } };
  }
}

async function checkRenewDate(request_id, partner_code, tax_code, contract_number, step_name) {
  try {
    const { data } = await getRenewDateApi({
      request_id,
      partner_code,
      tax_code,
      contract_number
    },
      step_name);
    if (data?.code === 0) {
      return { success: true, data: data };
    }
    return { success: false, data: data };
  } catch (error) {
    return { success: false, data: { message: error.message } };
  }
}

async function checkRenewDateAll({request_id, partner_code, tax_code, identity_card, contract_number, step_name}) {
  try {
    const { data } = await getRenewDateApi({
      request_id,
      partner_code,
      tax_code,
      identity_card,
      contract_number
    },
      step_name);
    if (data?.code === 0) {
      return { success: true, data: data };
    }
    return { success: false, data: data };
  } catch (error) {
    return { success: false, data: { message: error.message } };
  }
}

/**
 * Check contract renew date by identity card
 * @param {string} request_id
 * @param {string} partner_code
 * @param {string} identity_card
 * @param {string} contract_number
 * @param {string} [step_name]
 * @returns {Promise<boolean>}
 */
async function checkRenewDateByIdentityCard(request_id, partner_code, identity_card, contract_number, step_name) {
  let res = false;
  try {
    const { data } = await getRenewDateApi({
        request_id,
        partner_code,
        identity_card,
        contract_number
      },
      step_name);
    if (data.code === 0) {
      res = !data.data.is_locking;
    } else {
      res = false;
    }
  } catch (error) {
    res = false;
  }
  if (!res) {
    await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE, contract_number);
    await crmService.rejectContract(global.config, contract_number);
    callbackPartner(contract_number, partner_code, CALLBACK_STAUS.NOT_ELIGIBLE)
    //callback to 3P
  }
  return res;
}

async function checkBlacklist({
  request_id,
  partner_code,
  phone_number,
  contract_number,
  id_number,
  tax_code,
  email
}) {
  try {
    const response = await checkBlacklistApi({
      request_id,
      partner_code,
      tax_code,
      contract_number,
      id_number,
      phone_number,
      email
    });

    const { data } = response || {};
    if (response?.status !== 200) {
      return { success: false, message: data };
    }

    return { success: true, data: data };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

async function checkCicB11Sme(payload) {
  try {
    const { data } = await checkCicB11SmeApi(payload);
    if (data.code !== 0) {
      return { success: false, message: data };
    }
    return { success: true, data: data };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

const checkEligibleSmeV2Gw = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loanContract = await loanContractRepo.findLoan(contractNumber);
    if (!loanContract) {
      console.log(`${contractNumber}| ${new Date()} | checkEligibleSmeV2Gw error | cannot get loan_contract`)
      return false;
    }

    //danh sách người cần check eligible: Đại diện PL DN vay, Chủ DN là cá nhân, Vợ chồng chủ DN, Cổ đông là cá nhân
    let eligiblePersons = [];// danh sách người cần check eligible
    //danh sách doanh nghiệp cần check eligible: Doanh nghiệp vay, Chủ DN là doanh nghiệp, Cổ đông là DN
    let otherEnterprises = [];// danh sách doanh nghiệp cần check eligible;

    if (loanContract.loan_business_owner.subject === 'INDIVIDUAL') {
      if (!isDuplicatePerson({ idNumber: loanContract.loan_business_owner.id_number }, eligiblePersons)) {
        eligiblePersons.push({
          idNumber: loanContract.loan_business_owner.id_number,
          customerName: loanContract.loan_business_owner.full_name,
          otherIdNumber: null,
          issueDate: loanContract.loan_business_owner.issue_date ? moment(loanContract.loan_business_owner.issue_date).format("YYYY-MM-DD") : undefined,
          issuePlace: loanContract.loan_business_owner.issue_place,
          dateOfBirth: loanContract.loan_business_owner.dob ? moment(loanContract.loan_business_owner.dob).format("YYYY-MM-DD") : undefined,
          gender: null,
          phoneNumber: loanContract.loan_business_owner.phone_number
        })
      }
      //đã kết hôn - vợ chồng chủ doanh nghiệp
      if (loanContract.loan_business_owner.married_status === 'M' && 
        (!loanContract.loan_business_owner.partner_id_number || !loanContract.loan_business_owner.partner_full_name)) {
        console.log(`${contractNumber}| ${new Date()} | checkEligibleSmeV2Gw error | married_status is M but partner_id_number or partner_full_name is missing`);
        loggingRepo.saveStepLog(
          contractNumber,
          SERVICE_NAME.ANTI_FRAUD,
          TASK_FLOW.CHECK_ELIGIBLE_SME,
          payload,
          { message: 'married_status is M but partner_id_number or partner_full_name is missing' },
          url
        );
        return false;
      }
      if (loanContract.loan_business_owner.married_status === 'M' &&
        !isDuplicatePerson({ idNumber: loanContract.loan_business_owner.partner_id_number }, eligiblePersons)
      ) {
        eligiblePersons.push({
          idNumber: loanContract.loan_business_owner.partner_id_number,
          customerName: loanContract.loan_business_owner.partner_full_name,
          otherIdNumber: null,
          issueDate: null,
          issuePlace: null,
          dateOfBirth: loanContract.loan_business_owner.partner_dob ? moment(loanContract.loan_business_owner.partner_dob).format("YYYY-MM-DD") : undefined,
          gender: null,
          phoneNumber: loanContract.loan_business_owner.partner_phone_number
        })
      }
    } else if (loanContract.loan_business_owner.subject === 'ORGANIZATION') {
      //không check doanh nghiệp liên quan trừ doanh nghiệp vay: c Hà *********
      if (!isDuplicateEnterprise({ registrationNumber: loanContract.loan_business_owner.tax_id }, otherEnterprises)) {
        otherEnterprises.push({
          customerName: loanContract.loan_business_owner.full_name,
          idNumber: loanContract.loan_business_owner.id_number,
          otherIdNumber: null,
          issueDate: loanContract.loan_business_owner.issue_date ? moment(loanContract.loan_business_owner.issue_date).format("YYYY-MM-DD") : undefined,
          issuePlace: loanContract.loan_business_owner.issue_place,
          dateOfBirth: loanContract.loan_business_owner.dob ? moment(loanContract.loan_business_owner.dob).format("YYYY-MM-DD") : undefined,
          gender: null,
          phoneNumber: loanContract.loan_business_owner.phone_number,
          taxCode: loanContract.loan_business_owner.tax_id,
          registrationNumber: loanContract.loan_business_owner.registration_number ?? loanContract.loan_business_owner.tax_id,
          companyName: loanContract.loan_business_owner.company_name,
          registrationDate: null,
          companyType: null
        });
      }
    }
    //cổ đông
    if (loanContract.loan_customer_shareholders?.length > 0) {
      const shareholders = loanContract.loan_customer_shareholders;
      shareholders.forEach(shareholder => {
        if (shareholder.subject === 'INDIVIDUAL') {
          if (!isDuplicatePerson({ idNumber: shareholder.id_number }, eligiblePersons)) {
            eligiblePersons.push({
              idNumber: shareholder.id_number,
              customerName: shareholder.full_name,
              otherIdNumber: null,
              issueDate: null,
              issuePlace: null,
              dateOfBirth: shareholder.dob ? moment(shareholder.dob).format("YYYY-MM-DD") : undefined,
              gender: null,
              phoneNumber: shareholder.phone_number
            });
          }
        } else if (shareholder.subject === 'ORGANIZATION') {
          if (!isDuplicateEnterprise({ registrationNumber: shareholder.tax_id }, otherEnterprises)) {
            otherEnterprises.push({
              customerName: shareholder.full_name,
              idNumber: shareholder.id_number,
              otherIdNumber: null,
              issueDate: shareholder.issue_date ? moment(shareholder.issue_date).format("YYYY-MM-DD") : undefined,
              issuePlace: shareholder.issue_place,
              dateOfBirth: shareholder.dob ? moment(shareholder.dob).format("YYYY-MM-DD") : undefined,
              gender: null,
              phoneNumber: shareholder.phone_number,
              taxCode: shareholder.tax_id,
              registrationNumber: shareholder.registration_number ?? shareholder.tax_id,
              companyName: shareholder.company_name,
              registrationDate: null,
              companyType: null
            });
          }
        }
      });
    }

    let firstRegistrationDate = loanContract.registration_cert_issue_date ?? loanContract.registration_date;

    const eligiblePayload = {
      requestId: loanContract.request_id,
      legalRepresentative: {
        customerName: loanContract.sme_representation_name,
        idNumber: loanContract.sme_representation_id,
        issueDate: loanContract.sme_representation_issue_date ? moment(loanContract.sme_representation_issue_date).format("YYYY-MM-DD") : undefined,
        issuePlace: loanContract.sme_representation_issue_place,
        dateOfBirth: loanContract.sme_representation_dob ? moment(loanContract.sme_representation_dob).format("YYYY-MM-DD") : undefined,
        phoneNumber: loanContract.sme_representation_phone_number,
      },
      otherPersons: eligiblePersons,
      otherEnterprises: otherEnterprises,
      productType: '',
      registrationNumber: loanContract.registration_number ?? loanContract.sme_tax_id,
      taxCode: loanContract.sme_tax_id,
      companyName: loanContract.sme_name,
      registrationDate: firstRegistrationDate ? moment(firstRegistrationDate).format("YYYY-MM-DD") : undefined,
      productCode: '',
      caseCreationTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      partnerCode: loanContract.partner_code,
      channel: loanContract.channel,
      contractNumber: loanContract.contract_number,
      companyType: loanContract.business_type
    }

    const eligibleResult = await checkEligibleSmeV2(eligiblePayload)
    if (!eligibleResult?.decision) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_ELIGIBLE_SME.STEP_CODE, CASE_STATUS.CHECK_ELIGIBLE_SME.ACTION.ERROR, contractNumber);
      console.log(`${contractNumber}| ${new Date()} | checkEligibleSmeV2Gw error | cannot get response`)
    }
    await loggingRepo.saveWorkflow(TASK_FLOW.CHECK_ELIGIBLE_SME, eligibleResult.decision, contractNumber, 'system');
    if (eligibleResult?.decision && eligibleResult.decision != STATUS.ELIGIBLE) {
      const { decision, reason_code } = eligibleResult || {};
      await loanContractRepo.updateLoanStatusV2({
          status: decision,
          contractNumber,
          rejectionReason: reason_code ?? null,
          cancelledBy: 'ANTI_FRAUD',
          cancelledAt: new Date()
        })
      //callback to 3P
      if (loanContract.partner_code === PARTNER_CODE.BIZZ) {
        //handle callback 3P here
        await Promise.all([
          callbackPartner(contractNumber, loanContract.partner_code, CALLBACK_STAUS.REJECTED),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }
      if (loanContract.partner_code === PARTNER_CODE.BZHM) {
        //handle callback 3P here
        await Promise.all([
          callbackPartner(contractNumber, loanContract.partner_code, CALLBACK_STAUS.REJECTED),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_ELIGIBLE_SME.STEP_CODE, CASE_STATUS.CHECK_ELIGIBLE_SME.ACTION.NOT_ELIGIBLE, contractNumber);
      return false;
    }
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_ELIGIBLE_SME.STEP_CODE, CASE_STATUS.CHECK_ELIGIBLE_SME.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkEligibleSmeV2Gw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkCicDetailSmeGw = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loan = await loanContractRepo.findLoan(contractNumber);
    let persons = loan.loan_customer_representations.map((rep) => ({
      idNumber: rep.id_number,
      otherIdNumber: '',
      fullName: rep.full_name,
      address: '',
      subject: CHECK_CIC_DETAILS_SUBJECT.REPRESENTATION
    }));
    let enterprises = [{
      registrationNumber: loan.loan_customer.registration_number,
      taxCode: loan.loan_customer.tax_id ?? loan.loan_customer.registration_number,
      companyName: loan.loan_customer.company_name,
      address: loan.loan_customer.address_on_license,
      subject: CHECK_CIC_DETAILS_SUBJECT.MAIN_SME
    }];
    if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
      if (!isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
        persons.push({
          idNumber: loan.loan_business_owner.id_number,
          otherIdNumber: '',
          fullName: loan.loan_business_owner.full_name,
          address: '',
          subject: CHECK_CIC_DETAILS_SUBJECT.OWNER
        })
      }
      //đã kết hôn
      // let businessTypeChecked = [BUSINESS_TYPE["01"], BUSINESS_TYPE["04"]];
      // if (businessTypeChecked.includes(loan.business_type) && loan.loan_business_owner.married_status === 'M') {
      if (loan.loan_business_owner.married_status === 'M') {  
        if (!loan.loan_business_owner.partner_id_number ||
          !loan.loan_business_owner.partner_full_name) {
          throw new Error(`checkCicDetail | ${contractNumber} | Thiếu thông tin vợ/ chồng`)
        }
        persons.push({
          idNumber: loan.loan_business_owner.partner_id_number,
          otherIdNumber: '',
          fullName: loan.loan_business_owner.partner_full_name,
          address: '',
          subject: CHECK_CIC_DETAILS_SUBJECT.PARTNER_OF_OWNER
        })
      }
    } else {
      enterprises.push({
        registrationNumber: '',
        taxCode: loan.loan_business_owner.tax_id,
        companyName: loan.loan_business_owner.company_name,
        address: '',
        subject: CHECK_CIC_DETAILS_SUBJECT.OWNER
      });
    }
    if (loan.loan_customer_shareholders?.length > 0) {
      const shareholders = loan.loan_customer_shareholders;
      for (const shareholder of shareholders) {
        if (shareholder.subject === 'INDIVIDUAL') {
          if (!isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
            persons.push({
              idNumber: shareholder.id_number,
              otherIdNumber: '',
              fullName: shareholder.full_name,
              address: '',
              subject: CHECK_CIC_DETAILS_SUBJECT.SHAREHOLDER
            })
          }
        } else {
          if (!isDuplicateEnterprise({ taxCode: shareholder.tax_id }, enterprises)) {
            enterprises.push({
              registrationNumber: '',
              taxCode: shareholder.tax_id,
              companyName: shareholder.company_name,
              address: '',
              subject: CHECK_CIC_DETAILS_SUBJECT.SHAREHOLDER
            });
          }
        }
      }
    }

    const cicResult = await checkCicDetailsSme({
      contractNumber,
      custId: loan.cust_id,
      persons: persons,
      enterprises: enterprises,
      contractType: 'LIMIT',
      taxCode: loan?.sme_tax_id,
      partnerCode: loan?.partner_code,
    })
    return cicResult
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkCicDetailSmeGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const processCheckCicDetailKunn = async ({
  kunnId,
  custId,
  persons,
  enterprises,
  contractType,
  taxCode,
  partnerCode,
  contractNumber
}) => {
  try {
    const payload = {
      contractNumber: kunnId,
      custId,
      persons,
      enterprises,
      contractType,
      taxCode,
      partnerCode
    };
    // call api
    const cicRes = await cicDetailsApi(payload);
    console.log('check cic payload:', payload);
    console.log('check cic response:', cicRes);
    if (cicRes?.code === 0) {
      if ([CIC_DETAILS_STATUS.RECEIVED, CIC_DETAILS_STATUS.PROCESSING].includes(cicRes?.data?.status)) {
        // wait callback from anti_fraud
        await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.WAITING_CIC_RESULT);
        // case_status_log
        await loggingRepo.saveWorkflow(MisaStep.CHECK_CIC_DETAIL, STATUS.WAITING_CIC_RESULT, kunnId, "system");
        actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECK_CIC_DETAIL.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL.ACTION.WAIT_CIC, kunnId);
        return false;
      } else {
        // get result
        const { decision } = cicRes.data || {};
        if (!decision) {
          console.log(`${kunnId} | processCheckCicDetailKunn error: cannot get decision from response`);
          actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECK_CIC_DETAIL.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL.ACTION.ERROR, kunnId);
          return false;
        }
        // TODO: Check status BZHM
        let kunnStatus = ["A", "B", "C"].includes(decision) ? STATUS.APPROVED : STATUS.BAD_DEBT;
        if (partnerCode === PARTNER_CODE.BIZZ) {
          kunnStatus = ["A", "B"].includes(decision) ? STATUS.APPROVED : STATUS.REFUSED;
        }
        await kunnRepo.updateKUStatusV2(kunnId, kunnStatus);
        await loggingRepo.saveWorkflow(TASK_FLOW.CHECK_CIC_DETAIL_SME, kunnStatus, kunnId, "system");
        
        if (kunnStatus === STATUS.APPROVED) {
          actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECK_CIC_DETAIL.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL.ACTION.PASSED_CIC_DETAIL, kunnId);
          return true;
        } else {
          actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECK_CIC_DETAIL.STEP_CODE, CASE_STATUS.CHECK_CIC_DETAIL.ACTION.REFUSED, kunnId);
          await kunnRepo.update(kunnId, {
            status: decision,
            cancelled_reason: `ranking: ${decision}`,
            cancelled_by: "ANTI_FRAUD",
            cancelled_at: new Date()
          });
          
          if (partnerCode === PARTNER_CODE.BIZZ) {
            await Promise.all([
              callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED, null, null, kunnId),
              crmService.rejectContract(global.config, kunnId)
            ]);
          }
          return false;
        }
      }
    }

    console.log(`Call processCheckCicDetailKunn custId ${custId} error ${cicRes?.message}`);
    return false;
  } catch (error) {
    console.log(`[ANTI_FRAUD][processCheckCicDetailKunn] error ${error}`);
    return false;
  }
};

const checkCicDetailKunn = async (kunnId) => {
  const kunnData = await kunnRepo.getKunnData(kunnId);
  const contractNumber = kunnData?.contract_number;
  try {
    const loan = await loanContractRepo.findLoan(contractNumber);
    let persons = loan.loan_customer_representations.map((rep) => ({
      idNumber: rep.id_number,
      otherIdNumber: '',
      fullName: rep.full_name,
      address: '',
      subject: CHECK_CIC_DETAILS_SUBJECT.REPRESENTATION
    }));
    let enterprises = [{
      registrationNumber: loan.loan_customer.registration_number,
      taxCode: loan.loan_customer.tax_id ?? loan.loan_customer.registration_number,
      companyName: loan.loan_customer.company_name,
      address: loan.loan_customer.address_on_license,
      subject: CHECK_CIC_DETAILS_SUBJECT.MAIN_SME
    }];
    if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
      if (!isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
        persons.push({
          idNumber: loan.loan_business_owner.id_number,
          otherIdNumber: '',
          fullName: loan.loan_business_owner.full_name,
          address: '',
          subject: CHECK_CIC_DETAILS_SUBJECT.OWNER
        })
      }
      if (loan.loan_business_owner.married_status === 'M') {  
        if (!loan.loan_business_owner.partner_id_number ||
          !loan.loan_business_owner.partner_full_name) {
          throw new Error(`checkCicDetail | ${contractNumber} | Thiếu thông tin vợ/ chồng`)
        }
        persons.push({
          idNumber: loan.loan_business_owner.partner_id_number,
          otherIdNumber: '',
          fullName: loan.loan_business_owner.partner_full_name,
          address: '',
          subject: CHECK_CIC_DETAILS_SUBJECT.PARTNER_OF_OWNER
        })
      }
    } else {
      enterprises.push({
        registrationNumber: '',
        taxCode: loan.loan_business_owner.tax_id,
        companyName: loan.loan_business_owner.company_name,
        address: '',
        subject: CHECK_CIC_DETAILS_SUBJECT.OWNER
      });
    }
    if (loan.loan_customer_shareholders?.length > 0) {
      const shareholders = loan.loan_customer_shareholders;
      for (const shareholder of shareholders) {
        if (shareholder.subject === 'INDIVIDUAL') {
          if (!isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
            persons.push({
              idNumber: shareholder.id_number,
              otherIdNumber: '',
              fullName: shareholder.full_name,
              address: '',
              subject: CHECK_CIC_DETAILS_SUBJECT.SHAREHOLDER
            })
          }
        } else {
          if (!isDuplicateEnterprise({ taxCode: shareholder.tax_id }, enterprises)) {
            enterprises.push({
              registrationNumber: '',
              taxCode: shareholder.tax_id,
              companyName: shareholder.company_name,
              address: '',
              subject: CHECK_CIC_DETAILS_SUBJECT.SHAREHOLDER
            });
          }
        }
      }
    }

    const cicResult = await processCheckCicDetailKunn({
      kunnId: kunnId,
      custId: loan.cust_id,
      persons: persons,
      enterprises: enterprises,
      contractType: 'KUNN',
      taxCode: loan?.sme_tax_id,
      partnerCode: loan?.partner_code,
      contractNumber: contractNumber
    })
    return cicResult
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkCicDetailKunn] debt contract number :${kunnId}, error ${error}`);
    return false;
  }
};

function isDuplicatePerson(newPerson, personArray) {
  return personArray.some(person => person.idNumber === newPerson.idNumber);
}

function isDuplicateEnterprise(newEnterprise, enterprises) {
  return enterprises.some(enterprise => enterprise.taxCode === newEnterprise.taxCode);
}

const checkCicB11tSmeGw = async (body) => {
  const contractNumber = body.contract_number;
  try {

    const loan = await loanContractRepo.findLoan(contractNumber);
    if (!loan?.id) {
      console.log(`${contractNumber}| ${new Date()} | checkCicB11tSmeGw error |  loan not found`)
      return false;
    }

    const {
      partner_code: partnerCode,
    } = loan;
    let persons = loan.loan_customer_representations.map((rep) => ({
      idNumber: rep.id_number,
      otherIdNumber: '',
      fullName: rep.full_name,
      address: ''
    }));

    if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
      if (!isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
        persons.push({
          idNumber: loan.loan_business_owner.id_number,
          otherIdNumber: '',
          fullName: loan.loan_business_owner.full_name,
          address: ''
        })
      }
      //đã kết hôn
      // let businessTypeChecked = [BUSINESS_TYPE["01"], BUSINESS_TYPE["04"]];
      // if (businessTypeChecked.includes(loan.business_type) && loan.loan_business_owner.married_status === 'M') {
      if (loan.loan_business_owner.married_status === 'M') {
        if (!loan.loan_business_owner.partner_id_number ||
          !loan.loan_business_owner.partner_full_name) {
          console.log(`${contractNumber}| ${new Date()} | checkCicB11tSmeGw error | Thiếu thông tin vợ/ chồng`)
        }
        if (!isDuplicatePerson({ idNumber: loan.loan_business_owner.partner_id_number }, persons)) {
          persons.push({
            idNumber: loan.loan_business_owner.partner_id_number,
            otherIdNumber: '',
            fullName: loan.loan_business_owner.partner_full_name,
            address: ''
          })
        }
      }
    }

    if (loan.loan_customer_shareholders?.length > 0) {
      const shareholders = loan.loan_customer_shareholders;
      for (const shareholder of shareholders) {
        if (shareholder.subject === 'INDIVIDUAL') {
          if (!isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
            persons.push({
              idNumber: shareholder.id_number,
              otherIdNumber: '',
              fullName: shareholder.full_name,
              address: ''
            })
          }
        }
      }
    }

    const payload = {
      request_id: loan.request_id,
      partner_code: loan.partner_code,
      tax_code: loan.tax_id,
      contract_number: contractNumber,
      persons: persons
    }

    const cicResult = await checkCicB11Sme(payload)

    if (!cicResult?.success) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_B11T_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_B11T_SME.ACTION.ERROR, contractNumber);
      console.log(`${contractNumber}| ${new Date()} | checkCicB11tSmeGw error | cannot get response`)
      return false;
    }
    const cicData = cicResult?.data?.data || {};
    if (!["ELIGIBLE", "NOT_ELIGIBLE"].includes(cicData?.decision) || !cicData?.decision) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_B11T_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_B11T_SME.ACTION.ERROR, contractNumber);
      console.log(`${contractNumber}| ${new Date()} | checkCicB11tSmeGw error | decision invalid`)
      return false;
    }
    if (cicData?.decision && cicData.decision != STATUS.ELIGIBLE) {
      const { decision } = cicData || {};
      await loanContractRepo.updateLoanStatusV2({
        status: decision,
        contractNumber,
        rejectionReason: cicData?.reason_details?.length > 0
          ? JSON.stringify(cicData.reason_details) : null,
        cancelledBy: 'ANTI_FRAUD',
        cancelledAt: new Date()
      })
      //handle callback 3P here
      await Promise.all([
        callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED),
        crmService.rejectContract(global.config, contractNumber)
      ])
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_B11T_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_B11T_SME.ACTION.NOT_ELIGIBLE, contractNumber);
      return false;
    }
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CIC_B11T_SME.STEP_CODE, CASE_STATUS.CHECK_CIC_B11T_SME.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkCicB11tSmeGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkContractRenewDateGw = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loanContract = await loanContractRepo.findByContractNumber({
      contractNumber,
      partnerCode: body.partner_code,
    });
    if (!loanContract) {
      console.log(`${contractNumber}| ${new Date()} | checkContractRenewDateGw error | loan not found`);
      return false;
    }

    const { partner_code: partnerCode } = loanContract;
    const checkResult = await checkRenewDate(
      loanContract.request_id,
      loanContract.partner_code,
      loanContract.tax_id,
      contractNumber,
      TASK_FLOW.CHECK_CONTRACT_RENEW_DATE_AF2
    );
    if (!checkResult?.success) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CONTRACT_RENEW_DATE.STEP_CODE, CASE_STATUS.CHECK_CONTRACT_RENEW_DATE.ACTION.ERROR, contractNumber);
      console.log(`${contractNumber}| ${new Date()} | checkContractRenewDateGw error | cannot get response`);
      return false;
    }

    const { data } = checkResult || {};
    if (data?.data?.is_locking) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CONTRACT_RENEW_DATE.STEP_CODE, CASE_STATUS.CHECK_CONTRACT_RENEW_DATE.ACTION.NOT_ELIGIBLE, contractNumber);
      await loanContractRepo.updateLoanStatusV2({
        status: STATUS.REFUSED,
        contractNumber,
        rejectionReason: data?.data?.error_code,
        cancelledBy: 'ANTI_FRAUD',
        cancelledAt: new Date()
      })
      if (partnerCode === PARTNER_CODE.BIZZ) {
        await Promise.all([
          callbackPartner(
            contractNumber,
            partnerCode,
            CALLBACK_STAUS.REJECTED
          ),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }

      if (partnerCode === PARTNER_CODE.BZHM) {
        await Promise.all([
          callbackPartner(
            contractNumber,
            partnerCode,
            CALLBACK_STAUS.REJECTED
          ),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }
      
      return false;
    }
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_CONTRACT_RENEW_DATE.STEP_CODE, CASE_STATUS.CHECK_CONTRACT_RENEW_DATE.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkContractRenewDateGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const getBusinessOperatingTime = (registration_cert_issue_date) => {
  // Calculate duration (in months) between now and registration_cert_issue_date
  let business_operating_time = 0; // default value
  if (registration_cert_issue_date) {
    const now = moment();
    const issueDate = moment(registration_cert_issue_date, "YYYY-MM-DD");
    if (issueDate.isValid()) {
      business_operating_time = now.diff(issueDate, 'months');
    }
  }
  return business_operating_time;
}

const checkWhitelistFullLoanGw = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanGw | loan not found`);
      return false;
    }

    const revenue = await financialStatementDetailsRepo.getRevenue({ contractNumber });
    if (!revenue) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanGw | cannot get revenue data`);
      return false;
    }

    let payloadFullLoan = {
      ...(_.cloneDeep(loanContract)),
      request_limit: loanContract.request_amt,
      contract_type: 'LIMIT',
      tax_code: loanContract.tax_id,
      last_3m_transaction_volume: loanContract.last_3_month_sales_anchor || 0,
      business_operating_time: getBusinessOperatingTime(loanContract.registration_cert_issue_date),
      tax_report_revenue: revenue?.num_of_second_year 
    }

    if (loanContract.partner_code === PARTNER_CODE.BZHM) {
      payloadFullLoan = {
        ...payloadFullLoan,
        profit_pre_year:revenue.num_of_first_year || 0,
      };
    }

    const checkResult = await checkWhitelistFullLoanApi(payloadFullLoan)
    if (!checkResult?.data?.data) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanGw | cannot get response`);
      return false
    }

    if (!checkResult?.data?.code == 0) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanGw | request failed with code ${checkResult?.data?.code}`);
      return false
    }

    const { partner_code: partnerCode } = loanContract;
    const { data } = checkResult || {};
    if (data?.data?.decision == 'NOT_ELIGIBLE') {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.NOT_ELIGIBLE, contractNumber);
      await loanContractRepo.updateLoanStatusV2({
        status: STATUS.REFUSED,
        contractNumber,
        rejectionReason: `CHECK_WHITELIST_FULL_LOAN`,
        cancelledBy: 'ANTI_FRAUD',
        cancelledAt: new Date()
      })
      if (partnerCode === PARTNER_CODE.BIZZ) {
        await Promise.all([
          callbackPartner(
            contractNumber,
            partnerCode,
            CALLBACK_STAUS.REJECTED
          ),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }

      if (partnerCode === PARTNER_CODE.BZHM) {
        await Promise.all([
          callbackPartner(
            contractNumber,
            partnerCode,
            CALLBACK_STAUS.REJECTED
          ),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }

      return false;
    }

    await sqlHelper.patchUpdate({
      table: 'loan_contract',
      columns: ['approval_amt', 'updated_date'],
      values: sqlHelper.generateValues({
        approval_amt: data.data.limit,
        updated_date: new Date()
      }, ['approval_amt', 'updated_date']),
      conditions: {
        contract_number: contractNumber
      }
    })
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.error(error);
    console.log(
      `[ANTI_FRAUD][checkWhitelistFullLoanGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkWhitelistFullLoanHm = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanHm | loan not found`);
      return false;
    }

    const fnExrt = await financialStatementDetailsRepo.getFinancialStatementsExportByTemplate(contractNumber ,'TT133_BCTC');

    if (!fnExrt) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanHm | cannot get revenue data`);
      return false;
    }

   
    const ct_by_tmpBCTC01 = fnExrt.template == "TT133_BCTC_B01";
    const revenue_by_data = await financialStatementDetailsRepo.getRevenueByTaxFinancialReport(contractNumber, ct_by_tmpBCTC01 ? ["01", "07"] : ["10", "60"]);
    const tax_report_revenue = revenue_by_data.find((item) => (ct_by_tmpBCTC01 ? item?.code == "01" : item?.code == "10"));
    const profit_pre_year = revenue_by_data.find((item) => (ct_by_tmpBCTC01 ? item?.code == "07" : item?.code == "60"));


    let payloadFullLoan = {
      ..._.cloneDeep(loanContract),
      request_limit: loanContract.request_amt,
      contract_type: "LIMIT",
      tax_code: loanContract.tax_id,
      // last_3m_transaction_volume: loanContract.last_3_month_sales_anchor || 0,
      business_operating_time: getBusinessOperatingTime(loanContract.registration_cert_issue_date),
      tax_report_revenue: Number(tax_report_revenue?.num_of_second_year || 0),
      profit_pre_year: Number(profit_pre_year?.num_of_second_year || 0),
    };

    const checkResult = await checkWhitelistHmFullLoanApi(payloadFullLoan);
    if (!checkResult?.data?.data) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanHm | cannot get response`);
      return false;
    }

    if (!checkResult?.data?.code == 0) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkWhitelistFullLoanHm | request failed with code ${checkResult?.data?.code}`);
      return false;
    }

    const { partner_code: partnerCode } = loanContract;
    const { data } = checkResult || {};
    if (data?.data?.decision == "NOT_ELIGIBLE") {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.NOT_ELIGIBLE, contractNumber);
      await loanContractRepo.updateLoanStatusV2({
        status: STATUS.REFUSED,
        contractNumber,
        rejectionReason: `CHECK_WHITELIST_FULL_LOAN`,
        cancelledBy: "ANTI_FRAUD",
        cancelledAt: new Date(),
      });

      await Promise.all([callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED), crmService.rejectContract(global.config, contractNumber)]);

      return false;
    }

    await sqlHelper.patchUpdate({
      table: "loan_contract",
      columns: ["approval_amt", "updated_date"],
      values: sqlHelper.generateValues(
        {
          approval_amt: data.data.limit,
          updated_date: new Date(),
        },
        ["approval_amt", "updated_date"]
      ),
      conditions: {
        contract_number: contractNumber,
      },
    });
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.STEP_CODE, CASE_STATUS.CHECK_WHITELIST_FULL_LOAN.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.error(error);
    console.log(`[ANTI_FRAUD][checkWhitelistFullLoanHm] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const checkModelGw = async (body) => {
  const contractNumber = body.contract_number;
  // const partnerCode = body.partner_code;
  try {
    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | loan not found`);
      return false;
    }

    const revenueDocuments = await loanRevenuesRepo.findRevenueDocuments(contractNumber);
    const revenueDocument = revenueDocuments && revenueDocuments?.length > 0 ? revenueDocuments[0] : {};
    if (!revenueDocument.evf_file_url && !revenueDocument.evf_doc_id) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | không có file BCTC`)
    }

    const [
      reportData,
      fullLoanData
    ] = await Promise.all([
      getFinancialReportDataFromFile({
        contractNumber,
        docId: revenueDocument.evf_doc_id,
        fileUrl: revenueDocument.evf_file_url
      }),
      loanContractRepo.findLoan(contractNumber)
    ]);

    let payload = {
      ..._.cloneDeep(loanContract),
      tax_code: loanContract.tax_id ?? loanContract.sme_tax_id,
      business_operating_time: getBusinessOperatingTime(loanContract.registration_cert_issue_date),
      report_type: mappingFinancialReportType(revenueDocument.financial_report_type) ?? null,
      report_data: reportData,
      management_experience: fullLoanData?.loan_customer_representations[0].management_experience || null,
    }

    if (loanContract.partner_code === PARTNER_CODE.BZHM) {
      payload = {
        ...payload,
        request_limit: Number(loanContract.request_amt || 0),
        expected_revenue: Number(loanContract.total_turnover_next_year || 0.0),
      };
    }

    const checkResult = await checkModelApi(payload)
    if (!checkResult?.data) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | cannot get response`);
      return false
    }

    if (checkResult?.data?.code != 0) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | request failed with code ${checkResult?.data?.code}`);
      return false
    }

    const { data } = checkResult || {};
    if (data?.data?.decision == 'REJECT') {
      await loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber);
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.NOT_ELIGIBLE, contractNumber);
      //handle callback 3P here

      return false;
    }

    const product_code = data?.data?.product_code;
    if (product_code) {
      const [bundleInfoRes, allDocument] = await Promise.all([
        productService.getBundle(global.config, product_code),
        sqlHelper.find({
          table: "loan_contract_document",
          whereCondition: { contract_number: contractNumber, is_deleted: 0 },
        }),
        sqlHelper.updateData({
          table: "loan_contract",
          columns: ["product_code"],
          values: [product_code],
          conditions: { contract_number: contractNumber },
        }),
      ]);

      await documentRepo.updateBundleGroup({
        docList: utils.snakeToCamel(allDocument),
        bundleInfo: bundleInfoRes?.data ?? [],
      });
    }

    //save loan_rating
    let bodyRating = {
      contract_number: contractNumber,
      rank: data?.data?.rank,
      decision: data?.data?.decision,
      description: data?.data?.description,
      detail_data: data?.data?.detail_data
    };
    
    await Promise.all([
      sqlHelper.insertData(
        `loan_rating`,
        loanRatingRepo.columns,
        sqlHelper.generateValues(
          bodyRating,
          loanRatingRepo.columns
        ))
    ])
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkModelGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkModelHm = async (body) => {
  const contractNumber = body.contract_number;
  // const partnerCode = body.partner_code;
  try {
    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | loan not found`);
      return false;
    }

    const revenueDocuments = await loanRevenuesRepo.findRevenueDocuments(contractNumber);
    const revenueDocument = revenueDocuments && revenueDocuments?.length > 0 ? revenueDocuments[0] : {};
    if (!revenueDocument.evf_file_url && !revenueDocument.evf_doc_id) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | không có file BCTC`)
    }

    const [reportData, fullLoanData] = await Promise.all([
      getFinancialReportDataFromFile({
        contractNumber,
        docId: revenueDocument.evf_doc_id,
        fileUrl: revenueDocument.evf_file_url,
      }),
      loanContractRepo.findLoan(contractNumber),
    ]);

    let payload = {
      ..._.cloneDeep(loanContract),
      tax_code: loanContract.tax_id ?? loanContract.sme_tax_id,
      business_operating_time: getBusinessOperatingTime(loanContract.registration_cert_issue_date),
      report_type: mappingFinancialReportType(revenueDocument.financial_report_type) ?? null,
      report_data: reportData,
      management_experience: fullLoanData?.loan_customer_representations[0].management_experience || null,
      request_limit: Number(loanContract.request_amt || 0),
      expected_revenue: Number(loanContract.total_turnover_next_year || 0.0),
    }



    const checkResult = await checkModelHmApi(payload)
    if (!checkResult?.data) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | cannot get response`);
      return false
    }

    if (checkResult?.data?.code != 0) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.ERROR, contractNumber);
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelGw | request failed with code ${checkResult?.data?.code}`);
      return false
    }

    const { data } = checkResult || {};
    if (data?.data?.decision == 'REJECT') {
      await loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber);
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.NOT_ELIGIBLE, contractNumber);
      //handle callback 3P here

      return false;
    }

    const {  data: { product_code, approved_limit, approved_interest_rate }} = data;

    const contractupdated = await sqlHelper.updateData({
      table: "loan_contract",
      columns: ["product_code", "approval_amt", "approval_int_rate", "updated_date"],
      values: [product_code, approved_limit, approved_interest_rate, new Date()],
      conditions: {
        contract_number: contractNumber,
      },
    });

    if (contractupdated?.product_code) {
      const [bundleInfoRes, allDocument] = await Promise.all([
        productService.getBundle(global.config, product_code), 
        sqlHelper.find({ table: "loan_contract_document", whereCondition: { contract_number: contractNumber, is_deleted: 0 } })
      ]);

      await documentRepo.updateBundleGroup({
        docList: utils.snakeToCamel(allDocument),
        bundleInfo: bundleInfoRes?.data ?? [],
      });
    }

    //save loan_rating
    let bodyRating = {
      contract_number: contractNumber,
      rank: data?.data?.rank,
      decision: data?.data?.decision,
      description: data?.data?.description,
      detail_data: data?.data?.detail_data
    };
    
    await Promise.all([
      sqlHelper.insertData(
        `loan_rating`,
        loanRatingRepo.columns,
        sqlHelper.generateValues(
          bodyRating,
          loanRatingRepo.columns
        ))
    ])
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECK_MODEL.STEP_CODE, CASE_STATUS.CHECK_MODEL.ACTION.ELIGIBLE, contractNumber);
    return true;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkModelGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const checkModelFinvGw = async (body) => {
  const contractNumber = body.contract_number;  
  try {
    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelFinvGw | loan not found`);
      return false;
    }

    const businessData = loanContract?.business_data ? JSON.parse(loanContract.business_data) : {};

    const payload = {
      request_id: loanContract.request_id,
      partner_code: PARTNER_CODE.FINV,
      contract_number: contractNumber,
      product_code: loanContract.product_code,
      time_duration: loanContract.time_duration,
      total_purchase_amount: businessData?.turnover6M || businessData?.turnover3M || null,
      request_loan_amount: loanContract.request_amt,
    }

    const checkResult = await checkModelFinvApi(payload)
    if (!checkResult?.data) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelFinvGw | cannot get response`);
      return false
    }

    if (checkResult?.data?.code != 0) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | checkModelFinvGw | request failed with code ${checkResult?.data?.code}`);
      return false
    }

    const { decision, limit } = checkResult?.data?.data || {};
    if (decision == 'NOT_ELIGIBLE') {
       await loanContractRepo.updateLoanContract({
        contract_number: contractNumber,
        status: STATUS.REFUSED,
        updated_date: new Date(),
        rejection_reason: 'CHECK_MODEL',
        cancelled_at: new Date()
      });
      crmService.rejectContract(global.config, contractNumber);
      callbackPartner(contractNumber, PARTNER_CODE.FINV, CALLBACK_STAUS.NOT_ELIGIBLE);
      //callback to 3P
      return false;
    } 
    if (decision == 'ELIGIBLE') {
      await loanContractRepo.updateLoanContract({
        contract_number: contractNumber,
        status: STATUS.ELIGIBLE,
        updated_date: new Date(),
        approval_amt: limit,
        approval_tenor: loanContract.request_tenor
      });
      return true;
    }
    return false;
  } catch (error) {
    console.log(
      `[ANTI_FRAUD][checkModelFinvGw] contract number :${contractNumber}, error ${error}`
    );
    return false;
  }
};

const mappingFinancialReportType = (reportType) => {
  if (reportType === "133_B01") return "TT133_01";
  if (reportType === "133_B01A") return "TT133_01A";
  if (reportType === "133_B01B") return "TT133_01B";
  if (reportType === "200_BCTC") return "TT200";
  return null;
}

/**
 * param {*} contract_type: "LIMIT" or "KUNN"
*/
const checkWhitelistAf1 = async ({
  request_id,
  partner_code,
  tax_code,
  contract_number,
  contract_type,
  platform_usage_time,
  anchor_transaction_time
}) => {
  try {
    const response = await checkWhitelistAf1Api({
      request_id,
      partner_code,
      tax_code,
      contract_number,
      contract_type,
      platform_usage_time,
      anchor_transaction_time
    });

    const { data } = response || {};
    if (data?.code === 0) {
      return { success: true, data: data };
    }
    return { success: false, data: data };
  } catch (error) {
    return { success: false, message: error.message };
  }
}

const checkEligibleKunn = async (kunnId) => {
  try {
    const kunnData = await kunnRepo.getKunnData(kunnId);
    const contractNumber = kunnData?.contract_number;
    const loanContract = await loanContractRepo.findLoan(contractNumber);
    if (!loanContract) {
      console.log(`${kunnId}| ${new Date()} | checkEligibleKunn error | cannot get loan_contract`)
      return false;
    }
    //danh sách người cần check eligible: Đại diện PL DN vay, Chủ DN là cá nhân, Vợ chồng chủ DN, Cổ đông là cá nhân
    let eligiblePersons = [];// danh sách người cần check eligible
    //danh sách doanh nghiệp cần check eligible: Doanh nghiệp vay, Chủ DN là doanh nghiệp, Cổ đông là DN
    let otherEnterprises = [];// danh sách doanh nghiệp cần check eligible;

    if (loanContract.loan_business_owner.subject === 'INDIVIDUAL') {
      if (!isDuplicatePerson({ idNumber: loanContract.loan_business_owner.id_number }, eligiblePersons)) {
        eligiblePersons.push({
          idNumber: loanContract.loan_business_owner.id_number,
          customerName: loanContract.loan_business_owner.full_name,
          otherIdNumber: null,
          issueDate: loanContract.loan_business_owner.issue_date ? moment(loanContract.loan_business_owner.issue_date).format("YYYY-MM-DD") : undefined,
          issuePlace: loanContract.loan_business_owner.issue_place,
          dateOfBirth: loanContract.loan_business_owner.dob ? moment(loanContract.loan_business_owner.dob).format("YYYY-MM-DD") : undefined,
          gender: null,
          phoneNumber: loanContract.loan_business_owner.phone_number
        })
      }
      //đã kết hôn - vợ chồng chủ doanh nghiệp
      if (loanContract.loan_business_owner.married_status === 'M' && 
        (!loanContract.loan_business_owner.partner_id_number || !loanContract.loan_business_owner.partner_full_name)) {
        console.log(`${kunnId}| ${new Date()} | checkEligibleKunn error | married_status is M but partner_id_number or partner_full_name is missing`);
        loggingRepo.saveStepLog(
          kunnId,
          SERVICE_NAME.ANTI_FRAUD,
          TASK_FLOW.CHECK_ELIGIBLE_SME,
          payload,
          { message: 'married_status is M but partner_id_number or partner_full_name is missing' },
          url
        );
        return false;
      }
      if (loanContract.loan_business_owner.married_status === 'M' &&
        !isDuplicatePerson({ idNumber: loanContract.loan_business_owner.partner_id_number }, eligiblePersons)
      ) {
        eligiblePersons.push({
          idNumber: loanContract.loan_business_owner.partner_id_number,
          customerName: loanContract.loan_business_owner.partner_full_name,
          otherIdNumber: null,
          issueDate: null,
          issuePlace: null,
          dateOfBirth: loanContract.loan_business_owner.partner_dob ? moment(loanContract.loan_business_owner.partner_dob).format("YYYY-MM-DD") : undefined,
          gender: null,
          phoneNumber: loanContract.loan_business_owner.partner_phone_number
        })
      }
    } else if (loanContract.loan_business_owner.subject === 'ORGANIZATION') {
      //không check doanh nghiệp liên quan trừ doanh nghiệp vay: c Hà *********
      if (!isDuplicateEnterprise({ registrationNumber: loanContract.loan_business_owner.tax_id }, otherEnterprises)) {
        otherEnterprises.push({
          customerName: loanContract.loan_business_owner.full_name,
          idNumber: loanContract.loan_business_owner.id_number,
          otherIdNumber: null,
          issueDate: loanContract.loan_business_owner.issue_date ? moment(loanContract.loan_business_owner.issue_date).format("YYYY-MM-DD") : undefined,
          issuePlace: loanContract.loan_business_owner.issue_place,
          dateOfBirth: loanContract.loan_business_owner.dob ? moment(loanContract.loan_business_owner.dob).format("YYYY-MM-DD") : undefined,
          gender: null,
          phoneNumber: loanContract.loan_business_owner.phone_number,
          taxCode: loanContract.loan_business_owner.tax_id,
          registrationNumber: loanContract.loan_business_owner.registration_number ?? loanContract.loan_business_owner.tax_id,
          companyName: loanContract.loan_business_owner.company_name,
          registrationDate: null,
          companyType: null
        });
      }
    }
    //cổ đông
    if (loanContract.loan_customer_shareholders?.length > 0) {
      const shareholders = loanContract.loan_customer_shareholders;
      shareholders.forEach(shareholder => {
        if (shareholder.subject === 'INDIVIDUAL') {
          if (!isDuplicatePerson({ idNumber: shareholder.id_number }, eligiblePersons)) {
            eligiblePersons.push({
              idNumber: shareholder.id_number,
              customerName: shareholder.full_name,
              otherIdNumber: null,
              issueDate: null,
              issuePlace: null,
              dateOfBirth: shareholder.dob ? moment(shareholder.dob).format("YYYY-MM-DD") : undefined,
              gender: null,
              phoneNumber: shareholder.phone_number
            });
          }
        } else if (shareholder.subject === 'ORGANIZATION') {
          if (!isDuplicateEnterprise({ registrationNumber: shareholder.tax_id }, otherEnterprises)) {
            otherEnterprises.push({
              customerName: shareholder.full_name,
              idNumber: shareholder.id_number,
              otherIdNumber: null,
              issueDate: shareholder.issue_date ? moment(shareholder.issue_date).format("YYYY-MM-DD") : undefined,
              issuePlace: shareholder.issue_place,
              dateOfBirth: shareholder.dob ? moment(shareholder.dob).format("YYYY-MM-DD") : undefined,
              gender: null,
              phoneNumber: shareholder.phone_number,
              taxCode: shareholder.tax_id,
              registrationNumber: shareholder.registration_number ?? shareholder.tax_id,
              companyName: shareholder.company_name,
              registrationDate: null,
              companyType: null
            });
          }
        }
      });
    }

    let firstRegistrationDate = loanContract.registration_cert_issue_date ?? loanContract.registration_date;

    const eligiblePayload = {
      requestId: kunnData.request_id,
      contractType: 'KUNN',
      legalRepresentative: {
        customerName: loanContract.sme_representation_name,
        idNumber: loanContract.sme_representation_id,
        issueDate: loanContract.sme_representation_issue_date ? moment(loanContract.sme_representation_issue_date).format("YYYY-MM-DD") : undefined,
        issuePlace: loanContract.sme_representation_issue_place,
        dateOfBirth: loanContract.sme_representation_dob ? moment(loanContract.sme_representation_dob).format("YYYY-MM-DD") : undefined,
        phoneNumber: loanContract.sme_representation_phone_number,
      },
      otherPersons: eligiblePersons,
      otherEnterprises: otherEnterprises,
      productType: '',
      registrationNumber: loanContract.registration_number ?? loanContract.sme_tax_id,
      taxCode: loanContract.sme_tax_id,
      companyName: loanContract.sme_name,
      registrationDate: firstRegistrationDate ? moment(firstRegistrationDate).format("YYYY-MM-DD") : undefined,
      productCode: '',
      caseCreationTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      partnerCode: loanContract.partner_code,
      channel: loanContract.channel,
      contractNumber: kunnId,
      companyType: loanContract.business_type
    }

    const eligibleResult = await checkEligibleSmeV2(eligiblePayload)
    if (!eligibleResult?.decision) {
      console.log(`${kunnId}| ${new Date()} | checkEligibleKunn error | cannot get response`)
    }
    await loggingRepo.saveStepLog(kunnId, SERVICE_NAME.ANTI_FRAUD, TASK_FLOW.CHECK_ELIGIBLE_SME, eligiblePayload, eligibleResult);
    await loggingRepo.saveWorkflow(TASK_FLOW.CHECK_ELIGIBLE_SME, eligibleResult.decision, kunnId, 'system');
    let actionCode = '';
    if (eligibleResult?.decision && eligibleResult.decision != STATUS.ELIGIBLE) {
      actionCode = CASE_STATUS.CHECKED_ELIGIBILITY.ACTION.NOT_ELIGIBLE;
    } else {
      actionCode = CASE_STATUS.CHECKED_ELIGIBILITY.ACTION.ELIGIBLE;
    }
    actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECKED_ELIGIBILITY.STEP_CODE, actionCode, kunnId);

    if (eligibleResult?.decision && eligibleResult.decision != STATUS.ELIGIBLE) {
      const { decision, reason_code } = eligibleResult || {};
      await kunnRepo.update(kunnId,{
          status: decision,
          cancelled_reason: reason_code ?? null,
          cancelled_by: 'ANTI_FRAUD',
          cancelled_at: new Date()
        })
      //callback to 3P
      if (loanContract.partner_code === PARTNER_CODE.BIZZ) {
        //handle callback 3P here
        await Promise.all([
          callbackPartner(contractNumber, loanContract.partner_code, CALLBACK_STAUS.REJECTED, null, null, kunnId),
          crmService.rejectContract(global.config, contractNumber)
        ])
      }
      return false;
    }
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkEligibleKunn] debt contract number :${kunnId}, error ${error}`);
    return false;
  }
};

const processRenewDateCheckResultKunn = async (checkResult, kunnId) => {
  try {
      if (!checkResult?.success) {
        console.log(`${kunnId}| ${new Date()} | processRenewDateCheckResult error | cannot get response`);
        return false;
      }

      const { data } = checkResult || {};

      if (data?.data?.is_locking) {
          // handle callback 3P here
          return false;
      }

      const contractRenewDate = data?.data?.contract_renew_date;
      const now = moment();
      if (contractRenewDate && moment(contractRenewDate, "YYYY-MM-DD HH:mm:ss").isAfter(now)) {
          // handle callback 3P here
          return false;
      }
      // contract_renew_date is null or date is in the past
      return true;
  } catch (error) {
      console.log(`[KUNN TD1][processRenewDateCheckResult] kunnId :${kunnId}, error ${error}`);
      return false;
  }
};

const checkRenewDateKunn = async (kunnId) => {
  const kunnData = await kunnRepo.getKunnData(kunnId);
  const partnerCode = kunnData.partner_code;
  const contractNumber = kunnData.contract_number;
  const contract = await loanContractRepo.findByContractNumber({contractNumber, partnerCode});
  const taxtId = contract?.tax_id || '';
  const identityCard = [PARTNER_CODE.FINV].includes(partnerCode) ? contract?.id_number || '' : '';

  const bodyRenewDate = {
      request_id: uuid.v4(),
      partner_code: partnerCode,
      tax_code: taxtId,
      identity_card: identityCard,
      contract_number: kunnId
  };
  
  const renewResult = await checkRenewDateAll(bodyRenewDate);
  const isRenewDatePassed = await processRenewDateCheckResultKunn(renewResult, kunnId);
  const actionCode = isRenewDatePassed ? CASE_STATUS.CHECK_RENEW_DATE.ACTION.PASSED_RENEW_DATE : CASE_STATUS.CHECK_RENEW_DATE.ACTION.REFUSED;
  actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECK_RENEW_DATE.STEP_CODE, actionCode, kunnId);

  if (!isRenewDatePassed) {
      let status = KUNN_STATUS.REFUSED;
      await kunnRepo.updateKUStatus(kunnId, status);
      await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECT_KUNN, null, null, kunnId);
      return false;
  }
  return true;
};

const processB11TCheckResultKunn = async (checkResult, kunnId) => {
  try {
      if (!checkResult?.success) {
          console.log(`${kunnId}| ${new Date()} | BIZZ_TD1 | processB11TCheckResultKunn error | cannot get response`);
          return false;
      }

      const { data } = checkResult || {};
      if (!['ELIGIBLE', 'NOT_ELIGIBLE'].includes(data?.data?.decision)) {
          console.log(`${kunnId}| ${new Date()} | BIZZ_TD1 | processB11TCheckResultKunn error | decision code not valid`);
          return false;
      }

      if (data?.data?.decision === 'NOT_ELIGIBLE') {
          // handle callback 3P here
          return false;
      }

      return true;
  } catch (error) {
      console.log(`[BIZZ_TD1][processB11TCheckResultKunn] contract number :${kunnId}, error ${error}`);
      return false;
  }
};

const checkB11TKunn = async (kunnId) => {
  const kunnData = await kunnRepo.getKunnData(kunnId);
  const partnerCode = kunnData.partner_code;
  const contractNumber = kunnData.contract_number;
  const loan = await loanContractRepo.findLoan(contractNumber);

  let persons = loan.loan_customer_representations.map((rep) => ({
    idNumber: rep.id_number,
    otherIdNumber: '',
    fullName: rep.full_name,
    address: ''
  }));

  if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
    if (!isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
      persons.push({
        idNumber: loan.loan_business_owner.id_number,
        otherIdNumber: '',
        fullName: loan.loan_business_owner.full_name,
        address: ''
      })
    }
    //đã kết hôn
    if (loan.loan_business_owner.married_status === 'M') {
      if (!loan.loan_business_owner.partner_id_number ||
        !loan.loan_business_owner.partner_full_name) {
        console.log(`${contractNumber}| ${new Date()} | checkB11TKunn error | Thiếu thông tin vợ/ chồng`)
      }
      if (!isDuplicatePerson({ idNumber: loan.loan_business_owner.partner_id_number }, persons)) {
        persons.push({
          idNumber: loan.loan_business_owner.partner_id_number,
          otherIdNumber: '',
          fullName: loan.loan_business_owner.partner_full_name,
          address: ''
        })
      }
    }
  }

  if (loan.loan_customer_shareholders?.length > 0) {
    const shareholders = loan.loan_customer_shareholders;
    for (const shareholder of shareholders) {
      if (shareholder.subject === 'INDIVIDUAL') {
        if (!isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
          persons.push({
            idNumber: shareholder.id_number,
            otherIdNumber: '',
            fullName: shareholder.full_name,
            address: ''
          })
        }
      }
    }
  }
  
  const bodyCicB11Sme = {
      uuid: uuid.v4(),
      partner_code: partnerCode,
      contract_number: kunnId,
      tax_code: loan?.tax_id,
      persons: persons,
  };
  const cicB11SmeResult = await checkCicB11Sme(bodyCicB11Sme);
  const isB11TPassed = await processB11TCheckResultKunn(cicB11SmeResult, kunnId);
  const actionCode = isB11TPassed ? CASE_STATUS.CHECKED_B11T.ACTION.PASSED_B11T : CASE_STATUS.CHECKED_B11T.ACTION.REFUSED;
  actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.CHECKED_B11T.STEP_CODE, actionCode, kunnId);
  if (!isB11TPassed) {
      let status = KUNN_STATUS.REFUSED;
      await kunnRepo.updateKUStatus(kunnId, status);
      return false;
  }
  return true;
}

const checkBlacklistKunn = async (kunnId) => {
  const kunnData = await kunnRepo.getKunnData(kunnId);
  const partnerCode = kunnData.partner_code;
  const contractNumber = kunnData.contract_number;
  const contract = await loanContractRepo.findByContractNumber({contractNumber, partnerCode});

  const body = {
    request_id: kunnData.request_id,
    partner_code: partnerCode,
    phone_number: null,
    contract_number: kunnId,
    id_number: contract.id_number,
    tax_code: null,
    email: null
  };
  
  const result = await checkBlacklist(body);
  const { success, data } = result;
  if (!success || ![STATUS.ELIGIBLE, STATUS.NOT_ELIGIBLE].includes(data.code)) {
    console.log(`[FINV] [KUNN] Check blacklist error: ${JSON.stringify(result)}`);
    return false;
  }
  if(data.code === STATUS.NOT_ELIGIBLE) {
    await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.NOT_ELIGIBLE);
    await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.NOT_ELIGIBLE_KUNN, null, null, kunnId);
    return false;
  }
  await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.ELIGIBLE);
  return data.code === STATUS.ELIGIBLE;
};

const checkEkycKunn = async(kunnId) => {
  try {
    const kunnData = await kunnRepo.getKunnData(kunnId);
    const contractNumber = kunnData.contract_number;
    const [loan, nfcDocs, kunnDocs] = await Promise.all([
      loanContractRepo.getLoanContract(contractNumber),
      loanContractDocumentRepo.findByContractAndTypes(contractNumber, [DOC_TYPE.NFC]),
      loanContractDocumentRepo.getDocumentsByKunnAndDocTypes(kunnId, [DOC_TYPE.SELFIE])
    ]);
    const nfcData = await nfcDataRepo.findByRequestId(loan.request_id);
    const nfcPath = nfcDocs && nfcDocs.length > 0 ? nfcDocs.find(doc => doc.doc_type === DOC_TYPE.NFC)?.file_key : '';
    const selfiePath = kunnDocs && kunnDocs.length > 0 ? kunnDocs.find(doc => doc.doc_type === DOC_TYPE.SELFIE)?.file_key : '';
    const rs = await checkEkycApi({
      requestId: kunnData.request_id,
      contractNumber: kunnId,
      partnerCode: kunnData.partner_code,
      selfiePath,
      idCardFrontPath: nfcData?.front_id_card_url,
      idCardBackPath: nfcData?.back_id_card_url,
      nfcPath,
      step: 'KUNN',
    });
    if (!rs || rs.code !== "PASS_EKYC") {
      await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.REFUSED);
      await callbackPartner(contractNumber, kunnData.partner_code, CALLBACK_STAUS.REJECT_KUNN, null, null, kunnId);
      return false;
    }
    await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.PASSED_CHECK_EKYC);
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkEKYC KUNN error ${kunnId} error ${error}`);
    return false;
  }
}

const checkFullLoanKunn = async(kunnId) => {
  try {
    const kunnData = await kunnRepo.getKunnData(kunnId);
    const contractNumber = kunnData.contract_number;
    const contractData = await loanContractRepo.getLoanContract(contractNumber);
    let requestId = kunnData.request_id;

    let req = { config: global.config };
    const [currentProvince, currentWard, permanentProvince, permanentWard, bankInfo] = await Promise.all([
      getPlace(req, contractData.province_cur, "provinces"),
      getPlace(req, contractData.ward_cur, "wards"),
      getPlace(req, contractData.province_per, "provinces"),
      getPlace(req, contractData.ward_per, "wards"),
      getValueCodeMasterdataV2(kunnData.bank_code, "BANK"),
    ]);

    const [spidDocs, selfieDoc] = await Promise.all([
      documentRepo.findByContractAndTypes(contractNumber, [DOC_TYPE.ID_CARD.FRONT, DOC_TYPE.ID_CARD.BACK]),
      documentRepo.getDocByKunnAndType(kunnId, DOC_TYPE.SELFIE),
    ]);
    const frontIdDoc = spidDocs?.find(doc => doc.doc_type === DOC_TYPE.ID_CARD.FRONT);
    const backIdDoc = spidDocs?.find(doc => doc.doc_type === DOC_TYPE.ID_CARD.BACK);

    const caseCreationTime = new Date();
    const contractFrom = caseCreationTime.getFullYear();
    caseCreationTime.setMonth(
      caseCreationTime.getMonth() + Number(contractData.request_tenor || 0)
    );
    const contractTo = caseCreationTime.getFullYear();
    const payload = {
      requestId,
      partnerCode: contractData.partner_code,
      channel: contractData.channel,
      contractNumber: kunnId,
      contractNumberParent: contractNumber,
      slAdvanceContractType: "WD",
      custId: contractData?.cust_id,
      losType: LOS_TYPE,
      productType: contractData?.contract_type,
      riskGroup: null,
      productCode: contractData?.product_code,
      productId: contractData?.product_id,
      insuranceCode: null,
      customerName: contractData?.cust_full_name,
      gender: contractData.gender,
      dateOfBirth: contractData.birth_date
        ? moment(contractData.birth_date).format("yyyy-MM-DD")
        : null,
      idNumber: contractData.id_number,
      issueDate: contractData.id_issue_dt
        ? moment(contractData.id_issue_dt).format("yyyy-MM-DD")
        : null,
      issuePlace: contractData.id_issue_place,
      otherIdNumber: contractData.other_id_number,
      otherIdDateOfBirth: null,
      otherIdIssueDate: null,
      otherIdIssuePlace: null,
      phoneNumber: contractData.phone_number1,
      phoneTelco: contractData.phone_number1,
      email: contractData.email,
      disbursementMethod: contractData.disbursement_method,
      accountNumber: kunnData.bank_account,
      bankCode: kunnData.bank_code,
      bankName: bankInfo?.nameVn,
      bankBranchCode: kunnData.bank_branch_code,
      bankBranchName: null,
      beneficiaryName: contractData.beneficiary_name,

      temCountry: null,
      temProvince: currentProvince,
      temProvinceCode: contractData.province_cur,
      temDistrict: null,
      temDistrictCode: null,
      temWard: currentWard,
      temWardCode: contractData.ward_cur,
      temDetailAddress: contractData.address_cur,

      permanentCountry: null,
      permanentProvince: permanentProvince,
      permanentProvinceCode: contractData.province_per,
      permanentDistrict: null,
      permanentDistrictCode: null,
      permanentWard: permanentWard,
      permanentWardCode: contractData.ward_per,
      permanentDetailAddress: contractData.address_per,

      nationality: "VN",
      reference1Type: contractData.reference_type_1,
      reference2Type: contractData.reference_type_2,
      relativeReferenceName1: contractData.reference_name_1,
      relativeReferenceName2: contractData.reference_name_2,
      relativeReferencePhone1: contractData.reference_phone_1,
      relativeReferencePhone2: contractData.reference_phone_2,

      otherContact: null,
      contactDetail: null,
      occupation: null,
      monthlyIncome: Number(contractData.monthly_income || 0),
      otherIncome: Number(contractData.other_income || 0),
      monthlyExpenses: 0,
      companyName: contractData.workplace_name,
      companyAddress: contractData.workplace_address,
      companyCountry: "VN",
      companyProvince: contractData.workplace_province,
      companyDistrict: contractData.workplace_district,
      companyWard: contractData.workplace_ward,
      contractFrom: contractFrom,
      contractTo: contractTo,
      jobType: contractData.job_type,
      employmentType: contractData.empl_type,
      employmentContractType: contractData.empl_ctrct_type,
      marriedStatus: contractData.married_status,
      houseType: contractData.house_type,
      otherHouseType: null,
      numOfDependents: contractData.num_of_dependants || 0,
      yearsOfStay: null,
      incomeProof: contractData.income_proof_amount || 0,
      salaryFrequency: contractData.salary_frequency,
      salaryPaymentDate: contractData.salary_payment_day,
      taxId: contractData.tax_id,
      loanPurpose: contractData.loan_purpose,
      loanAmount: kunnData.with_draw_amount,
      loanTenor: kunnData.tenor,

      s3IdCardUrl: null,
      s3FrontIdCardUrl: frontIdDoc?.file_key,
      s3BackIdCardUrl: backIdDoc?.file_key,
      s3SelfieImageUrl: selfieDoc?.file_key,
      s3VerifyIncomeImageUrls: [],

      phoneNumber2: null,
      phoneNumber3: null,
      education: null,
      custType: null,
      sameCities: null,
      countryPer: null,
      villageCur: null,
      villagePer: null,
      requestIntRate: Number(contractData.request_int_rate || 0),
      requestInstalAmt: null,
      staffNumber: contractData.number_of_staffs,
      insuranceType: null,
      companyCode: null,
      companyTaxId: contractData.sme_tax_id,
      deviceId: null,
      deviceModel: null,
      deviceBrand: null,
      deviceOs: null,
      latitude: null,
      longitude: null,
      scanData: null,
      referencePersons: [
        {
          customerName: contractData.reference_name_1,
          idNumber: contractData.reference_id_number_1,
        },
        {
          customerName: contractData.reference_name_2,
          idNumber: contractData.reference_id_number_2,
        },
      ],
      step: TASK_FLOW.CHECK_FULL_LOAN_INDIVIDUAL
    };
    const res = await checkFullLoanApi(payload);

    const { data } = res || {};
    if (DECISION_V02_RES_DECISION.APPROVE === data?.decision) {
      await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.PASSED_CHECK_FULLLOAN);
      return true;
    }
    if (DECISION_V02_RES_DECISION.WAIT_CIC === data?.decision) {
      // wait cic
      await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.WAITING_CIC_RESULT);
      return false;
    } else {
      await kunnRepo.updateKUStatusV2(kunnId, KUNN_STATUS.REFUSED);
      await callbackPartner(contractNumber, kunnData.partner_code, CALLBACK_STAUS.REJECT_KUNN, null, null, kunnId);
      return false;
    }
  } catch (error) {
    console.log(`[ANTI_FRAUD][checkFullLoan] contract number :${kunnId}, error ${error}`);
    return false;
  }
}
const checkContractProgressGw = async (contractData, step) => {
  try {
    const data = await checkContractProgressApi(
      {
        requestId: contractData.request_id,
        partnerCode: contractData.partner_code,
        customerName: contractData.cust_full_name,
        phoneNumber: contractData.phone_number1,
        dateOfBirth: contractData.birth_date,
        contractNumber: contractData.contract_number,
        idNumber: contractData.id_number,
        issueDate: contractData.id_issue_dt,
        issuePlace: contractData.id_issue_place,
        gender: contractData.gender === 'Nam' ? 'M' : 'F',
        contractType: contractData.contract_type,
      },
      step
    );
    if (!data.code || data.code !== STATUS.ELIGIBLE) {
      await loanContractRepo.updateLoanContract({
        contract_number: contractData.contract_number,
        status: STATUS.NOT_ELIGIBLE,
        updated_date: new Date(),
      });
      await Promise.all([
        callbackPartner(contractData.contract_number, contractData.partner_code, CALLBACK_STAUS.NOT_ELIGIBLE),
        crmService.rejectContract(global.config, contractData.contract_number),
      ])
      return false;
    }
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkContractProgress error ${contractData.contract_number} error ${error}`);
    return false;
  }
};

const checkContractActiveGw = async (contractData, step) => {
  try {
    const data = await checkContractActiveApi(
      {
        requestId: contractData.request_id,
        partnerCode: contractData.partner_code,
        customerName: contractData.cust_full_name,
        phoneNumber: contractData.phone_number1,
        dateOfBirth: contractData.birth_date,
        contractNumber: contractData.contract_number,
        idNumber: contractData.id_number,
        issueDate: contractData.id_issue_dt,
        issuePlace: contractData.id_issue_place,
        gender: contractData.gender === 'Nam' ? 'M' : 'F',
        contractType: contractData.contract_type,
      },
      step
    );
    if (!data.code || data.code !== STATUS.ELIGIBLE) {
      await loanContractRepo.updateLoanContract({
        contract_number: contractData.contract_number,
        status: STATUS.NOT_ELIGIBLE,
        updated_date: new Date(),
      });

      await Promise.all([
        callbackPartner(contractData.contract_number, contractData.partner_code, CALLBACK_STAUS.NOT_ELIGIBLE),
        crmService.rejectContract(global.config, contractData.contract_number),
      ])
      return false;
    }
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkContractActiveGw error ${contractData.contract_number} error ${error}`);
    return false;
  }
};

const checkEligibleKunnFinV = async (kunnId) => {
  try {
    const kunnData = await kunnRepo.getKunnData(kunnId);
    if (!kunnData) {
      throw new Error(`kunn ${kunnId} not found`);
    }
    const contractNumber = kunnData.contract_number;
    const [loanContractData, loanContractDocumentsData] = await Promise.all([
      loanContractRepo.getLoanContract(contractNumber),
      documentRepo.findByContractAndTypes(contractNumber, [DOC_TYPE.ID_CARD.FRONT, DOC_TYPE.ID_CARD.BACK, DOC_TYPE.SELFIE])
    ]);
    const { frontIdFileKey, backIdFileKey, selfieFileKey } = loanContractDocumentsData.reduce((o, el) => {
      if (el.doc_type === DOC_TYPE.ID_CARD.FRONT) {
        o.frontIdFileKey = el.file_key;
      } else if (el.doc_type === DOC_TYPE.ID_CARD.BACK) {
        o.backIdFileKey = el.file_key;
      } else if (el.doc_type === DOC_TYPE.SELFIE) {
        o.selfieFileKey = el.file_key;
      }
      return o;
    }, {
      frontIdFileKey: '',
      backIdFileKey: '',
      selfieFileKey: '',
    });
    const requestId = uuid.v4();
    const payload = {
      request_id: requestId,
      partner_code: kunnData.partner_code + `_WD`,
      customer_name: loanContractData.cust_full_name,
      phone_number: loanContractData.phone_number1,
      date_of_birth: loanContractData.birth_date
        ? moment(loanContractData.birth_date).format("yyyy-MM-DD")
        : null,
      contract_number: kunnId,
      id_number: loanContractData.id_number,
      issue_date: loanContractData.id_issue_dt
        ? moment(loanContractData.id_issue_dt).format("yyyy-MM-DD")
        : null,
      issue_place: loanContractData.id_issue_place,
      tem_province: "",
      gender: loanContractData.gender,
      email: loanContractData.email,
      employment_type: loanContractData.empl_type, // check
      product_type: null,
      loan_amount: loanContractData.request_amt,
      loan_tenor: loanContractData.request_tenor,
      sale_channel: loanContractData.channel,
      dsa_agent_code: null,
      case_creation_time: null, // check
      is_insert: true,
      scan_data: null,
      channel: loanContractData.channel, // FINV
      product_code: loanContractData.channel, // FINV
      s3_front_id_card_url: frontIdFileKey,
      s3_back_id_card_url: backIdFileKey,
      s3_selfie_image_url: selfieFileKey,
      reference_persons: [
        {
          customer_name: loanContractData.reference_name_1,
          id_number: loanContractData.reference_id_number_1,
        },
        {
          customer_name: loanContractData.reference_name_2,
          id_number: loanContractData.reference_id_number_2,
        },
      ],
    };
    const eligibleRes = await eligibleApiCash(payload);
    if (!eligibleRes || eligibleRes?.code !== ELIGIBLE_STATUS.ELIGIBLE) {
      await kunnRepo.updateKUStatusV2(kunnId, STATUS.REFUSED);
      await callbackPartner(contractNumber, kunnData.partner_code, CALLBACK_STAUS.REJECT_KUNN, null, null, kunnId);
      return false
    }
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkEligibleKunnFinV error ${kunnId} error ${error}`);
    return false;
  }
};

const checkBlacklistGw = async ({ contractNumber }) => {
  try {
    const loan = await loanContractRepo.findLoan(contractNumber);
    if (!loan) {
      console.log(`${contractNumber}| ${new Date()} | checkBlacklistGw error | cannot get loan_contract`)
      return false;
    }
    const checkResult = await checkBlacklist({
      request_id: loan.request_id,
      partner_code: loan.partner_code,
      phone_number: loan.phone_number1,
      contract_number: contractNumber,
      id_number: loan.id_number,
      tax_code: loan.tax_id || loan.registration_number || null,
      email: loan.email || loan.sme_email
    });

    if (!checkResult?.success) {
      console.log(`${contractNumber}| ${new Date()} | processBlacklistCheckResult error | cannot get response`)
      return false
    }

    const { data } = checkResult || {};
    if (!['ELIGIBLE', 'NOT_ELIGIBLE'].includes(data?.code)) {
      console.log(`${contractNumber}| ${new Date()} | processBlacklistCheckResult error | decision code not valid`);
      return false;
    }

    if (data?.code == 'NOT_ELIGIBLE') {
      await loanContractRepo.updateLoanContract({
        contract_number: contractNumber,
        status: STATUS.REFUSED,
        updated_date: new Date(),
        rejection_reason: data?.error_code || 'CHECK_BLACKLIST',
        cancelled_at: new Date()
      });
      //handle callback 3P here
      await Promise.all([
        callbackPartner(contractNumber, loan.partner_code, CALLBACK_STAUS.NOT_ELIGIBLE),
        crmService.rejectContract(global.config, contractNumber),
      ])
      return false;
    }
    return true;
  } catch (error) {
    console.log(`[ANTI_FRAUD]checkBlacklistGw error ${contractNumber} error ${error}`);
    return false;
  }
}

const handleCallbackCicIndividual = async(payload) => {
  const { rjSubCode, contractNumber, reasonCode, decisionCode, groupReason } = payload;
  const kunnData = await kunnRepo.getKunnData(contractNumber);
  if(!kunnData || kunnData.status !== KUNN_STATUS.WAITING_CIC_RESULT) {
    throwBadReqError("body", "kunnData is not valid or invalid WAITING_CIC_RESULT status")
  }
  if (!['CANCEL', 'APPROVE', 'REJECT'].includes(decisionCode)) {
    throwBadReqError(
      "body",
      "decisionCode is not valid",
      MISA_ERROR_CODE.E400
    );
  }
  if(decisionCode === 'CANCEL') {
    await kunnRepo.update(kunnData.kunn_id, {
      status: KUNN_STATUS.CANCELLED,
      cancelled_reason: reasonCode || rjSubCode || groupReason || 'CIC_CHECK_FULL_LOAN',
      cancelled_by: 'CIC_CHECK_FULL_LOAN',
    });
    return;
  }

  if(decisionCode === 'REJECT') {
    await kunnRepo.update(kunnData.kunn_id, {
      status: KUNN_STATUS.REFUSED,
      cancelled_reason: reasonCode || rjSubCode || groupReason || 'CIC_CHECK_FULL_LOAN',
      cancelled_by: 'CIC_CHECK_FULL_LOAN',
    });
    return;
  }

  if(decisionCode === 'APPROVE') {
    await kunnRepo.updateKUStatusV2(contractNumber, KUNN_STATUS.PASSED_CHECK_FULLLOAN);
    if([PARTNER_CODE.FINV].includes(kunnData.partner_code)) {
      await workflowRouter.routing({
        currentTask: KUNN_WORKFLOW.CHECK_FULL_LOAN_KUNN,
        partnerCode: kunnData.partner_code,
        contractNumber: kunnData.contract_number,
        kunnId: kunnData.kunn_id,
        workflowCode: "FINV_KUNN"
      });
    }
  }
}

module.exports = {
  checkEligibleSme,
  checkEligibleSmeV2,
  checkEligibleApiIndividual,
  checkS37Sme,
  checkCicDetailsSme,
  handleCallbackCicDetailsSme,
  checkFullLoan,
  checkConsent,
  handleCallbackCic,
  ocrIdCard,
  checkNfc,
  checkNfcFinv,
  checkEKYC,
  checkCicB11t,
  checkCicB11Sme,
  checkRenewDate,
  checkContractStatus,
  checkRenewDateByIdentityCard,
  checkBlacklist,
  checkEligibleSmeV2Gw,
  checkCicDetailSmeGw,
  checkCicB11tSmeGw,
  checkContractRenewDateGw,
  checkWhitelistFullLoanGw,
  checkWhitelistFullLoanHm,
  checkModelGw,
  checkWhitelistAf1,
  checkCicDetailKunn,
  checkEligibleKunn,
  checkRenewDateKunn,
  checkB11TKunn,
  checkCicB11tInvidual,
  checkModelFinvGw,
  checkModelHm,
  checkBlacklistKunn,
  checkEkycKunn,
  checkFullLoanKunn,
  checkContractProgressGw,
  checkContractActiveGw,
  checkEligibleKunnFinV,
  checkBlacklistGw,
  checkRenewDateAll,
  handleCallbackCicIndividual,
};
