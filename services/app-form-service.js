const {convertBody} = require("../utils/converter/convert");
const {REQUEST_TYPE,CONTRACT_TYPE, PARTNER_CODE} = require("../const/definition")
const loanContractRepo = require("../repositories/loan-contract-repo")
const turnoverRepo = require("../repositories/turnover-repo")
const offerRepo = require("../repositories/offer")
const {routing} = require("./workflow-service")
const utils = require("../utils/helper")

class baseBasciInfo{
    constructor(req,res) {
        this.body = req.body
        this.res = res
    }

    async recieveBasic() {
        await this.#convertBasicBody()
        await this.#genContractNumber()  
        await this.#insertLoanContract() 
    }  

    async #convertBasicBody() {
        const newBody = convertBody(this.body,REQUEST_TYPE.BASIC,global.convertCache)
        this.convertedBody = newBody
    }

    async #genContractNumber() {
        const contractNumber = await loanContractRepo.genContractNumber()
        this.contractNumber = contractNumber
        this.convertedBody.contract_number = contractNumber
    } 

    async #insertLoanContract() {
        return await loanContractRepo.insertLoanContract(this.convertedBody)
    }

    async saveLoanBranchAddress() {
        if(!utils.isNullOrEmpty(this.convertedBody.branchAddress)) {
            return await loanContractRepo.saveBranchAddress(this.contractNumber,this.convertedBody.branchAddress)
        }
    }

    async saveTurnoverTransaction() {
        return await Promise.all([turnoverRepo.saveTurnOrTrans(global.poolWrite,this.convertedBody.turnover,"turnover",this.contractNumber),
            turnoverRepo.saveTurnOrTrans(global.poolWrite,this.convertedBody.turnover,"real_turn_over",this.contractNumber),
            turnoverRepo.saveTurnOrTrans(global.poolWrite,this.convertedBody.transaction,"transaction",this.contractNumber)])
    }
    
    async createLoanMainScore() {
        return await offerRepo.createLoanMainScore(this.contractNumber)
    }

    async createLoanScore() {
        return await offerRepo.createKOVLoanScore(this.contractNumber)
    }
    
    async saveLoanAcountTrading() {
        if(!utils.isNullOrEmpty(this.convertedBody.accountTrading)) {
            return loanContractRepo.saveLoanAcountTrading(this.contractNumber,this.convertedBody.accountTrading)
        }
    }

    async callWorkflow() {
        this.convertedBody.currentTask = 'BASIC'
        routing(this.convertedBody)
    }

    responseBasicSucess() {
        return this.res.status(200).json({
            "statusCode" : "200",
            "body" : {
                "code" : "RECEIVE",
                "message" : "The application is received",
                "data": {
                    "conractNumber": this.contractNumber,
                    "partnerCode": this.partnerCode
                }

            }
        })
    }

    responseBasicError() {
        return this.res.status(500).json({
            code : -1,
            msg : "error"
        })
    }
}

class baseFullloan {
    constructor(req,res) {
        this.body = req.body
        this.res = res
    }

    static async recieveFullloan() {

    }

    /*
    static async #convertFullloan() {

    }

    static async #responseFullLoanSucess() {

    }
    */
}

class mcCreditLineBasicInfo extends baseBasciInfo {
    constructor(req,res,partnerCode) {
        super(req,res)
        this.partnerCode = partnerCode
    }   

    async recieveBasic() {
        try {
            await super.recieveBasic()
            await Promise.all([
                super.saveLoanBranchAddress(),
                super.saveTurnoverTransaction(),
                super.createLoanMainScore(),
                super.createLoanScore(),
                super.saveLoanAcountTrading()
            ])
            super.callWorkflow()
            super.responseBasicSucess()
        }
        catch(err) {
            console.log(err)
            super.responseBasicError()
        }
    }
}

module.exports = {
    mcCreditLineBasicInfo
}