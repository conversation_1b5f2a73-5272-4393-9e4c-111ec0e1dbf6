const BizziLoanLender = require("./bizzi-loan-lender");
const uuid = require("uuid");
const { getLenderChangeRequestDetail, updateLenderChangeRequest, createLenderChangeRequest, getLatestLenderChangeRequest, getLatestLenderChangeRequestList, updateStatusOfListChangeRequest, updateStatusOfListChangeRequestDetails, checkChangeRequestDetailsHasChange, checkChangeRequestDetailsHasResubmit } = require("../repositories/lender-change-request-repo");

const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const { LENDER_REQUEST_TYPE, LENDER_REFERENCE_TYPE } = require("../const/variables-const");
const { updateKUStatusV2 } = require("../repositories/kunn-repo");
const { updateLoanContract } = require("../repositories/loan-contract-repo");
const { STATUS } = require("../const/caseStatus");
const { BadRequestResponseV2 } = require("../base/response");
const { TASK_FLOW } = require("../const/definition");
const loggingRepo = require("../repositories/logging-repo");
const { find, updateData } = require("../utils/sqlHelper");

class BizziLimitLoanLender extends BizziLoanLender {
  constructor({ referenceNumber, data, partnerCode, table, url, changeRequest, changeRequestDetails, groupType }) {
    super({
      referenceNumber,
      data,
      partnerCode,
      table,
      url,
      changeRequest,
      changeRequestDetails,
      groupType,
    });
    this.table = table || "loan_contract";
  }

}

module.exports = BizziLimitLoanLender;
