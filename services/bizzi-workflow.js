const { KUNN_STATUS } = require("../const/caseStatus");
const kunnRepo = require("../repositories/kunn-repo");
const { checkCicDetailKunn, checkEligibleKunn, checkRenewDateKunn, checkB11TKunn } = require("./anti-fraud");
const { callbackKunnBizzi } = require("./callback-service");
const { createLmsBizzKunn } = require("./lms-service");

async function checkTD1(body) {
    const { kunnId } = body;
    const kunnData = await kunnRepo.getKunnData(kunnId);
    if (!kunnData) {
        console.log(`[KUNN] KUNN not found for ID: ${kunnId}`);
        return false;
    }
    if (![KUNN_STATUS.PASSED_MANUAL_PROCESS, KUNN_STATUS.RECEIVED, KUNN_STATUS.RESUBMITED].includes(kunnData.status)) {
        console.log(`[KUNN] KUNN is not in PASSED_MANUAL_PROCESS or RECEIVED status for ID: ${kunnId}`);
        return false;
    }
    const isPassedRenewDate = await checkRenewDateKunn(kunnId);
    if (!isPassedRenewDate) {
        
        return false;
    }

    const eligible = await checkEligibleKunn(kunnId);
    if (!eligible) {
        callbackKunnBizzi(kunnId);
        return false;
    }
    await kunnRepo.updateKUNNPassedTD1(kunnId);
    return true;
}

async function checkTD2(body) {
    const { kunnId } = body;
    const kunnData = await kunnRepo.getKunnData(kunnId);
    if (!kunnData) {
        console.log(`[KUNN] KUNN not found for ID: ${kunnId}`);
        return false;
    }
    if (![KUNN_STATUS.PASSED_MANUAL_PROCESS, KUNN_STATUS.APPROVED_CHANGE_REQUEST].includes(kunnData.status)) {
        console.log(`[KUNN] KUNN is not in PASSED_MANUAL_PROCESS or APPROVED_CHANGE_REQUEST status for ID: ${kunnId}`);
        return false;
    }
    const isB11TPassed = await checkB11TKunn(kunnId);
    if (!isB11TPassed) {
        callbackKunnBizzi(kunnId);
        return false;
    }
    const isPassCicDetail = await checkCicDetailKunn(kunnId);
    if (!isPassCicDetail) {
        callbackKunnBizzi(kunnId);
        return false;
    }
    let status = KUNN_STATUS.PASSED_TD2;
    await kunnRepo.updateKUStatus(kunnId, status);
    return true
}

async function signedTobeDisburseAndCreateLms(kunnId) {
    if (!kunnId) return false;
    const kunnData = await kunnRepo.getKunnData(kunnId);
    if (!kunnData) return false;
    if (kunnData.status !== KUNN_STATUS.EVF_SIGNED) {
        return false;
    }
    // Create LMS
    await createLmsBizzKunn(kunnId);
    return true;
}

module.exports = {
    checkTD1,
    checkTD2,
    signedTobeDisburseAndCreateLms,
}