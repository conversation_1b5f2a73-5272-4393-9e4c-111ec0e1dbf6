const common = require("../utils/common");
const loggingRepo = require("../repositories/logging-repo");
const loanContractRepo = require("../repositories/loan-contract-repo");
const kunnRepo = require("../repositories/kunn-repo");
const documentRepo = require("../repositories/document");
const loanEsigningRepo = require("../repositories/loan-esigning");
const uuid = require("uuid");
const { PARTNER_CODE, TYPE_COLLECTION, SERVICE_NAME, TASK_FLOW, WORKFLOW_CODE } = require("../const/definition");
const { CALLBACK_STAUS, STATUS, MISA_CALLBACK_STATUS, KUNN_STATUS } = require("../const/caseStatus");
const smsService = require("../utils/smsService");
const offerRepo = require("../repositories/offer");
const s3Service = require("../upload_document/s3-service");
const lmsService = require("../utils/lmsService");
const { serviceEndpoint } = require("../const/config");
const querystring = require("querystring");
const { encryptDataMisa, signCompactJws } = require("../utils/encrypt/encrypt");
const { callbackKunnActApi, callbackLimitApi, callbackReceivedPaymentApi } = require("../apis/misa-api");
const { snakeToCamel, genRequestId } = require("../utils/helper");
const loanContractDocumentRepo = require("../repositories/document");
const { checkAvailableAmountApi, getKunnInstallmentApi, checkRemainPrinAmountApi } = require("../apis/lms-api");
const utilsCrmService = require("../utils/crmService");
const moment = require("moment");
const { DOC_GROUP, FINV_CALLBACK_OPERATION } = require("../const/variables-const");
const bizzKunnStatus = require("../KUNN/bizzi-kunn-status");
const { findOne } = require("../utils/sqlHelper");
const { normalizeJson } = require("../utils/converter/normalize");
const { ENDPOINT_CONST } = require("../const/endpoint-const");

class BaseCallbacker {
  constructor(contractNumber) {
    this.contractNumber = contractNumber;
  }

  callbackCancel() {
    console.log(`${this.contractNumber} : CALLBACK CANCEL`);
  }

  callbackReject() {
    console.log(`${this.contractNumber} : CALLBACK REJECT`);
  }

  callbackResubmit() {
    console.log(`${this.contractNumber} : CALLBACK RESUBMIT`);
  }

  callbackAltOffer() {
    console.log(`${this.contractNumber} : CALLBACK ALT OFFER`);
  }

  callbackApproved() {
    console.log(`${this.contractNumber} : CALLBACK APPROVED`);
  }

  callbackActivated() {
    console.log(`${this.contractNumber} :  CALLBACK ACTIVATED`);
  }

  callbackTerminated() {
    console.log(`${this.contractNumber} : CALLBACK TERMINATED`);
  }

  callbacPassBasic() {
    console.log(`${this.contractNumber} : CALLBACK PASS A1`);
  }

  callbackSigned() {
    console.log(`${this.contractNumber} : CALLBACK SIGNED`);
  }

  callbackApproveSignKunn() {
    console.log(`${this.contractNumber} : CALLBACK START SIGNING`);
  }

  callbackSignedKunn() {
    console.log(`${this.kunnNumber} : CALLBACK SIGNED KUNN`);
  }

  callbackActiveKunn() {
    console.log(`${this.kunnNumber} : CALLBACK ACTIVE KUNN`);
  }

  callbackActiveKunnV2() {
    console.log(`${this.kunnNumber} : CALLBACK ACTIVE KUNN V2`);
  }

  callbackWaitingToApprove() {
    console.log(`${this.contractNumber} : CALLBACK WAITING APPROVE`);
  }

  callbackNotEligible() {
    console.log(`${this.contractNumber} : CALLBACK NOT ELIGIBLE`);
  }
  updatePayment() {
    console.log(`${this.kunnNumber} : UPDATE PAYMENT`);
  }
  callbackReceivedPayment() {
    console.log(`${this.kunnNumber} : CALLBACK RECEIVED PAYMENT`);
  }
  callbackContractStatus() {
    console.log(`${this.contractNumber} : CALLBACK CONTRACT STATUS`);
  }
  callbackKunnStatus() {
    console.log(`${this.contractNumber} : CALLBACK KUNN STATUS`);
  }

  callbackExpiredLimit() {
    console.log(`${this.contractNumber} : CALLBACK EXPIRED LIMIT`);
  }

  callbackCancelKunn() {
    console.log(`${this.kunnNumber} : CALLBACK CANCEL KUNN`);
  }

  callbackCreditApproveAf1() {
    console.log(`${this.contractNumber} : CALLBACK CREDITLINE APPROVE AF1`);
  }

  callbackCreditSigningInProgress() {
    console.log(`${this.contractNumber} : CALLBACK CREDITLINE SIGNING IN PROGRESS`);
  }

  callbackKunnSigningInProgress() {
    console.log(`${this.kunnNumber} : CALLBACK KUNN SIGNING IN PROGRESS`);
  }

  callbackCreditProcessAf2() {
    console.log(`${this.contractNumber} : CALLBACK CREDITLINE PROCESS AF2`);
  }

  callbackCreditApproveAf2() {
    console.log(`${this.contractNumber} : CALLBACK CREDITLINE APPROVE AF2`);
  }

  callbackCreditProcessAf3() {
    console.log(`${this.contractNumber} : CALLBACK CREDITLINE PROCESS AF3`);
  }

  callbackCompletedSign() {
    console.log(`${this.contractNumber} : CALLBACK COMPLETED SIGN`);
  }

  callbackRejectKunn() {
    console.log(`${this.kunnNumber} : CALLBACK REJECT KUNN`);
  }
}

class VplCallbacker extends BaseCallbacker {
  constructor(contractNumber) {
    super(contractNumber);
    this.partnerCode = "VPL";
    this.token = "";
    this.apiKey = "F9rxVQlvAtvIfSha";
    this.clientId = "vay_pro_evn";
    this.clientSecret = "NHFJU%$#dkv124";
    this.getTokenUrl = global.config.data.partnerCallback.vdsTokenUrl;
    this.updateStatusUrl = global.config.data.partnerCallback.vdsCallbackUrl;
  }

  async getToken() {
    const body = {
      clientId: this.clientId,
      clientSecret: this.clientSecret,
    };
    const rs = await common.postApiV2(this.getTokenUrl, body, {
      apiKey: this.apiKey,
    });
    if (rs.data.status.code == "SUCCESS") {
      return rs.data.data.authenticationToken;
    } else {
      return false;
    }
  }

  async callbackVPL(body) {
    const token = await this.getToken();
    if (!token) {
      console.log("callback to VDS result : get token error");
    }
    const headers = {
      Authorization: "Bearer " + token,
      "Content-Type": "application/json",
      apiKey: this.apiKey,
    };
    body.request_id = "EC" + uuid.v4();
    const rs = await common.postApiV2(this.updateStatusUrl, body, headers);
    loggingRepo.saveCallback(this.contractNumber, body, rs);
    loggingRepo.updateCallback(this.contractNumber, body);
    return rs;
  }

  async callbackCancel() {
    super.callbackCancel();
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: CALLBACK_STAUS.CANCELLED,
      message: "The application is cancelled",
    };
    return await this.callbackVPL(callbackBody);
  }

  async callbackReject() {
    super.callbackReject();
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: CALLBACK_STAUS.REJECTED,
      message: "FAIL_POLICY",
    };
    return await this.callbackVPL(callbackBody);
  }

  async callbackResubmit(bodyResubmit) {
    super.callbackResubmit();
    const resubmitList = [];
    for (let i = 0; i < bodyResubmit.length; i++) {
      const element = bodyResubmit[i];
      resubmitList.push({
        doc_type: await documentRepo.getDocType(element.docId),
        mistake_code: element.mistakeCode,
        mistake_des: element.mistakeDes || " ",
      });
    }
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: CALLBACK_STAUS.RESUBMIT,
      message: "The documents are required to resubmit",
      reject_reason: "",
      list_doc_resubmit: resubmitList,
    };
    return await this.callbackVPL(callbackBody);
  }

  async callbackAltOffer() {
    super.callbackAltOffer();
    const offer = await offerRepo.getSelectedOffer(this.contractNumber);
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: CALLBACK_STAUS.ALT_OFFER,
      message: "The offer is not matched with the request, customer need to select new offer",
      reject_reason: "",
      offer_list: [
        {
          offer_id: offer.id,
          offer_amount: parseInt(offer.offer_amt),
          interest_rate: parseFloat(parseInt((offer.int_rate * 1000) / 12) / 10),
          monthly_installment: parseFloat(offer.instal_amt),
          offer_tenor: offer.tenor,
          offer_type: "N",
        },
      ],
    };
    return await this.callbackVPL(callbackBody);
  }

  async callbackActivated() {
    super.callbackActivated();
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: CALLBACK_STAUS.ACTIVATED,
      message: "ACTIVATED",
    };
    return await this.callbackVPL(callbackBody);
  }

  async callbackTerminated() {
    super.callbackTerminated();
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: CALLBACK_STAUS.TERMINATED,
      message: "TERMINATED",
    };
    return await this.callbackVPL(callbackBody);
  }

  async callbackApproved() {
    super.callbackApproved();
    const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
    const callbackBody = {
      contract_number: this.contractNumber,
      application_status: "APPROVED",
      message: "To be signed (Signing process to be called)",
      reject_reason: "",
      data: {
        channel: contractData.channel,
        partner_code: contractData.partner_code,
        customer_name: contractData.cust_full_name,
        identity_card_id: contractData.id_number,
        phone_number: contractData.phone_number1,
      },
    };
    return await this.callbackVPL(callbackBody);
  }
}

class KovCallbacker extends BaseCallbacker {
  constructor(contractNumber, withdrawId = undefined, targetType = "credit-creation") {
    super(contractNumber);
    this.withdrawId = withdrawId;
    this.targetType = targetType;
    this.partnerCode = "KOV";
    this.token = global.config.data.partnerToken.KOV;
    this.updateStatusUrl = global.config.data.partnerCallback.KOV;
    this.isEnable = global.config.data.partnerCallback.enable;
  }

  async callbackKOV(body) {
    const headers = {
      Authorization: "Bearer " + this.token,
    };
    body.requestId = this.partnerCode + new Date().getTime();
    if (this.isEnable === "true") {
      const callbackRs = await common.postApiV2(this.updateStatusUrl, body, headers);
      return callbackRs;
    }
    return false;
  }

  async callbackCancel() {
    super.callbackCancel();
    const callbackBody = {
      contractNumber: this.contractNumber,
      withdrawId: this.withdrawId,
      code: "CANCELLED",
      targetType: this.targetType,
      msg: "Cancelled",
    };
    return await this.callbackKOV(callbackBody);
  }

  async callbackReject() {
    super.callbackReject();
    const callbackBody = {
      contractNumber: this.contractNumber,
      withdrawId: this.withdrawId,
      code: "REJECTED",
      targetType: this.targetType,
      msg: "Not meet policy requirement",
      data: {
        partnerCode: this.partnerCode,
        rejectReason: "REJ_POLICY",
      },
    };
    return await this.callbackKOV(callbackBody);
  }

  async callbackResubmit(bodyResubmit) {
    super.callbackResubmit();
    const listDocResubmit = [];
    for (let i = 0; i < bodyResubmit.length; i++) {
      const element = bodyResubmit[i];
      listDocResubmit.push({
        docName: await documentRepo.getDocType(element.docId),
        mistakeCode: element.mistakeCode,
        mistakeDes: element.mistakeDes || " ",
      });
    }
    const callbackBody = {
      contractNumber: this.contractNumber,
      code: CALLBACK_STAUS.RESUBMIT,
      resubmitRole: "CP",
      targetType: this.targetType,
      message: "The documents are required to resubmit",
      resubmit: {
        listDocResubmit: listDocResubmit,
      },
    };
    const isValidCallbackKov = await documentRepo.isValidCallbackResubmitKOV(this.contractNumber);
    if (isValidCallbackKov) {
      return await this.callbackKOV(callbackBody);
    }
  }

  async callbackAltOffer() {
    super.callbackAltOffer();
    const offerRs = await offerRepo.getSelectedOffer(this.contractNumber);
    const offerList = [];
    offerList.push({
      offerId: offerRs.id,
      offerAmount: offerRs.offer_amt,
      offerTenor: offerRs.tenor,
      offerIr: offerRs.int_rate,
    });

    const callbackBody = {
      contractNumber: this.contractNumber,
      code: CALLBACK_STAUS.ALT_OFFER,
      targetType: this.targetType,
      msg: "The offer is not matched with the request, customer need to select new offer",
      offer: offerList,
    };
    return await this.callbackKOV(callbackBody);
  }

  async callbackSignedKunn() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      withdrawId: this.withdrawId,
      code: "SIGNED",
      targetType: this.targetType,
      msg: "Signed",
    };
    return await this.callbackKOV(callbackBody);
  }

  async callbackActiveKunn() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      withdrawId: this.withdrawId,
      code: "ACTIVE",
      targetType: this.targetType,
      msg: "Activated",
    };
    return await this.callbackKOV(callbackBody);
  }
}

class VtpCallbacker extends BaseCallbacker {
  constructor(contractNumber) {
    super(contractNumber);
    this.token = "";
    this.updateStatusUrl = global.config.data.partnerCallback.codUpdateStatusCallback;
  }

  async initialize() {
    const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
    this.cusId = contractData.third_party_cust_id;
    this.phoneNumber = contractData.phone_number1;
    this.isSigned = await loanContractRepo.validSigned(this.contractNumber);
    this.isValidCallback = await documentRepo.isValidCallbackResubmitKOV(this.contractNumber);
  }

  async callbackCOD(body) {
    try {
      body.requestId = "EC" + uuid.v4();
      const rs = await common.postApiV2(this.updateStatusUrl, body);
      if (rs.data.status != 200) {
        return false;
      }
      return true;
    } catch (err) {
      common.log(`callback COD error : ${err.message}`, body.contractNumber);
      return false;
    }
  }

  async updateStatusCOD(status) {
    const body = {
      contractNumber: this.contractNumber,
      requestId: "EC" + uuid.v4(),
      loanStatus: status,
      cusId: this.cusId,
    };
    return await this.callbackCOD(body);
  }

  async callbackCancel() {
    return await this.updateStatusCOD(CALLBACK_STAUS.CANCELLED);
  }

  async callbackReject() {
    return await this.updateStatusCOD(CALLBACK_STAUS.REJECTED);
  }

  async callbackResubmit() {
    if (!this.isSigned && this.isValidCallback) {
      return await this.updateStatusCOD(CALLBACK_STAUS.RESUBMIT);
    } else {
      return smsService.sendResubmit(contractNumber, contractData.phone_number1);
    }
  }

  async callbackAltOffer() {
    return await this.updateStatusCOD(CALLBACK_STAUS.APPROVED);
  }

  async callbackApproved() {
    return await this.updateStatusCOD(CALLBACK_STAUS.APPROVED);
  }

  async callbackTerminated() {
    return await this.updateStatusCOD(CALLBACK_STAUS.TERMINATED);
  }

  async callbackActivated() {
    return await this.updateStatusCOD(CALLBACK_STAUS.ACTIVE);
  }
}

class MisaCallbacker extends BaseCallbacker {
  constructor(contractNumber, withdrawId, body = {}) {
    super(contractNumber);
    this.partnerCode = PARTNER_CODE.MISA;
    this.token = "";
    this.apiKey = "F9rxVQlvAtvIfSha";
    this.clientId = global.config.data.partnerCallback.misaTokenClientId;
    this.clientSecret = global.config.data.partnerCallback.misaTokenClientSecret;
    this.grantType = global.config.data.partnerCallback.misaTokenGrantType;
    this.getTokenUrl = global.config.data.partnerCallback.misaTokenUrl;
    this.updateStatusUrl = global.config.data.partnerCallback.misaUpdateUrl;
    this.updateStatusKunnUrl = global.config.data.partnerCallback.misaUpdateKunnUrl;
    this.kunnNumber = withdrawId;
    this.body = body;
  }

  async initialize() {
    const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
    this.contractData = contractData;
  }

  getToken = async () => {
    try {
      const url = global.config.data.partnerCallback.misaTokenUrlV2;
      const form = {
        grant_type: global.config.data.partnerCallback.misaTokenGrantType,
        client_id: global.config.data.partnerCallback.misaTokenClientId,
        client_secret: global.config.data.partnerCallback.misaTokenClientSecret,
      };
      const formBody = querystring.stringify(form);

      const response = await common.postApiV2(url, formBody, {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });
      return response?.status === 200 ? response?.data?.access_token : undefined;
    } catch (e) {
      console.error(e);
      return false;
    }
  };

  async callbackMisa(body) {
    const token = await this.getToken();
    if (!token) {
      console.log("callback to MISA result : get token error");
    }
    const headers = {
      Authorization: "Bearer " + token,
      "Content-Type": "application/json",
      apiKey: this.apiKey,
    };

    body.request_id = "EC" + uuid.v4();
    const rs = await common.putAPIV2(this.updateStatusUrl, body, headers);
    loggingRepo.saveCallback(this.contractNumber, body, rs);
    loggingRepo.updateCallback(this.contractNumber, body);
    return rs;
  }

  async callbackKunnMisa(body) {
    const token = await this.getToken();
    if (!token) {
      console.log("callback to MISA result : get token error");
    }
    const headers = {
      Authorization: "Bearer " + token,
      "Content-Type": "application/json",
      apiKey: this.apiKey,
    };

    body.request_id = "EC" + uuid.v4();
    console.log(`[callbackKunnMisa] url ${this.updateStatusKunnUrl}, payload ${JSON.stringify(body)}`);
    const rs = await common.postApiV2(this.updateStatusKunnUrl, body, headers);
    console.log(`[callbackKunnMisa] rs : ${JSON.stringify(rs || {})},`);
    return rs;
  }

  async callbackCancel() {
    super.callbackCancel();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: MISA_CALLBACK_STATUS.CANCELLED,
      loanPackageInfo: {
        loanAmount: this.contractData.request_amt,
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackReject() {
    super.callbackReject();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: MISA_CALLBACK_STATUS.REFUSE,
      loanPackageInfo: {
        loanAmount: this.contractData.request_amt,
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackActivated() {
    super.callbackActivated();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: MISA_CALLBACK_STATUS.ACTIVE,
      loanPackageInfo: {
        loanAmount: this.contractData.approval_amt,
        interestRate: this.contractData.approval_int_rate * 100,
        term: this.contractData.tenor,
        effectiveDateContract: this.contractData.start_date,
        contractExpirationDate: this.contractData.end_date,
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackSigned() {
    super.callbackSigned();
    const contractSignedPath = await loanEsigningRepo.getSingedContract(this.contractNumber);
    const fileKey = "mc-credit" + contractSignedPath.split("mc-credit")[1];
    const file = (await s3Service.downloadFile(global.config.data, fileKey)).Body;
    const base64File = file.toString("base64");
    const fileSize = Buffer.byteLength(file);
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: MISA_CALLBACK_STATUS.CONTRACT_ELIGIBLE,
      loanPackageInfo: {
        creditContractFile: {
          fileValue: base64File,
          fileName: "HDTD.pdf",
          contentType: "application/pdf",
          fileSize: fileSize,
        },
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackBasic() {
    const callbackBody = {
      status: MISA_CALLBACK_STATUS.ELIGIBLE,
      contractNumber: this.contractNumber,
      loanPackageInfo: {
        loanAmount: this.contractData.approval_amt,
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackFullLoan() {
    const callbackBody = {
      status: MISA_CALLBACK_STATUS.FULLLOAN,
      contractNumber: this.contractNumber,
      loanPackageInfo: {
        loanAmount: this.contractData.approval_amt,
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackApproved() {
    const callbackBody = {
      status: MISA_CALLBACK_STATUS.APPROVE_SIGN,
      contractNumber: this.contractNumber,
      loanPackageInfo: {
        loanAmount: this.contractData.approval_amt,
        interestRate: this.contractData.approval_int_rate * 100,
        term: this.contractData.approval_tenor,
      },
    };
    return await this.callbackMisa(callbackBody);
  }

  async callbackResubmit(resubmitList) {
    const isRsmLCT = resubmitList.findIndex((item) => item.docName == "LCT");
    if (isRsmLCT !== -1) {
      const callbackBody = {
        status: MISA_CALLBACK_STATUS.RESUBMIT_CONTRACT,
        contractNumber: this.contractNumber,
      };
      return await this.callbackMisa(callbackBody);
    }
  }

  async callbackCreatedDebt() {
    const kunnData = await kunnRepo.getKunnData(this.kunnNumber);
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      loanInfo: {
        withdrawAmount: kunnData.with_draw_amount,
        interestRate: kunnData.ir * 100,
        termFrom: "",
        termTo: "",
        term: kunnData.tenor,
      },
      debtContractStatus: MISA_CALLBACK_STATUS.CREATED_KUNN,
    };
    return await this.callbackKunnMisa(callbackBody);
  }

  async callbackSignedKunn() {
    await kunnRepo.getKunnData(this.kunnNumber);
    const contractSignedPath = await loanEsigningRepo.getSingedContract(this.kunnNumber);
    const fileKey = "mc-credit" + contractSignedPath.split("mc-credit")[1];
    const file = (await s3Service.downloadFile(global.config.data, fileKey)).Body;
    const base64File = file.toString("base64");
    const fileSize = Buffer.byteLength(file);
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      debtContractStatus: MISA_CALLBACK_STATUS.SIGNED_KUNN,
      loanInfo: {
        fileDebtContract: {
          fileValue: base64File,
          fileName: "KUNN.pdf",
          contentType: "application/pdf",
          fileSize: fileSize,
        },
      },
    };
    return await this.callbackKunnMisa(callbackBody);
  }

  async callbackActiveKunn({ availableAmount, lastestLoanAmount } = {}) {
    const kunnData = await kunnRepo.getKunnData(this.kunnNumber);
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      loanInfo: {
        loanAmount: kunnData.with_draw_amount,
        interestRate: kunnData.ir * 100,
        termFrom: kunnData.start_date || new Date(),
        termTo: kunnData.end_date,
        term: kunnData.tenor,
        disbursementDate: kunnData.start_date,
        remainingPrincipal: kunnData.with_draw_amount,
        remainingPayments: (await lmsService.getRemainingPayment(this.kunnNumber)) || null,
        payMonthly: (await lmsService.getInstallmentKunn(this.kunnNumber))[0].total,
      },
      debtContractStatus: MISA_CALLBACK_STATUS.ACTIVE_KUNN,
    };
    const rs = await this.callbackKunnMisa(callbackBody);
    setTimeout(() => {
      this.updatePayment({ availableAmount, lastestLoanAmount });
    }, 60000);
    return rs;
  }

  async callbackRejectKunn() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      debtContractStatus: MISA_CALLBACK_STATUS.REJECT_KUNN,
    };
    return await this.callbackKunnMisa(callbackBody);
  }

  async callbackCancelKunn() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      debtContractStatus: MISA_CALLBACK_STATUS.CANCEL_KUNN,
    };
    return await this.callbackKunnMisa(callbackBody);
  }

  async callbackApproveSignKunn() {
    super.callbackApproveSignKunn();
    const kunnData = await kunnRepo.getKunnData(this.kunnNumber);
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      loanInfo: {
        loanAmount: kunnData.with_draw_amount,
        interestRate: kunnData.ir * 100,
        termFrom: "",
        termTo: "",
        term: kunnData.tenor,
      },
      debtContractStatus: MISA_CALLBACK_STATUS.APPROVE_FOR_SIGN_KUNN,
    };
    return await this.callbackKunnMisa(callbackBody);
  }

  async callbackResubmitKU(resubmitList) {
    const isRsmPhoto = resubmitList.findIndex((item) => item.docName == "LSPC");
    const isRsmContract = resubmitList.findIndex((item) => item.docName == "LCTKU");
    let debtContractSubStatus;
    if (isRsmPhoto > -1 && isRsmContract == -1) {
      debtContractSubStatus = 1;
    } else if (isRsmPhoto == -1 && isRsmContract > -1) {
      debtContractSubStatus = 2;
    } else {
      debtContractSubStatus = 3;
    }
    const callbackBody = {
      contractNumber: this.contractNumber,
      debtContractNumber: this.kunnNumber,
      debtContractStatus: MISA_CALLBACK_STATUS.RESUBMIT_KUNN_CONTRACT,
      debtContractSubStatus: debtContractSubStatus, //DebtContractSubStatus=1 ảnh không hợp lệ.  DebtContractSubStatus=2 hợp đồng không hợp lệ. DebtContractSubStatus=3 ảnh và hợp đồng không hợp lệ
    };
    return await this.callbackKunnMisa(callbackBody);
  }
  async updatePayment({ availableAmount, lastestLoanAmount } = {}) {
    try {
      //update limit
      const contractNumber = this.contractNumber;
      const installment = await getKunnInstallmentApi(contractNumber);

      const limitPayload = {
        contractNumber,
        availableLimit: availableAmount,
        lastestLoanAmount: lastestLoanAmount,
        debtContracts: installment.map((e) => ({
          debtContractNumber: e.debtAckContractNumber,
          remainingPrincipal: Number(e.financialDetail?.totalPrinAmount ?? "0"),
        })),
      };
      const limitRs = await callbackLimitApi(this.kunnNumber, limitPayload);
      console.log(`[CALLBACK][MISA][updatePayment] payload ${JSON.stringify(limitPayload)}, res: ${JSON.stringify(limitRs)}`);
    } catch (error) {
      console.log(`[CALLBACK][updatePayment] kunnId: ${this.kunnNumber}, error ${error}`);
    }
  }

  async callbackExpiredLimit() {
    try {
      const contractNumber = this.contractNumber;
      const installment = await getKunnInstallmentApi(contractNumber);
      // const avalibleAmountRs = await checkAvailableAmountApi(contractNumber);
      // const availableAmount = Number(avalibleAmountRs?.avalibleAmount || 0);
      // const lastestLoanAmount = Number(avalibleAmountRs?.remainPrincipalAmount || 0);
      const lastestLoanAmount = installment.reduce((sum, item) => sum + (Number(item.financialDetail?.totalPrinAmount ?? "0")), 0);
      const limitPayload = {
        contractNumber,
        availableLimit: 0,
        lastestLoanAmount: lastestLoanAmount,
        debtContracts: installment.map((e) => ({
          debtContractNumber: e.debtAckContractNumber,
          remainingPrincipal: Number(e.financialDetail?.totalPrinAmount ?? "0"),
        })),
      };
      const limitRs = await callbackLimitApi(this.contractNumber, limitPayload);
      console.log(`[CALLBACK][MISA][callbackExpiredLimit] payload ${JSON.stringify(limitPayload)}, res: ${JSON.stringify(limitRs)}`);
    }
    catch (error) {
      console.log(`[CALLBACK][callbackExpiredLimit] contractNumber: ${this.contractNumber}, kunnId: ${this.kunnNumber}, error ${error}`);
    }
  }

  async callbackReceivedPayment() {
    try {
      //update limit
      const contractNumber = this.contractNumber;
      const kunnNumber = this.kunnNumber;
      const { paymentDate, paymentAmount } = this.body;
      const payload = {
        contractNumber,
        debtContractNumber: kunnNumber,
        paymentDate: moment(paymentDate).format("YYYY-MM-DD"),
        paymentAmount: Number(paymentAmount),
      };

      const rs = await callbackReceivedPaymentApi(this.kunnNumber, payload);
      console.log(`[CALLBACK][MISA][callbackReceivedPayment] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(rs)}`);
    } catch (error) {
      console.log(`[CALLBACK][callbackReceivedPayment] kunnId: ${this.kunnNumber}, error ${error}`);
    }
  }

  async callbackActiveKunnV2({ availableAmount, lastestLoanAmount, resultS3, encryptResultS3 } = {}) {
    const kunnData = snakeToCamel(await kunnRepo.getKunnData(this.kunnNumber));
    try {
      const esignContractDoc = snakeToCamel(await documentRepo.getDocByKunnAndType(this.kunnNumber, "LCTKU_ENC"));
      // const contractSigned = kunnData.esignedContract;
      const lttFileName = `LTT_${kunnData.kunnId}.pdf`;
      const lttFileNameEnc = `LTT_ENC_${kunnData.kunnId}.pdf.pgp`;
      const contractNumber = kunnData.contractNumber;
      if (!esignContractDoc.fileSize) {
        const esignFile = await s3Service.downloadFile(global.config.data, esignContractDoc.fileKey);
        esignContractDoc.fileSize = Buffer.byteLength(esignFile.Body);
      }
      await Promise.all([
        loanContractDocumentRepo.insert({
          contractNumber,
          kunnContractNumber: kunnData.kunnId,
          docType: "LTT_ENC",
          docId: uuid.v4(),
          fileKey: encryptResultS3?.Key,
          fileName: lttFileNameEnc,
          url: encryptResultS3?.Location,
          typeCollection: TYPE_COLLECTION.DOC_INTERNAL,
          fileSize: encryptResultS3?.fileSize,
          docGroup: DOC_GROUP.MISA_CONTRACT_KUNN,
        }),
        loanContractDocumentRepo.insert({
          contractNumber,
          kunnContractNumber: kunnData.kunnId,
          docType: "LTT",
          docId: uuid.v4(),
          fileKey: resultS3?.Key,
          fileName: lttFileName,
          url: resultS3?.Location,
          typeCollection: TYPE_COLLECTION.DOC_INTERNAL,
          fileSize: resultS3?.fileSize,
          docGroup: DOC_GROUP.MISA_CONTRACT_KUNN,
        }),
      ]);

      const lttFile = {
        type: "LTT",
        fileName: lttFileName,
        fileSize: Number(encryptResultS3?.fileSize),
        fileUrl: encryptResultS3?.Key,
        contentType: "application/pdf",
      };
      const contractFile = {
        type: "HDGN",
        fileName: `HDGN_${kunnData.kunnId}.pdf`,
        fileSize: Number(esignContractDoc.fileSize),
        fileUrl: esignContractDoc.fileKey,
        contentType: "application/pdf",
      };
      //
      const callbackBody = {
        contractNumber: contractNumber,
        debtContractNumber: this.kunnNumber,
        result: true,
        files: [lttFile, contractFile],
      };
      const rs = await callbackKunnActApi(this.kunnNumber, callbackBody);
      if (rs?.Success && rs?.StatusCode == 200) {
        //update limit
        setTimeout(() => {
          this.updatePayment({ availableAmount, lastestLoanAmount });
        }, 60000);
      }
    } catch (error) {
      console.log(`[CALLBACK][callbackActiveKunnV2] kunnId: ${this.kunnNumber}, error ${error}`);
    }
  }
}

class SmaCallbacker extends BaseCallbacker {
  constructor(contractNumber) {
    super(contractNumber);
    this.partnerCode = PARTNER_CODE.SMA;
    this.updateStatusUrl = global.config.basic["superMcMobileBe"][global.config.env] + serviceEndpoint.SMA.callBackMobile;
  }

  async initialize() {
    const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
    this.cust_id = contractData?.cust_id;
    this.contractData = contractData;
  }

  async callbackSma(body) {
    body.request_id = "EC" + uuid.v4();
    const rs = await common.postApiV2(this.updateStatusUrl, body);
    loggingRepo.saveCallback(this.contractNumber, body, rs);
    // loggingRepo.updateCallback(this.contractNumber,body)
    return rs;
  }

  async callbackCancel() {
    super.callbackCancel();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.CANCELLED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackReject() {
    super.callbackReject();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.REJECTED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackActivated() {
    super.callbackActivated();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.ACTIVE,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackApproveSignKunn() {
    super.callbackApproveSignKunn();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.WAITING_TO_BE_SIGNED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackSignedKunn() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: STATUS.SIGNED_TO_BE_DISBURED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackActiveKunn() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.ACTIVATED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackSigned() {
    super.callbackSigned();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.WAITING_TO_BE_SIGNED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackBasic() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.ELIGIBLE,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackFullLoan() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.FULL_LOAN,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackAltOffer() {
    const offer = await offerRepo.getSelectedOffer(this.contractNumber);
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.ACCEPTED_WITH_OFFERS,
      custId: this.cust_id,
      offer: {
        id: offer.id,
        amount: parseInt(offer.offer_amt || 0),
        rate: parseFloat(parseInt((offer.int_rate * 1000) / 12) / 10),
        monthlyInstallment: parseFloat(offer?.instal_amt || 0),
        tenor: offer.tenor,
      },
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackResubmit() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: `SS_RESUBMIT`,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackNotEligible() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.NOT_ELIGIBLE,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackWaitingToApprove() {
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.IN_SS_QUEUE,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }

  async callbackTerminated() {
    super.callbackTerminated();
    const callbackBody = {
      contractNumber: this.contractNumber,
      status: CALLBACK_STAUS.TERMINATED,
      custId: this.cust_id,
    };
    return await this.callbackSma(callbackBody);
  }
}

class FinvCallbacker extends BaseCallbacker {
  constructor(contractNumber, kunnId, body) {
    super(contractNumber);
    this.kunnId = kunnId;
    this.body = body;
  }
  async initialize() {
    const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
    this.contractData = contractData;
  }
  // async callbackCancel() {
  //   super.callbackCancel();
  //   // const callbackBody = {
  //   //   contractNumber: this.contractNumber,
  //   //   status: MISA_CALLBACK_STATUS.CANCELLED,
  //   //   loanPackageInfo: {
  //   //     loanAmount: this.contractData.request_amt,
  //   //   },
  //   // };
  //   //todo
  //   utilsCrmService.removeContract(global.config, this.contractNumber);
  // }
  // async callbackReject() {
  //   super.callbackReject();
  //   // const callbackBody = {
  //   //   contractNumber: this.contractNumber,
  //   //   status: MISA_CALLBACK_STATUS.CANCELLED,
  //   //   loanPackageInfo: {
  //   //     loanAmount: this.contractData.request_amt,
  //   //   },
  //   // };
  //   //todo
  //   utilsCrmService.rejectContract(global.config, this.contractNumber);
  // }

  mappingCreditLimitStatus(status) {
    switch (status) {
      case STATUS.WAITING_CUSTOMER_SIGNATURE:
        return STATUS.WAITING_CUSTOMER_SIGNATURE;
      case STATUS.NOT_ELIGIBLE:
        return STATUS.NOT_ELIGIBLE;
      case STATUS.REFUSED:
        return STATUS.REFUSED;
      case STATUS.CANCELLED:
        return STATUS.CANCELLED;
      case STATUS.RESUBMIT_A2:
        return STATUS.RESUBMIT_A2;
      case STATUS.RESUBMIT_A3:
        return STATUS.RESUBMIT_A3;
      case STATUS.ELIGIBLE:
        return STATUS.ELIGIBLE;
      case STATUS.APPROVED:
        return STATUS.APPROVED;
      case STATUS.PASSED_REVIEW_A1:
        return STATUS.PASSED_REVIEW_A1;
      case STATUS.COMPLETED_SIGN:
        return STATUS.COMPLETED_SIGN;
      case STATUS.SIGNED: 
        return STATUS.CUSTOMER_SIGNED;
      case STATUS.PASSED_REVIEW_A2:
        return STATUS.PASSED_REVIEW_A2;
      case STATUS.PASSED_REVIEW_A3:
        return STATUS.PASSED_REVIEW_A3;
      case STATUS.SIGNING_IN_PROGRESS:
        return STATUS.SIGNING_IN_PROGRESS;
      case STATUS.ACTIVATED:
        return STATUS.ACTIVATED;
      case STATUS.EXPIRED:
        return STATUS.EXPIRED;
      case STATUS.IN_MANUAL_REVIEW_A2:
        return STATUS.IN_MANUAL_REVIEW_A2;
      case STATUS.IN_MANUAL_PROCESS_A3:
        return STATUS.IN_MANUAL_PROCESS_A3;
      default:
        return "UNKNOWN";
    }
  }

  mappingKunnStatus(status) {
    if (Object.values(KUNN_STATUS).includes(status)) {
      return status;
    }
    return "UNKNOWN";
  }

  getReasonDetail = async (contractNumber) => {
    const url = global.config.basic.antiFraud[global.config.env] + ENDPOINT_CONST.ANTI_FRAUD.GET_REASON_DETAIL + contractNumber;
    const response = await common.getAPI(url);
    return response;
  }

  // async callbackResubmit() {
  //   super.callbackResubmit();
  //   try {
  //     //update limit
  //     const contractNumber = this.contractNumber;
  //     const contractData = this.contractData;
  //     const status = this.mappingCreditLimitStatus(contractData.status);

  //     const contractDocument = await findOne({
  //       table: "loan_contract_document",
  //       whereCondition: {
  //         contract_number: contractNumber,
  //         doc_type: "VSRE",
  //       },
  //     });
  //     if (!contractDocument) {
  //       console.log(
  //         `[CALLBACK][FINV][callBackResubmitAf3Finv] contractNumber: ${contractNumber}, docType: VSRE not found`
  //       );
  //       return;
  //     }

  //     let files = [];
  //     if (contractDocument?.length > 0) {
  //       files = await s3Service.genMultiplePresigned(this.docs);
  //     }

  //     let resubmitData = this.body?.resubmitData || null;

  //     const payload = {
  //       contractNumber,
  //       status,
  //       files,
  //       resubmitData
  //     };

  //     const url = `...api finv`;
  //     const callbackRs = await common.postApiV2(url, payload);
  //     console.log(`[CALLBACK][FINV][callBackResubmitAf3Finv] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(callbackRs)}`);
  //     // loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CALLBACK_3P, TASK_FLOW.CALLBACK_CREDIT_LIMIT, payload, callbackRs, url);
  //     loggingRepo.saveCallback(this.contractNumber, payload, callbackRs);
  //     loggingRepo.updateCallback(this.contractNumber, payload);
  //     return callbackRs;
  //   } catch (error) {
  //     console.log(`[CALLBACK][FINV][callBackResubmitAf3Finv] contractNumber: ${this.contractNumber}, error ${error}`);
  //   }
  // }

  async callBackContractStatus() {
    try {
      //update limit
      const contractNumber = this.contractNumber;
      const contractData = this.contractData;
      const status = this.mappingCreditLimitStatus(contractData.status);

      let operation;
      if(contractData.workflow_code === WORKFLOW_CODE.FINV_AF1) operation = FINV_CALLBACK_OPERATION.AF1;
      else if (contractData.workflow_code === WORKFLOW_CODE.FINV_AF2) operation = FINV_CALLBACK_OPERATION.AF2;
      else if (contractData.workflow_code === WORKFLOW_CODE.FINV_AF3) operation = FINV_CALLBACK_OPERATION.AF3;

      let message = null;
      if([STATUS.NOT_ELIGIBLE, STATUS.REFUSED, STATUS.CANCELLED].includes(contractData.status)) {
        const reasonDetail = await this.getReasonDetail(contractNumber);
        message = reasonDetail?.data?.error_code;
      }

      const payload = {
        requestId: genRequestId('EVF'),
        requestTime: new Date().getTime(),
        partnerCode: 'EVF',
        operation,
        data: {
          contractNumber,
          status,
          message,
        },
      };
      
      const evnEcPrivateKey = global.env.FINV_EVN_EC_PRIVATE_KEY;
      const jws = await signCompactJws(evnEcPrivateKey, normalizeJson(payload))
      payload['signature'] = jws;
      
      if(this.body?.resubmitData) {
        payload['data']['validationErrors'] = this.body?.resubmitData.flat();
      }
      if(this.body?.contractData) {
        payload['data']['file'] = this.body?.contractData;
      }
      if(this.body?.iframeUrl) {
        payload['data']['iframeUrl'] = this.body?.iframeUrl;
      }
      
      const url = global.config.data.finv.callbackHost + ENDPOINT_CONST.PARTNER_CALLBACK.FINV_WEBHOOK 
      const callbackRs = await common.postApiV2(url, payload);
      console.log(`[CALLBACK][FINV][callBackContractStatus] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(callbackRs)}`);
      loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CALLBACK_3P, TASK_FLOW.CALLBACK_CREDIT_LIMIT, payload, callbackRs, url);
      return callbackRs;
    } catch (error) {
      console.log(`[CALLBACK][FINV][callBackContractStatus] contractNumber: ${this.contractNumber}, error ${error}`);
    }
  }

  async callBackKunnStatus() {
    try {
      //update limit
      // const contractNumber = this.contractNumber;
      const kunnId = this.kunnId;
      // const contractData = this.contractData;
      const kunnData = await kunnRepo.getKunnData(kunnId);
      const status = this.mappingKunnStatus(kunnData.status);

      let message = null;
      if([STATUS.NOT_ELIGIBLE, STATUS.REFUSED, STATUS.CANCELLED].includes(kunnData.status)) {
        const reasonDetail = await this.getReasonDetail(kunnId);
        message = reasonDetail?.data?.error_code;
      }

      const payload = {
        requestId: genRequestId('EVF'),
        requestTime: new Date().getTime(),
        partnerCode: 'EVF',
        operation: FINV_CALLBACK_OPERATION.KUNN,
        data: {
          contractNumber: kunnId,
          status,
          message,
        },
      };

      const evnEcPrivateKey = global.env.FINV_EVN_EC_PRIVATE_KEY;
      const jws = await signCompactJws(evnEcPrivateKey, normalizeJson(payload))
      payload['signature'] = jws;
      
      if(this.body?.contractData) {
        payload['data']['file'] = this.body?.contractData;
      }
      if(this.body?.iframeUrl) {
        payload['data']['iframeUrl'] = this.body?.iframeUrl;
      }

      const url = global.config.data.finv.callbackHost + ENDPOINT_CONST.PARTNER_CALLBACK.FINV_WEBHOOK 
      const callbackRs = await common.postApiV2(url, payload);
      console.log(`[CALLBACK][FINV][callBackKunnStatus] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(callbackRs)}`);
      loggingRepo.saveStepLog(kunnId, SERVICE_NAME.CALLBACK_3P, TASK_FLOW.CALLBACK_KUNN_STATUS, payload, callbackRs, url);
      return callbackRs;
    } catch (error) {
      console.log(`[CALLBACK][FINV][callBackKunnStatus] contractNumber: ${this.contractNumber}, error ${error}`);
    }
  }

  async callbackReject() {
    this.callBackContractStatus();
  }

  async callbackCancel() {
    this.callBackContractStatus();
  }

  async callbackResubmit() {
    this.callBackContractStatus();
  }

  async callbackActivated() {
    this.callBackContractStatus();
  }

  async callbackNotEligible() {
    this.callBackContractStatus();
  }

  async callbackApproved() {
    this.callBackContractStatus()
  }

  async callbackCreditSigningInProgress() {
    this.callBackContractStatus();
  }

  async callbackSigned() {
    this.callBackContractStatus();
  }

  async callbackKunnSigningInProgress() {
    this.callBackKunnStatus();
  }

  async callbackSignedKunn() {
    this.callBackKunnStatus();
  }

  async callbackCancelKunn() {
    this.callBackKunnStatus();
  }

  async callbackRejectKunn() {
    this.callBackKunnStatus();
  }

  async callbackNotEligibleKunn() {
    this.callBackKunnStatus();
  }

  async callbackSignedToBeDisbured() {
    this.callBackKunnStatus();
  }

  async callbackActiveKunn() {
    this.callBackKunnStatus();
  }

  async callbackCreditApproveAf1() {
    this.callBackContractStatus();
  }

  async callbackTerminated() {
    this.callbackKunnStatus();
  }

  async callbackCreditProcessAf2() {
    this.callBackContractStatus();
  }

  async callbackCreditApproveAf2() {
    this.callBackContractStatus();
  }

  async callbackCreditProcessAf3() {
    this.callBackContractStatus();
  }

  async callbackCompletedSign() {
    this.callBackContractStatus();
  }
}

class BizzCallbacker extends BaseCallbacker {
  constructor(contractNumber, withdrawId, body = {}) {
    super(contractNumber);
    const { bizzClientId, bizzClientSecret, bizzHost } = global.config.data.partnerCallback;
    this.partnerCode = PARTNER_CODE.BIZZ;
    this.clientId = bizzClientId;
    this.clientSecret = bizzClientSecret;
    this.host = bizzHost;
    this.kunnNumber = withdrawId;
    this.body = body;
    this.docs = body?.docs || [];
    this.initialize();
  }

  async initialize() {
    const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
    this.contractData = contractData;
  }

  buildHeader() {
    const token = btoa(`${this.clientId}:${this.clientSecret}`); // encode base64
    return {
      Authorization: `Basic ${token}`,
      "Content-Type": "application/json",
    };
  }

  async callBackContractStatus() {
    try {
      //update limit
      const contractNumber = this.contractNumber;
      const contractData = this.contractData;
      const status = this.mappingCreditLimitStatus(contractData.status);
      // const contractDocument = await findOne({
      //   table: "loan_contract_document",
      //   where: {
      //     contract_number: contractNumber,
      //     doc_type: "LCT",
      //   },
      // });
      // if (!contractDocument) {
      //   console.log(
      //     `[CALLBACK][BIZZ][callBackContractStatus] contractNumber: ${contractNumber}, docType: LCT not found`
      //   );
      //   return;
      // }

      // const files = this.docs.map(async (doc) => ({
      //   docType: doc.doc_type,
      //   fileName: doc.file_name,
      //   presignedUrl: await s3Service.genPresigns3FromKey(doc.file_key), //genpresign
      // }));

      let files = [];
      if (this.docs.length > 0) {
        files = await s3Service.genMultiplePresigned(this.docs);
      }

      let resubmitData = this.body?.resubmitData || null;

      const payload = {
        contractNumber,
        status,
        files,
        resubmitData
      };

      const url = `${this.host}/api/webhooks/evf/contracts/credit-limit/status`;
      const callbackRs = await common.postApiV2(url, payload, this.buildHeader());
      console.log(`[CALLBACK][BIZZ][callBackContractStatus] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(callbackRs)}`);
      loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CALLBACK_3P, TASK_FLOW.CALLBACK_CREDIT_LIMIT, payload, callbackRs, url);
      return callbackRs;
    } catch (error) {
      console.log(`[CALLBACK][BIZZ][callBackContractStatus] contractNumber: ${this.contractNumber}, error ${error}`);
    }
  }

  mappingCreditLimitStatus(status) {
    switch (status) {
      case STATUS.WAITING_CUSTOMER_SIGNATURE:
        return STATUS.WAITING_CUSTOMER_SIGNATURE;
      case STATUS.NOT_ELIGIBLE:
      case STATUS.REFUSED:
        return STATUS.REFUSED;
      case STATUS.CANCELLED:
        return STATUS.CANCELLED;
      case STATUS.RESUBMIT_A2:
        return STATUS.RESUBMIT_A2;
      case STATUS.RESUBMIT_A3:
        return STATUS.RESUBMIT_A3;
      default:
        return "UNKNOWN";
    }
  }

  async callBackKunnStatus() {
    try {
      //update limit
      // const contractNumber = this.contractNumber;
      const contractData = this.contractData;
      const status = contractData.status;
      const debtContractNumber = this.kunnNumber;
      if (!debtContractNumber) {
        console.log(`[CALLBACK][BIZZ][callBackKunnStatus] debtContractNumber: ${debtContractNumber}, error: debtContractNumber is required`);
        return;
      }
      // const contractDocument = await findOne({
      //   table: "loan_contract_document",
      //   where: {
      //     contract_number: contractNumber,
      //     doc_type: "LTT",
      //   },
      // });
      // if (!contractDocument) {
      //   console.log(
      //     `[CALLBACK][BIZZ][callBackKunnStatus] debtContractNumber: ${debtContractNumber}, docType: LTT not found`
      //   );
      //   return;
      // }
      const files = this.docs.map((doc) => ({
        docType: doc.doc_type,
        fileName: doc.file_name,
        presignedUrl: doc.file_key,
      }));

      const payload = {
        debtContractNumber,
        status,
        files,
      };

      const url = `${this.host}/api/webhooks/evf/contracts/promissory-note/status`;
      const callbackRs = await common.postApiV2(url, payload, this.buildHeader());
      console.log(`[CALLBACK][BIZZ][callBackKunnStatus] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(callbackRs)}`);
      return callbackRs;
    } catch (error) {
      console.log(`[CALLBACK][BIZZ][callBackKunnStatus] contractNumber: ${this.contractNumber}, error ${error}`);
    }
  }

  async callbackReject() {
    this.callBackContractStatus();
  }

  async callbackCancel() {
    this.callBackContractStatus();
  }
  async callbackSignedAf3() {
    this.callBackContractStatus();
  }
  async callbackSignedAf3Resubmit() {
    this.callBackContractStatus();
  }

  async callbackResubmit() {
    this.callBackContractStatus();
  }

  async callbackActivated() {
    this.callBackContractStatus();
  }
  
}

class BizzKunnCallback extends BaseCallbacker {
  constructor(withdrawId) {
    super(withdrawId);
    const { bizzClientId, bizzClientSecret, bizzHost } = global.config.data.partnerCallback;
    this.partnerCode = PARTNER_CODE.BIZZ;
    this.clientId = bizzClientId;
    this.clientSecret = bizzClientSecret;
    this.host = bizzHost;
    this.kunnNumber = withdrawId;
    this.initialize();
  }

  async initialize() {
    if(this.kunnNumber) {
      const kunnData = await kunnRepo.getKunnData(this.kunnNumber);
      this.kunnData = kunnData;
    }
  }

  buildHeader() {
    const token = btoa(`${this.clientId}:${this.clientSecret}`); // encode base64
    return {
      Authorization: `Basic ${token}`,
      "Content-Type": "application/json",
    };
  }

  async callbackKunnStatus() {
    try {
        const kunnData = this.kunnData;
        const status = kunnData.status;
        const debtContractNumber = this.kunnNumber;
        if (!debtContractNumber) {
            console.log(`[CALLBACK][BIZZ][callBackKunnStatus] debtContractNumber: ${debtContractNumber}, error: debtContractNumber is required`);
            return;
        }
        const result = await bizzKunnStatus.getKunnStatus({ contract_number: kunnData.contract_number, debt_contract_number: debtContractNumber });
        const payload = {
            debtContractNumber,
            status,
            files: snakeToCamel(result?.data?.files || []),
            data: snakeToCamel(result?.data || {})
        };

        const url = `${this.host}/api/webhooks/evf/contracts/promissory-note/status`;
        const callbackRs = await common.postApiV2(url, payload, this.buildHeader());
        console.log(`[CALLBACK][BIZZ][callBackKunnStatus] payload ${JSON.stringify(payload)}, res: ${JSON.stringify(callbackRs)}`);
        loggingRepo.saveStepLog(this.kunnNumber, SERVICE_NAME.CALLBACK_3P, TASK_FLOW.CALLBACK_KUNN_STATUS, payload, callbackRs, url);
        return callbackRs;
    } catch (error) {
        console.log(error);
        console.log(`[CALLBACK][BIZZ][callBackKunnStatus] contractNumber: ${this.contractNumber}, error ${error?.message}`);
    }
  }

  async callbackActiveKunn() {
    this.callbackKunnStatus();
  }
}

async function callbackPartner(contractNumber, partnerCode, status, resubmitList = undefined, targetType = "credit-creation", withdrawId = null, body = {}, { availableAmount, lastestLoanAmount, resultS3, encryptResultS3 } = {}) {
  let callbacker;
  switch (partnerCode) {
    case PARTNER_CODE.KOV:
      callbacker = new KovCallbacker(contractNumber, withdrawId, targetType);
      break;
    case PARTNER_CODE.VPL:
      callbacker = new VplCallbacker(contractNumber);
      break;
    case PARTNER_CODE.VTP:
      callbacker = new VtpCallbacker(contractNumber);
      await callbacker.initialize();
      break;
    case PARTNER_CODE.MISA:
      callbacker = new MisaCallbacker(contractNumber, withdrawId, body);
      await callbacker.initialize();
      break;
    case PARTNER_CODE.SMA:
    case PARTNER_CODE.SMASYNC:
      callbacker = new SmaCallbacker(contractNumber, withdrawId);
      await callbacker.initialize();
      break;
    case PARTNER_CODE.FINV:
      callbacker = new FinvCallbacker(contractNumber, withdrawId, body);
      await callbacker.initialize();
      break;
    case PARTNER_CODE.BIZZ:
      callbacker = new BizzCallbacker(contractNumber, withdrawId, body);
      if(withdrawId) {
        callbacker = new BizzKunnCallback(withdrawId);
      }
      await callbacker.initialize();
      break;
    default:
      callbacker = new BaseCallbacker(contractNumber);
      break;
  }

  switch (status) {
    case CALLBACK_STAUS.CANCELLED:
      await callbacker.callbackCancel();
      break;
    case CALLBACK_STAUS.REJECTED:
      await callbacker.callbackReject();
      break;
    case CALLBACK_STAUS.RESUBMIT:
      await callbacker.callbackResubmit(resubmitList);
      break;
    case CALLBACK_STAUS.APPROVED:
      await callbacker.callbackApproved();
      break;
    case CALLBACK_STAUS.SIGNED:
      await callbacker.callbackSigned();
      break;
    case CALLBACK_STAUS.ALT_OFFER:
      await callbacker.callbackAltOffer();
      break;
    case CALLBACK_STAUS.ACTIVE:
      await callbacker.callbackActivated();
      break;
    case CALLBACK_STAUS.TERMINATED:
      await callbacker.callbackTerminated();
      break;
    case CALLBACK_STAUS.ELIGIBLE:
      await callbacker.callbackBasic();
      break;
    case CALLBACK_STAUS.FULL_LOAN:
      await callbacker.callbackFullLoan();
      break;
    case CALLBACK_STAUS.CREATED_KUNN:
      await callbacker.callbackCreatedDebt();
      break;
    case CALLBACK_STAUS.APPROVE_KUNN_FOR_SIGN:
      await callbacker.callbackApproveSignKunn();
      break;
    case CALLBACK_STAUS.RESUBMIT_KU:
      await callbacker.callbackResubmitKU(resubmitList);
      break;
    case CALLBACK_STAUS.CANCEL_KUNN:
      await callbacker.callbackCancelKunn();
      break;
    case CALLBACK_STAUS.REJECT_KUNN:
      await callbacker.callbackRejectKunn();
      break;
    case CALLBACK_STAUS.NOT_ELIGIBLE_KUNN:
      await callbacker.callbackNotEligibleKunn();
      break;
    case CALLBACK_STAUS.ACTIVE_KUNN: {
      const kunn = await kunnRepo.getKunnData(withdrawId);
      if (kunn.api_version == "v2" && partnerCode === PARTNER_CODE.MISA) {
        await callbacker.callbackActiveKunnV2({ availableAmount, lastestLoanAmount, resultS3, encryptResultS3 });
      } else {
        await callbacker.callbackActiveKunn({ availableAmount, lastestLoanAmount });
      }
      break;
    }
    case CALLBACK_STAUS.UPDATE_PAYMENT: {
      await callbacker.updatePayment();
      break;
    }
    case CALLBACK_STAUS.RECEIVED_PAYMENT: {
      await callbacker.callbackReceivedPayment();
      break;
    }

    case CALLBACK_STAUS.SIGNED_KUNN:
      await callbacker.callbackSignedKunn();
      break;
    case CALLBACK_STAUS.NOT_ELIGIBLE:
       callbacker.callbackNotEligible();
      break;
    case CALLBACK_STAUS.IN_SS_QUEUE:
      callbacker.callbackWaitingToApprove();
      break;
    // case CALLBACK_STAUS.ACTIVE_KUNN:
    //   await callbacker.callbackActiveKunn({ availableAmount, lastestLoanAmount });
    //   break;
    case CALLBACK_STAUS.CONTRACT_STATUS:
       callbacker.callbackContractStatus();
      break;
    case CALLBACK_STAUS.KUNN_STATUS:
       callbacker.callbackKunnStatus();
      break;
    case CALLBACK_STAUS.WAITING_CUSTOMER_SIGNATURE:
      await callbacker.callbackSignedAf3();
      break;
    case CALLBACK_STAUS.RESUBMIT_A2:
    case CALLBACK_STAUS.RESUBMIT_A3:
      await callbacker.callbackResubmit();
      break;
    case CALLBACK_STAUS.EXPIRED_LIMIT:
      await callbacker.callbackExpiredLimit();
      break;
    case CALLBACK_STAUS.SIGNING_IN_PROGRESS:
       callbacker.callbackCreditSigningInProgress();
      break;
    case CALLBACK_STAUS.KUNN_SIGNING_IN_PROGRESS:
       callbacker.callbackKunnSigningInProgress();
      break;
    case CALLBACK_STAUS.PASSED_REVIEW_A1:
       callbacker.callbackCreditApproveAf1();
      break;
    case CALLBACK_STAUS.IN_MANUAL_REVIEW_A2:
       callbacker.callbackCreditProcessAf2();
      break;
    case CALLBACK_STAUS.PASSED_REVIEW_A2:
       callbacker.callbackCreditApproveAf2();
      break;
    case CALLBACK_STAUS.IN_MANUAL_PROCESS_A3:
       callbacker.callbackCreditProcessAf3();
      break;
    case CALLBACK_STAUS.COMPLETED_SIGN:
       callbacker.callbackCompletedSign();
      break;
    default:
      break;
  }
}

const callbackKunnBizzi = async(kunnId) => {
  callbackPartner(null, PARTNER_CODE.BIZZ, 'KUNN_STATUS', {}, null, kunnId, {});
}

const callbackFinv = async (contractNumber) => {
  try {
    const loan = snakeToCamel(await loanContractRepo.getLoanContract(contractNumber));
    if ([STATUS.NOT_ELIGIBLE, STATUS.CANCELLED].includes(loan.status)) {
      await callbackPartner(contractNumber, loan.partnerCode, CALLBACK_STAUS.CANCELLED);
    }
    if ([STATUS.REFUSED].includes(loan.status)) {
      await callbackPartner(contractNumber, loan.partnerCode, CALLBACK_STAUS.REJECTED);
    }
    return true;
  } catch (error) {
    console.log(`[WF-SERVICE][callbackFinv] contractNumber ${contractNumber} error ${error.message}`);
    return false;
  }
};

module.exports = {
  callbackPartner,
  callbackFinv,
  callbackKunnBizzi,
  baseCallbacker: BaseCallbacker
};
