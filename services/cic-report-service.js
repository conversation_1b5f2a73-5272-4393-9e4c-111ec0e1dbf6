const { findRevenueDocuments, updateFlagExport } = require("../repositories/loan-revenues-repo");
const loanRevenueRepo = require('../repositories/loan-revenues-repo');
const {
  snakeToCamel,
  getFileKeyFromUrl,
  throwServerError,
  xmlToJson,
  toArray,
  throwBadReqError,
} = require("../utils/helper");
const { downloadFileVer2 } = require("../upload_document/s3-service");

const financialStatementsExportRepo = require("../repositories/financial-statements-export-repo");
const financialStatementsDetailsRepo = require("../repositories/financial_statement_details-repo");
const {
  MAPPING_FINANCIAL_REGIME,
  BCTC_MAPPING_REPORT,
  BCTC_KEY,
} = require("../const/variables-const");
const loanRepo = require('../repositories/loan-contract-repo');
const { MISA_ERROR_CODE } = require("../const/response-const");
const { patchUpdate, generateValues } = require("../utils/sqlHelper");
const { PARTNER_CODE } = require("../const/definition");
const loanContractDocumentRepo = require('../repositories/document');
const actionAuditService = require("../services/action-audit")
const { CASE_STATUS } = require("../const/code-const")

const exportCicReportDataListContracts = async ({ listContractNumber }) => {
  try {
    listContractNumber = listContractNumber || [];
    const contracts = await  loanRepo.getLoanContracts(listContractNumber);
    for(const contract of contracts)
    {
      exportCicReportData({contractNumber: contract.contract_number});
    }
    return {
      code: 0,
      message: "success",
      data: [],
    };
  } catch (error) {
    console.log(
      `[CIC-REPORT][exportCicReportDataListContracts]listContractNumber: ${listContractNumber}, error ${error.message} `
    );
    throw error;
  }
};

const exportCicReportData = async ({ contractNumber }) => {
  try {
    const loan = await loanRepo.getLoanContract(contractNumber);
    if (!loan?.id) {
      throwBadReqError(`contractNumber`, `contract number invalid`, MISA_ERROR_CODE.E400);
    }
    const documents = snakeToCamel(await findRevenueDocuments(contractNumber, false));
    // return await handleExportDocumentData({
    //   doc: documents[0],
    //   contractNumber,
    // });
    let task = []
    for (const doc of documents) {
      task.push(handleExportDocumentData({ doc, contractNumber, custId: loan.cust_id }))
    }
    await Promise.all(task);
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.EXPORT_CIC_REPORT.STEP_CODE, CASE_STATUS.EXPORT_CIC_REPORT.ACTION.SUCCESS, contractNumber);
    return {
      code: 0,
      message: "success",
      data: [],
    };
  } catch (error) {
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.EXPORT_CIC_REPORT.STEP_CODE, CASE_STATUS.EXPORT_CIC_REPORT.ACTION.FAILED, contractNumber);
    console.log(
      `[CIC-REPORT][exportCicReportData]contractNumber: ${contractNumber}, error ${error.message} `
    );
    throw error;
  }
};

const handleExportDocumentData = async ({ doc, contractNumber, custId }) => {
  try {
    //get file from s3
    let fileKey = getFileKeyFromUrl(doc.evfFileUrl);
    if (!fileKey && !doc.evfDocId) {
      throwServerError(`${contractNumber} | handleExportDocumentData | fileKey and evfDocId missing`);
    }
    if (!fileKey) {
      const document = await loanContractDocumentRepo.findByDocID(doc.evfDocId);
      if (!document?.file_key) {
        throwServerError(`${contractNumber} | handleExportDocumentData | file_key not found for docId ${doc.evfDocId}`);
      }
      fileKey = document.file_key;
    }
    const file = await downloadFileVer2(global.config.data, fileKey);
    const fileData = xmlToJson(file?.Body)?.HSoThueDTu?.HSoKhaiThue || {};
    if (!fileData) {
      throwServerError(`cannot get file ${doc.evfFileUrl}`);
    }
    if(!doc.financialReportType)
    {
      const loanRevenue = await loanRevenueRepo.findById(doc.loanRevenuesId);
      //bizzi partner not have financial report type
      if (!loanRevenue?.financial_report_type) {
        doc.financialReportType = getFinancialReportType(fileData?.TTinChung?.TTinTKhaiThue?.TKhaiThue?.tenTKhai);
        await loanRevenueRepo.updateFinancialReportType(doc.loanRevenuesId, doc.financialReportType)
      } else {
        doc.financialReportType = loanRevenue.financial_report_type;
      }
    }
    if(!doc.financialReportType) return;
    await savefinancialStatements({
      data: fileData,
      contractNumber,
      revenuDocumentsId: doc.docId,
      fileKey,
      financialReportType: doc.financialReportType,
      custId
    });
    await updateFlagExport(doc.docId,true);
    return fileData;
  } catch (error) {
    console.log(
      `[CIC-REPORT][handleExportDocumentData] data: ${JSON.stringify(
        doc
      )}, error ${error} `
    );
  }
};

const savefinancialStatements = async ({
  data,
  contractNumber,
  revenuDocumentsId,
  fileKey,
  financialReportType,
  custId
}) => {
  try {
    const regime = MAPPING_FINANCIAL_REGIME[financialReportType];
    const financialDto = {
      accountingRegime: regime,
      accountingTemplate: regime,
      fileKey,
      revenuDocumentsId,
      contractNumber,
      custId
    };
    const taxReturn = data["TTinChung"]?.["TTinTKhaiThue"]?.["TKhaiThue"];
    let isAudit = data["CTieuTKhaiChinh"]?.bctcDaKiemToan;
    isAudit = isAudit == true || isAudit == 1 ? "Y" : "N";
    financialDto.isAudit = isAudit;
    financialDto.code = taxReturn?.["maTKhai"];
    financialDto.reportYear = taxReturn?.["KyKKhaiThue"]?.["kyKKhai"];
    financialDto.reportDate = taxReturn?.["ngayLapTKhai"];
    financialDto.financialYearEndDate = taxReturn?.["KyKKhaiThue"]?.[
      "kyKKhaiDenNgay"
    ]?.substring(0, 5);
    financialDto.reportDate = taxReturn?.["ngayLapTKhai"] || null;
    financialDto.unitOfMeasurement = "M";
    //save main data
    await Promise.all([
      handleMainItemFromXml({
        fileData: data,
        financialDto,
        reportType: financialReportType,
      }),
      handleAppendixItemFromXml({
        fileData: data,
        financialDto,
        reportType: financialReportType,
      }),
    ]);
  } catch (error) {
    console.log(
    `[CIC-REPORT][savefinancialStatements] contractNumber: ${contractNumber},loanRevenuesId: ${loanRevenuesId}, error ${error} `
    );
  }
};
const mappingReportName = (reportType, key) => {
  reportType = reportType.startsWith("TT") ? reportType : `TT${reportType}`;
  return BCTC_MAPPING_REPORT[reportType]?.[key];
};

const handleMainItemFromXml = async ({
  fileData,
  financialDto,
  reportType,
}) => {
  financialDto.template = mappingReportName(reportType, "CTieuTKhaiChinh");
  const financialData = fileData?.["CTieuTKhaiChinh"];
  const cdktData =
    financialData?.["CDKT_HoatDongLienTuc"] ||
    financialData?.["CDKT_HoatDongKLienTuc"];

  let notes = mergeData([
    financialData?.["ThuyetMinh"],
    cdktData?.["ThuyetMinh"],
  ]);
  let firstYearData = mergeData([
    financialData?.["SoDauNam"],
    cdktData?.["SoDauNam"],
  ]);
  let secondYearData = mergeData([
    financialData?.["SoCuoiNam"],
    cdktData?.["SoCuoiNam"],
  ]);
  const financialId = (await financialStatementsExportRepo.save(financialDto))
    .id;
  return await saveDetails({
    notes,
    firstYearData,
    secondYearData,
    financialId,
  });
};

const handleAppendixItemFromXml = async ({
  fileData,
  financialDto,
  reportType,
}) => {
  const appendixes = ["PL_KQHDXSKD","PL_KQHDSXKD", "PL_LCTTGT", "PL_LCTTTT"];
  for (const key of appendixes) {
    try {
      if (fileData["PLuc"]?.[key]) {
        financialDto.template = mappingReportName(reportType, key);
        const appendix = fileData["PLuc"][key];
        let notes = mergeData([appendix?.["ThuyetMinh"]]);
        let firstYearData = mergeData([appendix?.["NamTruoc"]]);
        let secondYearData = mergeData([appendix?.["NamNay"]]);
        const financialId = (
          await financialStatementsExportRepo.save(financialDto)
        ).id;
        await saveDetails({
          notes,
          firstYearData,
          secondYearData,
          financialId,
        });
      }
    } catch (error) {
      financialDto.template = null;
      console.log(`[handleAppendixItemFromXml]appendix ${key} error: ${error}`);
    }
  }
};

const mergeData = (datas) => {
  let _ = {};
  for (const data of datas) {
    _ = { ..._, ...(data || {}) };
  }
  return _;
};

const saveDetails = async ({
  notes,
  firstYearData,
  secondYearData,
  financialId,
}) => {
  const payload = {
    financialStatementsExportId: financialId,
  };
  for (const key in notes) {
    try {
      const code = key.replace("ct", "");
      const note = notes[key] || "";
      const numOfFirstYear = Number(firstYearData[key] || 0);
      const numOfSecondYear = Number(secondYearData[key] || 0);
      const saveData = {
        ...payload,
        code,
        note,
        numOfFirstYear,
        numOfSecondYear,
      };
      await financialStatementsDetailsRepo.save(saveData);
    } catch (error) {
      console.log(
        `[convertNoteToDetail] note to detail ${key} error: ${error}`
      );
    }
  }
};

const exportFinancialReportDataGw = async ({ contractNumber }) => {
  try {
    const loan = await loanRepo.getLoanContract(contractNumber);
    if (!loan?.id) {
      console.log(`${contractNumber} | ${new Date()} | [exportFinancialReportData] loan not found`);
      return false;
    }
    const { partner_code } = loan;
    if (partner_code === PARTNER_CODE.BIZZ) {
      await exportCicReportData({ contractNumber });
      return true;
    }

    if (partner_code === PARTNER_CODE.BZHM) {
      await exportCicReportData({ contractNumber });
      return true;
    }

    console.log(`${contractNumber} | ${new Date()} | [exportFinancialReportData] not supported for ${partner_code} partner`);
    return false;
  } catch (error) {
    console.log(`${contractNumber} | ${new Date()} | [exportFinancialReportData] error: ${error.message}`);
    return false;
  }
};

const getFinancialReportType = (financialReportType) => {
  if (financialReportType.toUpperCase() === "BỘ BÁO CÁO TÀI CHÍNH (B01-DNSN)(TT133/2016/TT-BTC)")
    return "133_B01";
  if (financialReportType.toUpperCase() === "BỘ BÁO CÁO TÀI CHÍNH (B01A - DNN)(TT133/2016/TT-BTC)")
    return "133_B01A";
  if (financialReportType.toUpperCase() === "BỘ BÁO CÁO TÀI CHÍNH (B01B - DNN)(TT133/2016/TT-BTC)")
    return "133_B01B";
  if (financialReportType.toUpperCase() === "BÁO CÁO TÀI CHÍNH (TT200/2014/TT-BTC)")
    return "200_BCTC";
  return null;
}

const getFinancialReportDataFromFile = async ({ contractNumber, docId, fileUrl, fileKey }) => {
  try {
    if (!docId && !fileUrl && !fileKey) {
      throwServerError(`${contractNumber} | handleExportDocumentData | docId, fileUrl and fileKey missing`);
    }
    if (!fileKey) {
      fileKey = getFileKeyFromUrl(fileUrl);
    }
    if (!fileKey) {
      const document = await loanContractDocumentRepo.findByDocID(docId);
      if (!document?.file_key) {
        throwServerError(`${contractNumber} | handleExportDocumentData | file_key not found for docId ${doc.evfDocId}`);
      }
      fileKey = document.file_key;
    }
    const file = await downloadFileVer2(global.config.data, fileKey);
    const fileData = xmlToJson(file?.Body)?.HSoThueDTu?.HSoKhaiThue || {};
    if (!fileData) {
      throwServerError(`cannot get file with key ${fileKey}`);
    }
    return fileData;
  } catch (error) {
    console.log(
      `[CIC-REPORT][getFinancialReportDataFromFile] data: ${JSON.stringify(
        { contractNumber, docId, fileUrl, fileKey }
      )}, error ${error} `
    );
  }
};

module.exports = {
  exportCicReportData,
  handleExportDocumentData,
  exportCicReportDataListContracts,
  exportFinancialReportDataGw,
  getFinancialReportDataFromFile
};
