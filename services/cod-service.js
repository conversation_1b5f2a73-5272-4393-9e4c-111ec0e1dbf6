const common = require("../utils/common")
const turnoverRepo = require("../repositories/turnover-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const {VTP_CUST_SCHEMA} = require("../const/definition")
const productService = require("../utils/productService")
const {computeVTPOffer} = require("./de-service")
const uuid = require("uuid")

async function checkPhoneCode(body) {
    try {
        const config = global.config
        const codTranscriptUrl = config.data.partnerCallback.vtpGetTranscript;
        let CODbody = {
          id : body.third_party_cust_id,
          phone : body.merchant_account
        }
        const response = await common.postApiV2(codTranscriptUrl,CODbody)
        if(response.data.status != 200) {
            console.log(`verify phone code không thành công : ` , body.contract_number)
            return -1
        }
        else {
            const data = response.data.data
            const promiseList = []
            for(let key in data) {
                let obj = data[key]
                if(key == 'turnover') {
                    for(let idx in data[key]) {
                        const valueMonth = data[key][idx]
                        if(valueMonth.amount < 1) {
                            console.log(`doanh thu từ thu hộ của Viettel Post < 1000000 => NOT ELIGIBLE : ` , body.contract_number)
                            return 0
                        }
                        
                    }
                    obj.map(x => x.amount *= 1000000 )
                }

                if(key == 'transaction') {
                    for(let idx in data[key]) {
                        const valueMonth = data[key][idx]
                        if(valueMonth.amount < 1) {
                            console.log(`Số lượng đơn hàng trên Viettel Post < 5 đơn => NOT ELIGIBLE : ` , body.contract_number)
                            return 0
                        }
                        obj.map(x => x.amount *= 5 )
                    }
                }

                if(key == 'codOrder') {
                    for(let idx in data[key]) {
                        const valueMonth = data[key][idx]
                        if(valueMonth.amount <= 0) {
                            console.log(`Số codOrder < 1=> NOT ELIGIBLE : ` , body.contract_number)
                            return 0
                        }
                    }
                }

                if(key == 'freighCharge') {
                    obj.map(x => x.amount *= 1000000 )
                }

                if(typeof(data[key]) == 'object') {
                    promiseList.push(turnoverRepo.saveTurnOrTrans(global.poolWrite,obj,key,body.contract_number))
                }
            }
            await Promise.all(promiseList)

            if(data.timeDurationPoints != 1 && data.timeDurationPoints != 2 ) {
                console.log(`Thời gian sử dụng không hợp lệ => NOT ELIGIBLE : ` , body.contract_number)
                return 0
            }
            const timeDuration = data.timeDurationPoints == 1 ? 9 : 13
            const turnover = data.turnover
            let totalTurnover = 0;
            turnover.map(x => {totalTurnover += x.amount})
            const avgTurnover = totalTurnover / 3
            let custType;
            if(avgTurnover >= 1000000 && avgTurnover <= 24000000) {
                custType = VTP_CUST_SCHEMA.STANDARD
            }
            else if(avgTurnover >= 25000000 && avgTurnover <= 99000000) {
                custType = VTP_CUST_SCHEMA.VIP
            }
            else if(avgTurnover >= 100000000){
                custType = VTP_CUST_SCHEMA.PREMIUM
            }
            else {
                common.log(`Không có sản phẩm phù hợp.`,body.contract_number)
                return 0
            }

            const productInfo = await productService.getProductByPartner(body.partner_code,custType)
            if(productInfo) {
                let productCode = productInfo.product_code
                let rate = productInfo.interest_rate / 100
                const updateRs = await Promise.all([loanContractRepo.updateFieldLoanContract(body.contract_number,'time_duration',timeDuration),
                loanContractRepo.updateFieldLoanContract(body.contract_number,'cust_type',custType)],
                loanContractRepo.updateFieldLoanContract(body.contract_number,'product_code',productCode),
                loanContractRepo.updateFieldLoanContract(body.contract_number,'request_int_rate',rate))
                updateRs.forEach(rs => {
                    if(!rs) {
                        common.log(`Update loan contract error.`,body.contract_number)
                        return 0
                    }
                })
                await computeVTPOffer(body.contract_number)
                return 1
            }
            else {
                common.log(`Không có sản phẩm phù hợp.`,body.contract_number)
                return 0
            }
        }
    }
    catch(err) {
        console.log(err)
        console.log(`check phone code error : ${err.message}`)
        return 0
    }
}

async function callbackCOD(body) {
    try {
        const config = global.config
        const url = config.data.partnerCallback.codUpdateStatusCallback
        const rs = await common.postApiV2(url,body)
        if(rs.data.status != 200) {
            return false
        }
        return true
    }
    catch(err) {
        common.log(`callback COD error : ${err.message}`,body.contractNumber)
        return false
    }
}

async function updateStatusCOD(contractNumber,status,cusId) {
    
    const body = {
        contractNumber,
        requestId : 'EC' + uuid.v4(),
        loanStatus : status,
        cusId
    }
    return callbackCOD(body)
}

async function updateFieldCOD(contractNumber,body,status,custId) {
    body.contractNumber = contractNumber
    body.cusId = parseFloat(custId)
    body.loanId = contractNumber
    body.requestId = 'EC' + uuid.v4()
    body.loanStatus = status
    return callbackCOD(body)   
}

async function getProductInfo(req,res) {
    try {
        const contractNumber = req.query.contractNumber
        const contractData = await loanContractRepo.getLoanContractJoinLoanScore(contractNumber)
        let productCode = contractData.product_code
        const productInfo = await productService.getProductInfoV2(productCode)
        return res.status(200).json({
            code : 1,
            msg : "get product list sucessfully.",
            data : {
                productCode : productCode,
                minTenor : parseFloat(productInfo.productVar[0].tenorFrm),
                maxTenor : parseFloat(productInfo.productVar[0].tenorTo),
                tenorStep : parseFloat(productInfo.productVar[0].tenorStep),
                minAmt : parseFloat(productInfo.productVar[0].minAmt),
                maxAmt : parseFloat(contractData.max_loan),
                amountStep : parseFloat(productInfo.productVar[0].amountStep),
                rate : parseFloat(productInfo.productVar[0].intRate / 100)
            }
        })
    }
    catch(err) {
        common.log(`Get product info error : ${err.message}`,req.query.contractNumber)
        common.responseErrorPublic(res)
    }
}

module.exports = {
    checkPhoneCode,
    getProductInfo,
    updateStatusCOD,
    updateFieldCOD
}