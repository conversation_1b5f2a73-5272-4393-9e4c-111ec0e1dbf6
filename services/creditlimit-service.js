const productService = require("../utils/productService");
const documentRepo = require("../repositories/document");
const kunnRepo = require("../repositories/kunn-repo");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loanContractRepresentationsRepo = require("../repositories/loan-customer-representations-repo");
const loanCustomerShareholdersRepo = require("../repositories/loan-customer-shareholders-repo");
const loanContractOwnerRepo = require("../repositories/loan-business-owner-repo.js");
const loanRevenuesRepo = require("../repositories/loan-revenues-repo");
const { getValueCodeMasterdataV2, getFullAddress, getFullAddressNew } = require("../utils/masterdataService");
const { AF3_TEMPLATE_DOCTYPE_SIGNED } = require("../const/variables-const");
const sqlHelper = require("../utils/sqlHelper");
const uuid = require("uuid");
const path = require("path");
const dateHelper = require("../utils/dateHelper");
const VNnum2words = require("vn-num2words");
const { numberToString } = require("../utils/numberToString");
const { formatCash } = require("../utils/helper");
const moment = require("moment");
const { get } = require("lodash");

function getFileKeyFromUrl(fileUrl) {
  if (!fileUrl) {
    return fileUrl;
  }
  const url = new URL(fileUrl);
  const relativePath = url.pathname.substring(1); // Loại bỏ ký tự '/' ở đầu
  return relativePath;
}

function getFileNameFromUrl(fileUrl) {
  const parsedUrl = new URL(fileUrl);
  const pathname = parsedUrl.pathname;
  const fileName = path.basename(pathname);
  return fileName;
}

const formatDate = (date) => {
  if (!date) return "";
  return moment(date).format("DD/MM/YYYY");
};
const getEVFName = () => {
  return "Công ty Tài chính Cổ phần Điện lực (EVF) ";
};

async function getDataForTemplateBTTHDTD(contract_number, callback_url) {
  // Giấy đề nghị ứng trước kiêm KUNN BM02
  const docType = "BTTHDTD";
  const taskName = "GEN_DOCS_CONTRACT_FILE";

  const loanContractData = await loanContractRepo.getAllContractData(contract_number);
  const loanContractRepresentationsData = await loanContractRepresentationsRepo.getRepresentationsByCustomer(contract_number);
  const loanRevenuesDatas = await loanRevenuesRepo.findRevenueDocuments(contract_number, false); //

  const legalRep = loanContractRepresentationsData[0] || {};
  const loanRevenuesData = loanRevenuesDatas[0] || {};
  const contractData = loanContractData || {};

  // const config = global.config;
  // const productData = await productService.getProductLimitInfo(config, contractData.product_code);

  const [bankInfo, legal_identity_issue_place, legal_position] = await Promise.all([getValueCodeMasterdataV2(contractData.bank_code, "BANK"), getValueCodeMasterdataV2(legalRep?.issue_place, "ISSUE_PLACE_VN"), getValueCodeMasterdataV2(legalRep?.position, "PROFESSION")]);
  // const doc_info = productService.mapBundleGroupBizz([{ doc_type: docType }], bundleInfoRes.data)[0];

  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),

        sme_name: contractData.sme_name || "",
        sme_headquarters_address: contractData.sme_headquarters_address || "",

        company_phone_number: contractData.company_phone_number || "",
        sme_email: contractData.sme_email || "",
        fax: "", //khong co data
        registration_cert_no: contractData.registration_cert_no || "",
        registration_cert_issue_date: formatDate(contractData.registration_cert_issue_date),
        business_registration_issued_by: contractData.registration_cert_issue_place || "",
        legal_representative_name: legalRep.full_name || "",
        legal_representative_positions: legal_position?.nameVn || "",
        legal_representative_identity_number: legalRep.id_number || "",
        legal_representative_identity_isued_date: formatDate(legalRep.issue_date),
        legal_representative_identity_issued_by: legal_identity_issue_place?.nameVn || "",
        account_number: contractData.bank_account || "",
        bank_name: bankInfo?.nameVn,
        digital_authorization_contract_num: contractData.authorization_letter_number || "",
        digital_authorization_contract_date: formatDate(contractData.authorization_letter_singed_date),
        digital_authorization_by: contractData.authorized_name || "",
        factoring_credit_limit_num: formatCash(parseInt(contractData.approval_amt || 0)) || "",
        factoring_credit_limit_text: numberToString(parseInt(contractData.approval_amt || 0)) || "",
        loan_revenue_year: loanRevenuesData.year,
        anchor_name: contractData.anchor_name || "",
        anchor_revenue_expected: formatCash(parseInt(contractData?.last_3_month_sales_anchor / 3 || 0)),
        anchor_address: "", //khong co data,
        anchor_reference_persion_position: "", //khong co data
        anchor_deferred_payment_period: "", //khong co data
      },
    },
  };
}

async function getDataForTemplateBTTCNKPT(contract_number, callback_url) {
  const docType = "BTTCNKPT";
  const taskName = "GEN_DOCS_CONTRACT_FILE";

  const contractData = (await loanContractRepo.getAllContractData(contract_number)) || {};
  const loanContractRepresentationsData = await loanContractRepresentationsRepo.getRepresentationsByCustomer(contract_number);
  const legalRep = loanContractRepresentationsData[0] || {};
  const document_date = `Ngày ${dateHelper.getDate()}, Tháng ${dateHelper.getMonth()}, Năm ${dateHelper.getYear()}`;
  const bankInfo = await getValueCodeMasterdataV2(contractData.bank_code, "BANK");

  // const bundleInfoRes = await productService.getBundle(global.config, contractData.product_code);
  // const doc_info = productService.mapBundleGroupBizz([{ doc_type: docType }], bundleInfoRes.data)[0];

  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        document_no: "",
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),
        sme_name: contractData.sme_name || "",
        factoring_contract_number: contractData.contract_number || "",
        factoring_contract_sign_date: formatDate(contractData.approval_date),
        factoring_limit: formatCash(parseInt(contractData.approval_amt || 0)) || "",
        factoring_limit_text: numberToString(parseInt(contractData.approval_amt || 0)) || "",
        account_holder: legalRep.full_name || "",
        account_number: contractData.bank_account || "",
        bank_name: bankInfo?.nameVn || "",
        anchor_name: contractData.anchor_name || "",
        document_date: document_date,
      },
    },
  };
}

async function getDataForTemplateBTTKCBB(contract_number, callback_url) {
  //  Giấy cam kết bên bán (1) BM07
  const docType = "BTTKCBB";
  const taskName = "GEN_DOCS_CONTRACT_FILE";
  const contractData = (await loanContractRepo.getAllContractData(contract_number)) || {};
  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        document_no: "",
        // document_place: "",
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),
        sme_name: contractData.sme_name || "",
        anchor_name: contractData.anchor_name || "",

        contract_sign_date: formatDate(contractData.approval_date),
        contract_number: contractData.contract_number || "",
        anchor_contract_number: contractData.anchor_contract_number,
        anchor_contract_number_date: formatDate(contractData.anchor_contract_number_date),
        processing_days: "3",
      },
    },
  };
}

async function getDataForTemplateBTTQDCV(contract_number, callback_url) {
  //  Quyết định cho vay BM08
  const docType = "BTTQDCV";
  const taskName = "GEN_DOCS_CONTRACT_FILE";

  const contractData = (await loanContractRepo.getAllContractData(contract_number)) || {};
  const productInfo = await productService.getProductLimitInfo(global.config, contractData?.product_code);

  const def_duration = productInfo?.defDuration;
  const factoring_term_days = productInfo?.factoringTermDays;
  const advance_rate = productInfo?.advanceRate;
  const factoring_rate = productInfo?.rate?.find((r) => r.intRateName === "OFFER_NORMAL_RATE").intRateVal ?? null;
  const factoring_fee = productInfo?.fee[0]?.feeDetail[0]?.calculaDetail[0]?.value;

  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        document_no: "",
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),
        sme_name: contractData.sme_name || "",
        sme_headquarters_address: contractData.sme_headquarters_address || "",
        registration_cert_no: contractData.registration_cert_no || "",
        registration_cert_issue_place: contractData.registration_cert_issue_place || "",
        registration_cert_issue_date: formatDate(contractData.registration_cert_issue_date) || "",
        contract_number: contractData.contract_number || "",
        approval_date: formatDate(contractData.approval_date),
        sme_representation_name: contractData.sme_representation_name || "",
        sme_representation_position: contractData.sme_representation_position || "",
        approval_amt: formatCash(parseInt(contractData.approval_amt || 0)) || "",
        approval_amt_text: numberToString(parseInt(contractData.approval_amt || 0)) || "",
        def_duration: def_duration ? parseInt(def_duration) : "",
        factoring_term_days: factoring_term_days ? parseInt(factoring_term_days) : "",
        advance_rate: advance_rate,
        factoring_rate: factoring_rate,
        factoring_fee: factoring_fee,
      },
    },
  };
}

async function getDataForTemplateBZHMHDTD(contract_number, callback_url) {
  // Giấy đề nghị ứng trước kiêm KUNN BM02
  const docType = "VLDHDTD";
  const taskName = "GEN_DOCS_CONTRACT_FILE";

  const loanContractData = await loanContractRepo.getAllContractData(contract_number);
  const loanContractRepresentationsData = await loanContractRepresentationsRepo.getRepresentationsByCustomer(contract_number);

  const legalRep = loanContractRepresentationsData[0] || {};
  const contractData = loanContractData || {};

  // const config = global.config;

  const [bankInfo, legal_identity_issue_place, legal_position] = await Promise.all([getValueCodeMasterdataV2(contractData.bank_code, "BANK"), getValueCodeMasterdataV2(legalRep?.issue_place, "ISSUE_PLACE_VN"), getValueCodeMasterdataV2(legalRep?.position, "PROFESSION")]);

  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),
        contract_number: contractData.contract_number || "",
        sme_name: contractData.sme_name || "",
        registration_cert_no: contractData.registration_cert_no || "",
        registration_cert_issue_date: formatDate(contractData.registration_cert_issue_date) || "",
        registration_cert_issue_place: contractData.registration_cert_issue_place || "",
        sme_headquarters_address: await getFullAddress(global.config, contractData?.sme_headquarters_province, contractData?.sme_headquarters_district, contractData?.sme_headquarters_ward, contractData?.sme_headquarters_address) || "",
        sme_headquarters_new_address: await getFullAddressNew(contractData?.sme_headquarters_new_province, contractData?.sme_headquarters_new_ward, contractData?.sme_headquarters_address) || "",
        company_phone_number: contractData.company_phone_number || "",
        sme_tax_id: contractData?.sme_tax_id || "",
        sme_email: contractData.sme_email || "",
        sme_representation_name: legalRep.full_name || "",
        sme_representation_position: legal_position?.nameVn || "",
        legal_rep_id_number: legalRep.id_number || "",
        legal_rep_id_isued_date: formatDate(legalRep.issue_date) || "",
        legal_rep_id_issued_by: legal_identity_issue_place?.nameVn || "",
        account_number: contractData.bank_account || "",
        bank_name: bankInfo?.nameVn || "",
        total_turnover_next_year: contractData?.total_turnover_next_year || "",
        total_cost_over_next_year: contractData?.total_cost_over_next_year || "",
        pre_tax_profit_next_year: contractData?.pre_tax_profit_next_year || "",
        owner_equity: contractData?.owner_equity || "",
        loans_other_financial_institutions: contractData?.loans_other_financial_institutions || "",
        other_capital: contractData?.other_capital || "",
        request_amt: contractData?.request_amt || "",
        request_amt_text: numberToString(parseInt(contractData.request_amt || 0)) || "",
      },
    },
  };
}

async function getDataForTemplateBZHMKCBL(contract_number, callback_url) {
  //  Giấy cam kết bên bán (1) BM07
  const docType = "VLDCKBL";
  const taskName = "GEN_DOCS_CONTRACT_FILE";
  const contractData = (await loanContractRepo.getAllContractData(contract_number)) || {};
  const legal_position = await getValueCodeMasterdataV2(contractData?.sme_representation_position, "PROFESSION");

  const shareholders_bycontract = await sqlHelper.find({
    table: "loan_customer_shareholders",
    whereCondition: {
      contract_number: contract_number,
    },
  });

  let order = 0;
  const individual_shareholders = [];
  const organization_shareholders = [];

  const shareholders = await Promise.all(
    shareholders_bycontract?.map(async (sh) => {
      if (sh?.subject === "INDIVIDUAL") {
        individual_shareholders.push({
          order: ++order,
          shareholder_full_name: sh?.full_name || "",
          shareholder_dob: formatDate(sh?.dob) || "",
          shareholder_id_number: sh?.id_number || "",
          shareholder_issue_date: formatDate(sh?.issue_date) || "",
          shareholder_issue_place: sh?.issue_place || "",
          shareholder_per_address: (await getFullAddressNew(sh?.per_new_province_code, sh?.per_new_ward_code, sh?.per_detail_address)) || "",
          shareholder_cur_address: (await getFullAddressNew(sh?.cur_new_province_code, sh?.cur_new_ward_code, sh?.cur_detail_address)) || "",
          shareholder_phone: sh?.phone_number || "",
          shareholder_email: sh?.email || "",
        });
      } else if (sh?.subject === "ORGANIZATION") {
        organization_shareholders.push({
          order: ++order,
          shareholder_company_name: sh?.company_name || "",
          shareholder_registration: sh?.tax_id || "",
          shareholder_issue_place: sh?.issue_place || "",
          shareholder_issue_date: formatDate(sh?.issue_date) || "",
          shareholder_headquarter: (await getFullAddressNew(sh?.business_new_province_code, sh?.business_new_ward_code, sh?.business_detail_address)) || "",
          shareholder_business_phone_number: sh?.business_phone_number || "",
          shareholder_business_email: sh?.business_email || "",
          shareholder_fax: "", // khong co data
          shareholder_full_name: sh?.full_name || "",
          shareholder_position: sh?.position || "",
          shareholder_id_number: sh?.id_number || "",
          shareholder_per_address: (await getFullAddressNew(sh?.per_new_province_code, sh?.per_new_ward_code, sh?.per_detail_address)) || "",
          shareholder_phone_number: sh?.phone_number || "",
          shareholder_email: sh?.email || "",
        });
      }
      return { order: order, shareholder_name: sh?.subject === "INDIVIDUAL" ? sh?.full_name : sh?.company_name };
    })
  );

  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),
        individual_shareholders: individual_shareholders,
        organization_shareholders: organization_shareholders,
        sme_name: contractData.sme_name || "",
        registration_cert_no: contractData.registration_cert_no || "",
        registration_cert_issue_place: contractData.registration_cert_issue_place || "",
        registration_cert_issue_date: formatDate(contractData.registration_cert_issue_date) || "",
        sme_headquarters_address: (await getFullAddress(global.config, contractData?.sme_headquarters_province, contractData?.sme_headquarters_district, contractData?.sme_headquarters_ward, contractData?.sme_headquarters_address)) || "",
        sme_headquarters_new_address: (await getFullAddressNew(contractData?.sme_headquarters_new_province, contractData?.sme_headquarters_new_ward, contractData?.sme_headquarters_address)) || "",
        company_phone_number: contractData.company_phone_number || "",
        sme_email: contractData.sme_email || "",
        sme_fax: "", // khong co data
        sme_representation_name: contractData.sme_representation_name || "",
        sme_representation_position: legal_position?.nameVn || "",
        contract_number: contractData.contract_number || "",
        shareholders: shareholders,
      },
    },
  };
}

async function getDataForTemplateBZHMQDCV(contract_number, callback_url) {
  //  Quyết định cho vay BM08
  const docType = "VLDQDCV";
  const taskName = "GEN_DOCS_CONTRACT_FILE";

  const contractData = (await loanContractRepo.getAllContractData(contract_number)) || {};
  const productInfo = await productService.getProductLimitInfo(global.config, contractData?.product_code);

  const def_duration = productInfo?.defDuration;
  const factoring_term_days = productInfo?.factoringTermDays;
  const advance_rate = productInfo?.advanceRate;
  const factoring_rate = productInfo?.rate?.find((r) => r.intRateName === "OFFER_NORMAL_RATE").intRateVal ?? null;
  const factoring_fee = productInfo?.fee[0]?.feeDetail[0]?.calculaDetail[0]?.value;

  return {
    task_name: taskName,
    body: {
      callback_url: callback_url,
      contract_number: contractData?.contract_number || "",
      doc_type: docType,
      contract_data: {
        document_no: "",
        day: dateHelper.getDate(),
        month: dateHelper.getMonth(),
        year: dateHelper.getYear(),
        sme_name: contractData.sme_name || "",
        sme_headquarters_address: contractData.sme_headquarters_address || "",
        registration_cert_no: contractData.registration_cert_no || "",
        registration_cert_issue_place: contractData.registration_cert_issue_place || "",
        registration_cert_issue_date: formatDate(contractData.registration_cert_issue_date) || "",
        contract_number: contractData.contract_number || "",
        approval_date: formatDate(contractData.approval_date),
        sme_representation_name: contractData.sme_representation_name || "",
        sme_representation_position: contractData.sme_representation_position || "",
        approval_amt: formatCash(parseInt(contractData.approval_amt || 0)) || "",
        approval_amt_text: numberToString(parseInt(contractData.approval_amt || 0)) || "",
        def_duration: def_duration ? parseInt(def_duration) : "",
        factoring_term_days: factoring_term_days ? parseInt(factoring_term_days) : "",
        advance_rate: advance_rate,
        factoring_rate: factoring_rate,
        factoring_fee: factoring_fee,
      },
    },
  };
}

const checkAllAF3DocumentsExist = async (contract_number, doctypeToCheck = AF3_TEMPLATE_DOCTYPE_SIGNED) => {
  const documents = await documentRepo.findByContractAndTypes(contract_number, doctypeToCheck);
  const result = doctypeToCheck.every((docType) => documents.some((doc) => doc.doc_type === docType));

  const doctypegenerated = documents.filter((doc) => doctypeToCheck.includes(doc.doc_type));

  console.log(`document generated for contract ${contract_number}:  ${doctypegenerated.map((doc) => doc?.doc_type).join(", ")}`);

  return result;
};

module.exports = {
  getDataForTemplateBTTHDTD,
  getDataForTemplateBTTCNKPT,
  getDataForTemplateBTTKCBB,
  getDataForTemplateBTTQDCV,
  getDataForTemplateBZHMHDTD,
  getDataForTemplateBZHMKCBL,
  getDataForTemplateBZHMQDCV,
  checkAllAF3DocumentsExist,
};
