const dataentryRepo = require("../repositories/data-entry-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const turnoverRepo = require("../repositories/turnover-repo")
const common = require("../utils/common")
const constant = require('../const/definition');
const moment = require('moment')
const status = require("../const/caseStatus");
const { json } = require("express");
const { sum } = require("lodash");
const aadService = require("../utils/aadService")
const utils = require("../utils/helper")
const dateHelper = require("../utils/dateHelper")
const loggingService = require("../utils/loggingService")
const defind = require("../const/definition")
const { computeVSKOffer } = require("../offer/VSK-offer");
const {MANUAL_TASK_CODE,CONTRACT_TYPE, PARTNER_CODE} = require("../const/definition")
const {routing} = require("../services/workflow-service")
const {cusType} = require("../const/cusType")
const {roleCode} = require('../const/definition')
const {getProductInfoV2} = require("../utils/productService")
const {caseStatusCode} = require("../const/caseStatus")
const moment1 = require('moment-timezone')
moment1().tz('Asia/Ho_Chi_Minh').format()
const crmService = require("../utils/crmService")

const saveDataEntry = async function (req, res) {
    try { 
        let contractNumber = req.body.contractNumber
        let schemaUpdate = 'FAILD'
        let countInclude = 0
        let rsDE
        let dataEntry = await dataentryRepo.getDataEntry(req.poolRead, contractNumber)
        const partnerCode = (await loanContractRepo.getLoanContract(contractNumber))?.partner_code
        if(dataEntry.rowCount>0){
            await dataEntry.rows.forEach(e => {
                if(["turnover","totalCP","DE_COMMENT","CE_COMMENT","transaction"].includes(e.info_type)) countInclude ++
            });
        }
        let countError = 0
        let sumUpdate1_6 = 0
        // let turnOverValueUpdate = []
        let transaction_6m = 0
        // let transactionValueFor_6m = []
        if(countInclude == 0){//save
            let pl = req.body
            if(pl.turnOver){
                for (const element of pl.turnOver) {
                    if(element.value_of_month == ''){
                        element.value_of_month = null
                    } 
                    else{
                        // turnOverValueUpdate.push(element.value_of_month)
                        sumUpdate1_6 += element.value_of_month
                    }
                    if(partnerCode===PARTNER_CODE.VSK){
                        const saveDataEntryRs = await dataentryRepo.saveDataEntry(req.poolWrite, contractNumber, "turnover", element)
                        const saveDataEntryRs1 = await dataentryRepo.saveDataEntry(req.poolWrite, contractNumber, "mod_turnover", element)
                        if(!saveDataEntryRs || !saveDataEntryRs1) {
                            countError++
                        }
                    }
                    if(partnerCode===PARTNER_CODE.KOV){
                        const saveDataEntryRs = await dataentryRepo.saveDataEntry(req.poolWrite, contractNumber, "turnover", element)
                        const saveDataEntryRs1 = await dataentryRepo.saveDataEntry(req.poolWrite, contractNumber, "real_turn_over", element)
                        if(!saveDataEntryRs || !saveDataEntryRs1) {
                            countError++
                        }
                    }
                    
                }
                // sumUpdate1_6 = sum([turnOverValueUpdate[0],turnOverValueUpdate[1],turnOverValueUpdate[2],turnOverValueUpdate[3],turnOverValueUpdate[4],turnOverValueUpdate[5]])
            }
            if(pl.totalCP){
                for (const element of pl.totalCP) {
                    if(element.value_of_month == '') element.value_of_month = null
                    const saveDataEntryRs = await dataentryRepo.saveDataEntry(req.poolWrite, contractNumber, "TOTAL_CP", element)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
            }
            if(pl.DeCommentInfo){
                for (const element of pl.DeCommentInfo) {
                    const saveDataEntryRs = await dataentryRepo.saveDataEntryV2(req.poolWrite, contractNumber, "DE_COMMENT", element)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
            }
            if(pl.CeCommentInfo){
                for (const element of pl.CeCommentInfo) {
                    const saveDataEntryRs = await dataentryRepo.saveDataEntryV2(req.poolWrite, contractNumber, "CE_COMMENT", element)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
            }
            if(pl.transactionInfo){
                for (const element of pl.transactionInfo) {
                    if(element.value_of_month == ''){
                        element.value_of_month = null
                    }
                    else{
                        // transactionValueFor_6m.push(element.value_of_month)
                        transaction_6m += element.value_of_month
                    }
                    const saveDataEntryRs = await dataentryRepo.saveDataEntry(req.poolWrite, contractNumber, "transaction", element)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
                // transaction_6m = sum([transactionValueFor_6m[0],transactionValueFor_6m[1],transactionValueFor_6m[2],transactionValueFor_6m[3],transactionValueFor_6m[4],transactionValueFor_6m[5]])
            }
        }
        else{
            let pl = req.body
            let updated_date = moment().format(constant.DATE_FORMAT.YYYYMMDD_HHmmss)
            if(pl.turnOver){
                for (const element of pl.turnOver) {
                    if(element.value_of_month == ''){
                        element.value_of_month = null
                    }
                    else{
                        // turnOverValueUpdate.push(element.value_of_month)
                        sumUpdate1_6 += element.value_of_month
                    }
                    if(partnerCode===PARTNER_CODE.VSK){
                        const saveDataEntryRs = await dataentryRepo.updateDataEntry(req.poolWrite, contractNumber, "turnover", element, updated_date)
                        const saveDataEntryRs1 = await dataentryRepo.updateDataEntry(req.poolWrite, contractNumber, "mod_turnover", element, updated_date)
                        if(!saveDataEntryRs || !saveDataEntryRs1) {
                            countError++
                        }
                    }
                    if(partnerCode===PARTNER_CODE.KOV){
                        const saveDataEntryRs = await dataentryRepo.updateDataEntry(req.poolWrite, contractNumber, "turnover", element, updated_date)
                        const saveDataEntryRs1 = await dataentryRepo.updateDataEntry(req.poolWrite, contractNumber, "real_turn_over", element, updated_date)
                        if(!saveDataEntryRs || !saveDataEntryRs1) {
                            countError++
                        }
                    }
                }
                // sumUpdate1_6 = sum([turnOverValueUpdate[0],turnOverValueUpdate[1],turnOverValueUpdate[2],turnOverValueUpdate[3],turnOverValueUpdate[4],turnOverValueUpdate[5]])
            }
            
            if(pl.totalCP){
                for (const element of pl.totalCP) {
                    if(element.value_of_month == '') element.value_of_month = null
                    const saveDataEntryRs = await dataentryRepo.updateDataEntry(req.poolWrite, contractNumber, "TOTAL_CP", element, updated_date)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
            }
        
            if(pl.DeCommentInfo){
                for (const element of pl.DeCommentInfo) {
                    const saveDataEntryRs = await dataentryRepo.updateDataEntryV2(req.poolWrite, contractNumber, "DE_COMMENT", element, updated_date)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
            }
        
            if(pl.CeCommentInfo){
                for (const element of pl.CeCommentInfo) {
                    const saveDataEntryRs = await dataentryRepo.updateDataEntryV2(req.poolWrite, contractNumber, "CE_COMMENT", element, updated_date)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
            }
        
            if(pl.transactionInfo){
                for (const element of pl.transactionInfo) {
                    if(element.value_of_month == ''){
                        element.value_of_month = null
                    }
                    else{
                        // transactionValueFor_6m.push(element.value_of_month)
                        transaction_6m += element.value_of_month
                    }
                    const saveDataEntryRs = await dataentryRepo.updateDataEntry(req.poolWrite, contractNumber, "transaction", element, updated_date)
                    if(!saveDataEntryRs) {
                        countError++
                    }
                }
                // transaction_6m = sum([transactionValueFor_6m[0],transactionValueFor_6m[1],transactionValueFor_6m[2],transactionValueFor_6m[3],transactionValueFor_6m[4],transactionValueFor_6m[5]])
            }
        }
        
        if(countError > 0) {
            return res.status(400).json({
                code: 1,
                message: `${contractNumber}: Error at save data entry`
            })
        }
        else {
            let dataEntry2 = await dataentryRepo.getDataEntry(req.poolRead, contractNumber)
            let dataLoanContract = await loanContractRepo.getLoanContract(contractNumber) 
            if(partnerCode===PARTNER_CODE.VSK){
                if(dataLoanContract&&dataEntry2.rowCount>0){
                    schemaUpdate = configProduct(dataEntry2,dataLoanContract)
                    console.log("schemaUpdate", JSON.stringify(schemaUpdate));
                    rsDE = await getDataEntryForSaveHis(req, contractNumber)
                }
               
                const productCode = schemaUpdate=='VIP'?'MCBAS_VIP':schemaUpdate=='PREMIUM'?'MCBAS_PREMIUM':schemaUpdate=='STANDARD'?'MCBAS_STANDARD':schemaUpdate=='ALL'?'MCBAS_ALL':schemaUpdate=='HMTD'?'MCBAS_HMTD':'FALSE'
                const productData = await getProductInfoV2(productCode)
    
                if(productCode != 'FALSE') loanContractRepo.updateFieldLoanContract(contractNumber,'product_code',productCode)
                if(productCode == 'MCBAS_HMTD'){
                    const curTime = moment1().add(36,'months').format('yyyy-MM-DD')
                    loanContractRepo.updateFieldLoanContract(contractNumber,'end_date',curTime)
                }
                loanContractRepo.updateFieldLoanContract(contractNumber,'cust_type',schemaUpdate==cusType.STANDARD?cusType.STANDARD:schemaUpdate==cusType.VIP?cusType.VIP:schemaUpdate==cusType.PREMIUM?cusType.PREMIUM:'')
                if(productData){
                    loanContractRepo.updateFieldLoanContract(contractNumber,'request_int_rate',productData.productVar[0].intRate/100)
                }
                if(rsDE){
                    const userName = req.body.userName || ''
                    let maxCount = 1
                    let dataMaxCount = await dataentryRepo.getMaxCount(req.poolRead,contractNumber)
                    if(dataMaxCount.rowCount != 0) maxCount = dataMaxCount.rows[0].count_assignment+1
    
                    /*phase 2 khong can tính tong*/
                    loggingService.saveUpdateHist(req.poolWrite,'sum1_6',rsDE.sum1_6,sumUpdate1_6,userName,contractNumber,maxCount)
                    loggingService.saveUpdateHist(req.poolWrite,'schema',rsDE.schema,schemaUpdate,userName,contractNumber,maxCount)
                    
                }
            }
            
            if(req.body.taskId){
                if(partnerCode===PARTNER_CODE.VSK){
                    if(schemaUpdate=='FAILD' || schemaUpdate=='ALL'){
                        await Promise.all([
                            loanContractRepo.updateFieldLoanContract(contractNumber,'status',caseStatusCode.REFUSED),
                            aadService.completedTaskByTaskId(contractNumber,req.body.taskId),
                            crmService.rejectContract(global.config, contractNumber)
                        ])
                       
                        let responseBody = {
                            "code": "0",
                            "step" : "DE SAVE & SUBMIT",
                            "message": "save info success",
                            "nextStep" : "REFUSE CASE"
                        }
                        return res.status(200).json(responseBody)
                    }
                    else{
                        loanContractRepo.updateFieldLoanContract(contractNumber,'turnover_12m',sumUpdate1_6)
                        loanContractRepo.updateFieldLoanContract(contractNumber,'transaction12m',transaction_6m)
                        
                        let currentTask
                        let workflowBody = {
                            contract_number : contractNumber,
                            partner_code : "VSK",
                            product_code : schemaUpdate=='VIP'?'MCBAS_VIP':schemaUpdate=='PREMIUM'?'MCBAS_PREMIUM':schemaUpdate=='STANDARD'?'MCBAS_STANDARD':schemaUpdate=='HMTD'?'MCBAS_HMTD':''
                        }
                        let responseBody = {
                            "code": "0",
                            "step" : "DE SAVE & SUBMIT",
                            "message": "save info successful",
                            "nextStep" : "move task to CE"
                        }
                        aadService.completedTaskByTaskId(contractNumber,req.body.taskId)
                        if(req.body.decision=="RESUBMIT_DE"){
                            const poolWrite = req.poolWrite
                            const mistakeCode = req.body.mistakeCode
                            const userName = req.body.userName
                            const taskId = req.body.taskId
                            const mistakeDes = req.body.mistakeDes
                            const sql = "insert into loan_manual_decision (contract_number,case_code,step,task_id,assignee,role,mistake_desc,result_chk) values ($1,$2,$3,$4,$5,$6,$7,$8)"
                            poolWrite.query(sql,[contractNumber,mistakeCode,roleCode.CE,taskId,userName,roleCode.CE,mistakeDes,"RESUBMIT"])
                            responseBody = {
                                "code": "0",
                                "step" : "CE RESUBMIT DE",
                                "message": "save info successful",
                                "nextStep" : "move task to DE"
                            }
                            currentTask = MANUAL_TASK_CODE.CE_MANUAL.CE_RESUBMIT_DE
                            workflowBody.currentTask = currentTask
                            routing(workflowBody)
                            return res.status(200).json(responseBody)
                        }
                        currentTask = MANUAL_TASK_CODE.DE_MANUAL.DE_SAVE_SUBMIT
                        workflowBody.currentTask = currentTask
                        routing(workflowBody)
                        
                        return res.status(200).json(responseBody)
                    }
                }
                if(partnerCode===PARTNER_CODE.KOV){
                    loanContractRepo.updateFieldLoanContract(contractNumber,'turnover_12m',sumUpdate1_6)
                    loanContractRepo.updateFieldLoanContract(contractNumber,'transaction12m',transaction_6m)
                    const loanData = await loanContractRepo.getLoanContract(contractNumber)
                    let body = {}
                    let currentTaskCode = 'MC_CRE_A1';
                    body.current_task_code = currentTaskCode
                    const sql = `select month_of_info as "month", value_of_month as "amount", info_type from loan_turnover_transaction where 1=1 and info_type in ('turnover','transaction') and contract_number=$1`;
                    const sqlRs = await req.poolWrite.query(sql,[contractNumber])
                    let turnOver = [], transaction = [];
                    await sqlRs?.rows.map(
                        async (item)=>{
                            if(item.info_type==='turnover') turnOver.push({month: item.month,amount: item.amount})
                            if(item.info_type==='transaction') transaction.push({month: item.month,amount: item.amount})
                        }
                    )
                    body.data = {
                        "requestId": utils.genRequestId(PARTNER_CODE.KOV),
                        "channel": loanData?.channel,
                        "partnerCode": PARTNER_CODE.KOV,
                        "customerName": loanData?.cust_full_name,
                        "gender": loanData?.gender||"F",
                        "dateOfBirth": moment(loanData?.birth_date).format('yyyy-MM-DD')||"",
                        "identityCardId": loanData?.id_number,
                        "issueDate": moment(loanData?.id_issue_dt).format('yyyy-MM-DD')||"",
                        "issuePlace": loanData?.id_issue_place||"",
                        "otherIdentityCardId": loanData?.other_id_number||"",
                        "otherIssueDate": moment(loanData?.other_issue_date).format('yyyy-MM-DD')||"",
                        "otherIssuePlace": loanData?.other_issue_place||"",
                        "phoneNumber": loanData?.phone_number1||"",
                        "numberOfDependents": loanData?.num_of_dependants||0,
                        "temProvince":loanData?.province_cur||"",
                        "monthlyExpenses": loanData?.m_household_expenses||"",
                        "loanAmount": loanData?.request_amt||"",
                        "productCode": loanData?.product_code||"",
                        "typeTrading": loanData?.type_trading||"",
                        "businessLegal": loanData?.business_legal||"",
                        "turnover12m": loanData?.turnover_12m||"",
                        "turnover": turnOver,
                        "transaction12m": loanData?.transaction12m||"",
                        "transaction": transaction,
                        "timeDuration": loanData?.time_duration||"",
                        "businessDuration": loanData?.bussiness_duration||"",
                        "latestTaxPayment": loanData?.latest_tax_payment||"",
                        "activeMonth": loanData?.active_month||"",
                        "sectorIndustry": loanData?.sector_industry||"",
                        "businessType": loanData?.business_type||"",
                        "capitalNeed": loanData?.capital_need||"",
                        "selfFinancing": loanData?.self_financing||"",
                        "otherCapital": loanData?.other_capital||"",
                        "fundingFromEc": loanData?.funding_from_ec||"",
                        "repaymentSources": loanData?.repayment_sources||"",
                        "merchantContractNumber": loanData?.merchant_contract_number||"",
                        "representatiMerchantContract": loanData?.representati_merchant_contract||"",
                        "startDateOnMerchant": moment(loanData?.start_date_on_merchant).format('yyyy-MM-DD')||"",
                        "endDateOnMerchant": moment(loanData?.end_date_on_merchant).format('yyyy-MM-DD')||""
                    }
                    body.data.contractNumber = contractNumber
                    
                    let lb = req.config.basic.wfMcCredit[req.config.env];

                    const envType = req.config.data.env.wf_uri;
                    let wf_lb = lb
                    if(envType=='local') {
                        wf_lb = "http://localhost:1001"
                    }
                    
                    let workflowUri = req.config.data.workflow.uri;
                    let workflowUrl = wf_lb + workflowUri

                    common.postAPI(workflowUrl,body).then()
                    .catch(err =>{
                        console.log(err)
                        common.log("CALL WORKFLOW : error","ERROR")
                    })
                    let responseBody = {
                        "code": "0",
                        "step" : "DE SAVE & SUBMIT",
                        "message": `${contractNumber}: save info successful`,
                        "nextStep" : "call workflow"
                    }
                    aadService.completedTaskByTaskId(contractNumber,req.body.taskId)
                    
                    return res.status(200).json(responseBody)
                }
            }
            else{
                return res.status(200).json({
                    code: 0,
                    message: 'Save data entry success'
                })
            }
        }
    }
    catch(err) {
        common.log(`Save data entry error : ${err.message}`)
        return res.status(500).json({
            code: 2,
            message: 'Service error'
        })
    }
}

async function getDataEntryForSaveHis(req, contractNumber){
    let dataEntries, turnOverValue = [];
    let sum1_6;
    let dataEntry = await dataentryRepo.getDataEntry(req.poolRead, contractNumber)
    let contractData = await loanContractRepo.getLoanContract(contractNumber)
    const cusType = contractData.product_code
    const cusTypeConfig = status.VSK_PRODUCT 
    let productType = ''
    if(cusType == cusTypeConfig.STANDARD){
        productType = 'STANDARD'
    }
    else if(cusType == cusTypeConfig.VIP){
        productType = 'VIP'
    }
    else if(cusType == cusTypeConfig.PREMIUM){
        productType = 'PREMIUM'
    }
    if(dataEntry.rowCount > 0)  dataEntries = dataEntry.rows
    dataEntries.forEach(i => {
        if(i.info_type == 'turnover'){
            turnOverValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
        } 
    });
    sum1_6 = sum([turnOverValue[0],turnOverValue[1],turnOverValue[2],turnOverValue[3],turnOverValue[4],turnOverValue[5],turnOverValue[6]])
    return {
        schema: productType,
        sum1_6: sum1_6
    }
}

const getDataEntry = async function (req, res) {
    try { 
        const contractNumber = req.query.contractNumber
        let contractData = await loanContractRepo.getLoanContract(contractNumber)
        const partnerCode = contractData?.partner_code
        if(partnerCode===PARTNER_CODE.VSK){
            let cusTypeConfig = status.VSK_PRODUCT 
            let turnoverData = await dataentryRepo.getDataEntry(req.poolRead, contractNumber)
            let tsum1_6, lsum1_6, tranSum1_6;
            let maxLoan,turnoverEstimate;
            let turnOverValue = [], totalCpValue = [], transactionValue = [], turnOverInfo = [], totalCpInfo = [], DeCommentInfo = [], CeCommentInfo = [], transactionInfo = []
            let turnOverMOI = [], totalCpMOI = [], deCommentMOI = [], ceCommentMOI = [], transactionMOI = []
            const arrTemp = [1,2,3,4,5,6]
            // const arrTemp2 = [1,2,3,4,5,6]  
            
            const turnoverDatas = turnoverData.rows
            const cusType = contractData.product_code
            // const typeTrading = contractData.type_trading
    
            let productType = ''
            if(cusType == cusTypeConfig.STANDARD){
                productType = 'STANDARD'
            }
            else if(cusType == cusTypeConfig.VIP){
                productType = 'VIP'
            }
            else if(cusType == cusTypeConfig.PREMIUM){
                productType = 'PREMIUM'
            }
    
            turnoverDatas.forEach(i => {
                if(i.info_type == 'turnover'){
                    turnOverInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:''}) 
                    turnOverMOI.push(i.month_of_info)
                    turnOverValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
                } 
                else if(i.info_type == 'TOTAL_CP'){
                    totalCpInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:''}) 
                    totalCpMOI.push(i.month_of_info)
                    totalCpValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
                }
                else if(i.info_type == 'DE_COMMENT'){
                    DeCommentInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month_string?i.value_of_month_string:''}) 
                    deCommentMOI.push(i.month_of_info)
                }
                else if(i.info_type == 'CE_COMMENT'){
                    CeCommentInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month_string?i.value_of_month_string:''}) 
                    ceCommentMOI.push(i.month_of_info)
                }
                else if(i.info_type == 'transaction'){
                    transactionInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:''}) 
                    transactionMOI.push(i.month_of_info)
                    transactionValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
                }
            });
            
            const tempTurnOverMOI = arrTemp.filter(x => !turnOverMOI.includes(x));
            const tempTotalCpMOI = arrTemp.filter(x => !totalCpMOI.includes(x));
            const tempDeCommentMOI = arrTemp.filter(x => !deCommentMOI.includes(x));
            const tempCeCommentMOI = arrTemp.filter(x => !ceCommentMOI.includes(x));
            const tempTransactionMOI = arrTemp.filter(x => !transactionMOI.includes(x));
            if(tempTurnOverMOI){
                tempTurnOverMOI.forEach(e => {
                    turnOverInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempTotalCpMOI){
                tempTotalCpMOI.forEach(e => {
                    totalCpInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempDeCommentMOI){
                tempDeCommentMOI.forEach(e => {
                    DeCommentInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempCeCommentMOI){
                tempCeCommentMOI.forEach(e => {
                    CeCommentInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempTransactionMOI){
                tempTransactionMOI.forEach(e => {
                    transactionInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            tsum1_6 = sum([turnOverValue[0],turnOverValue[1],turnOverValue[2],turnOverValue[3],turnOverValue[4],turnOverValue[5]])
            lsum1_6 = sum([totalCpValue[0],totalCpValue[1],totalCpValue[2],totalCpValue[3],totalCpValue[4],totalCpValue[5]])
            tranSum1_6 = sum([transactionValue[0],transactionValue[1],transactionValue[2],transactionValue[3],transactionValue[4],transactionValue[5]])
            const offerData = await computeVSKOffer(contractNumber,1)
            // console.log('offerData.code',offerData.code)
            if(offerData.code == -1){
                maxLoan = turnoverEstimate = 0
            }
            else{
                maxLoan = offerData.data.offer
                turnoverEstimate = offerData.data.estimateRevenue.totalEstimateRevenue
            }
            
            return res.status(200).json({
                code: 0,
                message: `${contractNumber}: get data entry success`,
                data: {
                    productType: productType,
                    turnOver:turnOverInfo,
                    totalCP:totalCpInfo,
                    DeCommentInfo: DeCommentInfo,
                    CeCommentInfo: CeCommentInfo,
                    transactionInfo: transactionInfo,
                    tsum1_6: tsum1_6?Math.round(tsum1_6):'', 
                    lsum1_6: lsum1_6?Math.round(lsum1_6):'', 
                    tranSum1_6: tranSum1_6?Math.round(tranSum1_6):'',
                    maxLoan: maxLoan?Math.round(maxLoan):'',
                    turnoverEstimate: turnoverEstimate?Math.round(turnoverEstimate):''
                }
            })
        }

        if(partnerCode===PARTNER_CODE.KOV){
            let turnoverData = await dataentryRepo.getDataEntry(req.poolRead, contractNumber)
            let tsum1_12, lsum1_12, tranSum1_12;
            let maxLoan,turnoverEstimate;
            // let turnOverValue = [], totalCpValue = [], transactionValue = []
            let turnOverInfo = [], totalCpInfo = [], DeCommentInfo = [], CeCommentInfo = [], transactionInfo = []
            let turnOverMOI = [], totalCpMOI = [], deCommentMOI = [], ceCommentMOI = [], transactionMOI = []
            const arrTemp = [1,2,3,4,5,6,7,8,9,10,11,12]            
            const turnoverDatas = turnoverData.rows
    
            turnoverDatas.forEach(i => {
                if(i.info_type == 'turnover'){
                    turnOverInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:''}) 
                    turnOverMOI.push(i.month_of_info)
                    // turnOverValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
                    tsum1_12 += Number.parseFloat(i.value_of_month?i.value_of_month:0)
                } 
                else if(i.info_type == 'TOTAL_CP'){
                    totalCpInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:''}) 
                    totalCpMOI.push(i.month_of_info)
                    // totalCpValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
                }
                else if(i.info_type == 'DE_COMMENT'){
                    DeCommentInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month_string?i.value_of_month_string:''}) 
                    deCommentMOI.push(i.month_of_info)
                }
                else if(i.info_type == 'CE_COMMENT'){
                    CeCommentInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month_string?i.value_of_month_string:''}) 
                    ceCommentMOI.push(i.month_of_info)
                }
                else if(i.info_type == 'transaction'){
                    transactionInfo.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:''}) 
                    transactionMOI.push(i.month_of_info)
                    // transactionValue.push(Number.parseFloat(i.value_of_month?i.value_of_month:0))
                    tranSum1_12 += Number.parseFloat(i.value_of_month?i.value_of_month:0)
                }
            });
            
            const tempTurnOverMOI = arrTemp.filter(x => !turnOverMOI.includes(x));
            const tempTotalCpMOI = arrTemp.filter(x => !totalCpMOI.includes(x));
            const tempDeCommentMOI = arrTemp.filter(x => !deCommentMOI.includes(x));
            const tempCeCommentMOI = arrTemp.filter(x => !ceCommentMOI.includes(x));
            const tempTransactionMOI = arrTemp.filter(x => !transactionMOI.includes(x));
            if(tempTurnOverMOI){
                tempTurnOverMOI.forEach(e => {
                    turnOverInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempTotalCpMOI){
                tempTotalCpMOI.forEach(e => {
                    totalCpInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempDeCommentMOI){
                tempDeCommentMOI.forEach(e => {
                    DeCommentInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempCeCommentMOI){
                tempCeCommentMOI.forEach(e => {
                    CeCommentInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            if(tempTransactionMOI){
                tempTransactionMOI.forEach(e => {
                    transactionInfo.push({month_of_info:e,value_of_month:''}) 
                });
            }
            // tsum1_6 = sum([turnOverValue[0],turnOverValue[1],turnOverValue[2],turnOverValue[3],turnOverValue[4],turnOverValue[5]])
            // lsum1_6 = sum([totalCpValue[0],totalCpValue[1],totalCpValue[2],totalCpValue[3],totalCpValue[4],totalCpValue[5]])
            // tranSum1_6 = sum([transactionValue[0],transactionValue[1],transactionValue[2],transactionValue[3],transactionValue[4],transactionValue[5]])

            return res.status(200).json({
                code: 0,
                message: `${contractNumber}: get data entry success`,
                data: {
                    productType: '',
                    turnOver:turnOverInfo,
                    totalCP:totalCpInfo,
                    DeCommentInfo: DeCommentInfo,
                    CeCommentInfo: CeCommentInfo,
                    transactionInfo: transactionInfo,
                    tsum1_6: tsum1_12?Math.round(tsum1_12):'', 
                    lsum1_6: '', 
                    tranSum1_6: tranSum1_12?Math.round(tranSum1_12):'',
                    maxLoan: '',
                    turnoverEstimate: ''
                }
            })
        }
        
    }
    catch(err) {
        common.log(`get data entry error : ${err.message}`)
        return res.status(500).json({
            code: 2,
            message: 'Service error'
        })
    }
}

function configProduct(dataEntry,dataLoanContract){
    try {
        if(dataEntry&&dataLoanContract){
            let count = 0, accumulatedTurnOver = 0, turnOver1_3 = 0, countErrorTrans = 0, countErrorTurns = 0
            let resultKNKD, resultDKDT, result
            let turnOverValue=[], transactionValue=[]
       
            dataEntry.rows.forEach(i => {
                if(i.info_type == 'turnover'){
                    turnOverValue.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:0}) 
                } 
                else if(i.info_type == 'transaction'){
                    transactionValue.push({month_of_info:i.month_of_info,value_of_month:i.value_of_month?i.value_of_month:0}) 
                }
            });
            console.log(turnOverValue);
            turnOverValue.forEach(i => {
                accumulatedTurnOver += i.value_of_month
                if(count < 3){
                    if(i.value_of_month < 10000000){
                        countErrorTurns ++
                    }
                    turnOver1_3 += i.value_of_month
                }
                count ++
            });
            console.log(countErrorTurns+ "countErrorTurns");
            console.log(global.config.data.vsk.transaction_number + "transaction_number");
            // config database 10
            //update transaction_number = 0
            let transactionNumber = global.config.data.vsk.transaction_number;
            if(transactionNumber && transactionNumber > 0) {
                transactionValue.forEach(i => {
                    if(i.value_of_month < transactionNumber) countErrorTrans ++
                });
            }
            const dkkd = utils.caculateMonth(dateHelper.formatDate(dataLoanContract.first_registration_date, defind.DATE_FORMAT.DB_FORMAT))
            console.log("dkkd: ", dkkd);
            console.log("bussiness_duration: ", dataLoanContract.bussiness_duration);
            if(dataLoanContract.contract_type==CONTRACT_TYPE.CASH_LOAN){
                //thời gian knkd
                
                if(dkkd > 24 || (dkkd > 12 && dataLoanContract.bussiness_duration > 36)){
                    resultKNKD = 'PREMIUM'
                }
                else if(dkkd > 12 || (dkkd > 6 && dataLoanContract.bussiness_duration > 24)){
                    resultKNKD = 'VIP'
                }
                else if(dkkd > 6 || (dkkd > 3 && dataLoanContract.bussiness_duration > 12)){
                    resultKNKD = 'STANDARD'
                }
                else {
                    resultKNKD = 'FAILD'
                }

                console.log("turnOver1_3", turnOver1_3);
                console.log("countErrorTrans", countErrorTrans);
                //điều kiện về doanh thu
                if(accumulatedTurnOver < 600000000 && turnOver1_3 >= 10000000 && countErrorTrans == 0){
                    resultDKDT = 'STANDARD'
                }
                else if(accumulatedTurnOver >= 600000000 && turnOver1_3 >= 20000000 && accumulatedTurnOver < 1800000000 && countErrorTrans == 0){
                    resultDKDT = 'VIP'
                }
                else if(accumulatedTurnOver >= 1800000000 && turnOver1_3 >= 50000000 && countErrorTrans == 0){
                    resultDKDT = 'PREMIUM'
                }
                else{
                    resultDKDT = 'FAILD'
                }
                //xét điều kiện tổng thể    
                if(resultDKDT == 'STANDARD' && (resultKNKD == 'STANDARD' || resultKNKD == 'VIP' || resultKNKD == 'PREMIUM')){
                    result = 'STANDARD'
                }

                else if(resultDKDT == 'VIP' && (resultKNKD == 'VIP' || resultKNKD == 'PREMIUM')){
                    result = 'VIP'
                }
                else if(resultDKDT == 'VIP' && resultKNKD == 'STANDARD'){
                    result = 'STANDARD'
                }
                else if(resultDKDT == 'PREMIUM' && resultKNKD == 'PREMIUM'){
                    result = 'PREMIUM'
                }
                else if(resultDKDT == 'PREMIUM' && (resultKNKD == 'VIP' || resultKNKD == 'STANDARD' )){
                    result = 'VIP'
                }
                else if((resultDKDT == 'STANDARD' && resultKNKD == 'FAILD')
                    ||(resultDKDT == 'VIP' && resultKNKD == 'FAILD')
                    ||(resultDKDT == 'PREMIUM' && resultKNKD == 'FAILD')){
                    result = 'ALL'
                }
                else{
                    result = 'ALL'
                }
            }
            else if(dataLoanContract.contract_type==CONTRACT_TYPE.CREDIT_LINE){
                if(countErrorTurns == 0 && countErrorTrans == 0 && (dkkd > 12 || (dkkd > 6 && dataLoanContract.bussiness_duration > 24))){
                    result = 'HMTD'
                }
                else{
                    result = 'FAILD'
                }
            }
            console.log("configProduct result:", result);
            console.log("configProduct resultDKDT && resultKNKD", resultDKDT +'-'+resultKNKD);
            return result
            
        }
        else {
            return 'ERROR'
        }
    } catch (error) {
        return 'ERROR'
    }
}

const getHistory = async function (req, res) {
    try {
        let contractNumber = req.query.contractNumber
        let rs = await dataentryRepo.getHistory(req.poolRead,contractNumber)
        // console.log('rs',rs)
        if(rs.rowCount > 0){
            rs.rows.forEach(i => {
                i.updated_date = dateHelper.formatDate(i.updated_date,defind.DATE_FORMAT.DDMMYYYY_HHmmss) 
            });
            return res.status(200).json({
                code: 1,
                message: 'get history success',
                data: rs.rows
            })
        }
        else{
            return res.status(400).json({
                code: 0,
                message: 'get history error',
                contractNumber: contractNumber
            })
        }
    } catch (error) {
        return res.status(500).json({
            code: -1,
            message: 'service error'
        })
    }
}

const getMistakeInfo = async function (req, res) {
    try {
        let contractNumber = req.query.contractNumber
        let rs = await dataentryRepo.getMistakeInfo(req.poolRead,contractNumber)
        // console.log('rs',rs)
        rs.forEach(i => {
            i.date_created = dateHelper.formatDate(i.date_created,defind.DATE_FORMAT.DDMMYYYY) 
        });
        return res.status(200).json({
            code: 1,
            message: 'get mistake info success',
            data: rs
        })
        
        
    } catch (error) {
        return res.status(500).json({
            code: -1,
            message: 'service error'
        })
    }
}

module.exports = {
    saveDataEntry,
    // updateDataEntry,
    getDataEntry,
    getHistory,
    getMistakeInfo
}