const common = require("../utils/common")
const deService = require("../utils/deService")
const vdsService = require("./vds-service")
const {saveDEScore,updateDEScore} = require("../repositories/offer")
const utils = require("../utils/helper")
const callbackUtils = require("../utils/callbackService")
const callbackService = require("./callback-service")
const loggingRepo = require("../repositories/logging-repo")
const turnoverRepo = require("../repositories/turnover-repo")
const {SERVICE_NAME,PARTNER_CODE, TASK_FLOW, roleCode, CONTRACT_TYPE} = require("../const/definition")
const {checkEKYC, checkEKYCKunn} = require("../utils/ekycService")
const {STATUS, CALLBACK_STAUS, KUNN_STATUS, EKYC_RESULT_CODE} = require("../const/caseStatus")
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")
const loggingService = require("../utils/loggingService")
const aaaService = require("../utils/aaaService")
const offerRepo = require("../repositories/offer")
const dateHelper = require("../utils/dateHelper")
const {DATE_FORMAT} = require("../const/definition")
const _ = require("underscore")
const {serviceEndpoint} = require("../const/config")
const crmService = require("../utils/crmService")
const  uuid  = require("uuid")
const { pushTaskMcV2 } = require("../utils/aadService")
const { sendNotification } = require("./notification-service")
const loanAttributeRepo = require("../repositories/loan-atribute-repo")
const { getCicScoreV2 } = require("../repositories/loan-cic-score")
const turnOverRepo = require("../repositories/turnover-repo")

const baseCheckEligible = async function(input, isKunn = false){
	// const poolWrite = global.poolWrite;
	const config = global.config;
	// const request_id = input.request_id
    const contract_number = input.contract_number||null
	const inputKovCL = input?.data
	if(inputKovCL!==undefined){
		input = inputKovCL; 
		input.requestId = utils.genRequestId(PARTNER_CODE.KOV);
	}
	try{
		let body = {
			"requestId": input.request_id||input.requestId,
			"contractNumber": isKunn ? input?.kunn_number : (contract_number||input.contractNumber),
			"dob": input.birth_date||input.birthDate||input.dateOfBirth,
			"issueDate": input.id_issue_dt||input.issueDate,
			"gender": input.gender || 'F',
			"phoneNumber": input.phone_number1||input.phoneNumber,
			"custName": input.cust_full_name||input.custName||input.customerName,
			"idNumber": input.id_number||input.idNumber||input.identityCardId,
			"partnerCode": input.partner_code||input.partnerCode,
			"email": input.email || "",
			"bankAccount": input.account_number || input.bankAccount || "",
			"loanAmount": input.loan_amount || input.loanAmount || input.request_amt || 0,
			"afCreationDate" : utils.getYYYYMMDD(),
			"productCode" : 'VTCOD_STANDARD'//bỏ qua rule check recent reject
        }
		console.log({config})
		let eligible_url = config.basic.decisionsV02[config.env] + config.data.deService.eligibleDeV2;
		const serviceName = 'ELIGIBLE';
		const flow = serviceName;
		const headers = utils.initHeaders(serviceName, flow);
		const result = await common.postApiV2(eligible_url, body, headers);
		loggingRepo.saveStepLog(utils.isNullOrEmpty(input.kunn_number)?contract_number:input.kunn_number,SERVICE_NAME.DE,SERVICE_NAME.ELIGIBLE,body,result)
		if (result?.data?.code === 1 || result?.data?.code === -1) {
			if (!isKunn) loanContractRepo.updateContractStatus(STATUS.ELIGIBLE,contract_number)
			return true; 
		}
		else {
			if (!isKunn) {
				await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE,contract_number)
			} else {
				await kunnRepo.updateKUStatusV2(input.kunn_number, STATUS.NOT_ELIGIBLE)
			}
			return false
		}
	}catch(err){
		common.log(`baseCheckEligible error: ${err.message}`);
		throw new Error(err.message);
	}
}

const RESPONSE_CODE = {
    "RECIEVED" : 'RECEIVED',
    "ERROR" : "ERROR",
    "SERVER_ERROR" : "SERVER_ERROR",
    "INVALID_REQUEST" : "INVALID_REQUEST",
    "ELIGIBLE" : "ELIGIBLE",
    "NOT_ELIGIBLE" : "NOT_ELIGIBLE",
    "SUCCESS" : "SUCCESS"
}

const baseCheckEligibleSME = async function(input){
	const config = global.config;
    const contract_number = input.contract_number || null
	await loanContractRepo.getLoanContract(contract_number);
	// var 
	// const pcbBody = {
	// 	"enterprise": {
	// 		"requestAmount": contractData?.request_amt||'',
	// 		"tenor": contractData?.request_tenor||'',
	// 		"businessRegistrationNumber": contractData?.registration_number||'',
	// 		"taxCode": contractData?.sme_tax_id||'',
	// 		"placeOfRegistration": '',
	// 		"address": contractData?.sme_headquarters_address||'',
	// 		"phoneNumber": contractData?.sme_phone_number||'',
	// 		"companyName": contractData?.sme_name||''
	// 	}
	try{
		let body = {
			"request_id": input?.request_id,
			"fullName": input?.sme_representation_name,
			"idNumber": input?.sme_representation_id,
			"idIssueDt": input?.sme_representation_issue_date,
			"idIssuePlace": input?.sme_representation_issue_place,
			"birthDate": input?.sme_representation_dob,
			"phoneNumber": input?.sme_phone_number,
			"gender": input?.sme_representation_gender || "F",
			"contractNumber": contract_number,
			"chanel": "LOS",
			"productType": "CASH",
			"bussinessCode": input?.registration_number||'',
			"tax": input?.sme_tax_id,
			"companyType": "E",//E: SME, I: hộ kinh doanh
			"companyName": input?.sme_name,
			"afCreationDate": utils.getYYYYMMDD(),
			"productCode": "SME_MISA_HM_STANDARD",
			"partnerCode": input?.partner_code||''
        }
		let eligible_url = config.basic.decisionsV02[config.env] + config.data.deService.eligibleSmeDeV2;
		const serviceName = 'ELIGIBLE';
		const flow = serviceName;
		const headers = utils.initHeaders(serviceName, flow);
		const result = await common.postApiV2(eligible_url, body, headers);
		loggingRepo.saveStepLog(contract_number,SERVICE_NAME.DE,SERVICE_NAME.ELIGIBLE,body,result)
		if (result.data.code === "ELIGIBLE") {
			loanContractRepo.updateContractStatus(STATUS.ELIGIBLE,contract_number)
			return true; 
		}
		else {
			await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE,contract_number)
			return false;
		}
	}catch(err){
		common.log(`baseCheckEligible error: ${err.message}`);
		throw new Error(err.message);
	}
}

const baseCheckEligibleKOV = async function(input){
	// const poolWrite = global.poolWrite;
	const config = global.config;
	const currentTimestamp = new Date().getTime()
	const requestID = "KOV" + currentTimestamp
	try{
		let body = {
			"requestId": requestID,
			"contractNumber": "",
            "dob": input.dob,
            "issueDate": input.issueDate,
            "gender": input.gender || 'F',
            "phoneNumber": input.phoneNumber,
            "custName": input.custName,
            "idNumber": input.idNumber,
			"partnerCode": "KOV",
			"email": input.email || "",
			"bankAccount": input.account_number || "",
			"loanAmount": input.loan_amount || 0,
			"afCreationDate" : utils.getYYYYMMDD(),
			"is_insert": false,
			"productCode": "KIOT_VIET_CREDIT_LINE"
        }
		let eligible_url = config.basic.decisionsV02[config.env] + config.data.deService.eligibleDeV2;
		const serviceName = 'ELIGIBLE';
		const flow = serviceName;
		const headers = utils.initHeaders(serviceName, flow);
		const result = await common.postApiV2(eligible_url, body, headers);
		if (result.data.code === 1 || result.data.code === -1) {
			return true
		}
		else {
			return false
		}
	}catch(err){
		common.log(`baseCheckEligible KOV error: ${err.message}`);
		throw new Error(err.message);
	}
}

async function checkEligibleMc(req,res) {
	const currentTaskCode = 'MC_CRE_ELI';
	const statusCode = 'KH03'
	// console.log('reqCheckEligible: '+JSON.stringify(req.body))
	try {
		const config = req.config
		let data = req.body.data
		const contractNumber = data.contractNumber
		const poolWrite = req.poolWrite
		const poolRead = req.poolRead
		utils.saveStatus(poolWrite,contractNumber,statusCode)

		// let uri = req.config.data.deService.eligible;
		// let eligible_url = config.basic.de[config.env] + uri

		let workflowUri = req.config.data.workflow.uri;
		let workflowUrl = config.basic.wfMcCredit[config.env] + workflowUri

		aaaService.getLosKey(config)
		.then(async key => {
			if(key.status === 0) {
				let finalStatus = STATUS.NOT_ELIGIBLE
				let code = 0
				let isEligible = await baseCheckEligible(req.body);
				if(isEligible) {
					finalStatus = STATUS.ELIGIBLE;
					code = 1;
				}
				
				if(code === 1) {
					let flowData = req.body
					flowData.current_task_code = currentTaskCode

					common.postAPI(workflowUrl,flowData)
					.then()
					.catch(err => common.log("CALL WORKFLOW : error " + err.message))
				}
				else {
					const partnerCode = await utils.getPartnerCode(poolRead,contractNumber)
					const currentTimestamp = new Date().getTime()
					const requestID = partnerCode + currentTimestamp
					const callbackBody = {
					
						"requestId" : requestID,
						contractNumber : contractNumber,
						"code" : "REJECTED",
						targetType : "credit-creation",
						"message": "Not meet the payment requirement ",
						"data" : {
							"contractNumber" : contractNumber,
							"partnerCode" : partnerCode,
							"rejectReason" : "REJ_POLICY"
						}
					}
					callbackUtils.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)
				}
				await Promise.all([loggingService.saveRequestV2(poolWrite,req.body,undefined,contractNumber,req.body.request_id,req.body.partner_code),
					utils.saveStatus(poolWrite,contractNumber,finalStatus)])
			} 
			else {
				common.log("get key fail")
			}
		})
		.catch(error => console.log(error))
				
	
		res.status(200).json({
			"msg" : "check eligible",
			"code" : 1
		})

	}
	catch (error) {
		common.log(error)
		res.status(500).json({
			"msg" : "check eligible",
			"code" : -1
		})
	}
}

async function checkEligibleMcV2(body) {
	try {
		const contractNumber = body.contract_number
		let finalStatus = STATUS.NOT_ELIGIBLE
		let code = 0
		let isEligible = await baseCheckEligible(body);
        if(isEligible) {
            await loanContractRepo.updateContractStatus(STATUS.ELIGIBLE,contractNumber)
			return true
        }
		
		if(finalStatus != STATUS.ELIGIBLE) {
			await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE,contractNumber)
			return false	
		}
		else {
			await loanContractRepo.updateContractStatus(STATUS.ELIGIBLE,contractNumber)
			return true
		}
		// const config = global.config
		// const contractNumber = body.contract_number
		// const eligibleBody = {
		// 	"dob" : body.birth_date,
		// 	"issueDate" : body.id_issue_dt,
		// 	"gender" : body.gender,
		// 	"phoneNumber" : body.phone_number1,
		// 	"custName" : body.cust_full_name,
		// 	"idNumber" : body.id_number,
		// 	"afCreationDate" : utils.getYYYYMMDD(),
		// 	"systemType" : "MCC",
		// 	"contractNumber" : contractNumber
		// }
		// const losKey = await aaaService.getLosKey(global.config)
		// const headers = {
		// 	"uiid" : losKey.uiid,
		// 	"token" : losKey.token
		// }
		// const eligibleUrl = config.basic.de[config.env] + config.data.deService.eligible;
		// const rs = await common.postApiV2(eligibleUrl,eligibleBody,headers)
		// await loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.ELIGIBLE,SERVICE_NAME.CHECK_ELIGIBLE,eligibleBody,rs)
		// if(rs.data.code == 1) {
		// 	await loanContractRepo.updateContractStatus(STATUS.ELIGIBLE,contractNumber)
		// 	return true
		// }
		// else {
		// 	await loanContractRepo.updateContractStatus(STATUS.NOT_ELIGIBLE,contractNumber)
		// 	return false
		// }
	}
	catch(err) {
		console.log(err)
		return false
	}
}

async function checkEKYCGateWay(contractNumber,partnerCode,isSme=false){
    try {
		const ekycRs = await checkEKYC(contractNumber,partnerCode,isSme)
        const errorService = ekycRs?.data?.service || ''
        const errorMsg = ekycRs?.data?.message || ''
        loggingRepo.saveEkycCheck(contractNumber,errorService,errorMsg)
        if(partnerCode == PARTNER_CODE.VPL) {
            loanContractRepo.updateContractStatus(STATUS.PASSED_EKYC,contractNumber)
            return true
        }
		if([PARTNER_CODE.MCAPP,PARTNER_CODE.SMA].includes(partnerCode)) {
			if(ekycRs?.status !== 200 || ekycRs?.data?.code == EKYC_RESULT_CODE.REJECT_EKYC) {
				//send noti mc app
				try{
					const contractData = await loanContractRepo.getLoanContract(contractNumber);
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Hạn mức đã bị từ chối',
						message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị từ chối`, 
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
					sendNotification( bodyNoti, endPoint, global.config );  
				} catch(err){
					common.log('send notification to appMC error', contractNumber)
					console.log(err?.message)
				}

				Promise.all([
					loanContractRepo.updateContractStatus(STATUS.REFUSED, contractNumber),
					callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.REJECTED),
					crmService.rejectContract(global.config, contractNumber)
				]);
				return false;
			}
			if(ekycRs?.data?.code == EKYC_RESULT_CODE.PORTAL_EKYC) {
				const loanData = await loanContractRepo.getLoanContract(contractNumber);
				Promise.all([
					loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_KYC, contractNumber),
					pushTaskMcV2(roleCode.SS, contractNumber, loanData?.contract_type, STATUS.KYC_CHECK, true)
				]);
				return false;
			}
			return true;
		}
        return true
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function checkEKYCKunnGateWay(kunnNumber,contractNumber,partnerCode){
    try {
		const ekycRs = await checkEKYCKunn(kunnNumber,contractNumber,partnerCode)
        const errorService = ekycRs.data.service || ''
        const errorMsg = ekycRs.data.message || ''
        loggingRepo.saveEkycCheck(kunnNumber,errorService,errorMsg)
    }
    catch(err) {
        console.log(err)
    }
}

const baseCheckS37 = async function(input,isSME=false){
    // const poolWrite = global.poolWrite;
	const config = global.config;
	// const request_id = input.request_id
    const contract_number = input.contract_number || null
	try{
		let body = {}
		let body2 = {}
		let body3 = {}
		body.contractNumber = contract_number
		let cicS37Url
		const serviceName = 'CIC';
		let flow = 'S37';
		if(isSME){
			body.taxCode = input.sme_tax_id
			body.userName = input.sme_name
			body2.idNumber = input.sme_representation_id
			body2.userName = input.sme_representation_name
			body3.idNumber = input.authorized_id
			body3.userName = input.authorized_name
			cicS37Url = config.basic.decisionsV02[config.env] + config.data.deService.hmCicSmeDeV2;
			flow = 'S37_ENTERPRISE';
		}
		else{
			body.idNumber = input.id_number
			body.userName = input.cust_full_name
			cicS37Url = config.basic.decisionsV02[config.env] + config.data.deService.hmCicDeV2;
		}
        const headers = utils.initHeaders(serviceName, flow);
        const result = await common.postApiV2(cicS37Url, body, headers);
        loggingRepo.saveStepLog(contract_number,SERVICE_NAME.DE,SERVICE_NAME.S37,body,result)
        if (result.data.code === 1){
			if(isSME){
				let result2 = await common.postApiV2(cicS37Url, body2, headers);
				if(result2.data.code === 1){
					let result3 = await common.postApiV2(cicS37Url, body3, headers);
					if(result3.data.code === 1){
						return true;
					}else{
						await loanContractRepo.updateContractStatus(STATUS.BAD_DEBT,contract_number)
						return false
					}
				}else{
					await loanContractRepo.updateContractStatus(STATUS.BAD_DEBT,contract_number)
					return false
				}
			}
			else{
				return true; //PASSED
			} 
        }
		else {
			await loanContractRepo.updateContractStatus(STATUS.BAD_DEBT,contract_number)
			return false
		}
	}catch(err){
		console.log(err);
        //stepLog.saveStepCheckLogV2(poolWrite, contract_number, request_id, service_name, step, body, undefined, err.message);
		throw new Error(err.message);
	}
}

async function checkPCBMcV2(body) {
	try {
		const config = global.config
		const contractNumber = body.contract_number
		const pcbBody = {
			"FIContractCode" : contractNumber,
			"DateRequestContract" : utils.getYYYYMMDD(),
			"OperationType" : config.data.pcbConfig.pcbOperationType,
			"CodCurrency" : "VND",
			"CodPaymentPeriodicity" : "M",
			"AmountCreditLimitAccount" : body.request_amt,
			"FISubjectCode" : body.cust_id,
			"Name" : utils.nonAccentVietnamese(body.cust_full_name),
			"Gender" : body.gender,
			"DateOfBirth" : body.birth_date,
			"CountryOfBirth" : "VN",
			"IDCard" : body.id_number,
			"FullAddress" : body.province_cur,
			"AdditionalFullAddress" : "",		
			"DocumentType" : "",
			"numDocument" : "",
			"DateIssued" : body.id_issue_dt,
			"CountryIssued" : "",
			"Role" : "A",
			"creditLimit" : body.request_amt
		}
		const url = config.basic.decisionsV02[config.env] + config.data.deService.hmPcbDeV2;
		const serviceName = 'PCB';
		const flow = 'CHECK_PCB';
		const headers = utils.initHeaders(serviceName, flow);
		const response = await common.postApiV2(url, pcbBody, headers)
		const result = response.data
		await loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.DE,SERVICE_NAME.CHECK_PCB,pcbBody,result)
		if(result.code === 1) {
			const pcbDebtGroup = result.pcbDebtGroup || 1
			offerRepo.updatePCBScore(result.pcbScore1,result.pcbScore2,pcbDebtGroup,contractNumber)
			loanContractRepo.updateContractStatus(STATUS.ELIGIBLE,contractNumber)
			return true
		}
		else {
			loanContractRepo.updateContractStatus(STATUS.BAD_DEBT,contractNumber)
			return false
		}
	}
	catch(err) {
		console.log(err)
		return false	
	}
}

async function checkPCBMc(req,res) {
	let currentTaskCode = 'MC_CRE_PCB';
	const statusCode = 'KH03'
	const config = req.config 
	const contractNumber = req.body.data.contractNumber
	const poolWrite = req.poolWrite
	const poolRead = req.poolRead
	const loanData = await loanContractRepo.getLoanContract(contractNumber)
	try {
		let data = req.body.data
		if(data.custName==undefined) data.custName=data.customerName
		utils.saveStatus(poolWrite,contractNumber,statusCode)
		const isDmyDate = await dateHelper.dmyValid(data?.issueDate)
		const body = {
			"FIContractCode" : data.contractNumber,
			"DateRequestContract" : utils.getYYYYMMDD(),
			"OperationType" : config.data.pcbConfig.pcbOperationType,
			"CodCurrency" : "VND",
			"CodPaymentPeriodicity" : "M",
			"AmountCreditLimitAccount" : data.loanAmount,
			"FISubjectCode" : loanData?.cust_id,
			"Name" : utils.nonAccentVietnamese(data.custName)?.normalize('NFC'),
			"Gender" : data.gender,
			"DateOfBirth" : data.dateOfBirth,
			"CountryOfBirth" : "VN",
			"IDCard" : data.identityCardId,
			"FullAddress" : data.temProvince,
			"AdditionalFullAddress" : "",		
			"DocumentType" : "",
			"numDocument" : "",
			"DateIssued" : !isDmyDate?dateHelper.convertDMY2YMD(data.issueDate):data.issueDate,
			"CountryIssued" : "",
			"Role" : "A",
			"creditLimit" : data.loanAmount
		}

		let uri = req.config.data.deService.hmPcbDeV2;
		let pcb_url = config.basic.decisionsV02[config.env] + uri
		const serviceName = 'PCB';
		const flow = 'CHECK_PCB';
		const headers = utils.initHeaders(serviceName, flow);
		// const envType = req.config.data.env.wf_uri;
		// if(envType=='local') {
		// 	wf_lb = "http://localhost:1001"
		// }
		let workflowUri = req.config.data.workflow.uri;
		let workflowUrl = config.basic.wfMcCredit[config.env] + workflowUri

		common.postAPI(pcb_url, body, headers)
		.then(async result => {
			console.log(`result check pcb KOV: ${contractNumber}`,JSON.stringify(result))
			if(result.code === 1) {
				let flowData = req.body
				flowData.data.pcbResult = result
				flowData.current_task_code = currentTaskCode
				const pcbDebtGroup = result.pcbDebtGroup || 1

				offerRepo.updatePCBScore(result.pcbScore1,result.pcbScore2,pcbDebtGroup,contractNumber)
				common.postAPI(workflowUrl,flowData)
				.then()
				.catch(err => common.log("CALL WORKFLOW : error " + err.message))
			}
			else {
				
				const partnerCode = await utils.getPartnerCode(poolRead,contractNumber)
				const currentTimestamp = new Date().getTime()
				const requestID = partnerCode + currentTimestamp
				const callbackBody = {
				
					"requestId" : requestID,
					contractNumber : contractNumber,
					"code" : "REJECTED",
					targetType : "credit-creation",
					"message": "Not meet the payment requirement ",
					"data" : {
						"contractNumber" : contractNumber,
						"partnerCode" : partnerCode,
						"rejectReason" : "BAD_DEBT"
					}
				}
				callbackUtils.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)
			}

		})
		.catch(error => {
			console.log('error',error.message)
			let flowData = req.body
			flowData.current_task_code = currentTaskCode
			// savePCB(req.poolWrite,0,0,0,flowData.data.contractNumber)

			common.postAPI(workflowUrl,flowData)
			.then()
			.catch(err => common.log("CALL WORKFLOW : error " + err.message))
			return res.status(201).json({
				"msg" : "call pcb error",
				"code" : -1
			})
		})

		return res.status(200).json({
			"msg" : "check pcb",
			"code" : 1
		})

	}
	catch (error) {
		callbackUtils.callbackError(poolWrite,config,contractNumber)
		console.log(error)
		return res.status(500).json({
			"msg" : "service error",
			"code" : -1
		})
	}
}

async function checkPrescore(contractNumber) {
    try {
        const preScoreResult = await deService.checkPreScore(contractNumber)
		// const pcbScore1 = parseInt(preScoreResult?.data?.pcbInst)||0;
		// const pcbScore2 = parseInt(preScoreResult?.data?.pcbCreditCard)||0;
		// const pcbDebtGroup = parseInt(preScoreResult?.data?.pcbMaxWorst)||0;
        if(preScoreResult.code == 0 && preScoreResult?.data?.decision == 'APPROVE') {
			// await Promise.all([
			// 	offerRepo.updateMainScore(contractNumber,'pcb_score1',pcbScore1),
			// 	offerRepo.updateMainScore(contractNumber,'pcb_score2',pcbScore2),
			// 	offerRepo.updateMainScore(contractNumber,'pcb_debt_group',pcbDebtGroup)
			// ]) 
            return true
        }
        else {
            vdsService.callbackRejectVds(contractNumber)
            return false
        }
    }
    catch(err) {
        common.log(`check pre score error : ${err.message}`,req.body.contract_number)
        return false
    }
}

async function checkInternalScore(contractNumber) {
    try {
        const internalScoreResult  = await deService.checkInternalScore(contractNumber)
        if(parseInt(internalScoreResult.code) == 0 || parseInt(internalScoreResult.code) == 2) {
            await updateDEScore(contractNumber,internalScoreResult.data)
            return true
        }
        else {
            vdsService.callbackRejectVds(contractNumber)
            return false
        }
    }	
    catch(err) {
        common.log(`check internal score error : ${err.message}`,req.body.contract_number)
        return false
    }
}

function checkCIC(req,res) {
    const currentTaskCode = 'MC_CRE_CIC';
    const statusCode = 'KH03'
	try {
		const config = req.config
		let data = req.body.data
		const poolWrite = req.poolWrite
		const poolRead = req.poolRead
		const contractNumber = data.contractNumber

		utils.saveStatus(poolWrite,contractNumber,statusCode)

		const body = {
			"idNumber" : data.identityCardId,
			"contractNumber" : contractNumber
		}

		// const envType = req.config.data.env.wf_uri;
		// let wf_lb ;
		// if(envType=='local') {
		// 	wf_lb = "http://localhost:1001"
		// }

		const uri = req.config.data.deService.hmCicDeV2;
		const cic_url = config.basic.decisionsV02[config.env] + uri
		const serviceName = 'CIC';
		const flow = 'S37';
		const headers = utils.initHeaders(serviceName, flow);

		let workflowUri = req.config.data.workflow.uri;
		let workflowUrl = config.basic.wfMcCredit[config.env] + workflowUri
		common.postAPI(cic_url, body, headers)
		.then(async result => {
			if(result.code === 1) {
				let flowData = req.body
				flowData.data.cicResult = result
				flowData.current_task_code = currentTaskCode
				common.postAPI(workflowUrl,flowData)
				.then()
				.catch(err => common.log("CALL WORKFLOW : error"))
			}
			else {
				const partnerCode = await utils.getPartnerCode(poolRead,contractNumber)
				const currentTimestamp = new Date().getTime()
				const requestID = partnerCode + currentTimestamp
				const callbackBody = {
					"requestId" : requestID,
					"contractNumber" : contractNumber,
					"code" : "REJECTED",
					targetType : "credit-creation",
					"message": "Not meet the payment requirement ",
					"rejectReason" : "BAD_DEBT"
				}
				callbackUtils.callbackPartner(poolWrite,contractNumber,config,partnerCode,callbackBody)

			}

		})
		.catch(error => {
			console.log(error)
			return res.status(500).json({
				"msg" : "service error",
				"code" : -1
			})
		})
		return res.status(200).json({
			"msg" : "check cic",
			"code" : 1
		})

	}
	catch (error) {
		return res.status(500).json({
			"msg" : "service error",
			"code" : -1
		})
	}
}

async function checkDeV2(contractNumber,url,isOffer=false,VSK=false, isAf1 = true, headers = {'Content-Type': 'application/json'}, isKunn = false, kunnNumber) 
{
	try {
		const data = await Promise.all([loanContractRepo.getLoanContract(contractNumber),turnoverRepo.getTurnOver(contractNumber)])
		const contractData = data[0]
		const turnoverData = data[1]

		let date_of_birth = dateHelper.formatDate(contractData.birth_date, DATE_FORMAT.DB_FORMAT);
        let issue_date = dateHelper.formatDate(contractData.id_issue_dt, DATE_FORMAT.DB_FORMAT);
        let date_birth, month_birth, year_birth,
            date_issue, month_issue, year_issue;

        if(date_of_birth !== undefined) {
            //format YYYY-MM-DD
            let dob = date_of_birth.toString();
            year_birth = dateHelper.LEFT(dob, 4) ;
            month_birth = dob.substr(5, 2);
            date_birth = dateHelper.RIGHT(dob, 2);
        }

        if(issue_date !== undefined) {
            issue_date = issue_date.toString();
            year_issue = dateHelper.LEFT(issue_date, 4) ;
            month_issue = issue_date.substr(5, 2);
            date_issue = dateHelper.RIGHT(issue_date, 2);
        }
		const deBody = {
			"contractNumber": isKunn ? kunnNumber : contractNumber,
			"taxId": contractData?.tax_id,
			"custId": contractData.cust_id,
			"partnerCode": contractData.partner_code,
			"fullName": contractData.cust_full_name,
			"idNumber": contractData.id_number,
			"otherIdNumber": contractData.other_id_number || null,
			"phoneNumber": contractData.phone_number1,
			"gender": contractData.gender,
			"dateOfBirth": date_birth,
			"monthOfBirth": month_birth,
			"yearOfBirth": year_birth,
			"issuePlace": contractData.id_issue_place,
			"dateOfIssue": date_issue,
			"monthOfIssue": month_issue,
			"yearOfIssue": year_issue,
			"totalMonthlyNetIncome": contractData.monthly_income ? parseInt(contractData.monthly_income) : 0,
			"otherIncome": contractData.other_income ? parseInt(contractData.other_income) : 0,
			"totalMonthlyExpenses": contractData.m_household_expenses ? parseInt(contractData.m_household_expenses) : 0,
			"employmentType": contractData.empl_type ?? 'SE',
			"productGroup": null,
			"documentsProvide": null,
			"requestAmount": contractData.request_amt ? parseInt(contractData.request_amt) : 0,
			"tenor": contractData.request_tenor,
			"prdctRiskClass": null,
			"saleChannel": "",
			"saleNetwork": "",
			"submittedBy": "",
			"address": {
				"currentAddress": {
					"nation": "VN",
					"city": contractData.province_cur,
					"district": contractData.district_cur,
					"ward": contractData.ward_cur,
					"addressDetails": contractData.address_cur
				},
				"permanentAddress": {
					"nation": "VN",
					"city": contractData.province_per,
					"district": contractData.district_per,
					"ward": contractData.ward_per,
					"addressDetails": contractData.address_per
				}
			},
			"reference": {
				"reference1": {
					"relationship": contractData.reference_type_1,
					"fullName": contractData.reference_name_1,
					"phoneNumber": contractData.reference_phone_1
				},
				"reference2": {
					"relationship": contractData.reference_type_2,
					"fullName": contractData.reference_name_2,
					"phoneNumber": contractData.reference_phone_2
				}
			},
			"disbursement": {
				"loanPurpose": contractData.loan_purpose,
				"method": "2",
				"nameOfBeneficiary": contractData.bank_account_owner,
				"beneficiaryBank": contractData.bank_code,
				"city": "",
				"bankBranch": contractData.bank_branch,
				"bankAccountNumber": contractData.bank_account
			},
			"contact": {
				"otherContact": "",
				"contactDetail": "",
				"mailingAddress": "",
				"email": "",
				"currentCity": "",
				"provinceOfResidence": "",
				"employment": ""
			},
			"runManual" : 0,
			"isKunn": isKunn ? true : false
		}
		if(VSK){
			deBody.diData = {
				"other":{
					"businessType": contractData.type_trading == 'ON'?'ON':contractData.type_trading == 'OF'?'OFF':contractData.type_trading == 'BO'?'BOTH':'',
					"loanType": contractData.contract_type == 'CREDITLINE'?'CREDIT':contractData.contract_type == 'CASHLOAN'?'CASH':''
				},
				"expenses": {
					"addr": contractData.province_cur,
					"totalMonthlyExpenses": parseFloat(contractData.m_household_expenses),
					"numberOfDepend": contractData.num_of_dependants
				},
				"internalInst": {
					"internalInstEC": 0
				},
				"income": {
					"turnover1": turnoverData['1']?.turnover,
					"turnover2": turnoverData['2']?.turnover,
					"turnover3": turnoverData['3']?.turnover,
					"turnover4": turnoverData['4']?.turnover,
					"turnover5": turnoverData['5']?.turnover,
					"turnover6": turnoverData['6']?.turnover
				},
				"pcbDebt": {
					"pcbInst": 0,
					"pcbCreditCard": 0
				},
				"cic1": {
					"other": 0,
					"creditCard": 0,
					"longTermDebt": 0,
					"mediumTermMortgageDebt": 0,
					"mediumTermUnsecuredDebt": 0,
					"shortTermDebt": 0
				},
				"cic2": {
					"creditCard": 0,
					"longTermPrincipalDebt": 0,
					"mediumTermPrincipalDebt": 0,
					"shortTermPrincipalDebt": 0,
					"longTermInterestDebt": 0,
					"mediumTermInterestDebt": 0,
					"shortTermInterestDebt": 0
				}
			}
		}
		if(isOffer) {
			deBody.offerData = {
				"di": null,
				"turnoverN6N5": turnoverData['3'].turnover,
				"turnoverN4N3": turnoverData['2'].turnover,
				"turnoverN2N1": turnoverData['1'].turnover,
				"transactionN6N5": turnoverData['3'].transaction,
				"transactionN4N3": turnoverData['2'].transaction,
				"transactionN2N1": turnoverData['1'].transaction,
				"codOrderDensityN6N5": turnoverData['3'].codOrder,
				"codOrderDensityN4N3": turnoverData['2'].codOrder,
				"codOrderDensityN2N1": turnoverData['1'].codOrder,
				"timeDuration": parseInt(contractData.time_duration),
				"scheme": "0",
				"legalStatus": contractData.business_legal,
				"businessType": contractData.type_trading.includes('OF') ? 'OFF' : contractData.type_trading,
				"timeRegistration": contractData.first_registration_date ? dateHelper.formatDate(new Date(contractData.first_registration_date),'YYYY-MM-DD') : undefined,
				"monthRegistration": null
			}
		}

		const deRs = await common.postApiV2(url,deBody, headers);
		if(!utils.isNullOrEmpty(kunnNumber)) loggingRepo.saveStepLog(kunnNumber,SERVICE_NAME.DE,SERVICE_NAME.CHECK_PCB_KUNN,deBody,deRs);
		let taskFlow = isAf1 ? TASK_FLOW.CHECK_AF1 : TASK_FLOW.CHECK_AF2;
		loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.DE, taskFlow, deBody, deRs);
		return deRs
	}
	catch(err) {
		console.log(err)
		console.log(`call DE compute offer error : ${err.message}`,contractNumber)
		return false
	}
}

async function checkDeV3(contractNumber,url,dataBody,cicBody, headers = {'Content-Type': 'application/json'}) {
	try {
		const data = await Promise.all([
			loanContractRepo.getLoanContract(contractNumber),
			turnoverRepo.getTurnOver(contractNumber),
			loanAttributeRepo.getDataByContractNumber(contractNumber)
		])
		const contractData = data[0]
		const turnOverData = data[1] || []
		const loanAttributeData = data[2]
		const productScheme = utils.detectSuperAppSchema(contractData.product_code)
		let businessExpenses = 0
		businessExpenses = turnOverData['0']?.expenses + turnOverData['1']?.expenses + turnOverData['2']?.expenses

		const deBody = {
			"contractNumber": contractNumber,
			"custId": contractData?.cust_id||null,
			"partnerCode": contractData?.partner_code||null,
			"productScheme": productScheme||null,
			"slAdvanceContractType": "HM"
		}
		deBody.diData = {
			"assetsPrice": loanAttributeData?.assetsPrice || null,
			"other":{
				"businessType": contractData?.type_trading == 'ON'?'ON':contractData?.type_trading == 'OF'?'OFF':contractData?.type_trading == 'BO'?'BOTH':'',
				"loanType": contractData?.contract_type == 'CREDITLINE'?'CREDIT':contractData?.contract_type == 'CASHLOAN'?'CASH':'',
				"idNumber": contractData?.id_number||null,
				"taxId": contractData?.tax_id||null
			},
			"expenses": {
				"addr": contractData?.province_cur||null,
				"temProvince": contractData?.province_cur||null,
				"totalMonthlyExpenses": parseFloat(contractData?.m_household_expenses)||null,
				"numberOfDepend": contractData?.num_of_dependants||null,
				businessExpenses
			},
			"internalInst": {
				"internalInstEC": parseFloat(cicBody?.monthlyPaymentAtEc)||0
			},
			"income": {
				"turnover1": dataBody['0']?.amount || turnOverData['0']?.turnover || 0,
				"turnover2": dataBody['1']?.amount || turnOverData['1']?.turnover || 0,
				"turnover3": dataBody['2']?.amount || turnOverData['2']?.turnover ||0
				// "turnover4": dataBody['3']?.amount || 0,
				// "turnover5": dataBody['4']?.amount || 0,
				// "turnover6": dataBody['5']?.amount || 0
			},
			"pcbDebt": {
				"pcbInst": parseFloat(cicBody?.pcbMonthlyPay)||0,
				"pcbCreditCard": parseFloat(cicBody?.pcbCardDebt)||0
			},
			"cic1": {
				"other": parseFloat(cicBody?.personDebt.otherDebt1)||0,
				"creditCard": parseFloat(cicBody?.personDebt.cardDebt)||0,
				"longTermDebt": parseFloat(cicBody?.personDebt.longTerm)||0,
				"mediumTermMortgageDebt": parseFloat(cicBody?.personDebt.mortgageMediumTerm)||0,
				"mediumTermUnsecuredDebt": parseFloat(cicBody?.personDebt.creditMediumTerm)||0,
				"shortTermDebt": parseFloat(cicBody?.personDebt.shortTerm)||0
			},
			"cic2": {
				"creditCard": parseFloat(cicBody?.legalDebt.cardDebt2)||0,
				"longTermPrincipalDebt": parseFloat(cicBody?.legalDebt.longTermPrincipalAndInterest)||0,
				"mediumTermPrincipalDebt": parseFloat(cicBody?.legalDebt.mediumTermPrincipalAndInterest)||0,
				"shortTermPrincipalDebt": parseFloat(cicBody?.legalDebt.shortTermPrincipalAndInterest)||0,
				"longTermInterestDebt": parseFloat(cicBody?.legalDebt.longTermInterest)||0,
				"mediumTermInterestDebt": parseFloat(cicBody?.legalDebt.mediumTermInterest)||0,
				"shortTermInterestDebt": parseFloat(cicBody?.legalDebt.shortTermInterest)||0
			}
		}
		// console.log('deBody',deBody)
		const deRs = await common.postApiV2(url,deBody, headers)
		return deRs
	}
	catch(err) {
		console.log(err)
		console.log(`call DE compute offer error : ${err.message}`,contractNumber)
		return false
	}
}

async function checkDeWithDrawRequest(contractNumber, url, kunnCode, headers = {'Content-Type': 'application/json'}) {
	try {
		const data = await Promise.all([
			loanContractRepo.getLoanContract(contractNumber),
			turnoverRepo.getTurnOver(contractNumber),
			loanAttributeRepo.getDataByContractNumber(contractNumber),
			getCicScoreV2(contractNumber)
		])
		const contractData = data[0]
		const turnOverData = data[1] || []
		const loanAttributeData = data[2]
		const cicScoreData = data[3]
		const productScheme = utils.detectSuperAppSchema(kunnCode)
		let businessExpenses = 0
		businessExpenses = turnOverData['0']?.expenses + turnOverData['1']?.expenses + turnOverData['2']?.expenses
		const deBody = {
			"contractNumber": contractNumber,
			"custId": contractData?.cust_id||null,
			"partnerCode": contractData?.partner_code||null,
			"productScheme": productScheme||null,
			"slAdvanceContractType": "WD",
			"diData": {
				"assetsPrice": loanAttributeData?.assetsPrice || null,
				"other":{
					"businessType": contractData?.type_trading == 'ON'?'ON':contractData?.type_trading == 'OF'?'OFF':contractData?.type_trading == 'BO'?'BOTH':'',
					"loanType": contractData?.contract_type == 'CREDITLINE'?'CREDIT':contractData?.contract_type == 'CASHLOAN'?'CASH':'',
					"idNumber": contractData?.id_number||null,
					"taxId": contractData?.tax_id||null
				},
				"expenses": {
					"addr": contractData?.province_cur||null,
					"temProvince": contractData?.province_cur||null,
					"totalMonthlyExpenses": parseFloat(contractData?.m_household_expenses)||null,
					"numberOfDepend": contractData?.num_of_dependants||null,
					businessExpenses
				},
				"internalInst": {
					"internalInstEC": parseFloat(cicScoreData?.monthlyPaymentAtEc)||0
				},
				"income": {
					"turnover1": turnOverData['0']?.turnover || 0,
					"turnover2": turnOverData['1']?.turnover || 0,
					"turnover3": turnOverData['2']?.turnover || 0,
				},
				"pcbDebt": {
					"pcbInst": parseFloat(cicScoreData?.pcbMonthlyPay || 0),
					"pcbCreditCard": parseFloat(cicScoreData?.pcbCardDebt || 0)
				},
				"cic1": {
					"other": parseFloat(cicScoreData?.otherDebt1 || 0),
					"creditCard": parseFloat(cicScoreData?.cardDebt|| 0),
					"longTermDebt": parseFloat(cicScoreData?.longTerm || 0),
					"mediumTermMortgageDebt": parseFloat(cicScoreData?.mortgageMediumTerm || 0),
					"mediumTermUnsecuredDebt": parseFloat(cicScoreData?.creditMediumTerm || 0),
					"shortTermDebt": parseFloat(cicScoreData?.shortTerm || 0)
				},
				"cic2": {
					"creditCard": parseFloat(cicScoreData?.cardDebt2 || 0),
					"longTermPrincipalDebt": parseFloat(cicScoreData?.longTermPrincipalAndInterest || 0),
					"mediumTermPrincipalDebt": parseFloat(cicScoreData?.mediumTermPrincipalAndInterest || 0),
					"shortTermPrincipalDebt": parseFloat(cicScoreData?.shortTermPrincipalAndInterest || 0),
					"longTermInterestDebt": parseFloat(cicScoreData?.longTermInterest || 0),
					"mediumTermInterestDebt": parseFloat(cicScoreData?.mediumTermInterest || 0),
					"shortTermInterestDebt": parseFloat(cicScoreData?.shortTermInterest || 0)
				}
			}
		}
		// console.log('deBody',deBody)
		const deRs = await common.postApiV2(url,deBody, headers)
		return deRs
	}
	catch(err) {
		console.log(err)
		console.log(`call DE compute offer error : ${err.message}`,contractNumber)
		return false
	}
}

async function computeOfferWithDrawRequest(contractNumber, {paymentMethod, diBeforeCE, kunnCode, url}) {
    try {
        const data = await Promise.all([
            loanContractRepo.getLoanContract(contractNumber),
            loanAttributeRepo.getDataByContractNumber(contractNumber),
            turnOverRepo.getTurnOver(contractNumber)
        ])
        const contractData = data[0]
        const loanAttributeData = data[1]
        const turnOverData = data[2]
        let offerBody = {
            "contractNumber": contractNumber,
            "partnerCode": contractData?.partner_code,
            "custId": contractData?.cust_id,
            "idNumber": contractData?.id_number || null,
            "taxId": contractData?.tax_id || null,
            "temProvince": contractData?.province_cur || null,
			"assetsType": loanAttributeData?.assetsType,
			paymentMethod,
            "productScheme": utils.detectSuperAppSchema(kunnCode) || null,
            "slAdvanceContractType": "WD",
            "offerData": {
                "assetsPrice": loanAttributeData?.assetsPrice || null,
                "tenor": contractData?.approval_int_rate || contractData?.request_int_rate || null,
                "interestRate": contractData?.approval_int_rate || contractData?.request_int_rate || null,
                "diBeforeCE": diBeforeCE || null,
                "diAfterCE": 0,
                "turnover1": turnOverData['0']?.turnover || 0,
                "turnover2": turnOverData['1']?.turnover || 0,
                "turnover3": turnOverData['2']?.turnover || 0,
                "timeDuration": 0,
				"legalStatus": null,
				"businessType": "",
				"timeRegistration": null,
				"monthRegistration": null,
                "loanType": contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE ? 'CREDIT' : contractData.contract_type == CONTRACT_TYPE.CASH_LOAN ? 'CASH' : ''
            }
        }
        console.log('offerBody: ', JSON.stringify(offerBody));
        const headers = utils.initHeaders('DECISION', 'OFFER');
        const offerRs = await common.postApiV2(url, offerBody, headers);
        console.log(`offerRs | ${contractData?.partner_code}: `, JSON.stringify(offerRs));
		let offer = 0
		if (offerRs) {
			offer = parseInt(offerRs?.data?.data?.offer / 1000000) * 1000000
		}
		
		return offer
    } catch (error) {
        common.log(`compute offer error: ${error.message}`)
    }
}

async function computeVTPOffer(contractNumber) {
	try {	
		const deUrl = config.basic.decisionsV02[config.env] + serviceEndpoint.deOfferVTP
		const headers = utils.initHeaders('DECISION','OFFER');
		const deRs = await checkDeV2(contractNumber,deUrl,true,false,false,headers)
		const maxLoan = parseInt(deRs.data.data.offer / 1000000) * 1000000
		const estimatedRevenueDE = deRs.data.data.estimateRevenue
		const estimatedTurnover = [
			{
				month : 1,
				amount : estimatedRevenueDE.revenue1
			},
			{
				month : 2,
				amount : estimatedRevenueDE.revenue2
			},
			{
				month : 3,
				amount : estimatedRevenueDE.revenue3
			}
		]
		await turnoverRepo.saveTurnOrTrans(global.poolWrite,estimatedTurnover,'ESTIMATED_TURNOVER',contractNumber)
		await offerRepo.createLoanMainScore(contractNumber)
		await offerRepo.updateMainScore(contractNumber,'max_loan',maxLoan)
	}
	catch(err) {
		console.log(err)
		return false
	}
}

async function checkAf1(contractNumber,partnerCode) {
	try {
		let af1Url = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].af1
		let headers = utils.initHeaders('DECISION','DECISION')
		if(partnerCode == PARTNER_CODE.SMA) {
			af1Url = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].di
			// headers = utils.initHeaders('DECISION','MC_DI')
			const turnOver = await turnoverRepo.getTurnOver(contractNumber)
			const rsDe = await checkDeV3(contractNumber, af1Url, turnOver, null)
			if (rsDe?.status == 200 && rsDe?.data.code == '0') {
				const diBeforeCe = rsDe.data.data.anuity ? parseFloat(rsDe.data.data.anuity) : 0
				Promise.all([
					offerRepo.updateMainScore(contractNumber,'di_before_ce',diBeforeCe),
					offerRepo.updateMainScore(contractNumber,'net_income',diBeforeCe)
				])
				return true
			} else {
				Promise.all([
					callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.REJECTED),
					crmService.rejectContract(global.config,contractNumber),
					loanContractRepo.updateContractStatus(STATUS.REFUSED,contractNumber)
				])
				return false
			}
		}
		const rs = await checkDeV2(contractNumber,af1Url,false,false,true,headers)
		if(rs?.data?.data?.result != 'approve') {
			if(partnerCode == PARTNER_CODE.MCAPP){
				//send noti mc app
				try{
					const contractData = await loanContractRepo.getLoanContract(contractNumber);
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Hạn mức đã bị từ chối',
						message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị từ chối`, 
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
					sendNotification( bodyNoti, endPoint, global.config );  
				} catch(err){
					common.log('send notification to appMC error', contractNumber)
					console.log(err?.message)
				}
			}
			common.log(`check af1 error`,contractNumber)
			Promise.all([
				callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.REJECTED),
				crmService.rejectContract(global.config,contractNumber),
				loanContractRepo.updateContractStatus(STATUS.REFUSED,contractNumber)
			])
			return false
		}
		return true
	}
	catch(err) {
		console.log(err)
		return false
	}
}

async function checkAf2(contractNumber,partnerCode) {
	try {
		if(partnerCode==PARTNER_CODE.SMA) return true
		let deRs;
		const af2Url = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].af2
		const headers = utils.initHeaders('DECISION','DECISION');
		if([PARTNER_CODE.VSK, PARTNER_CODE.MCAPP].includes(partnerCode)){
			deRs = await checkDeV2(contractNumber,af2Url,false,true, false,headers)
		}
		else{
			deRs = await checkDeV2(contractNumber,af2Url, false,false, false,headers)
		}
		
		if(deRs.data.code != '0') {
			if (partnerCode == PARTNER_CODE.MCAPP) {
				//send noti mc app
				try{
					const contractData = await loanContractRepo.getLoanContract(contractNumber);
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Hạn mức đã bị từ chối',
						message: `Hợp đồng hạn mức ${contractNumber} của bạn đã bị từ chối`, 
						value: {
							contractNumber: contractNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
					sendNotification( bodyNoti, endPoint, global.config );  
				} catch(err){
					common.log('send notification to appMC error', contractNumber)
					console.log(err?.message)
				}
			}
			common.log(`check af2 error`,contractNumber)
			callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.REJECTED)
			 crmService.rejectContract(global.config,contractNumber)
			await loanContractRepo.updateContractStatus(STATUS.REFUSED,contractNumber)
			return false
		}
		const diBeforeCe = deRs.data.data.anuity ? parseFloat(deRs.data.data.anuity) : 0;
		await offerRepo.updateMainScore(contractNumber,'di_before_ce',diBeforeCe)
		await offerRepo.updateMainScore(contractNumber,'net_income',diBeforeCe)
		if([PARTNER_CODE.VSK, PARTNER_CODE.MCAPP].includes(partnerCode)){
			if(deRs.data.data.pcbDebt){
				const pcbScore1 = parseFloat(deRs.data.data.pcbDebt.pcbInst)
				const pcbScore2 = parseFloat(deRs.data.data.pcbDebt.pcbCreditCard)
				const pcbDebtGroup = deRs.data.data.pcbDebt.pcbDebtGroup
				await Promise.all([
					offerRepo.updateMainScore(contractNumber,'pcb_score1',pcbScore1),
					offerRepo.updateMainScore(contractNumber,'pcb_score2',pcbScore2),
					offerRepo.updateMainScore(contractNumber,'pcb_debt_group',pcbDebtGroup)
				])
			}
		}
		return true
	}
	catch(err) {
		console.log(err)
		return false
	}
}

async function checkCICKUNN(contractNumber,kunnNumber,idNumber,isSme=false) {
	try {
		let body={};
		let cicUrl;
		if(utils.isNullOrEmpty(idNumber)) {
			if(isSme){
				idNumber = (await loanContractRepo.getLoanContract(contractNumber)).sme_tax_id;
			} 
			else{
				idNumber = (await loanContractRepo.getLoanContract(contractNumber)).id_number;
			}
		}
		const serviceName = 'CIC';
		let flow = 'S37';
		if(isSme){
			body.taxCode = idNumber;
			cicUrl = global.config.basic.decisionsV02[config.env] + global.config.data.deService.hmCicSmeDeV2;
			flow = 'S37_ENTERPRISE';
		} 
		else{
			body.idNumber = idNumber;
			cicUrl = global.config.basic.decisionsV02[config.env] + global.config.data.deService.hmCicDeV2;
		}
		const headers = utils.initHeaders(serviceName, flow);
		const rs = await common.postApiV2(cicUrl, body, headers);
		loggingRepo.saveStepLog(kunnNumber,SERVICE_NAME.DE,SERVICE_NAME.CHECK_CIC_KUNN,body,rs);
		if(rs.data.code != 1){
			const isSendNotiAppMcFlag = await utils.isSendNotiAppMc(kunnNumber);
			if(isSendNotiAppMcFlag){
				//send noti mc app
				try{
					const contractData = await loanContractRepo.getLoanContract(contractNumber);
					const bodyNoti = {
						phoneNumber: contractData?.phone_number1,
						title: 'Khế ước nhận nợ đã bị từ chối',
						message: `Khế ước nhận nợ ${kunnNumber} của bạn đã bị từ chối`, 
						value: {
							contractNumber: contractNumber,
							kuNumber: kunnNumber
						}
					};
					const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
					sendNotification( bodyNoti, endPoint, global.config );  
				} catch(err){
					common.log('send notification to appMC error', kunnNumber)
					console.log(err?.message)
				}
				callbackService.callbackPartner(kunnNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.REJECTED)
			}
			await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.REFUSE);
			return false;
		}
		return true;
	}
	catch(err) {
		console.log(err);
		await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.CHECK_CIC);
		return false;
	}
}

async function checkPCBKUNN(contractNumber,kunnNumber,partnerCode) {
	try {
		// if(partnerCode==PARTNER_CODE.MCAPP){
		// 	await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.CHECK_PCB)
		// 	return true
		// }else{
			const config = global.config
			const af2Url = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].af2
			const headers = utils.initHeaders('DECISION','DECISION');
			const deRs = await checkDeV2(contractNumber,af2Url,false,true,false,headers,true,kunnNumber)
			if(deRs.data.code != '0') {
				common.log(`check pcb KU`,kunnNumber)
				const isSendNotiAppMcFlag = await utils.isSendNotiAppMc(kunnNumber);
				if(isSendNotiAppMcFlag){
					//send noti mc app
					try{
						const contractData = await loanContractRepo.getLoanContract(contractNumber);
						const bodyNoti = {
							phoneNumber: contractData?.phone_number1,
							title: 'Khế ước nhận nợ đã bị từ chối',
							message: `Khế ước nhận nợ ${kunnNumber} của bạn đã bị từ chối`, 
							value: {
								contractNumber: contractNumber,
								kuNumber: kunnNumber
							}
						};
						const endPoint = serviceEndpoint.NOTIFICATION.CANCEL;
						sendNotification( bodyNoti, endPoint, global.config );  
					} catch(err){
						common.log('send notification to appMC error', kunnNumber)
						console.log(err?.message)
					}
					callbackService.callbackPartner(kunnNumber, partnerCode, CALLBACK_STAUS.REJECTED)
				}
				await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.REFUSE)
				return false
			}
		
			if(deRs?.data?.data?.pcbDebt){
				const pcbScore1 = parseFloat(deRs.data.data.pcbDebt.pcbInst)
				const pcbScore2 = parseFloat(deRs.data.data.pcbDebt.pcbCreditCard)
				const pcbDebtGroup = deRs.data.data.pcbDebt.pcbDebtGroup
				await Promise.all([
					offerRepo.updateMainScore(contractNumber,'pcb_score1',pcbScore1),
					offerRepo.updateMainScore(contractNumber,'pcb_score2',pcbScore2),
					offerRepo.updateMainScore(contractNumber,'pcb_debt_group',pcbDebtGroup)
				])
			}
			return true
		// }
	}
	catch(err) {
		await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.CHECK_PCB)
		console.log(err)
		return false
	}
}

async function checkPcbSME(body,kunnNumber) {
	try {
		const config = global.config;
		const contractNumber = body.contract_number;
		const contractData = await loanContractRepo.getLoanContract(contractNumber);
		const pcbBody = {
			"enterprise": {
				"requestAmount": contractData?.request_amt||'',
				"tenor": contractData?.request_tenor||'',
				"businessRegistrationNumber": contractData?.registration_number||'',
				"taxCode": contractData?.sme_tax_id||'',
				"placeOfRegistration": '',
				"address": contractData?.sme_headquarters_address||'',
				"phoneNumber": contractData?.sme_phone_number||'',
				"companyName": contractData?.sme_name||''
			},
			"representative": {
				"name": contractData?.sme_representation_name||'',
				"gender": contractData?.sme_representation_gender||'F',
				"dob": dateHelper.formatDate(contractData?.sme_representation_dob,DATE_FORMAT.DDMMYYYYN)||'',
				"placeOfBirth": 'ha noi',
				"countryOfBirth": 'VN',
				"idCard": contractData?.sme_representation_id||'',
				"taxCode": '',
				"address": contractData?.sme_representation_address_cur||',',
				"docType": '',
				"docNumber": '',
				"docIssedDate": dateHelper.formatDate(contractData?.sme_representation_issue_date,DATE_FORMAT.DDMMYYYYN)||'',
				"docCountryIssued": ''
			},
			"authorizedPerson": {
				"name": contractData?.authorized_name||'',
				"gender": contractData?.authorized_gender||'F',
				"dob": dateHelper.formatDate(contractData?.authorized_dob,DATE_FORMAT.DDMMYYYYN)||'',
				"placeOfBirth": 'ha noi',
				"countryOfBirth": 'VN',
				"idCard": contractData?.authorized_id||'',
				"taxCode": '',
				"address": contractData?.authorized_address_cur||',',
				"docType": '',
				"docNumber": '',
				"docIssedDate": dateHelper.formatDate(contractData?.authorized_issue_date,DATE_FORMAT.DDMMYYYYN)||'',
				"docCountryIssued": ''
			}
		}
		// console.log({pcbBody})
		const url = config.basic.decisionsV02[config.env] + config.data.deService.hmPcbSmeDeV2;
		const serviceName = 'PCB';
		const flow = 'CHECK_PCB';
		const headers = utils.initHeaders(serviceName, flow);
		const response = await common.postApiV2(url, pcbBody, headers);
		const result = response.data;
		await loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.DE,SERVICE_NAME.CHECK_PCB,pcbBody,result);
		let enterPriseData = result?.data.enterprise;
		if(result?.code == '0') {
			if(result?.data?.result=='ELIGIBLE'){
				await Promise.all([
					offerRepo.updateMainScore(contractNumber,'net_income',enterPriseData.pcbInstallment),
					offerRepo.updateMainScore(contractNumber,'card_balance',enterPriseData.creditCardBalance),
					offerRepo.updateMainScore(contractNumber,'pcb_debt_group',enterPriseData.scoreWorst),
					offerRepo.updateMainScore(contractNumber,'risk_date',enterPriseData.timeDebtGroup)
				])
				return true;
			}else{
				// callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.REJECTED)
				if(!utils.isNullOrEmpty(kunnNumber)){
					common.log(`[DE SERVICE] NOT PASS PCB`,kunnNumber)
					await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.REFUSE);
				}
				else{
					common.log(`[DE SERVICE] NOT PASS PCB`,body.contract_number);
					crmService.rejectContract(global.config,body.contract_number);
					await loanContractRepo.updateContractStatus(STATUS.REFUSED,body.contract_number);
				}
				return false;
			}
		}
		else {
			common.log(`[DE SERVICE] SERVICE ERROR CHECK PCB`,body.contract_number)
			return false;
		}
	}
	catch(err) {
		console.log(err)
		return false;	
	}
}

async function checkEligibleKUNN(isSME=false,body) {
	try {
		let responseBody;
		const contractNumber = body.contract_number;
		let finalStatus = STATUS.NOT_ELIGIBLE;
		let code = RESPONSE_CODE.INVALID_REQUEST;
		if(isSME){
			const isEligible = await baseCheckEligibleSME(body);
			if(isEligible) {
				finalStatus = STATUS.ELIGIBLE;
			}

			if(finalStatus != STATUS.ELIGIBLE) {
				responseBody = {
					code,
					message : "NOT ELIGIBLE"
				}	
			}
			else {
				responseBody = {
					code,
					message : "ELIGIBLE",
					contractNumber
				}
			}
		}else{
			const isEligible = await baseCheckEligible(body,true);
			if(isEligible) {
				finalStatus = STATUS.ELIGIBLE;
				code = RESPONSE_CODE.RECIEVED;
			}

			if(finalStatus != STATUS.ELIGIBLE) {
				responseBody = {
					code,
					message : "NOT ELIGIBLE"
				}	
			}
			else {
				responseBody = {
					code,
					message : "ELIGIBLE",
					contractNumber
				}
			}
		}
		if(responseBody.message == "ELIGIBLE") {
			return true
		}
		else {
			callbackService.callbackPartner(body?.kunn_number, body?.partner_code, CALLBACK_STAUS.NOT_ELIGIBLE)
			return false
		}
	}
	catch(err) {
		common.log('Check eligible a1 error:' + err.message);
	}
}

async function checkDeCar(contractNumber,url,headers = {'Content-Type': 'application/json'}) 
{
	try {
		const data = await Promise.all([loanContractRepo.getLoanContract(contractNumber)])
		const contractData = data[0]

		let date_of_birth = dateHelper.formatDate(contractData.birth_date, DATE_FORMAT.DB_FORMAT);
        let issue_date = dateHelper.formatDate(contractData.id_issue_dt, DATE_FORMAT.DB_FORMAT);

		let payload = {
            channel: "MCC",
            partnerCode: contractData?.partner_code,
            contractNumber: contractNumber,
            custId: contractData?.cust_id || null,
            losType: "LOS",
            productType: "CASH",
            agentCode: null,
            productId:  null,
            productCode: contractData?.product_code || null,
            telcoCsScore: null,
            telcoFsScore: null,
            customerName: contractData?.cust_full_name || null,
            gender: contractData?.gender || null,
            dateOfBirth: date_of_birth || null,
            idNumber: contractData?.id_number || null,
            otherIdNumber: contractData?.other_id_number || null,
            issueDate: contractData?.id_issue_place || null,
            issuePlace: issue_date || contractData.issue_place || null,
            phoneNumber: contractData?.phone_number1,
            email: contractData?.email || null,
            disbursementMethod: null,
            accountNumber: contractData.account_number || null,
            bankCode: contractData.bank_code || null,
            bankName: contractData?.bank_name || null,
            branchCode: null,
            branchName: null,
            beneficiaryName: contractData?.bank_account_owner || null,
            temCountry: "VN",
            temProvince: null,
            temProvinceCode: contractData.province_cur || null,
            temDistrict: null,
            temDistrictCode: contractData.district_cur || null,
            temWard: null,
            temWardCode: contractData.ward_cur || null,
            temDetailAddress: contractData.address_cur || null,
            permanentCountry: "VN",
            permanentProvince: null,
            permanentProvinceCode: contractData.province_per || null,
            permanentDistrict: null,
            permanentDistrictCode: contractData.district_per || null,
            permanentWard: null,
            permanentWardCode: contractData.ward_per || null,
            permanentDetailAddress: contractData.address_per || null,
            nationality: "VN",
            reference1: contractData.reference_type_1 || null,
            reference2: contractData.reference_type_2 || null,
            relativeReferenceName1: contractData.reference_name_1 || null,
            relativeReferenceName2: contractData.reference_name_2|| null,
            relativeReferencePhone1: contractData.reference_phone_1 || null,
            relativeReferencePhone2: contractData.reference_phone_2 || null,
			riskGroup: 1
        }

		const deRs = await common.postApiV2(url,payload, headers);
		loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.DE, TASK_FLOW.CHECK_AF2, payload, deRs);
		return deRs
	}
	catch(err) {
		console.log(err)
		console.log(`call DE compute offer error : ${err.message}`,contractNumber)
		return false
	}
}

module.exports = {
    checkPrescore,
    checkInternalScore,
    checkCIC,
	baseCheckS37,
	checkEKYCGateWay,
	baseCheckEligible,
	checkEligibleMc,
	checkEligibleMcV2,
	checkPCBMc,
	checkPCBMcV2,
	computeVTPOffer,
	checkAf1,
	checkAf2,
	checkCICKUNN,
	checkPCBKUNN,
	checkDeV3,
	baseCheckEligibleKOV,
	baseCheckEligibleSME,
	checkPcbSME,
	checkEligibleKUNN,
	checkEKYCKunnGateWay,
	checkDeCar,
	checkDeWithDrawRequest,
	computeOfferWithDrawRequest
}