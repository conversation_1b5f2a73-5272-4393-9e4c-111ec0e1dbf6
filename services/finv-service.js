const { PARTNER_CODE, KUNN_WORKFLOW, DOC_TYPE, DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const uuid = require("uuid");
const { SuccessResponse, ServerErrorResponse, BadRequestResponseV2, BadRequestResponse } = require("../base/response");
const { getValueByCodeType, getValueCodeMasterdataV2, getValueCodeByCodeType } = require("../utils/masterdataService");
const { RESPONSE_CODE } = require("../const/definition");
const { RESPONSE_MSG } = require("../const/response-const");
const deV02 = require("../integrated/de-v02");
const { validatePhoneNumber } = require("../utils/validator/validate");
const s3Service = require("../upload_document/s3-service");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loanCustomerRepo = require("../repositories/loan-customer-repo");
const loanAf1Repo = require("../repositories/loans-af1-repo");
const {
  FinVietBadRequestResponse,
  FinVietSuccessResponse,
  FinVietServerErrorResponse,
} = require("../entity/response-base-finv");
const resConst = require("../const/response-const");
const documentRepo = require("../repositories/document");
const lenderChangeRequestRepo = require("../repositories/lender-change-request-repo");
const { checkAvailableAmountApi } = require("../apis/lms-api");
const kunnRepo = require("../repositories/kunn-repo");
const { KUNN_STATUS, STATUS, CALLBACK_STAUS } = require("../const/caseStatus");
const { findByContractNumber } = require("../repositories/loan-contract-repo");
const moment = require("moment");
const { LENDER_REQUEST_TYPE, FINV_DOCTYPES, FINV_KUNN_DOCTYPES, LENDER_REFERENCE_TYPE, MASTER_DATA, LENDER_CONFIG_GROUP_TYPE, OWNER_ID, BIZZ_MODEL_DATA_TYPE, TERMINATION_TYPE, REF_TABLE } = require("../const/variables-const");
const { validateSubmitKunn, checkProcessingKunn, updateKunnDocument } = require("../KUNN/finv-kunn");
const { routing } = require("./workflow-router");
const AF3FinvModel = require("../A3_BASE/af3-finv");
const { convertCamelToSnake } = require("../utils/helper");
const FinvLoanLender = require("./finv-loan-lender");
const { callbackPartner } = require("./callback-service");
const { goNextStep } = require("./workflow-continue");
const loanContractDocumentRepo = require("../repositories/document");
const esingingRepo = require("../repositories/loan-esigning");
const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const { ENDPOINT_CONST } = require("../const/endpoint-const");
const common = require('../utils/common');
const { checkS3FilesExist } = require("../upload_document/s3-service");
const { findOne, find, updateData } = require("../utils/sqlHelper");

const lenderColumnConfigRepo = require("../repositories/lender-collumn-config-repo");
const { cancelContract } = require("./contract-service");

const af2PartnerMapping = {
  sme_name: "companyName",
  cust_full_name: "customerInfo.fullname",
  married_status: "customerInfo.marriedStatus",
  birth_date: "customerInfo.dateOfBirth",
  id_number: "customerInfo.identityCard",
  id_issue_dt: "customerInfo.issueDate",
  id_issue_place: "customerInfo.issuePlace",
  phone_number1: "customerInfo.phoneNumber",
  email: "customerInfo.email",
  per_new_province_code: "customerInfo.perAddress.provinceCode",
  per_new_ward_code: "customerInfo.perAddress.wardCode",
  address_per: "customerInfo.perAddress.detailAddress",
  cur_new_province_code: "customerInfo.curAddress.provinceCode",
  cur_new_ward_code: "customerInfo.curAddress.wardCode",
  address_cur: "customerInfo.curAddress.detailAddress",
  reference_type_1: "refPerson1.relationType",
  reference_name_1: "refPerson1.fullname",
  reference_phone_1: "refPerson1.phoneNumber",
  reference_id_number_1: "refPerson1.identityCard",
  reference_type_2: "refPerson2.relationType",
  reference_name_2: "refPerson2.fullname",
  reference_phone_2: "refPerson2.phoneNumber",
  reference_id_number_2: "refPerson2.identityCard",
  workplace_new_province_code: "workplaceAddress.provinceCode",
  workplace_new_ward_code: "workplaceAddress.wardCode",
  workplace_address: "workplaceAddress.detailAddress",
  partner_full_name: "partnerInfo.fullname",
  partner_id_number: "partnerInfo.identityCard",
  partner_phone_number: "partnerInfo.phoneNumber",
  partner_per_province_code: "partnerInfo.perAddress.provinceCode",
  partner_per_ward_code: "partnerInfo.perAddress.wardCode",
  partner_per_detail_address: "partnerInfo.perAddress.detailAddress",
  sme_headquarters_new_province: "registrationAddress.provinceCode",
  sme_headquarters_new_ward: "registrationAddress.wardCode",
  sme_headquarters_address: "registrationAddress.detailAddress",
  sme_phone_number: "companyPhoneNumber",
  sme_email: "companyEmail",
  request_tenor: "tenor",
  timeDuration: "businessData.timeDuration",
  turnoverAmount: "businessData.turnoverAmount",
  businessCost: "businessData.businessCost",
  avgTurnover: "businessData.avgTurnover",
  turnover3M: "businessData.turnover3M",
  turnover6M: "businessData.turnover6M",
  totalTurnOverNextYear: "businessData.totalTurnOverNextYear",
  totalCostOverNextYear: "businessData.totalCostOverNextYear",
  profit: "businessData.profit",
  projectedNextTimeProfit: "businessData.projectedNextTimeProfit",
};

async function getMasterdata(req, res) {
  const { codeType } = req.query;

  let masterdata = await getValueByCodeType(req, codeType);
  if (!masterdata) {
    throw new BadRequestResponse([], `Masterdata not found for code_type: ${codeType}`);
  }

  if(codeType === MASTER_DATA.CODE_TYPE.NEW_WARD && req.query?.codeParent){
    masterdata = masterdata.filter(item => item.valueCodeParent === req.query.codeParent);
  }
  if(codeType === MASTER_DATA.CODE_TYPE.BANK_BRANCH && req.query?.codeParent){
    masterdata = masterdata.filter(item => item.valueCodeParent === req.query.codeParent);
  }

  return new SuccessResponse(masterdata, "get masterdata success");
}

async function checkConsent(req, res) {
  let payloadRequest = req.body;
  try {
    let resultValidate = await validateCheckConsent(req, payloadRequest);
    if (resultValidate !== "VALID")
      throw new BadRequestResponse([new Error(resultValidate.code, resultValidate.message)], RESPONSE_MSG.BAD_REQUEST);

    const { requestId, phoneNumber, partnerCode } = payloadRequest;

    const consentRes = await deV02.checkConsent({
      phoneNumber,
      partnerCode,
      requestId
    });

    return new SuccessResponse({ check_consent: consentRes === 1 }, "");
  } catch (error) {
    console.log('[checkConsent] error: ', error);
    throw error
  }
}

const validateCheckConsent = async (req, input) => {
  let result = {};
  try {
    let requiredFields = [
      'requestId',
      'phoneNumber',
      'partnerCode',
    ];
    let isMissing = false;
    for (const field of requiredFields) {
      if (input[field] === null || input[field] === undefined) {
        result.code = field;
        result.message = `Missing required field: ${field}`;
        isMissing = true;
        break;
      }
    }
    if (isMissing) return result;
    let {
      phoneNumber,
      partnerCode,
    } = input;

    if (partnerCode !== PARTNER_CODE.FINV) {
      return { message: 'invalid partner code' };
    }

    let rsValidate = validatePhoneNumber('phoneNumber', phoneNumber);
    if (!rsValidate.isValid) return { code: rsValidate.field, message: rsValidate.message };

    return "VALID";
  } catch (error) {
    consoleLogException({ functionName: 'validateCheckConsent', inputObject: input, errorMessage: error.message });
    console.error(error);
    return { code: RESPONSE_CODE.SERVER_ERROR, message: RESPONSE_MSG.INTERNAL_SERVER_ERROR };
  }
}

async function getPresignedUploadDocumentUrl(req, res) {
  req.body = convertCamelToSnake(req.body)
  return s3Service.genPresignedUploadUrlForSme(req, res);
}

async function checkAvailableLimit(contractNumber) {
  try {
    if (!contractNumber) {
      throw new BadRequestResponse([], "contractNumber is required");
    }
  
    const avalibleAmountRs = await checkAvailableAmountApi(contractNumber);
  
    return new SuccessResponse({
      availableCreditLimit: Number(avalibleAmountRs?.avalibleAmount) || null,
      insurance: 'AA',
      insuranceCode: 'AA',
      insuranceRate: 2
    }, "get available limit success");
    
  } catch (error) {
    throw error
  }
}

async function submitKunn(req, res) {
  const { body } = req;
  delete body.ecSignature;
  await checkProcessingKunn(body.contractNumber);
  let value = await validateSubmitKunn(body);
  value = convertCamelToSnake(value)

  const avalibleAmountRs = await checkAvailableAmountApi(body.contractNumber);

  if (!avalibleAmountRs?.avalibleAmount) {
    throw new BadRequestResponse([], `Can not get avalible amount`);
  }

  if (Number(avalibleAmountRs?.avalibleAmount) < Number(body.orderAmount)) {
    throw new BadRequestResponse([], "Available amount is not enough");
  }

  const kunnId = await kunnRepo.genKunnNumber();
  if (!kunnId) {
    throw new BadRequestResponse([], "Failed to generate KUNN ID");
  }

  const loanContract = await findByContractNumber({ contractNumber: value.contract_number, partnerCode: PARTNER_CODE.FINV });

  if(!loanContract?.product_code) {
    throw new BadRequestResponse([], 'Missing loan contract product code');
  }

  const url = global.config.basic['product'][global.config.env] + ENDPOINT_CONST.PRODUCT.PRODUCT_DETAIL + `${loanContract.product_code}`;
  const productRs = await common.getApiV2(url);

  if(Number(body.orderAmount) < Number(productRs.data.data.kunn.minAmount) || Number(body.orderAmount) > Number(productRs.data.data.kunn.maxAmount)) {
    throw new BadRequestResponse([], "Order amount is not valid");
  }

  const endDate = moment().add(body.tenor, "months").format("yyyy-MM-DD");
  const startDate = moment().format("yyyy-MM-DD");

  const savedBody = {
    ...value,
    status: KUNN_STATUS.RECEIVED,
    kunn_id: kunnId,
    kunn_code: productRs.data.data.kunn.kunnCode,
    start_date: startDate,
    end_date: endDate,
    tenor: Number(body.tenor),
    lms_type: loanContract.contract_type,
    with_draw_amount: value.order_amount,
    available_amount: avalibleAmountRs?.avalibleAmount,
    partner_code: PARTNER_CODE.FINV,
  };

  const isInserted = await kunnRepo.insertKunn(savedBody);
  if (!isInserted) {
    throw new BadRequestResponse([], "Failed to insert KUNN data");
  }

  const docs = body.docs;
  await updateKunnDocument({ files: docs, kunnContractNumber: kunnId, contractNumber: value.contract_number });

  const bodyRouting = {
    currentTask: KUNN_WORKFLOW.START_KUNN,
    partnerCode: PARTNER_CODE.FINV,
    contractNumber: value.contract_number,
    kunnId: kunnId,
    workflowCode: "FINV_KUNN",
  };
  routing(bodyRouting);
  return new SuccessResponse({ status: 'RECEIVED', debtContractNumber: kunnId }, "Submit kunn success");
}

const getContractStatus = async (req, res) => {
  try {
    const { contract_number: contractNumber } = req.params;
    const loanContract = await loanContractRepo.findByContractNumber({
      contractNumber,
      partnerCode: PARTNER_CODE.FINV,
    });
    if (!loanContract) {
      const message = "loan_contract not found";
      return new FinVietBadRequestResponse(message, {}, []);
    }
    //mapping status
    let status;
    switch (loanContract.status) {
      case STATUS.RECEIVEDA1:
        status = 'A1_PROCESSING';
        break;
      case STATUS.PASSED_REVIEW_A1:
        status = STATUS.PASSED_REVIEW_A1;
        break;
      case STATUS.RECEIVEDA2:
        status = 'A2_PROCESSING';
        break;
      case STATUS.PASSED_REVIEW_A2:
        status = STATUS.PASSED_REVIEW_A2;
        break;
      case STATUS.SIGNING_IN_PROGRESS:
        status = STATUS.SIGNING_IN_PROGRESS;
        break;
      case STATUS.SIGNED:
        status = STATUS.SIGNED;
        break;
      case STATUS.RECEIVEDA3:
      case STATUS.IN_MANUAL_PROCESS_A3:
      case STATUS.PASSED_REVIEW_A3:
      case STATUS.COMPLETED_SIGN:
        status = 'A3_PROCESSING';
        break;
      case STATUS.NOT_ELIGIBLE:
        status = STATUS.NOT_ELIGIBLE;
        break;
      case STATUS.CANCELLED:
        status = STATUS.CANCELLED;
        break;
      case STATUS.REFUSED:
        status = STATUS.REFUSED;
        break;
      case STATUS.ACTIVATED:
        status = STATUS.ACTIVATED;
        break;
      case STATUS.EXPIRED:
        status = STATUS.EXPIRED;
        break;
      default:
        status = 'UNKNOWN';
        break;
    };
    return new FinVietSuccessResponse(
      {
        contractNumber: contractNumber,
        status: status,
      }
    )
  } catch (error) {
    console.error(`[FINV] Check contract status error:`, error);
    return new FinVietServerErrorResponse();
  }
};

const getPresignedDownloadDocumentUrl = async (req, res) => {
  try {
    const { contract_number: contractNumber } = req.params;
    const { docType } = req.query;
    const listDocs = await documentRepo.findByContractAndTypes(contractNumber, [docType]);
    if (!listDocs.length) {
      const message = "List of documents not found";
      return new FinVietBadRequestResponse(message, {}, []);
    }
    const presignedUrlData = await s3Service.genMultiplePresignedDownloadUrlForSme(listDocs);
    if (!presignedUrlData || !presignedUrlData.length) {
      const message = "Cannot generate presigned URL for documents";
      return new FinVietBadRequestResponse(message, {}, []);
    }
    const responseBody = {
      contractNumber,
      files: presignedUrlData.map((el) => ({
        presignedUrl: el.url,
        fileName: el.file_name,
      })),
    }
    return new FinVietSuccessResponse(responseBody);
  } catch (error) {
    console.error(`[FINV] Get presigned download document url error:`, error);
    return new FinVietServerErrorResponse();
  }
};

async function af3Resubmit(req, res) {
  req.body = convertCamelToSnake(req.body)
  req.body.documents = req.body.docs
  return new AF3FinvModel(req, res).process();
}

const getResubmitData = async (contract_number, status) => {
  let result = "";

  if (status == STATUS.RESUBMIT_A2) {
    const loan = await loanContractRepo.getLoanContract(contract_number)
    const payload = await getLoanAf2FormData(loan);
    result = await getResubmitAf2FormData(contract_number, payload);
  } else if (status == STATUS.RESUBMIT_A3) {
    let change_request = await lenderChangeRequestRepo.getLatestLenderChangeRequest({
      request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS,
      reference_code: contract_number,
      reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
      status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
    });

    if (change_request && change_request.request_body) {
      const doc_types = change_request?.request_body?.info?.map((entry) => {
        if (entry.type === "file") return entry.key;
      });
      const docs = await documentRepo.findByContractAndTypes(contract_number, doc_types);
      result = await s3Service.genMultiplePresigned(docs);
    }
  }

  return result;
};

const processAf3Data = async (req, res) => {
     // const body = convertCamelToSnake(req.body);
  const body = req.body;
  body['request_type'] = LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS;
  const contract_number = body.contract_number;

  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }

  if (loan.status != STATUS.IN_MANUAL_PROCESS_A3) {
    throw new BadRequestResponse([], "Loan is not in manual process status");
  }

  const partnerCode = loan.partner_code;

  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "LOAN_CONTRACT_DOCUMENT",
    groupType: "AF3",
  });

  const result = await loanLenderModel.af3process(body);
  if (result?.resubmit) {
    callbackPartner(contract_number, PARTNER_CODE.FINV, CALLBACK_STAUS.RESUBMIT, null, null, null, { resubmitData: await getResubmitAf3FormData(contract_number, body) });
    return new SuccessResponse("Process data to resubmit. successfully");
  } else {
    await loanLenderModel.commitAF3ChangeRequest({ comment: body.comment || "Auto approve change request", createdBy: body.createdBy || "system" }, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ);
    goNextStep(contract_number);
    return new SuccessResponse("Process data successfully");

  }
}

const checkAllFilesExist = async (contractNumber, docTypes) => {
  const documents = await documentRepo.findByContractAndTypes(contractNumber, docTypes);
  if (new Set(documents.map((doc) => doc.doc_type)).size !== docTypes.length) {
    return false;
  }
  const fileKeys = documents.map((doc) => doc.file_key);
  const fileExists = await checkS3FilesExist(fileKeys);
  return fileExists === true;
};

const genIframeUrl = (contractNumber, idNumber) => {
  return global.config.data.finv.esignUrl
        .replace('{contract_number}', contractNumber)
        .replace('{id_number}', idNumber)
}

const genFileResult = async (req, res) => {
  try {
    const { contractNumber, fileName, docType, fileKey, fileUrl, kunnId } = req.body;
    if (FINV_DOCTYPES.includes(docType)) {
      const loan = await loanContractRepo.getLoanContract(contractNumber);
      if (![STATUS.SIGNING_IN_PROGRESS, STATUS.PASSED_REVIEW_A2].includes(loan?.status)) {
        throw new BadRequestResponse([], "contractNumber invalid or status not ELIGIBLE");
      }

      const tasks = [];
      tasks.push(
        loanContractDocumentRepo.insert({
          contractNumber,
          docType: docType,
          docId: uuid.v4(),
          fileKey,
          fileName,
          url: fileUrl,
          // docGroup: "LOAN_CONTRACT",
        })
      );
      if (docType === DOC_TYPE.LCT) {
        tasks.push(
          esingingRepo.saveUnsignedContract(
            contractNumber,
            fileUrl,
            fileKey || DOC_TYPE.LCT
          )
        );
      }
      await Promise.all(tasks);
      checkAllFilesExist(contractNumber, FINV_DOCTYPES).then((result) => {
        console.log(`[genFileResult] checkAllDocumentsExist: ${result}`);
        if (result) {
          Promise.all([
            updateData({
              table: "loan_contract",
              columns:[
                'approval_date',
              ],
              values: [
                new Date()
              ],
              conditions: {
                contract_number: contractNumber,
              }
            })
          ]);
          loanContractRepo.updateContractStatus(STATUS.SIGNING_IN_PROGRESS, contractNumber);
          callbackPartner(contractNumber, loan.partner_code, CALLBACK_STAUS.SIGNING_IN_PROGRESS, null, null, null, {iframeUrl: genIframeUrl(contractNumber, loan.id_number)})
        }
      });

      return new SuccessResponse({},"contract update success");
    } else if (FINV_KUNN_DOCTYPES.includes(docType)) {
      const tasks = [];
      tasks.push(loanContractDocumentRepo.insert({
        kunnContractNumber: kunnId,
        contractNumber: contractNumber,
        docType: docType,
        docId: uuid.v4(),
        fileKey,
        fileName,
        url: fileUrl,
        // docGroup: "LOAN_CONTRACT",
      }));
      if (docType === DOC_TYPE.LCTKU) {
        tasks.push(
          esingingRepo.saveUnsignedContract(
            kunnId,
            fileUrl,
            fileKey || DOC_TYPE.LCTKU
          )
        );
      }
      await Promise.all(tasks);
      checkAllFilesExist(contractNumber, FINV_KUNN_DOCTYPES).then(async (result) => {
        console.log(`[genFileResult] checkAllDocumentsExist: ${result}`);
        if (result) {
          const updateData = { status: KUNN_STATUS.SIGNING_IN_PROGRESS, approve_signing_date: new Date() };
          await kunnRepo.update(kunnId, updateData);
          const kunnData = await kunnRepo.getKunnData(kunnId);
          await callbackPartner(contractNumber, kunnData.partner_code, CALLBACK_STAUS.KUNN_SIGNING_IN_PROGRESS, null, null, kunnId, {iframeUrl: genIframeUrl(kunnId, kunnData.identity_card_id)});
        }
      });
      return new SuccessResponse({},'contract update success');
    }
  } catch (error) {
    console.log(
      `[A2][genFileResult] contractNumber error ${error.message}`
    );
    throw error;
  }
};

const approveAF3ChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_AF3_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_PROCESS_A3) {
    throw new BadRequestResponseV2([], "Loan is not in waiting approve change request or MANUAL_PROCESS_A3 status");
  }

  //parse data to loan contract
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });

  await loanLenderModel.commitAF3ChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ);

  goNextStep(contract_number);
  return new SuccessResponse({}, "Change request approved successfully");
};

async function getCustomerInfoDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "contract_number, id, partner_code, cust_full_name, birth_date, gender, id_number, id_issue_dt, id_issue_place, phone_number1, email, married_status";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_CUST_INFO,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getPermanentAddress(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "contract_number, id, partner_code, per_new_ward_code, per_new_province_code, address_per";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_PERMANENT_ADDRESS,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getCurrentAddress(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "contract_number, id, partner_code, cur_new_province_code, cur_new_ward_code, address_cur";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_CURRENT_ADDRESS,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getSpouseDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loanCustomer = await loanCustomerRepo.getLoanCustomer(contractNumber);
  if (!loanCustomer) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, "partner_code");
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loanCustomer.contract_number,
    data: loanCustomer,
    partnerCode: loan.partner_code,
    table: "loan_customer",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_SPOUSE,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getReferenceDetails1(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = 'contract_number, id, partner_code, reference_type_1, reference_name_1, reference_phone_1, reference_id_number_1';
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_REFERENCE1,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getReferenceDetails2(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = 'contract_number, id, partner_code, reference_type_2, reference_name_2, reference_phone_2, reference_id_number_2';
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_REFERENCE2,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getProposalDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = 'contract_number, id, partner_code, loan_purpose, request_tenor, repayment_method, repayment_sources, bill_day, total_turnover_next_year, cost_next_year, pre_tax_profit_next_year, capital_need, request_amt, owner_equity';
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_LOAN_PROPOSAL,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getBusinessOperations(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = 'contract_number, id, partner_code, business_data';
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: JSON.parse(loan?.business_data) || {},
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_BUSINESS_OPERATIONS,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getBusinessesInfoDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "contract_number, id, partner_code, sme_name, registration_number, sector_industry, conditional_business_industry, sme_phone_number, company_website, sme_headquarters_new_province, sme_headquarters_new_ward";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_BUSINESSES_INFO,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

const getLoanRequestDetails = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  return new SuccessResponse({
    request_id: loan.request_id,
    contract_number: loan.contract_number,
    status: loan.status,
    partner_code: loan.partner_code,
    tax_code: loan.tax_code,
    created_date: loan.created_date,
    updated_date: loan.updated_date,
  });
};

const getLoanRequestDetailsAf1 = async (req, res) => {
  try {
    const { contract_number } = req.query;
    if (!contract_number) {
      throw new BadRequestResponseV2("Missing contract_number");
    }

    const loanAf1 = await loanAf1Repo.getLoanAf1(contract_number);

    return new SuccessResponse(loanAf1, "Loan request af1 details retrieved successfully");
  } catch (error) {
    console.error("Error in getLoanRequestDetailsAf1:", error);
    throw error;
  }
};

async function getLoanRequestDetailsAf2(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  //sua lay nhung docs can thiet
  // const documents = await documentRepo.findByContractAndTypes(contractNumber, ["SBIZ", "SPCB", "SCR", "STCRC", "SLOCH", "SDACA", "SPCHQ", "SMRCO"]);

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2,
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

const approveChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponse([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponse([], "Loan is not in waiting approve change request status");
  }

  //parse data to loan contract
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });
  await loanLenderModel.commitChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ);
  //di tiếp workflow
  goNextStep(contract_number);
  return new FinVietSuccessResponse({}, "Change request approved successfully");
};

const rejectChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponse([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponse([], "Loan is not in waiting approve change request status");
  }

  //parse data to loan contract
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });
  await loanLenderModel.commitChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_CHANGE_REQ);
  return new FinVietSuccessResponse({}, "Change request rejected successfully");
};

async function getLoanRequestDetailsAF3(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract_document",
    groupType: "AF3",
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getOriginDocumentByDocType(contract_number) {
  const doc_types = [...FINV_KUNN_DOCTYPES, ...FINV_DOCTYPES];
  const result = await documentRepo.findOriginContractByDocType(contract_number, doc_types);

  return new SuccessResponse(result);
}

const mappingModelData = async (modelData) => {
  if (!modelData) {
    return {};
  }
  const customer_rank = [
    {
      rank: modelData.rank || null,
      score: modelData.avg_score || null,
    },
  ];
  const finance_metric_details = await mappingFinanceMetric(modelData.finance_metric_details);
  const finance_metric = [
    {
      name: "Tổng chỉ tiêu tài chính",
      ratio: 70,
      score: modelData.total_financial_indicators || null,
    },
  ];
  const non_finance_metric_details = await mappingFinanceMetric(modelData.non_finance_metric_details);

  const non_finance_metric = [
    {
      name: "Tổng chỉ tiêu phi tài chính",
      ratio: 30,
      score: modelData.total_non_financial_indicators || null,
    },
  ];

  return {
    customer_rank: customer_rank,
    finance_metric_details: finance_metric_details,
    finance_metric: finance_metric,
    non_finance_metric_details: non_finance_metric_details,
    non_finance_metric: non_finance_metric,
  };
};

const mappingFinanceMetric = async (metricDetails) => {
  const result_metric_details = [];
  for (const key in metricDetails) {
    const { name, type, masterdata, order } = mappingMetricName(key);
    let metric_score = metricDetails[key].metric_score;
    if (masterdata) {
      const masterdataValue = await getValueCodeMasterdataV2(metric_score, masterdata);
      metric_score = masterdataValue?.nameVn || metric_score;
    } else if (type === BIZZ_MODEL_DATA_TYPE.percent) {
      metric_score = Number(metric_score || 0) * 100;
    }
    result_metric_details.push({
      code: key,
      score: metricDetails[key].score,
      name: name,
      weight_score: metricDetails[key].weight_score,
      ratio: Number(metricDetails[key].ratio || 0) * 100,
      metric_score: metric_score,
      type: type,
      order: order || 99,
    });
  }

  //order by order field asc, if not have order field, put to the end
  result_metric_details.sort((a, b) => a.order - b.order);
  return result_metric_details;
};

const getLoanRequestFinanceModel = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const modelData = await findOne({
    table: "loan_rating",
    whereCondition: {
      contract_number: contract_number,
    },
  });
  const rating = JSON.parse(modelData?.detail_data || "{}");
  return new SuccessResponse(await mappingModelData(rating));
};

const getLoanRequestRelatedDocs = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const documents = await find({
    table: "loan_contract_document",
    whereCondition: {
      contract_number: contract_number,
      owner_id: OWNER_ID.EVF,
      is_deleted: 0,
    },
    select: ["id", "doc_type", "file_key", "doc_id", "file_name", "url", "creation_time as created_date"],
    orderBy: {
      created_date: "DESC",
    },
  });
  return new SuccessResponse(documents || []);
};

const getLoanRequestRelated = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const selectKey = ["id", "request_id", "partner_code", "contract_number", "status", "registration_number", "tax_id", "sme_name", "created_date", "channel", "updated_date"];

  const relatedLoans = await find({
    table: "loan_contract",
    whereCondition: {
      sme_tax_id: loan.sme_tax_id,
    },
    select: selectKey,
  });
  return new SuccessResponse(relatedLoans.filter((item) => item.contract_number !== contract_number));
};

const mappingHistoryAction = (request_type) => {
  switch (request_type) {
    case LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ:
      return "Duyệt hồ sơ AF2";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_CHANGE_REQ:
      return "Từ chối hồ sơ AF2";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS:
      return "Xử lý thông tin bổ sung cho đối tượng phụ thuộc";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS:
      return "Yêu cầu chỉnh sửa AF2";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ:
      return "Duyệt hồ sơ AF3";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_AF3_CHANGE_REQ:
      return "Từ chối hồ sơ AF3";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_RESUBMIT_AF3_CHANGE_REQ:
      return "Yêu cầu chỉnh sửa AF3";
    default:
      return "unknown";
  }
};

const getLoanChangeRequestHistory = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const history = await find({
    table: "lender_change_request",
    whereCondition: {
      reference_code: loan.contract_number,
      // reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT,
    },
    orderBy: {
      id: "DESC",
    },
  });
  const result = history.map((item) => ({
    id: item.id,
    status: item.status,
    created_date: item.created_date,
    created_by: item.created_by,
    comment: item.comment,
    request_type: item.request_type,
    reference_code: item.reference_code,
    reference_type: item.reference_type,
    action: mappingHistoryAction(item.request_type),
  }));
  return new SuccessResponse(result, "Get change request history successfully");
};

const getLoanCustomerShareholders = async (contractNumber) => {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  let shareholders = await find({
    table: "loan_customer_shareholders",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
  shareholders = shareholders.map((item) => ({
    ...item,
    key: `parent_${item?.id}`,
  }));

  //lender
  const shareholderLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: shareholders,
    partnerCode: loan.partner_code,
    table: "loan_customer_shareholders",
  });
  const columns = await shareholderLenderModel.columnConfig;

  return new SuccessResponse({
    shareholders: shareholderLenderModel.extendData(),
    columns: columns,
  });
};

const getLoanCustomerShareholdersDetails = async (contractNumber, id) => {
  if (!contractNumber || !id) {
    throw new BadRequestResponseV2([], "contractNumber and id are required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  const shareholder = await findOne({
    table: "loan_customer_shareholders",
    whereCondition: {
      contract_number: contractNumber,
      id: id,
    },
  });

  const shareholderLenderModel = await BizziLoanLender.init({
    referenceNumber: id,
    data: shareholder,
    partnerCode,
    table: "loan_customer_shareholders",
    groupType: shareholder.subject,
  });

  const data = await shareholderLenderModel.bindingData({isSubInfo: true});
  return new SuccessResponse({
    info: data,
  });
};

const getLoanCustomerPartners = async (contractNumber) => {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  let partners = await find({
    table: "loan_customer_partners",
    whereCondition: {
      contract_number: contractNumber,
    },
  });

  partners = partners.map((item) => ({
    ...item,
    key: `parent_${item?.id}`,
  }));

  const partnerLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: partners,
    partnerCode: loan.partner_code,
    table: "loan_customer_partners",
  });
  const columns = await partnerLenderModel.columnConfig;
  return new SuccessResponse({
    partners: partnerLenderModel.extendData(),
    columns: columns,
  });
};

//detail of partners
const getLoanCustomerPartnersDetails = async (contractNumber, id) => {
  if (!contractNumber || !id) {
    throw new BadRequestResponseV2([], "contractNumber and id are required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;
  const partner = await findOne({
    table: "loan_customer_partners",
    whereCondition: {
      contract_number: contractNumber,
      id: id,
    },
  });
  if (!partner) {
    throw new BadRequestResponseV2([], "Partner not found for id: " + id);
  }
  const partnerLenderModel = await FinvLoanLender.init({
    referenceNumber: id,
    data: partner,
    partnerCode,
    table: "loan_customer_partners",
    // docs: documents
  });
  const data = await partnerLenderModel.bindingData({isSubInfo: true});
  return new SuccessResponse({
    info: data,
  });
};

const getLoanRequestFinanceInfoDetails = async (contractNumber) => {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_FINANCE_INFO,
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();
  return new SuccessResponse({
    info: loanModel,
  });
};

async function getLoanCustomerManagerDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  const manager = await findOne({
    table: "loan_customer_managers",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
  if (!manager) {
    throw new BadRequestResponseV2([], "Manager not found for id: " + id);
  }

  const managerLenderModel = await FinvLoanLender.init({
    referenceNumber: manager.id,
    data: manager,
    partnerCode,
    table: "loan_customer_managers",
  });

  const data = await managerLenderModel.bindingData({isSubInfo: true});

  return new SuccessResponse({
    id: manager.id,
    info: data,
  });
}

async function getLoanRepresentations(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  let representations = await find({
    table: "loan_customer_representations",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
  const profession = (await getValueCodeByCodeType("PROFESSION")) || [];
  representations = representations.map((item) => {
    return {
      ...item,
      position: `${profession.find((p) => p.code === item.position)?.value || ""}`,
      key: `parent_${item?.id}`,
    };
  });
  const representationLenderModel = await FinvLoanLender.init({
    referenceNumber: contractNumber,
    data: representations,
    partnerCode,
    table: "loan_customer_representations",
  });
  const columns = await representationLenderModel.columnConfig;
  const data = representationLenderModel.extendData();
  return new SuccessResponse({ representations: data, columns: columns });
}

async function getLoanRepresentationDetail(contractNumber, id) {
  if (!id || !contractNumber) {
    throw new BadRequestResponseV2([], "id and contractNumber are required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  const representation = await findOne({
    table: "loan_customer_representations",
    whereCondition: {
      id: id,
    },
  });
  if (!representation) {
    throw new BadRequestResponseV2([], "Representation not found for id: " + id);
  }

  const representationLenderModel = await FinvLoanLender.init({
    referenceNumber: id,
    data: representation,
    partnerCode,
    table: "loan_customer_representations",
  });

  const data = await representationLenderModel.bindingData({isSubInfo: true});

  return new SuccessResponse({
    info: data,
  });
}

async function getLoanRequestCommonDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  //sua lay nhung docs can thiet
  // const documents = await documentRepo.findByContractAndTypes(contractNumber, ["SBIZ", "SPCB", "SCR", "STCRC", "SLOCH", "SDACA", "SPCHQ", "SMRCO"]);

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_COMMON,
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

const mappingInstallment = (installments) => {
  const schedules = [];
  for (const ins of installments) {
    schedules.push({
      installmentId: ins.instalId,
      dueDate: moment(ins.dueDate).format("YYYY-MM-DD"),
      isInvoiced: ins.invoiced ? true : false, //check
      fee: ins.fee,
      lpi: ins.lpi,
      prinAmt: Number(ins.capitalRefunded || 0),
      interest: ins.interest,
      paidPrinAmt: Number(ins.paidDueCapital || 0),
      paidIrAmt: Number(ins.paidDueInterest || 0),
      paidLpiAmt: Number(ins.lpi || 0) - Number(ins.remainingLpi || 0),
      remainFeeAmt: Number(ins.fee || 0) - Number(ins.paidDueFee || 0),
      remainPrinAmt: Number(ins.capitalRefunded || 0) - Number(ins.paidDueCapital || 0),
      paidFeeAmt: Number(ins.paidDueFee || 0),
      remainIrAmt: Number(ins.interest || 0) - Number(ins.paidDueInterest || 0),
      remainLpi: Number(ins.remainingLpi || 0),
    });
  }

  return schedules;
};

async function getInstallmentData(debtContractNumber) {
  const url = global.config.basic['lmsMc'][global.config.env] + ENDPOINT_CONST.LMS_MC.GET_INSTALLMENT_BY_KUNN + `?debtAckContractNumber=${debtContractNumber}`;
  try {
    const installmentRs = await common.getApiV2(url);

    if (installmentRs?.data?.code != 0) {
      throw new Error("get installment error");
    }

    const { data, nonAllocationAmt } = installmentRs.data;

    const installments = mappingInstallment(data || []);
    return {
      installments,
      nonAllocationAmt,
    };
  } catch (error) {
    console.log('[FinvService][getInstallmentData] error: ', error)
    throw error;
  }
}

const getOverDueInstallmentData = (installments, trackingDate) => {
  const overDueInstallments = installments.filter(
    (e) => e.dueDate < trackingDate && e.paymentStatus == 0
  );
  return mapingDueObj(overDueInstallments);
};

const mapingDueObj = (installments) => {
  const dueObj = {};
  dueObj.prinAmt =
    installments.reduce((sum, item) => sum + item.prinAmt, 0) -
    installments.reduce((sum, item) => sum + item.paidPrinAmt, 0);
  dueObj.interest =
    installments.reduce((sum, item) => sum + item.interest, 0) -
    installments.reduce((sum, item) => sum + item.paidIrAmt, 0);
  dueObj.fee =
    installments.reduce((sum, item) => sum + item.fee, 0) -
    installments.reduce((sum, item) => sum + item.paidFeeAmt, 0);
  dueObj.lpi =
    installments.reduce((sum, item) => sum + item.lpi, 0) -
    installments.reduce((sum, item) => sum + item.paidLpiAmt, 0);
  return dueObj;
};

const getNextDueInstallmentData = (
  installments,
  trackingDate,
  isEqDate = false
) => {
  const nextDueInstallment = installments.find((e) =>
    !isEqDate
      ? e.dueDate >= trackingDate && e.isInvoiced
      : e.dueDate == trackingDate && e.isInvoiced
  );
  if (nextDueInstallment) {
    return mapingDueObj([nextDueInstallment]);
  }
  return mapingDueObj([]);
};

async function getInstallmentByKunn(req, res) {
  const trackingDate = moment().format("YYYY-MM-DD");
  try {
    const kunn = await kunnRepo.getKunnByIdAndContractNumber(req.params.debtContractNumber, req.body.contractNumber)
    if (!kunn) {
      throw new BadRequestResponse([], "Kunn not found")
    }

    const { installments, nonAllocationAmt } = await getInstallmentData(
      req.params.debtContractNumber
    );

    const overDueInstallment = getOverDueInstallmentData(
      installments,
      trackingDate
    );

    const nextDueInstallment = getNextDueInstallmentData(
      installments,
      trackingDate
    );
    
    return new SuccessResponse({
      debtContractNumber: req.params.debtContractNumber,
      nonAllocationAmt,
      nextDueInstallment,
      installments,
      overDueInstallment,
    });
  } catch (error) {
    console.log('[FinvService][getInstallmentByKunn] error: ', error)
    throw error
  }
}

async function trackingInstallment(req, res) {
  const trackingDate = req.query?.trackingDate;
  try {
    const kunn = await kunnRepo.getKunnByIdAndContractNumber(req.params.debtContractNumber, req.body.contractNumber)
    if (!kunn) {
      throw new BadRequestResponse([], "Kunn not found")
    }

    const { installments, nonAllocationAmt } = await getInstallmentData(
      req.params.debtContractNumber
    );

    const overDueInstallment = getOverDueInstallmentData(
      installments,
      trackingDate
    );

    const nextDueInstallment = getNextDueInstallmentData(
      installments,
      trackingDate
    );
    
    return new SuccessResponse({
      debtContractNumber: req.params.debtContractNumber,
      nonAllocationAmt,
      nextDueInstallment,
      installments,
      overDueInstallment,
    });
  } catch (error) {
    console.log('[FinvService][trackingInstallment] error: ', error)
    throw error
  }
}

async function handleFetWithdrawal(req, res) {
  try {
    let dataResponse = {}
    const {debtContractNumber, terminationType, terminationPartially} = req.body
    const kunn = await kunnRepo.getKunnData(debtContractNumber)

    if(kunn?.partner_code != PARTNER_CODE.FINV) {
      throw new BadRequestResponse([], "Kunn not found for debtContractNumber: " + debtContractNumber)
    }

    let url;
    let dataPayload;

    if(terminationType === TERMINATION_TYPE.PARTIAL) {
      url = global.config.basic['lmsMc'][global.config.env] + ENDPOINT_CONST.LMS_MC.CREATE_ANNEX;
      dataPayload = {
        debtAckContractNumber: debtContractNumber,
        effectStartDate: moment().format("YYYY-MM-DD"),
        amountAnnex: terminationPartially,
        createdBy: PARTNER_CODE.FINV,
      }
    } else {
      url = global.config.basic['lmsMc'][global.config.env] + ENDPOINT_CONST.LMS_MC.CREATE_FULL_ANNEX;
      dataPayload = {
        debtAckContractNumber: debtContractNumber,
        effectStartDate: moment().format("YYYY-MM-DD"),
        createdBy: PARTNER_CODE.FINV,
      }
    }

    const annexRs = await common.postApiV2(url, dataPayload);

    if(annexRs?.data?.code != 0) {
      throw new ServerErrorResponse([], "Create annex error");
    }

    const data = {
      debtContractNumber,
      annexNumber: annexRs?.data?.data?.annexNumber,
      totalPayment: annexRs?.data?.data?.totalPayment,
      totalNonAllAmt: annexRs?.data?.data?.totalNonAllAmt,
      totalAmount: annexRs?.data?.data?.TOTAL_AMOUNT,

      overDueCapital: annexRs?.data?.data?.overDueCapital,
      overDueInterest: annexRs?.data?.data?.overDueInterest,
      overDueFee: annexRs?.data?.data?.overDueFee,
      overDueLpi: (annexRs?.data?.data?.lpiPrin ?? 0) + (annexRs?.data?.data?.lpiInterest ?? 0),

      onDueCapital: annexRs?.data?.data?.onDueCapital,
      onDueInterest: annexRs?.data?.data?.onDueInterest,
      onDueFee: annexRs?.data?.data?.onDueFee,

      amountAnnex: annexRs?.data?.data?.amountAnnex,
      feeAnnex: annexRs?.data?.data?.feeAnnex,
      penaltyAmt: annexRs?.data?.data?.penalitiesAmt,
    }

    return new SuccessResponse({
      ...data,
    });
    
  } catch (error) {
    console.log('[FinvService][handleReqDnseFetWithdrawal] error: ', error)
    throw error
  }
}


const getLoanAf2FormData = async (loanContractData) => {
  const { contract_number: contractNumber } = loanContractData;
  const [ loanCustomerData, loanContractDocs ] = await Promise.all([
    loanCustomerRepo.getLoanCustomer(contractNumber),
    loanContractDocumentRepo.getDocumentByContractNumber(contractNumber)
  ]) 
  const businessData = JSON.parse(loanContractData.business_data)
  const af2Obj = {
    requestId: loanContractData.request_id,
    status: loanContractData.status,
    contractNumber: contractNumber,
    registrationNumber: loanContractData.registration_number,
    smeTaxId: loanContractData.sme_tax_id,
    partnerCode: loanContractData.partner_code,
    companyEmail: loanContractData.company_email,
    companyWebsite: loanContractData.company_website,
    loanPurpose: loanContractData.loan_purpose,
    capitalNeed: loanContractData.capital_need,
    loanAmount: loanContractData.approved_amt, 
    ownerEquity: loanContractData.owner_equity,
    tenor: loanContractData.request_tenor,
    otherCapital: loanContractData.other_capital,
    monthlyInterestPaymentDate: '',
    companyName: loanContractData.company_name,
    billDay: loanContractData.bill_day,
    workplaceAddress: {
      provinceCode: loanContractData.workplace_province,
      wardCode: loanContractData.workplace_ward,
      detailAddress: loanContractData.workplace_address,
    },
    registrationAddress: {
      provinceCode: loanCustomerData.province_on_license,
      wardCode: loanCustomerData.ward_on_license,
      detailAddress: loanCustomerData.address_on_license,
    },
    repaymentMethod: loanContractData.repayment_method,
    repaymentSources: loanContractData.repayment_sources,
    businessType: loanContractData.business_type, 
    businessIndustry: loanContractData.sector_industry, // 
    conditionalBusinessIndustry: loanContractData.conditional_business_industry,
    companyPhoneNumber: loanContractData.company_phone_number,
    customerInfo: {
      fullname: loanContractData.cust_full_name,
      marriedStatus: loanContractData.married_status,
      identityCard: loanContractData.id_number,
      dateOfBirth: loanContractData.birth_date,
      issueDate: loanContractData.id_issue_dt,
      issuePlace: loanContractData.id_issue_place,
      phoneNumber: loanContractData.phone_number1,
      email: loanContractData.email,
      perAddress: {
        provinceCode: loanContractData.province_per,
        wardCode: loanContractData.ward_per,
        detailAddress: loanContractData.address_per,
        typeOfOwnership: "",
      },
      curAddress: {
        provinceCode: loanContractData.province_cur,
        wardCode: loanContractData.ward_cur,
        detailAddress: loanContractData.address_cur,
        typeOfOwnership: "",
      },
    },
    refPerson1: {
      relationType: loanContractData.reference_type_1,
      fullname: loanContractData.reference_name_1,
      phoneNumber: loanContractData.reference_phone_1,
    },
    refPerson2: {
      relationType: loanContractData.reference_type_2,
      fullname: loanContractData.reference_name_2,
      phoneNumber: loanContractData.reference_phone_2,
    },
    businessData: {
      timeDuration: businessData?.timeDuration,
      turnoverAmount: businessData?.turnoverAmount,
      businessCost: businessData?.businessCost,
      avgTurnover: businessData?.avgTurnover,
      turnover3M: businessData?.turnover3M,
      turnover6M: businessData?.turnover6M,
      totalTurnOverNextYear: businessData?.totalTurnOverNextYear,
      totalCostOverNextYear: businessData?.totalCostOverNextYear,
      profit: businessData?.profit,
      projectedNextTimeProfit: businessData?.projectedNextTimeProfit,
    },
    docs: loanContractDocs.map(({doc_type, doc_id}) => ({ docType: doc_type, docId: doc_id})),
    isAuthorizationSign: loanContractData.is_authorization_sign,
  }

  if(loanContractData.married_status === 'M') {
      af2Obj.partnerInfo = {
      fullname: loanCustomerData.partner_full_name,
      identityCard: loanCustomerData.partner_id_number,
      phoneNumber: loanCustomerData.partner_phone_number,
      perAddress: {
        provinceCode: loanCustomerData.partner_per_province_code,
        wardCode: loanCustomerData.partner_per_ward_code,
        detailAddress: loanCustomerData.partner_per_detail_address,
        typeOfOwnership: "",
      }
    }
  }
  return af2Obj;
}

async function getResubmitAf2FormData(contract_number, payload) {
  const changeRequests = await lenderChangeRequestRepo.getChangeRequestDetailByContract({
    contract_number,
  });
  payload = flattenAf2FormObject(payload);
  const lenderColumnConfig = await lenderColumnConfigRepo.getLenderColumnConfig({ partnerCode: PARTNER_CODE.FINV });
  const form = {};
  for (const element of changeRequests) {
    if (element.status) continue;
    let config = lenderColumnConfig.find((item) => item.key === element.key && item.table_name?.toUpperCase() === element.ref_table);
    if (element.ref_table === "LOAN_CONTRACT" || element.ref_table === REF_TABLE.LOAN_CUSTOMER) {
      if (config?.type === "file") {
        if(!form?.docs) form.docs = []
        let doc = payload.docs?.find((doc) => doc.docType === element.key);
        form.docs.push({
          field: 'docId',
          value: doc?.docId ?? '',
          comment: element.comment,
        });
      } else if (config) {
        const field = af2PartnerMapping[element.key] ?? (element.key).replace(/_([a-z])/g, (_, c) => c.toUpperCase());
        form[element.key] = {
          field,
          value: payload[field] || '',
          comment: element.comment,
        };
      }
    }
  }
  return Object.values(form);
}

async function getResubmitAf3FormData(contract_number, payload) {
  const changRequest = await lenderChangeRequestRepo.getLatestLenderChangeRequest({
    request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS,
    reference_code: contract_number,
    reference_type: REF_TABLE.LOAN_CONTRACT_DOCUMENT,
  });
  const changeDetails = await lenderChangeRequestRepo.getLenderChangeRequestDetail(changRequest.id);
  const docTypes = [DOC_TYPE.VIDEO];
  const docs = await loanContractDocumentRepo.findByContractAndTypes(
    contract_number,
    docTypes
  );
  
  const form = [];
  for(const changeDetail of changeDetails) {
    if (changeDetail.status) return form;
    if (changRequest.reference_type === "LOAN_CONTRACT_DOCUMENT") {
        let doc = docs.find((doc) => doc?.doc_type === changeDetail.key);
        form.push(
          {
            field: 'docId',
            value: doc?.doc_id,
            comment: changeDetail.comment,
          }
        )
    }
  }
  return form;
}


const processSubInfo = async ({ contract_number, sub_type, reference_id, info, request_id }) => {
  if (!contract_number || !sub_type || !reference_id || !info) {
    throw new BadRequestResponse([], `contract_number, sub_type, reference_id and info are required`);
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponse([], "Loan not found for contract_number: " + contract_number);
  }
  const partnerCode = loan.partner_code;
  let table = "";
  let dto = null;
  switch (sub_type) {
    case LENDER_REFERENCE_TYPE.LOAN_CUSTOMER:
      table = LENDER_REFERENCE_TYPE.LOAN_CUSTOMER.toLowerCase();
      dto = await findOne({
        table: "loan_customer",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    default:
      throw new BadRequestResponse(
        [],
        `sub_type ${sub_type} is not supported 
        [${LENDER_REFERENCE_TYPE.LOAN_CUSTOMER}]`
      );
  }
  const lenderModel = await FinvLoanLender.init({
    referenceNumber: reference_id,
    data: dto,
    partnerCode,
    table: table,
  });
  await lenderModel.process({
    contractNumber: contract_number,
    requestId: request_id || null,
    requestType: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
    referenceCode: reference_id,
    referenceType: sub_type,
    info: info,
  });
  return new FinVietSuccessResponse("Process sub info successfully");
};

const processData = async (req, res) => {
  const body = req.body;
  const contract_number = body.contract_number;
  if (!contract_number) {
    throw new BadRequestResponse([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponse([], "Loan not found for contract_number: " + contract_number);
  }
  if (loan.status !== STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponse([], "Loan is not in manual review status");
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
  });
  const result = await loanLenderModel.process(body);

  if (result.resubmit) {
    const validationErrors = await getResubmitData(loan.contract_number, STATUS.RESUBMIT_A2);
    // callback to finv partner
    callbackPartner(loan.contract_number, partnerCode, CALLBACK_STAUS.RESUBMIT_A2, null, null, null, {
      resubmitData: validationErrors
    });
    return new FinVietSuccessResponse(validationErrors, "Process data successfully");
  } else {
    //go next step
    await loanLenderModel.commitChangeRequest(
      {
        comment: body.comment || "Auto approve change request",
        createdBy: body.createdBy || "system",
      },
      LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ
    );
    goNextStep(contract_number);
  }
  return new FinVietSuccessResponse(result, "Process data successfully");
};

function flattenAf2FormObject(obj, parentKey = '', res = {}) {
  const nonExtract = ['docs']
  for (let key in obj) {
    if (!obj.hasOwnProperty(key)) continue;

    const newKey = parentKey ? `${parentKey}.${key}` : key;
    const value = obj[key];

    // Skip flattening businessData and docs
    if (nonExtract.includes(key)) {
      res[key] = value;
    }
    // If value is object (and not array), recurse
    else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      flattenAf2FormObject(value, newKey, res);
    }
    // Otherwise, assign directly
    else {
      res[newKey] = value;
    }
  }
  return res;
}

const a2ReSubmit = async (req, res) => {
  let payload = req.body;
  const { contractNumber } = payload;
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponse([], "Loan not found for contract_number: " + contractNumber);
  }
  const af2FormData = await getLoanAf2FormData(loan);
  mergeIds(af2FormData, payload);
  const changeRequests = await lenderChangeRequestRepo.getChangeRequestDetailByContract({
    contract_number: contractNumber,
  });
  payload = flattenAf2FormObject(payload);
  const lenderColumnConfig = await lenderColumnConfigRepo.getLenderColumnConfig({ partnerCode: PARTNER_CODE.FINV });
  for (const element of changeRequests) {
    if (element.status) continue;
    let config = lenderColumnConfig.find((item) => item.key === element.key && item.table_name?.toUpperCase() === element.ref_table);
    if (element.ref_table === "LOAN_CONTRACT" || element.ref_table.toLowerCase() === DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER) {
      if (config?.type === "file") {
        let docs = payload.docs?.filter((doc) => doc.docType === element.key);
        // for (const doc of docs) {
          //   await documentRepo.updateLoanContractNumberKunn({ docId: doc.doc_id, docType: doc.doc_type, contractNumber });
          // }
        element.new_value = JSON.stringify(docs);
      } else {
        element.new_value = payload[af2PartnerMapping[element.key] ?? (element.key).replace(/_([a-z])/g, (_, c) => c.toUpperCase())];
      }
    }
    // element.status = element.is_change = element.new_value !== element.old_value;
    element.status = element.is_change = JSON.stringify(element.new_value) !== JSON.stringify(element.old_value);
  } 
  await lenderChangeRequestRepo.updateChangeRequestDetails(changeRequests);
  let checkFull = true;
  for (const element of changeRequests) {
    if (!element.status && !element.new_value) {
      checkFull = false;
      break;
    }
  }
  if (checkFull) {
    // auto approve change request
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    const loanLenderModel = await FinvLoanLender.init({
      referenceNumber: contractNumber,
      data: loan,
      partnerCode: loan.partner_code,
      table: "loan_contract",
    });
    let request = await lenderChangeRequestRepo.getLatestLenderChangeRequest({
      request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS,
      reference_code: contractNumber,
      reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT,
      status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
    });
    if (!request) {
      throw new BadRequestResponse([], "Lender change request not found");
    };
    await lenderChangeRequestRepo.updateStatusOfListChangeRequest([request.id], LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE);
    await loanLenderModel.commitChangeRequest(
      {
        comment: "Partner resubmit",
        createdBy: "system",
      },
      LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ
    );
    goNextStep(contractNumber);
    return new FinVietSuccessResponse({
      message: "Resumit successfully",
    });
  } else {
    return new FinVietSuccessResponse({
      message: "Resumit successfully, but not all fields are filled",
    });
  }
}

function mergeIds(srcObj, destObj) {
  if (!srcObj || !destObj) return;
  if (Array.isArray(destObj) && Array.isArray(srcObj)) {
    for (let i = 0; i < destObj.length; i++) {
      mergeIds(srcObj[i], destObj[i]);
    }
  } else {
    if (srcObj.id) {
      destObj.id = srcObj.id;
    }
    for (const key in destObj) {
      if (typeof destObj[key] === "object" && typeof srcObj[key] === "object") {
        mergeIds(srcObj[key], destObj[key]);
      }
    }
  }
}

const cancelCreditLimitSigningInProgress = async (req, res) => {
  try {
    const {
        days,
        partnerCode,
        comment
    } = req.body;

    const loans = await loanContractRepo.getContractsEsigningByStatusAndCreatedDate({
        days,
        partnerCode: partnerCode ?? PARTNER_CODE.FINV,
    });

    const now = moment().format('YYYY-MM-DD HH:mm:ss');

    if (!loans || loans.length === 0) {
        console.log(`${now} | cancelCreditLimitSigningInProgress | ${JSON.stringify(req.body)} | No contracts found for the given criteria`);
        return new SuccessResponse([], `${now} - No contracts found for the given criteria`)
    }

    let cancelPromises = [];
    let effectiveContractNumbers = [];
    for (const loan of loans) {
        try {
            //handle cancel
            cancelPromises.push(cancelContract({
                contractNumber: loan.contract_number,
                comment: comment ?? 'Cancelled due to timeout credit signing in progress',
                cancelledBy: 'system',
                loanContract: loan
            }));
            effectiveContractNumbers.push(loan.contract_number);
          } catch (error) {
            console.error(`Error cancelling contract ${loan.contract_number}:`, error);
          }
        }
    Promise.all(cancelPromises).then(() => {
        console.log(`${now} | cancelCreditLimitSigningInProgress | ${JSON.stringify(req.body)} | Successfully cancelled ${cancelPromises.length} contracts`)
    }).catch((error) => {
        console.error(`${now} | cancelCreditLimitSigningInProgress | ${JSON.stringify(req.body)} | Error cancelling contracts:`, error);
    });
    if (cancelPromises.length === 0) {
        return new SuccessResponse([], `${now} - No contracts found for the given criteria`)
    }

    return new SuccessResponse({
        effectiveContractNumbers: effectiveContractNumbers ?? []
    }, `${now} - Processing ${cancelPromises.length} contracts`)
    
} catch (e) {
    console.error(e);
    return new ServerErrorResponse(`Error processing cancelCreditLimitSigningInProgress: ${e.message}`, e)
}
}

const getKunnContractStatus = async (req, res) => {
  try {
    const { contractNumber, debtContractNumber } = req.params;

    const contract = await loanContractRepo.getLoanContract(contractNumber);
    if (!contract) {
      throw new BadRequestResponse([], "Loan not found for contractNumber: " + contractNumber);
    }

    const kunn = await kunnRepo.getKunnData(debtContractNumber);
    if (!kunn) {
      throw new BadRequestResponse([], "Kunn not found for debtContractNumber: " + debtContractNumber);
    }

    return new SuccessResponse({
      contractNumber: contract.contract_number,
      debtContractNumber: kunn.kunn_id,
      status: kunn.status,
    });
  } catch (error) {
    throw error
  }
}

const rejectAF3ChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_AF3_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_PROCESS_A3) {
    throw new BadRequestResponseV2([], "Loan is not in waiting approve change request status");
  }

  //parse data to loan contract
  const loanLenderModel = await FinvLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });
  await loanLenderModel.commitAF3ChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_AF3_CHANGE_REQ);
  return new SuccessResponse({}, "Change request rejected successfully");
};

module.exports = {
  getMasterdata,
  checkConsent,
  getPresignedUploadDocumentUrl,
  getContractStatus,
  getPresignedDownloadDocumentUrl,
  checkAvailableLimit,
  submitKunn,
  af3Resubmit,
  processAf3Data,
  genFileResult,
  approveAF3ChangeRequest,
  getCustomerInfoDetails,
  getPermanentAddress,
  getCurrentAddress,
  getSpouseDetails,
  getReferenceDetails1,
  getReferenceDetails2,
  getProposalDetails,
  getBusinessOperations,
  getBusinessesInfoDetails,
  getLoanRequestDetailsAf1,
  getLoanRequestDetailsAf2,
  approveChangeRequest,
  rejectChangeRequest,
  getLoanRequestDetails,
  getLoanRequestDetailsAF3,
  getOriginDocumentByDocType,
  getLoanRequestFinanceModel,
  getLoanRequestRelatedDocs,
  getLoanRequestRelated,
  getLoanChangeRequestHistory,
  getLoanCustomerShareholders,
  getLoanCustomerPartners,
  getLoanCustomerPartnersDetails,
  getLoanCustomerShareholdersDetails,
  getLoanRequestFinanceInfoDetails,
  getLoanCustomerManagerDetails,
  getLoanRepresentations,
  getLoanRepresentationDetail,
  getLoanRequestCommonDetails,
  getInstallmentByKunn,
  trackingInstallment,
  handleFetWithdrawal,
  processSubInfo,
  processData,
  a2ReSubmit,
  cancelCreditLimitSigningInProgress,
  getKunnContractStatus,
  rejectAF3ChangeRequest
}