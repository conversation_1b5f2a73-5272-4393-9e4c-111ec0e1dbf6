const common = require("../utils/common")
const loggingRepo = require("../repositories/logging-repo")
const { SERVICE_NAME, PARTNER_CODE } = require("../const/definition")
const { STEP } = require("../const/step-const")
const { RESPONSE_CODE_DE } = require("../const/response-const")
const loanContractRepo = require("../repositories/loan-contract-repo")
const offerRepo = require("../repositories/offer")
const turnoverRepo = require("../repositories/turnover-repo")
const kovCashService = require("./kov-cash-service")
const productService = require("../utils/productService")
const { SCHEME } = require("../const/scheme-kov-const")
const { initHeaders } = require("../utils/helper")
/**
 * @Params {turnovers} object turnover từ 1 đến 10
 * @Param {string} schema San pham value P-V-S
 * @Params {int} timeRegistration THOI GIAN THAM GIA GIAI PHAP MERCHANT vi du 12
 * @Params {string} legalStatus Tinh trang dang ky kinh doanh value Y/N
 * @Params {int} businessDuration Thoi gian thuc te tham gia vi du 12
 * @Params {int} diBeforeCE DI before ce  vidu 0
 * @Params {float} interestRate  vi du 0.6
 * @Params {string} registrationDate  vidu "2022-01-10 00:10:10"
*/
const calculatorMaxLoan = async function ({ contractNumber, partnerCode, custId, diBeforeCE, diAfterCE, tenor, interestRate, turnovers, businessDuration, legalStatus, timeRegistration, registrationDate, scheme, timeDuration, requestAmount, fundingFromEc }) {
    const config = global.config;
    let bodyRequest;
    scheme = getSchemeFillMaxLoan(scheme);
    try {
        bodyRequest = {
            contractNumber: contractNumber,
            partnerCode: partnerCode || PARTNER_CODE.KOV,
            custId: custId || null,
            offerData: {
                diBeforeCE: diBeforeCE || 0, //di before ce
                diAfterCE: diAfterCE || 0, //di after, 
                tenor: tenor,
                interestRate: interestRate, //lai suat
                turnover1: turnovers.turnover1 ? parseFloat(turnovers.turnover1) : 0,
                turnover2: turnovers.turnover2 ? parseFloat(turnovers.turnover2) : 0,
                turnover3: turnovers.turnover3 ? parseFloat(turnovers.turnover3) : 0,
                turnover4: turnovers.turnover4 ? parseFloat(turnovers.turnover4) : 0,
                turnover5: turnovers.turnover5 ? parseFloat(turnovers.turnover5) : 0,
                turnover6: turnovers.turnover6 ? parseFloat(turnovers.turnover6) : 0,
                turnover7: turnovers.turnover7 ? parseFloat(turnovers.turnover7) : 0,
                turnover8: turnovers.turnover8 ? parseFloat(turnovers.turnover8) : 0,
                turnover9: turnovers.turnover9 ? parseFloat(turnovers.turnover9) : 0,
                turnover10: turnovers.turnover10 ? parseFloat(turnovers.turnover10) : 0,
                turnover11: turnovers.turnover11 ? parseFloat(turnovers.turnover11) : 0,
                turnover12: turnovers.turnover12 ? parseFloat(turnovers.turnover12) : 0,
                businessDuration: businessDuration || 12, //thoi gian thuc te tham gia 
                legalStatus: legalStatus || "N", //tinh trang dang ky kinh doanh value Y/N
                // timeRegistration: parseFloat(timeRegistration) || 12, //THOI GIAN THAM GIA GIAI PHAP MERCHANT
                registrationDate: registrationDate || null, // datetime dang ky
                scheme: scheme,
                timeDuration: timeDuration ? parseFloat(timeDuration) : 0,
                requestAmount: requestAmount ? parseFloat(requestAmount) : 0,
                fundingFromEc: fundingFromEc ? parseFloat(fundingFromEc) : 0
            }
        }
        const calculatorMaxLoanUrl = config.basic.decisionsV02[config.env] + config.data.deService.kovCalculatorMaxLoanV2;
        const headers = initHeaders('DECISION', 'OFFER');
        const response = await common.postApiV2(calculatorMaxLoanUrl, bodyRequest, headers);
        loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.DE, STEP.CALCULATOR_MAXLOAN, bodyRequest, response);

        if (!response || !response.data || response.data.code != RESPONSE_CODE_DE.SUCCESS) return undefined;
        return response.data.data;
    } catch (err) {
        common.log(err.message, bodyRequest, 3);
        console.log(err);
        return undefined;
    }
}

/**
 * @Params {income} object turnover từ 1 đến 10
 * @Param {string} addr - expenses.addr  - vidu: "27"
 * @Params {int} totalMonthlyExpenses - expenses.totalMonthlyExpenses - vidu: 5000000
 * @Params {int} numberOfDepend - expenses.numberOfDepend - vidu: null
 * @Params {int} internalInstEC - internalInst.internalInstEC - vidu: 0
 * @Params {int} pcbInst - pcbDebt.pcbInst - vidu 0
 * @Params {int} pcbCreditCard - pcbDebt.pcbCreditCard - vidu 0
 * @Params {int} cic1Other - cic1.other vi du 0
 * @Params {int} cic1CreditCard - cic1.creditCard - vidu 0
 * @Params {int} longTermDebt - cic1.longTermDebt - vidu 0
 * @Params {int} mediumTermMortgageDebt - cic1.mediumTermMortgageDebt - vidu 0
 * @Params {int} mediumTermUnsecuredDebt - cic1.mediumTermUnsecuredDebt - vidu 0
 * @Params {int} shortTermDebt - cic1.shortTermDebt - vidu 0
 * @Params {int} cic2CreditCard - cic2.creditCard - vidu 0
 * @Params {int} longTermPrincipalDebt - cic1.longTermPrincipalDebt - vidu 0
 * @Params {int} mediumTermPrincipalDebt - cic1.mediumTermPrincipalDebt - vidu 0
 * @Params {int} shortTermPrincipalDebt - cic1.shortTermPrincipalDebt - vidu 0
 * @Params {int} longTermInterestDebt - cic1.longTermInterestDebt - vidu 0
 * @Params {int} mediumTermInterestDebt - cic1.mediumTermInterestDebt - vidu 0
 * @Params {int} shortTermInterestDebt - cic1.shortTermInterestDebt - vidu 0
*/
const kovDICalculation = async function ({ contractNumber, partnerCode, custId, addr, totalMonthlyExpenses, numberOfDepend, internalInstEC, income, pcbInst,
    pcbCreditCard, timeRegistration, cic1Other, cic1CreditCard, longTermDebt, mediumTermMortgageDebt, mediumTermUnsecuredDebt, shortTermDebt,
    cic2CreditCard, longTermPrincipalDebt, mediumTermPrincipalDebt, shortTermPrincipalDebt, longTermInterestDebt, mediumTermInterestDebt, shortTermInterestDebt, timeDuration }) {

    const config = global.config;
    let bodyRequest;
    try {
        bodyRequest = {
            contractNumber: contractNumber || null,
            custId: custId || null,
            partnerCode: partnerCode || PARTNER_CODE.KOV,
            diData: {
                other: {
                    timeRegistration: timeRegistration || 0,
                    timeDuration: timeDuration ? parseFloat(timeDuration) : 0
                },
                expenses: {
                    addr: addr || null,
                    totalMonthlyExpenses: totalMonthlyExpenses ? parseFloat(totalMonthlyExpenses) : 0,
                    numberOfDepend: numberOfDepend || null
                },
                internalInst: {
                    internalInstEC: internalInstEC ? parseFloat(internalInstEC) : 0
                },
                income: {
                    turnover1: income.turnover1 ? parseFloat(income.turnover1) : 0,
                    turnover2: income.turnover2 ? parseFloat(income.turnover2) : 0,
                    turnover3: income.turnover3 ? parseFloat(income.turnover3) : 0,
                    turnover4: income.turnover4 ? parseFloat(income.turnover4) : 0,
                    turnover5: income.turnover5 ? parseFloat(income.turnover5) : 0,
                    turnover6: income.turnover6 ? parseFloat(income.turnover6) : 0,
                    turnover7: income.turnover7 ? parseFloat(income.turnover7) : 0,
                    turnover8: income.turnover8 ? parseFloat(income.turnover8) : 0,
                    turnover9: income.turnover9 ? parseFloat(income.turnover9) : 0,
                    turnover10: income.turnover10 ? parseFloat(income.turnover10) : 0,
                    turnover11: income.turnover11 ? parseFloat(income.turnover11) : 0,
                    turnover12: income.turnover12 ? parseFloat(income.turnover12) : 0,
                },
                pcbDebt: {
                    pcbInst: pcbInst ? parseFloat(pcbInst) : 0,
                    pcbCreditCard: pcbCreditCard ? parseFloat(pcbCreditCard) : 0
                },
                cic1: {
                    other: cic1Other ? parseFloat(cic1Other) : 0,
                    creditCard: cic1CreditCard ? parseFloat(cic1CreditCard) : 0,
                    longTermDebt: longTermDebt ? parseFloat(longTermDebt) : 0,
                    mediumTermMortgageDebt: mediumTermMortgageDebt ? parseFloat(mediumTermMortgageDebt) : 0,
                    mediumTermUnsecuredDebt: mediumTermUnsecuredDebt ? parseFloat(mediumTermUnsecuredDebt) : 0,
                    shortTermDebt: shortTermDebt ? parseFloat(shortTermDebt) : 0
                },
                cic2: {
                    creditCard: cic2CreditCard ? parseFloat(cic2CreditCard) : 0,
                    longTermPrincipalDebt: longTermPrincipalDebt ? parseFloat(longTermPrincipalDebt) : 0,
                    mediumTermPrincipalDebt: mediumTermPrincipalDebt ? parseFloat(mediumTermPrincipalDebt) : 0,
                    shortTermPrincipalDebt: shortTermPrincipalDebt ? parseFloat(shortTermPrincipalDebt) : 0,
                    longTermInterestDebt: longTermInterestDebt ? parseFloat(longTermInterestDebt) : 0,
                    mediumTermInterestDebt: mediumTermInterestDebt ? parseFloat(mediumTermInterestDebt) : 0,
                    shortTermInterestDebt: shortTermInterestDebt ? parseFloat(shortTermInterestDebt) : 0
                }
            }
        }
        const diCalculationUrl = config.basic.decisionsV02[config.env] + config.data.deService.kovDICalculationV2;
        const headers = initHeaders('DECISION', 'DI');
        const response = await common.postApiV2(diCalculationUrl, bodyRequest, headers);
        loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.DE, STEP.CALCULATE_DI, bodyRequest, response);

        if (!response || !response.data || response.data.code != RESPONSE_CODE_DE.SUCCESS) return undefined;
        return response.data.data;
    } catch (err) {
        common.log(err.message, bodyRequest, 3);
        console.log(err);
        return undefined;
    }
}

const gwDICalculation = async ({ contractNumber }) => {
    try {
        const loanContractEntity = await loanContractRepo.getLoanContract(contractNumber);
        if (!loanContractEntity) {
            console.log(`gwDICalculation, contractNumber: ${contractNumber}: loan contract entity not found.`);
            return false;
        }
        const payloadDICalculation = await makePayloadKovDICalculation({ loanContractEntity });
        const dataDICalculation = await kovDICalculation(payloadDICalculation);
        const diBeforeCe = dataDICalculation.anuity ? parseFloat(dataDICalculation.anuity) : 0;
        await Promise.all([
            offerRepo.updateMainScore(contractNumber, 'di_before_ce', diBeforeCe),
            offerRepo.updateMainScore(contractNumber, 'net_income', diBeforeCe)
        ]);
        return true;
    } catch (error) {
        console.log(`EXCEPTION. gwDICalculation with contract: ${contractNumber}, error: ${error.message}`);
        console.log(error);
        return false;
    }
}

const makePayloadKovDICalculation = async ({ loanContractEntity, cicBody }) => {
    const income = await getIncomeKovCash(loanContractEntity.contract_number);
    // const timeRegistration = kovCashService.getBusinessDuration({ firstRegistrationDate: loanContractEntity.first_registration_date });
    const bodyDICalculation = {
        contractNumber: loanContractEntity.contract_number,
        partnerCode: loanContractEntity.partner_code,
        custId: loanContractEntity.cust_id,
        addr: loanContractEntity.province_cur || null,
        totalMonthlyExpenses: parseFloat(loanContractEntity.m_household_expenses) || null,
        numberOfDepend: loanContractEntity.num_of_dependants || null,
        internalInstEC: parseFloat(cicBody?.monthlyPaymentAtEc) || 0,
        income: income,
        pcbInst: parseFloat(cicBody?.pcbMonthlyPay) || 0,
        pcbCreditCard: parseFloat(cicBody?.pcbCardDebt) || 0,
        timeRegistration: loanContractEntity.time_duration,
        cic1Other: parseFloat(cicBody?.personDebt.otherDebt1) || 0,
        cic1CreditCard: parseFloat(cicBody?.personDebt.cardDebt) || 0,
        longTermDebt: parseFloat(cicBody?.personDebt.longTerm) || 0,
        mediumTermMortgageDebt: parseFloat(cicBody?.personDebt.mortgageMediumTerm) || 0,
        mediumTermUnsecuredDebt: parseFloat(cicBody?.personDebt.creditMediumTerm) || 0,
        shortTermDebt: parseFloat(cicBody?.personDebt.shortTerm) || 0,
        cic2CreditCard: parseFloat(cicBody?.legalDebt.cardDebt2) || 0,
        longTermPrincipalDebt: parseFloat(cicBody?.legalDebt.longTermPrincipalAndInterest) || 0,
        mediumTermPrincipalDebt: parseFloat(cicBody?.legalDebt.mediumTermPrincipalAndInterest) || 0,
        shortTermPrincipalDebt: parseFloat(cicBody?.legalDebt.shortTermPrincipalAndInterest) || 0,
        longTermInterestDebt: parseFloat(cicBody?.legalDebt.longTermInterest) || 0,
        mediumTermInterestDebt: parseFloat(cicBody?.legalDebt.mediumTermInterest) || 0,
        shortTermInterestDebt: parseFloat(cicBody?.legalDebt.shortTermInterest) || 0,
        timeDuration: parseFloat(loanContractEntity?.time_duration) || 0
    }
    return bodyDICalculation;
}

const getIncomeKovCash = async (contractNumber) => {
    let turnover = await turnoverRepo.getTurnOverV2(contractNumber);
    if (!turnover) return null;
    let income = {};
    for (const i of turnover) {
        switch (i.month_of_info) {
            case 1:
                income.turnover1 = i.value_of_month;
                break;
            case 2:
                income.turnover2 = i.value_of_month;
                break;
            case 3:
                income.turnover3 = i.value_of_month;
                break;
            case 4:
                income.turnover4 = i.value_of_month;
                break;
            case 5:
                income.turnover5 = i.value_of_month;
                break;
            case 6:
                income.turnover6 = i.value_of_month;
                break;
            case 7:
                income.turnover7 = i.value_of_month;
                break;
            case 8:
                income.turnover8 = i.value_of_month;
                break;
            case 9:
                income.turnover9 = i.value_of_month;
                break;
            case 10:
                income.turnover10 = i.value_of_month;
                break;
            case 11:
                income.turnover11 = i.value_of_month;
                break;
            case 12:
                income.turnover12 = i.value_of_month;
                break;
            default:
                break;
        }
    }
    return income;
}

const calculateDIAterCE = async ({ contractNumber, cicBody }) => {
    try {
        const loanContractEntity = await loanContractRepo.getLoanContract(contractNumber);
        if (!loanContractEntity) {
            console.log(`calculateDI afterCE, contractNumber: ${contractNumber}: loan contract entity not found.`);
            return 0;
        }
        const payloadDICalculation = await makePayloadKovDICalculation({ loanContractEntity, cicBody });
        const dataDICalculation = await kovDICalculation(payloadDICalculation);
        const diAfterCe = dataDICalculation.anuity ? parseFloat(dataDICalculation.anuity) : 0;
        return diAfterCe;
    } catch (error) {
        console.log(`EXCEPTION. calculateDI afterCE with contract: ${contractNumber}, error: ${error.message}`);
        console.log(error);
        return 0;
    }
}

const calculateOfferKovCash = async ({ contractDataJoinMainScore }) => {
    try {
        const productInfo = await productService.getProductInfoV2(contractDataJoinMainScore.product_code);
        let intRate = productInfo.productVar[0].intRate ? parseFloat(productInfo.productVar[0].intRate) : null;
        if (intRate) intRate = intRate / 100;
        const payloadOfferCalculation = await makePayloadKovMaxLoanCalculation({ loanContractEntity: contractDataJoinMainScore, diBeforeCE: contractDataJoinMainScore.di_before_ce, diAfterCE: contractDataJoinMainScore.di_after_ce, interestRate: intRate, tenor: contractDataJoinMainScore.request_tenor });
        const offerData = await calculatorMaxLoan(payloadOfferCalculation);
        const offer = offerData.offer ? parseFloat(offerData.offer) : 0;
        return offer;
    } catch (error) {
        console.log(`EXCEPTION. calculateOfferKovCash with contract: ${contractNumber}, error: ${error.message}`);
        console.log(error);
        return 0;
    }
}

const makePayloadKovMaxLoanCalculation = async ({ loanContractEntity, diBeforeCE, diAfterCE, interestRate, tenor }) => {
    const contractNumber = loanContractEntity.contract_number;
    const income = await getIncomeKovCash(contractNumber);
    const turnovers = { ...income };
    const legalStatus = loanContractEntity.first_registration_date ? 'Y' : 'N';
    const payloadKovMaxLoan = {
        contractNumber: contractNumber,
        partnerCode: loanContractEntity.partner_code || PARTNER_CODE.KOV,
        custId: loanContractEntity.cust_id || null,
        diBeforeCE: parseFloat(diBeforeCE) || 0, //di before ce
        diAfterCE: parseFloat(diAfterCE) || 0, //di after, 
        tenor: tenor,
        interestRate: interestRate, //lai suat
        turnovers: turnovers,
        businessDuration: loanContractEntity.bussiness_duration || 0, //thoi gian thuc te tham gia 
        legalStatus: legalStatus || "N", //tinh trang dang ky kinh doanh value Y/N
        // timeRegistration: loanContractEntity.time_duration || 12, //THOI GIAN THAM GIA GIAI PHAP MERCHANT
        registrationDate: loanContractEntity.first_registration_date || null, // datetime dang ky
        scheme: loanContractEntity.product_code,
        timeDuration: loanContractEntity.time_duration ? parseFloat(loanContractEntity.time_duration) : 0,
        requestAmount: loanContractEntity.request_amt ? parseFloat(loanContractEntity.request_amt) : 0,
        fundingFromEc: loanContractEntity.funding_from_ec ? parseFloat(loanContractEntity.funding_from_ec) : 0
    }
    return payloadKovMaxLoan;
}

const getSchemeFillMaxLoan = (productCode) => {
    if (productCode.includes("PLATINUM")) return "KOV_PLATINUM";
    if (productCode.includes("PREMIUM")) return "KOV_PREMIUM";
    if (productCode.includes("VIP")) return "KOV_VIP";
    return "KOV_STANDARD";
}

module.exports = {
    calculatorMaxLoan,
    kovDICalculation,
    gwDICalculation,
    getIncomeKovCash,
    calculateDIAterCE,
    calculateOfferKovCash,
    makePayloadKovMaxLoanCalculation
}