// const getData = require("../../utils/getData")
const loanContractRepo = require("../repositories/loan-contract-repo")
const helper = require("../utils/helper")
const { getKycByContract, updateEkycStatus } = require("../repositories/ekyc-repo")
const VARIABLE = require("../const/variables-const")
const loanDocRepo = require('../repositories/document')
const validator = require('../utils/validator')
const STATUS = require("../const/status-const")
const common = require("../utils/common")
const { CASE_STATUS } = require('../const/code-const.js');
const caseStatus = require('../const/caseStatus.js')
const CODE = require('../const/code-const');
const crmService = require("../utils/crmService")
const callbackService = require("./callback-service")
const { routing } = require("../services/workflow-service")
const { MANUAL_TASK_CODE } = require("../const/definition")
const { getValueCode_v2 } = require("../utils/masterdataService")
const { checkDedupCash } = require("./crm-service")
const { serviceEndpoint } = require("../const/config")

async function getKYCInfo(req, res) {
    try {
        const contractNumber = req.query.contractNumber
        // const contractType = req.query.contractType
        // const poolRead = req.poolRead
        // const taskId = req.query.taskId

        // if(contractType  === VARIABLE.PRODUCT_TYPE.CCN){
        //     return await getKYCDebtStructuringInfo(req, res);
        // }
        if (contractNumber == undefined) {
            return res.status(400).json({
                code: 0,
                msg: "contractNumber or taskId is missing."
            })
        }
        const data = await Promise.all([
            loanContractRepo.getAllContractData(contractNumber),
            getKycByContract(contractNumber)
        ])

        const contractData = data[0],
            ekycData = data[1];
        if (contractData == undefined) {
            return res.status(200).json({
                code: 0,
                msg: "invalid contract"
            })
        }
        // let vtPayInfo;
        // const listPartnerGetPersonal = [VARIABLE.PARTNER_CODE.VDS];
        // if (listPartnerGetPersonal.indexOf(contractData.partner_code) !== -1){
        //     vtPayInfo = await findOne({poolWrite: poolRead, af2_request_id: contractData.af2_request_id});
        // }
        const info = await Promise.all([
            getValueCode_v2(global.config, contractData.ward_cur, VARIABLE.MASTER_DATA.CODE_TYPE.WARD),
            getValueCode_v2(global.config, contractData.district_cur, VARIABLE.MASTER_DATA.CODE_TYPE.DISTRICT),
            getValueCode_v2(global.config, contractData.province_cur, VARIABLE.MASTER_DATA.CODE_TYPE.PROVINCE),
            getValueCode_v2(global.config, contractData.ward_per, VARIABLE.MASTER_DATA.CODE_TYPE.WARD),
            getValueCode_v2(global.config, contractData.district_per, VARIABLE.MASTER_DATA.CODE_TYPE.DISTRICT),
            getValueCode_v2(global.config, contractData.province_per, VARIABLE.MASTER_DATA.CODE_TYPE.PROVINCE),
            getValueCode_v2(global.config, contractData.id_issue_place, VARIABLE.MASTER_DATA.CODE_TYPE.ISSUE_PLACE_VN),
            {},
            {},
            {},
            {},
            {},
            getValueCode_v2(global.config, contractData.loan_purpose, VARIABLE.MASTER_DATA.CODE_TYPE.LOAN_PURPOSE),
            getValueCode_v2(global.config, contractData.reference_type_1, VARIABLE.MASTER_DATA.CODE_TYPE.TYPTEL),
            getValueCode_v2(global.config, contractData.reference_type_2, VARIABLE.MASTER_DATA.CODE_TYPE.TYPTEL),
            {},
        ]);

        const tem_ward = info[0], tem_district = info[1], tem_province = info[2], permanent_ward = info[3], permanent_district = info[4],
            permanent_province = info[5], issue_place = info[6], company_ward = info[7], company_district = info[8], company_province = info[9],
            job_type = info[10], job_title = info[11], loan_purpose = info[12], reference_1 = info[13], reference_2 = info[14], empl_type = info[15];

        const personalInfo = {
            customer_name: contractData.cust_full_name || '',
            date_of_birth: helper.formatDate(contractData.birth_date, 'YYYY-MM-DD'),
            identity_card: contractData.id_number,
            email: contractData.email || '',
            gender: contractData.gender == 'M' ? 'Male' : 'Female',
            issue_date: helper.formatDate(contractData.id_issue_dt, 'YYYY-MM-DD'),
            nationality: "VN",
            issue_place: issue_place || "",
            phone_number: contractData.phone_number1
        }

        const addressInfo = {
            tempAddress: {
                tem_detail_address: contractData.address_cur || '',
                tem_ward: tem_ward || contractData.tem_ward,
                tem_district: tem_district || contractData.tem_district,
                tem_province: tem_province || contractData.tem_province
            },
            perAddress: {
                permanent_detail_address: contractData.address_per || '',
                permanent_ward: permanent_ward || contractData.permanent_ward,
                permanent_district: permanent_district || contractData.permanent_district,
                permanent_province: permanent_province || contractData.permanent_province
            }
        }

        const otherContact = [
            {
                reference_1: reference_1 || contractData.reference_type_1,
                relative_reference_name_1: contractData.reference_name_1 || '',
                relative_reference_phone_1: contractData.reference_phone_1 || ''
            },
            {
                reference_2: reference_2 || contractData.reference_type_2,
                relative_reference_name_2: contractData.reference_name_2 || '',
                relative_reference_phone_2: contractData.reference_phone_2 || ''
            }
        ]

        const jobInfo = {
            empl_type: empl_type || '',
            job_type: job_type || '',
            job_title: job_title || '',
            company_name: contractData.company_name || '',
            street: contractData.street || '',
            company_ward: company_ward || contractData.company_ward,
            company_district: company_district || contractData.company_district,
            company_province: company_province || contractData.company_province,
            monthly_income: contractData.monthly_income || '',
            other_income: contractData.other_income || '',
            monthly_expense: contractData.monthly_expenses || ''
        }

        const loanInfo = {
            loan_amount: contractData.request_amt || '',
            loan_tenor: contractData.request_tenor || '',
            loan_purpose: loan_purpose || '',
            product_code: contractData.product_code || ''
        }

        const otherInfo = {
            leadInfo: {
                PECode: contractData.pe_code || ""
            },
            goodDetail: {
                downPayment: "",
                totalPrice: "",
                detail: [
                    /*{
                        name : "dien thoai",
                        brand : "apple",
                        model : "hehe",
                        price : 1232
                    }*/
                ]
            },
            merchantInfo: {
                merchant: "",
                code: "",
                storeName: "",
                address: ""
            },
            // vtPayInfo : {
            //     customerName: !helper.isNullOrEmpty(vtPayInfo) ? vtPayInfo.customer_name : "",
            //     gender: !helper.isNullOrEmpty(vtPayInfo) ? vtPayInfo.gender : "",
            //     identityCard: !helper.isNullOrEmpty(vtPayInfo) ? vtPayInfo.identity_card : "",
            //     issueDate: !helper.isNullOrEmpty(vtPayInfo) ? dateHelper.formatDate(vtPayInfo.issue_date, "YYYY-MM-DD") : "",
            //     dateOfBirth: !helper.isNullOrEmpty(vtPayInfo) ? dateHelper.formatDate(vtPayInfo.date_of_birth, "YYYY-MM-DD") : "",
            //     issuePlace: !helper.isNullOrEmpty(vtPayInfo) ? await helper.getValueCode(vtPayInfo.issue_place, VARIABLE.MASTER_DATA.CODE_TYPE.ISSUE_PLACE_VN) : "",
            //     nationality: ""
            // }
        }

        // if (isPartnerEasyLuong(contractData.partner_code)) {
        //     otherInfo.vtPayInfo = await getCustomerInfoPortalFromCrm({loanContractEntity: contractData});
        // }

        let documentData = await loanDocRepo.getAllDocumentByContractNumber(contractNumber)
        if (documentData == undefined) {
            documentData = []
        }
        //xoa dup PIC,SPIC,PID,SPID 
        let documentRemoveDup = [];
        // documentRemoveDup.push(documentData[])
        for (let i = 0; i < documentData.length; i++) {
            if (['PID', 'PIC', 'SPID', 'SPIC'].includes(documentData[i].doc_type)) {
                let docTypeTemp = documentData[i].doc_type;
                if (documentRemoveDup.length == 0) {
                    documentRemoveDup.push(documentData[i]);
                } else {
                    let hasPicOrSpic = false;
                    let hasPidOrSpid = false;
                    for (let j = 0; j < documentRemoveDup.length; j++) {
                        if (['PIC', 'SPIC'].includes(documentRemoveDup[j].doc_type)) {
                            hasPicOrSpic = true;
                        }
                        if (['PID', 'SPID'].includes(documentRemoveDup[j].doc_type)) {
                            hasPidOrSpid = true;
                        }
                    }
                    if (['PIC', 'SPIC'].includes(docTypeTemp) && !hasPicOrSpic) {
                        documentRemoveDup.push(documentData[i]);
                    }
                    if (['PID', 'SPID'].includes(docTypeTemp) && !hasPidOrSpid) {
                        documentRemoveDup.push(documentData[i]);
                    }
                }
            }
        }
        console.log("Array Doc List: " + JSON.stringify(documentRemoveDup));
        const document = documentRemoveDup.map(x => {
            if (['PID', 'PIC', 'SPID', 'SPIC'].includes(x.doc_type)) {
                let docSource = x.doc_source;
                if (docSource && docSource !== VARIABLE.DOCUMENT_SOURCE.LIVE) {
                    docSource = "NO LIVE"
                }
                return {
                    docName: (x.doc_type == 'SPIC' || x.doc_type == 'PIC') ? "Image Selfie" : "ID Card/National ID",
                    docDesc: "",
                    docId: x.doc_id,
                    docSource: (docSource) ? docSource : ""
                }
            }
            else {
                return null
            }
        }).filter(x => x !== null)

        let ekyc_message = ekycData !== null ? ekycData.msg_error || "" : "";
        const ekyc = {
            service: ekycData !== null ? (ekycData.service_error || "") : "",
            errorMsg: ekyc_message,
            document: document,
            comment: "",
            idQrCode: contractData.id_qr_code || ""
        }

        return res.status(200).json({
            code: 1,
            msg: "get ekyc data successfully.",
            data: {
                personalInfo,
                addressInfo,
                otherContact,
                jobInfo,
                loanInfo,
                otherInfo,
                ekyc
            }
        })
    } catch (err) {
        console.log(`getKYCInfo error: ${err.message}`);
        console.log(err);
        return res.status(400).json({
            code: 0,
            msg: "get KYC info error: " + err.message
        })
    }
}
const updateStatus = async function (req, res) {
    const serviceName = 'EKYC';
    try {
        const poolWrite = global.poolWrite;
        // const config = req.config;
        const data = req.body;
        const contract_number = data.contract_number;
        // const contractType = data.contractType;
        const loanData = await loanContractRepo.getLoanContract(contract_number);
        const act = parseInt(data.act);
        // const step = VARIABLE.STEP.MANUAL_KYC;
        let workflowBody = {
            contract_number: contract_number,
            partner_code: loanData?.partner_code,
            product_code: loanData?.product_code
        }

        let message = await validInput(poolWrite, data)

        if (message != undefined && message !== '') {
            common.resSuccess(res, message, CODE.RESPONSE_CODE.ERROR_VALID)
        } else {
            // const contract = await loanContractRepo.getContractData(poolWrite, contract_number);
            //update manual kyc
            // await updateEkycStatus(poolWrite, data);
            // let step_status, loan_status, caseActionKyc;

            // switch (act) {
            //     case 0:
            //         step_status = STATUS.STEP_STATUS.FAIL_MANUAL_KYC;
            //         break;
            //     case 1:
            //         step_status = STATUS.STEP_STATUS.APPROVED;
            //         break;
            // }
            // statusHistRepo.saveContractStatusHst(poolWrite, contract_number, service_name, step, step_status);
            //Go next step to check loan_contract   
            if (act === 1) {
                console.log(`[${serviceName}] contract ${contract_number} is APPROVED`)
                // caseActionKyc = CASE_STATUS.IN_MANUAL_KYC.ACTION.COMPLETED_KYC;
                loanContractRepo.updateContractStatus(CASE_STATUS.CHECKED_EKYC.ACTION.PASSED_EKYC, contract_number);
                const isPassDeDup = await checkDedupCash(contract_number);
                if (isPassDeDup) {
                    workflowBody.currentTask = MANUAL_TASK_CODE.EKYC_MANUAL.EKYC_APPROVE;
                    routing(workflowBody);
                }
            } else {
                await Promise.all([
                    loanContractRepo.updateContractStatus(CASE_STATUS.CHECKED_EKYC.ACTION.CANCELLED, contract_number),
                    callbackService.callbackPartner(contract_number, data.chanel, CASE_STATUS.CHECKED_EKYC.ACTION.REFUSED),
                    crmService.removeContract(global.config, contract_number)
                ]);
                workflowBody.currentTask = MANUAL_TASK_CODE.EKYC_MANUAL.EKYC_CANCEL;
                routing(workflowBody);
            }
            return res.status(200).json({
                response_code: 1,
                response_message: 'sucessfull'
            });
        }
    } catch (error) {
        common.log(`[${serviceName}] Error while update kyc decision: ${error.message}`)
        console.log(error);
        // common.resError(res, error);
    }
}
const validInput = async function (poolRead, input) {
    let missingMessage = checkMissingParams(input)
    if (missingMessage != '')
        return missingMessage;

    //valid act
    let actList = [0, 1];
    let act = parseInt(input.act);
    if (actList.indexOf(act) === -1)
        return 'act is not valid'

    //check status in MANUAL_DEDUP
    let status = await loanContractRepo.getLoanContract(input.contract_number);
    if (status === undefined)
        return `contract_number: ${input.contract_number} does not exists`

    let status_code = status.status;
    if (status_code !== caseStatus.STATUS.IN_MANUAL_KYC)
        return `contract_number: ${input.contract_number} is not IN_MANUAL_KYC`

    return '';
}
const checkMissingParams = (input) => {
    let mandatoryFields = ['contract_number', 'act'];
    let missingMessage = validator.checkMissingAndEmpty(mandatoryFields, input);

    if (missingMessage !== '')
        return missingMessage;

    return '';
}

const editKyc = async function (req, res) {
    try {
        const config = req.config;
        const body = req.body;
        const contractNumber = body.contractNumber;
        const loanData = await loanContractRepo.getLoanContract(contractNumber);
        const phone = loanData?.phone_number1;
        const updateList = body.update ?? [];
        const promiseList = [];
        let formUpdateApp = {};
        updateList.map(x => {
            if(!['comment','nationality'].includes(x.updatedField)){
                let updateField, bank_account_owner;
                switch (x.updatedField) {
                    case 'customer_name':
                        updateField = 'cust_full_name';
                        promiseList.push(loanContractRepo.updateFieldLoanContract(contractNumber, `bank_account_owner`, x.newValue ));
                        break;
                    case 'date_of_birth':
                        updateField = 'birth_date';
                        break;
                    case 'issue_date':
                        updateField = 'id_issue_dt';
                        break;
                    case 'identity_card':
                        updateField = 'id_number';
                        break;
                    default:
                        updateField = x.updatedField;
                        break;
                }
                promiseList.push(loanContractRepo.updateFieldLoanContract(contractNumber, `${updateField}`, x.newValue ));
            }
            formUpdateApp[x.updatedField] = x.newValue;
        })
        formUpdateApp['phone'] = phone || '';
        let urlUpdateInfoApp = config.basic.mcMobileBe[config.env] + serviceEndpoint.MCA.updateInfoApp;
        common.putAPIV2(urlUpdateInfoApp, formUpdateApp);
        Promise.all([promiseList]);
        return res.status(200).json({
            code: 0,
            message: 'Update contract info success'
        });
    } catch (error) {
        common.log(`Error while edit kyc decision: ${error.message}`)
        // console.log(error);
        // common.resError(res, error);
    }
}

module.exports = {
    updateStatus,
    getKYCInfo,
    editKyc
}