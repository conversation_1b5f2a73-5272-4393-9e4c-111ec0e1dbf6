const common = require("../utils/common")
const {getValueCode_v3, getValueCodeMasterdataV2} = require("../utils/masterdataService")
const {SYS_CHANEL,DATE_FORMAT,SERVICE_NAME, PARTNER_CODE, CHANNEL, MisaStep, TASK_FLOW, KUNN_WORKFLOW} = require("../const/definition")
const {STATUS, CALLBACK_STAUS, SME_CONTENT_DISBURSE, KUNN_STEP, ActionAuditCaseStatus, WORKFLOW_STAGE} = require("../const/caseStatus")
const dateHelper = require("../utils/dateHelper")
const productService = require("../utils/productService")
const crmService = require("../utils/crmService")
const callbackService = require("../services/callback-service")
const loanContractRepo = require("../repositories/loan-contract-repo")
const loggingRepo = require("../repositories/logging-repo")
const smsService = require("../utils/smsService")
const {CONTRACT_TYPE} = require("../const/definition")
const utils = require("../utils/helper")
const { serviceEndpoint } = require("../const/config")
const { sendNotification } = require("./notification-service")
const { getProductByCodeApi } = require("../apis/product-api")
const { getKunnData, update } = require("../repositories/kunn-repo")
const { createBizziKunn, lockLimitApi, createDebtApiV3 } = require("../apis/lms-api")
const { getInvoicesByKunn } = require("../repositories/invoice-repo")
const actionAuditService = require("../services/action-audit");
const { CASE_STATUS } = require("../const/code-const");
const disbursementInfoRepo = require("../repositories/kunn-disbursement-info-repo");

const createLmsBody = async(contractNumber) => {
    const contract = await loanContractRepo.getLoanContract(contractNumber)
    if(!contract) {
        return false;
    }

    let product_code = contract.product_code;

    const data = await Promise.all([
        productService.getProductInfoV2(product_code),
        getValueCode_v3(contract.bank_code, 'BANK')
    ]);
    let prd = data[0], bankName = data[1];

    let loan_amount = contract.approval_amt; 
    let today = dateHelper.LMS_DATE();

    let body = {
        "losType": SYS_CHANEL.MCC,
        "custId": contract.cust_id,
        "contractNumber": contractNumber,
        "productCode": product_code,
        "status": "SIG",
        "statusDate": today,
        "intRates": prd.rate,
        "channel": contract.channel,
        "approvalAmt": loan_amount,
        "graceDayNumber": prd.graceDayNumber || 5,
        "productIdCtlg": prd.prdctId,
        "approvalDate": today,
        "signingInProgressDate": today,
        "signatureDate": today,
        "insuranceType": "",
        "tenor": contract.approval_tenor,
        "startDate": today,
        "endDate": "",
        "amort": {
            "amtAmort": loan_amount,
            "numDayOfYear": dateHelper.numdayOfYear(),
            "amortType": "M"
        },
        "fees": productService.getFees(prd),
        "insurances": "",
        "firstTenor": [{"startDay":2,"endDay":5,"deltaMonth":1},{"startDay":3,"endDay":6,"deltaMonth":1},{"startDay":4,"endDay":7,"deltaMonth":1},{"startDay":5,"endDay":8,"deltaMonth":1},{"startDay":27,"endDay":5,"deltaMonth":2},{"startDay":28,"endDay":5,"deltaMonth":2},{"startDay":29,"endDay":6,"deltaMonth":2},{"startDay":30,"endDay":6,"deltaMonth":2},{"startDay":31,"endDay":7,"deltaMonth":2},{"startDay":1,"endDay":7,"deltaMonth":1}],
        "ccycd": "VND",
        "recieverName": contract.bank_account_owner,
        "partnerCode": contract.partner_code,
        "customerName": contract.cust_full_name,
        "partnerTranNo": "",
        "apiCode": "",
        "tranDesc": "",
        "comments": "",
        "comment1": "",
        "comment2": "",
        "disbursementType": "2",
        "phoneNumber": contract.phone_number1,
        "identityCardId": contract.id_number,
        "fullName": contract.cust_full_name,
        "numDayOfYear": dateHelper.numdayOfYear(),
        "bankCode": contract.bank_code,
        "bankName": bankName,
        "branchCode": "",
        "bankAccount": contract.bank_account,
        "createdUser": "system",
        "ownerId": 1,
        "issuedDate": dateHelper.formatDate(contract.id_issue_dt, DATE_FORMAT.DDMMYYYY),
        "issuedPlace": contract.issue_place,
    }
    return body;
}

async function createLMSMerchantCL(contractNumber) {
    try {
        const contractData = await loanContractRepo.getLoanContract(contractNumber)

        let lmsBody = {
            "productCode":contractData.product_code,
            "amount": contractData.approval_amt,
            "custId": contractData.cust_id,
            "contractNumber":contractNumber,
            "tenor": contractData.approval_tenor,
            "periodicity": 1,
            "graceDayNumber":15,
            "billDay": contractData.bill_day || 5,
            "irCharge":[
                {
                    "irCode":"IR001",
                    "irName":"Lãi xuất theo kỳ",
                    "irType":1,
                    "irValue":contractData.approval_int_rate||contractData.request_int_rate
                },
                {
                    "irCode":"IR002",
                    "irName":"Lãi xuất gốc quá hạn",
                    "irType":2,
                    "irValue":contractData.approval_int_rate||contractData.request_int_rate
                },
                {
                    "irCode":"IR003",
                    "irName":"Lãi xuất lãi quá hạn",
                    "irType":3,
                    "irValue":contractData.approval_int_rate||contractData.request_int_rate
                }
            ],
            "partnerCode": contractData.partner_code,
            "contractType": contractData.contract_type,
            "channel": contractData.channel
        }

        let lmsMcUrl = global.config.basic.lmsMc[config.env] + global.config.data.lms.createService;
        const response = await common.postApiV2(lmsMcUrl,lmsBody)
        await loggingRepo.saveStepLog(
            contractNumber,
            SERVICE_NAME.LMS,
            TASK_FLOW.ACTIVE_CREDIT_LIMIT,
            lmsBody,
            JSON.stringify(response?.data || {})
        );
        return response;
    }
    catch(err) {
        console.log(err)
        return false
    }
}

const inactiveLMSMerchantCL = async (contractNumber) => {
    try {
        const response = await lockLimitApi(contractNumber);
        await loggingRepo.saveStepLog(
            contractNumber,
            SERVICE_NAME.LMS,
            TASK_FLOW.INACTIVE_CREDIT_LIMIT,
            { contractNumber },
            JSON.stringify(response || {})
        );
        return response;
    } catch (error) {
        console.log(`[LMS][inactiveLMSMerchantCL] ${contractNumber} | Error: ${error.message}`);
        return false;
    }
};

const createCashLoanAccount = async function(contractNumber){
    try{
        const config = global.config

        let lmsCreateLoanUrl = config.basic.lmsCashloan[config.env] + config.data.lms.lmsCashLoanCreateLoanAccountUrl;
        let body = await createLmsBody(contractNumber);
        common.bodyLog(SERVICE_NAME.LMS,contractNumber,body)
        const result = await common.postApiV2(lmsCreateLoanUrl, body)
        await loanContractRepo.updateContractStatus(STATUS.SIGNED_TO_BE_DISBURED,contractNumber)
        common.serviceLog(SERVICE_NAME.LMS,contractNumber,result)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.LMS,SERVICE_NAME.CREATE_LOAN_ACCOUNT,body,result)
        if(result.data.code != 200){
            common.log(`Khoi tao khoan vay LMS khong thanh cong`,contractNumber);
            return false
        }
        return true
    }
    catch(error){
        console.log(error)
        common.log(`Error khoi tao khoan vay LMS: ${error.message}`,contractNumber)
        return false
    }
}

async function updateStatus(req,res) {
    try {
        const config = global.config
        const body = req.body
        const {status,contract_number} = body
        const loanContract = await loanContractRepo.getLoanContract(contract_number)
        if(status == 'ACT') {
            loanContractRepo.updateActiveContract(contract_number,STATUS.ACTIVATED,body.start_date,body.end_date)
            crmService.activeContract(config,contract_number)
            callbackService.callbackPartner(contract_number,loanContract.partner_code,CALLBACK_STAUS.ACTIVE)
        }
        else if(status == 'ANN') {
            loanContractRepo.updateContractStatus(STATUS.CANCELLED,contract_number)
            crmService.removeContract(config,contract_number)
            callbackService.callbackPartner(contract_number,loanContract.partner_code,CALLBACK_STAUS.CANCELLED)
        }
        else if(status == 'TER') {
            loanContractRepo.updateContractStatus(STATUS.TERMINATED,contract_number)
            crmService.terminateContract(config,contract_number)
            callbackService.callbackPartner(contract_number,loanContract.partner_code,CALLBACK_STAUS.TERMINATED)
        }
        else if (status == 'ECT') {
            loanContractRepo.updateContractStatus(STATUS.TERMINATION_IN_PROGRESS,contract_number)
        }

        return res.status(200).json({
            code : 0,
            message : 'Update contract status success',
            contractNumber : contract_number
        })
    }
    catch(err) {
        common.log(`LMS callback error : ${err.message}`,req.body.contract_number)
        return res.status(500).json({
            code : -1,
            msg : `INTERNAL SERVER ERROR : ${err.message}`
        })
    }
}

async function createLMS(contractNumber,contractType,phoneNumber,partnerCode,chanel=CHANNEL.MC) {
    try {
        const config = global.config
		let msg = config.data.smsService.ActivateHMMsg
		msg = msg.replace("contractNumber", contractNumber)
		const smsUrl = config.data.smsService.sendSMS
        const isSuperApp = chanel != CHANNEL.SMA ? true : false
        if (contractType == CONTRACT_TYPE.CREDIT_LINE || (partnerCode===PARTNER_CODE.MISA && contractType === CONTRACT_TYPE.CASH_LOAN)) {
            const createLmsResult = await createLMSMerchantCL(contractNumber)
            if(createLmsResult?.data?.code == 0) {
                await loanContractRepo.updateActiveContract(contractNumber,STATUS.ACTIVATED,createLmsResult.data.data[0].startDate,createLmsResult.data.data[0].endDate)
                crmService.updateActiveCrm(global.config,contractNumber)
                if(!utils.isNullOrEmpty(phoneNumber)) {
                    smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumber)       
                }
                callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.ACTIVE)   
                //send noti appMC
                if ( partnerCode == PARTNER_CODE.MCAPP && !isSuperApp) {
                    const body = {
                        phoneNumber: phoneNumber,
                        title: 'Hạn mức đã được duyệt!',
                        message: `Hợp đồng hạn mức ${contractNumber} của bạn đã được phê duyệt`, 
                        value: {
                            contractNumber: contractNumber
                        }
                    };
                    const endPoint = serviceEndpoint.NOTIFICATION.LIMIT_APPROVE;
                    sendNotification( body, endPoint, config );    
                }
            }
        }else if(contractType == CONTRACT_TYPE.CASH_LOAN) {
            return await createCashLoanAccount(contractNumber)
        }
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function getInstallmentKunn(kunnNumber) {
    try {
        const installmentUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.getInstallment}${kunnNumber}`
        const data = await common.getApiV2(installmentUrl)
        return data?.data?.data
    }
    catch(err) {
        console.log(err)
        return false
    }
}

const misaCreateLms = async (contractNumber, contractType, phoneNumber, partnerCode, chanel = CHANNEL.MC) => {
    try {
        // const config = global.config
        // let msg = config.data.smsService.ActivateHMMsg
        // msg = msg.replace("contractNumber", contractNumber)
        // const smsUrl = config.data.smsService.sendSMS
        // const isSuperApp = chanel != CHANNEL.SMA ? true : false
        if (contractType == CONTRACT_TYPE.CREDIT_LINE || (partnerCode === PARTNER_CODE.MISA && contractType === CONTRACT_TYPE.CASH_LOAN)) {
            const createLmsResult = await createLMSMerchantCL(contractNumber)
            if (createLmsResult?.data?.code == 0) {
                await loanContractRepo.updateActiveContract(contractNumber, STATUS.ACTIVATED, createLmsResult.data.data[0].startDate, createLmsResult.data.data[0].endDate)
                crmService.updateActiveCrm(global.config, contractNumber);
                await loggingRepo.saveWorkflow(MisaStep.SIGNATURE, STATUS.ACTIVATED, contractNumber, 'system')
                // if(!utils.isNullOrEmpty(phoneNumber)) {
                //     smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumber)       
                // }
                // callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.ACTIVE)   
                //send noti appMC
                // if ( partnerCode == PARTNER_CODE.MCAPP && !isSuperApp) {
                //     const body = {
                //         phoneNumber: phoneNumber,
                //         title: 'Hạn mức đã được duyệt!',
                //         message: `Hợp đồng hạn mức ${contractNumber} của bạn đã được phê duyệt`, 
                //         value: {
                //             contractNumber: contractNumber
                //         }
                //     };
                //     const endPoint = serviceEndpoint.NOTIFICATION.LIMIT_APPROVE;
                //     sendNotification( body, endPoint, config );    
                // }
            }
        } else if (contractType == CONTRACT_TYPE.CASH_LOAN) {
            return await createCashLoanAccount(contractNumber)
        }
    } catch (err) {
        console.log(err)
        return false
    }
}

const misaRefinanceLms = async (contractNumber, oldContractNumber, contractType, phoneNumber, partnerCode, chanel = CHANNEL.MC) => {
    try {
        // const config = global.config
        // let msg = config.data.smsService.ActivateHMMsg
        // msg = msg.replace("contractNumber", contractNumber)
        // const smsUrl = config.data.smsService.sendSMS
        // const isSuperApp = chanel != CHANNEL.SMA ? true : false
        if (contractType == CONTRACT_TYPE.CREDIT_LINE && partnerCode === PARTNER_CODE.MISA) {
            // INACTIVATE OLD CONTRACT
            const inactiveLmsResult = await inactiveLMSMerchantCL(oldContractNumber)
            if (inactiveLmsResult?.code == 0) {
                await loanContractRepo.updateInactiveContract(oldContractNumber);
                crmService.updateInactiveCrm(global.config, oldContractNumber);
                actionAuditService.saveCaseHistoryActionAudit(
                    oldContractNumber,
                    ActionAuditCaseStatus.INACTIVATED.STEP_CODE,
                    ActionAuditCaseStatus.INACTIVATED.ACTION.INACTIVATED,
                    oldContractNumber,
                    'system',
                    'Success'
                );
                await loggingRepo.saveWorkflow(MisaStep.SIGNATURE, STATUS.INACTIVATED, oldContractNumber, 'system')
            }
            
            // ACTIVE CONTRACT
            const createLmsResult = await createLMSMerchantCL(contractNumber)
            if (createLmsResult?.data?.code == 0) {
                await loanContractRepo.updateActiveContract(contractNumber, STATUS.ACTIVATED, createLmsResult.data.data[0].startDate, createLmsResult.data.data[0].endDate)
                crmService.updateActiveCrm(global.config, contractNumber);
                await loggingRepo.saveWorkflow(MisaStep.SIGNATURE, STATUS.ACTIVATED, contractNumber, 'system')
                // if(!utils.isNullOrEmpty(phoneNumber)) {
                //     smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumber)       
                // }
                // callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.ACTIVE)   
                //send noti appMC
                // if ( partnerCode == PARTNER_CODE.MCAPP && !isSuperApp) {
                //     const body = {
                //         phoneNumber: phoneNumber,
                //         title: 'Hạn mức đã được duyệt!',
                //         message: `Hợp đồng hạn mức ${contractNumber} của bạn đã được phê duyệt`, 
                //         value: {
                //             contractNumber: contractNumber
                //         }
                //     };
                //     const endPoint = serviceEndpoint.NOTIFICATION.LIMIT_APPROVE;
                //     sendNotification( body, endPoint, config );    
                // }
            }
        }
    } catch (err) {
        console.log(err)
        return false
    }
}

const calculateInvoicePaymentDateBizz = (invoices, extendInvoicePaymentDate = 0, extendMaxEndDate = 0) => {
    const toDay = dateHelper.PG_DATE_TODAY();
    const maxEndDate = dateHelper.addDays(toDay, extendMaxEndDate);
    let invoicePaymentDates = invoices.map(invoice => {
        let paymentDate = dateHelper.formatDate(invoice.payment_date, DATE_FORMAT.DB_FORMAT);
        return dateHelper.addDays(paymentDate, extendInvoicePaymentDate);
    });
    const minDate = invoicePaymentDates.reduce((min, date) => {
        return dateHelper.isBeforeDate(date, min) ? date : min;
    }, maxEndDate);
    return dateHelper.formatDate(minDate, DATE_FORMAT.DB_FORMAT);
}

const createLmsBizzKunnBody = async (kunnId) => {
    const kunnData = await getKunnData(kunnId);
    const [productInfo, bankInfo, invoices] = await Promise.all([
        getProductByCodeApi(kunnData.kunn_code),
        getValueCodeMasterdataV2(kunnData.bank_code, "BANK"),
        getInvoicesByKunn(kunnId)
    ]);
    const extendInvoicePaymentDate = productInfo?.factoringTermDays;
    const extendMaxEndDate = productInfo?.maxDurationDay;
    const minPaymentDate = calculateInvoicePaymentDateBizz(invoices, extendInvoicePaymentDate, extendMaxEndDate);
    const advanceRate = productInfo?.advanceRate ? Number(productInfo.advanceRate) : 0;

    return {
        contractNumber: kunnData.contract_number,
        debtAckContractNumber: kunnId,
        productCode: kunnData.kunn_code,
        amount: Number(kunnData.with_draw_amount) || 0,
        tenor: kunnData.tenor || 1,
        fee: productInfo?.fee,
        periodicity: 1,
        partnerCode: kunnData.partner_code,
        beneficiaryName: kunnData.beneficiary_name,
        bankCode: kunnData.bank_code,
        bankAccount: kunnData.bank_account,
        branchCode: kunnData.bank_branch_code,
        partnerTranNo: kunnData.partner_code,
        debtToleranceAmt: productInfo?.debtToleranceAmt || 0,
        suspendAccountHoldingDay: productInfo?.suspendAccountHoldingDay || 0,
        endDate: minPaymentDate,
        approvalDate: dateHelper.formatDate(kunnData.approve_signing_date, DATE_FORMAT.YYYYMMDD_HHmmss),
        signingInProgressDate: dateHelper.formatDate(kunnData.approve_signing_date, DATE_FORMAT.YYYYMMDD_HHmmss),
        signatureDate: dateHelper.formatDate(kunnData.signature_date, DATE_FORMAT.YYYYMMDD_HHmmss),
        channel: kunnData.partner_code,
        orders: invoices.map(invoice => ({
            orderIndex: invoice.invoice_order,
            orderNumber: invoice.invoice_number,
            prinAmt: invoice.invoice_amount * (advanceRate / 100),
            orderAmt: invoice.invoice_amount,
            paymentDate: invoice.payment_date

        })),
        graceDayNumber: 0,
        email: "",
        ccycd: "VND",
        contractType: "CREDITLINE",
        phoneNumber: "",
        bankName: bankInfo?.nameVn
    }
}

const createLmsBizzKunn = async (kunnId) => {
    try {
        const createDebtPayload = await createLmsBizzKunnBody(kunnId);
        const lmsRs = await createBizziKunn(kunnId, createDebtPayload);

        if (lmsRs?.code === 0) {
            actionAuditService.saveCaseHistoryActionAudit(kunnId, CASE_STATUS.SIGNED_TO_BE_DISBURED.STEP_CODE, CASE_STATUS.SIGNED_TO_BE_DISBURED.ACTION.DISBURSEMENT_PROCESSING, kunnId);
            await update(kunnId, {
                status: STATUS.SIGNED_TO_BE_DISBURED,
                step: KUNN_STEP.SIGNED_TO_BE_DISBURED,
            });
            await loggingRepo.saveWorkflow(
                WORKFLOW_STAGE.DISBURSEMENT,
                STATUS.SIGNED_TO_BE_DISBURED,
                kunnId,
                'system',
                kunnId
            );
        } else {
            throw new Error(`LMS ERROR: ${lmsRs?.message}`);
        }
    } catch (error) {
        console.log(`[BIZZ][KUNN][V2][createKunnDebt] kunn ${kunnId}, error ${error}`);
    }
};

const createActiveCreditLimit = async ({ contractNumber }) => {
    try {
        const loan = await loanContractRepo.getLoanContract(contractNumber);
        if (!loan?.id) {
            console.log(`ERROR | ${contractNumber}| ${new Date()} | createActiveCreditLimit | loan not found`);
            return false;
        }
        const {
            contract_type: contractType,
            partner_code: partnerCode
        } = loan;
        // const config = global.config
        // let msg = config.data.smsService.ActivateHMMsg
        // msg = msg.replace("contractNumber", contractNumber)
        if (contractType == CONTRACT_TYPE.CREDIT_LINE || (partnerCode === PARTNER_CODE.MISA && contractType === CONTRACT_TYPE.CASH_LOAN)) {
            const createLmsResult = await createLMSMerchantCL(contractNumber)
            if (createLmsResult?.data?.code != 0) {
                console.log(`ERROR | ${contractNumber}| ${new Date()} | createActiveCreditLimit | createLmsResult error: ${JSON.stringify(createLmsResult)}`);
            }
            await loanContractRepo.updateActiveContract(contractNumber, STATUS.ACTIVATED, createLmsResult.data.data[0].startDate, createLmsResult.data.data[0].endDate)
            await callbackService.callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.ACTIVE)
            crmService.updateActiveCrm(global.config, contractNumber);
            await loggingRepo.saveWorkflow(TASK_FLOW.ACTIVE_CREDIT_LIMIT, STATUS.ACTIVATED, contractNumber, 'system')
            actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.ACTIVATED_CREDIT_LIMIT.STEP_CODE, CASE_STATUS.ACTIVATED_CREDIT_LIMIT.ACTION.ACTIVATED, contractNumber);
            return true;
        } else if (contractType == CONTRACT_TYPE.CASH_LOAN) {
            return await createCashLoanAccount(contractNumber)
        }
        return false;
    } catch (err) {
        console.log(`ERROR | ${contractNumber}| ${new Date()} | createActiveCreditLimit | ${err.message}`);
        return false
    }
}

const createDebtV3 = async (kunnId) => {
    try {
        const kunn = await getKunnData(kunnId);
        const [loanContract, disbursements] = await Promise.all([
            loanContractRepo.getLoanContract(kunn.contract_number),
            disbursementInfoRepo.findByKunnId(kunnId)
        ]);
        const disbursementItems = disbursements.map((disbursement) => ({
          accountName: disbursement.account_name,
          beneficiaryName: disbursement.beneficiary,
          bankCode: disbursement.bank_code,
          bankName: disbursement.bank_name,
          bankAccount: disbursement.bank_account,
          disbursedAmount: disbursement.amount,
          content: disbursement.transfer_content
        }));
        const payload = {
          contractNumber: loanContract.contract_number,
          debtAckContractNumber: kunnId,
          productCode: loanContract.product_code,
          amount: kunn.with_draw_amount,
          tenor: kunn.tenor,
          periodicity: 1,
          partnerCode: loanContract.partner_code,
          channel: loanContract.channel,
          graceDayNumber: 0,
          billDay: kunn.bill_day,
          email: loanContract.email,
          ccycd: "VND",
          contractType: loanContract.contract_type,
          phoneNumber: loanContract.phone_number,
          bankInfo: disbursementItems,
        };
        const lmsRs = await createDebtApiV3(kunnId, payload);
        if (lmsRs?.code === 0) {
          actionAuditService.saveCaseHistoryActionAudit(
            kunnId,
            CASE_STATUS.SIGNED_TO_BE_DISBURED.STEP_CODE,
            CASE_STATUS.SIGNED_TO_BE_DISBURED.ACTION.DISBURSEMENT_PROCESSING,
            kunnId
          );
          await update(kunnId, {
            status: STATUS.SIGNED_TO_BE_DISBURED,
            step: KUNN_STEP.SIGNED_TO_BE_DISBURED,
          });
          await loggingRepo.saveWorkflow(
            WORKFLOW_STAGE.DISBURSEMENT,
            STATUS.SIGNED_TO_BE_DISBURED,
            kunnId,
            "system",
            kunnId
          );
        } else {
          throw new Error(`LMS ERROR: ${lmsRs?.message}`);
        }
    } catch (error) {
        console.log(`[BIZZ][KUNN][V2][createKunnDebt] kunn ${kunnId}, error ${error}`);
    }
};

module.exports = {
    createCashLoanAccount,
    createLmsBody,
    createDebtV3,
    updateStatus,
    createLMSMerchantCL,
    inactiveLMSMerchantCL,
    createLMS,
    getInstallmentKunn,
    misaCreateLms,
    misaRefinanceLms,
    createLmsBizzKunn,
    createActiveCreditLimit,
    calculateInvoicePaymentDateBizz
}