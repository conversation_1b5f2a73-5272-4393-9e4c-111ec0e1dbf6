const kunnRepo = require('../repositories/kunn-repo')
const { isNullOrEmpty } = require('../utils/helper')
const loanContractRepo = require('../repositories/loan-contract-repo')

async function getKunnSignedDisburByTime(time) {
    let responseBody = {
        statusCode: 400,
        status: 0,
        message: 'get kunn signed disbur failed',
        data: []
    }
    try {
        if(isNullOrEmpty(time)){
            responseBody.message = 'missing time parameter'
            return responseBody
        }
        const rs = await kunnRepo.getKunnSignedDisburByTime(time)
        if(rs){
            responseBody.statusCode = 200
            responseBody.status = 1
            responseBody.message = 'get kunn signed disbur success'
            responseBody.data = rs
        }
        return responseBody
    } catch (e) {
        console.log("error when get kunn signed disbur by time: ", e?.message)
        console.log(e)
        responseBody.statusCode = 500
        responseBody.status = -1
        responseBody.message = 'internal server error'
        return responseBody
    }
}

async function getNotYetDecidedLoanByTime(time) {
    let responseBody = {
        statusCode: 400,
        status: 0,
        message: 'get not yet decided loan failed',
        data: []
    }
    try {
        if(isNullOrEmpty(time)){
            responseBody.message = 'missing time parameter'
            return responseBody
        }
        const rs = await loanContractRepo.getNotYetDecidedLoanByTime(time)
        if(rs){
            responseBody.statusCode = 200
            responseBody.status = 1
            responseBody.message = 'get not yet decided loan success'
            responseBody.data = rs
        }
        return responseBody
    } catch (e) {
        console.log("error when get not yet decided loan by time: ", e?.message)
        console.log(e)
        responseBody.statusCode = 500
        responseBody.status = -1
        responseBody.message = 'internal server error'
        return responseBody
    }
}

async function getCancelledEkycLoan() {
    let responseBody = {
        statusCode: 400,
        status: 0,
        message: 'get cancelled ekyc loan failed',
        data: []
    }
    try {
        const rs = await loanContractRepo.getCancelEkycLoan()
        if(rs){
            responseBody.statusCode = 200
            responseBody.status = 1
            responseBody.message = 'get cancelled ekyc loan success'
            responseBody.data = rs
        }
        return responseBody
    } catch (e) {
        console.log("error when get cancelled ekyc loan: ", e?.message)
        console.log(e)
        responseBody.statusCode = 500
        responseBody.status = -1
        responseBody.message = 'internal server error'
        return responseBody
    }
}

module.exports = {
    getKunnSignedDisburByTime,
    getNotYetDecidedLoanByTime,
    getCancelledEkycLoan
}