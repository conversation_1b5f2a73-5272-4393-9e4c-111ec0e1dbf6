const common = require("../utils/common")
const utils = require("../utils/helper")
const offerRepo = require("../repositories/offer")
const turnoverRepo = require("../repositories/turnover-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const {SERVICE_NAME,PARTNER_CODE, RESPONSE_CODE} = require("../const/definition")
const loggingRepo = require("../repositories/logging-repo")
const {computeKOVOffer,computeNetIncome} = require("../offer/KOV-offer")
const {saveRequest} = require("../utils/loggingService")
const productService = require("../utils/productService")
const pmt = require('formula-pmt');   
const {PV,round_decimals} = require('../utils/helper')
const {CONTRACT_TYPE} = require('../const/definition')
const deService = require('../services/de-service')
const {serviceEndpoint} = require("../const/config")
const loanCicScoreRepo = require("../repositories/loan-cic-score")
const vskOffer = require("../offer/VSK-offer")
const { sum } = require("lodash")
const { ResponseBaseKov } = require('../entity/response-base-kov')
const { RES_STT_CODE, RESPONSE_MSG, ERROR_CODE } = require("../const/response-const")
const { calculateDIAterCE, calculateOfferKovCash } = require("./kov-de-service")
const { computeOffer } = require("../offer/offer")
const loanAttributeRepo = require("../repositories/loan-atribute-repo")

async function getOffer(contractNumber,requestAmt,requestTenor,riskGrade,emplType,dob,partnerCode,productCode,netIncome,runTrueNegative=0) {
    try {
        const config = global.config
        const url = config.basic.product[config.env] + config.data.productService.getOfferCash
        const offerBody = {
            "requestedAmount": requestAmt,
            "requestedTenor": requestTenor,
            "riskGrade": riskGrade,
            "employmentType": emplType,
            "currency": "VND",
            "customerAge": utils.caculateAge(dob),
            "runAlternatives": 1,
            "partnerCode": partnerCode,
            "contractType": "CASH",
            "productCode": productCode,
            "chanel": "MCC",
            "requestedAnnuity": netIncome,
            runTrueNegative,
            contractNumber
        }
        
        const offerRs = await common.postApiV2(url,offerBody)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.PRODUCT,SERVICE_NAME.GET_OFFER,offerBody,offerRs)
        return offerRs.data
    }
    catch(err) {
        common.log(`call get offer error : ${err.message}`,contractNumber)
        return false
   }
}

async function selectOffer(req,res) {
    let response
	try {
		const poolWrite = req.poolWrite
		const contractNumber = req.body.contractNumber
		const offerId = req.body.selectedOfferId;
        // const updateOfferRs  = await Promise.all([offerRepo.updateSelectedOffer(contractNumber,offerId),offerRepo.selectOffer(poolWrite,offerId,contractNumber)])

        // contractService.generateContract(req,contractNumber)
        if (!contractNumber || !offerId) {
            let errors = [];
            if (!contractNumber) {
                const error = getResponseError({code: ERROR_CODE.MISSING, field: 'contractNumber'});
                errors.push(error);
            } else {
                const error = getResponseError({code: ERROR_CODE.MISSING, field: 'selectedOfferId'});
                errors.push(error);
            }
            response = new ResponseBaseKov(RES_STT_CODE.BAD_REQUEST, RESPONSE_CODE.INVALID_REQUEST, RESPONSE_MSG.PARAMS_INVALID, null, errors);
            return res.status(400).json(response)
        }
            
        const dataResponse = {
            contractNumber: contractNumber,
            selectedOfferId: offerId
        }
        response = new ResponseBaseKov(RES_STT_CODE.SUCCESS, RESPONSE_CODE.RECIEVED, RESPONSE_MSG.SELECTION_OFFER_RECEIVED, dataResponse, []);
        saveRequest(poolWrite,req.body,response,contractNumber)
        return res.status(200).json(response)
	}
	catch(error) {
		common.log(`select offer error : ${error.message}`,req.body.contractNumber);
        console.log(error);
        response = new ResponseBaseKov(RES_STT_CODE.SERVER_ERROR, RESPONSE_CODE.SERVER_ERROR, RESPONSE_MSG.INTERNAL_SERVER_ERROR, null, []);
		return res.status(500).json(response);
	}

}

function getResponseError({code, field, message}) {
    if (!message && code === ERROR_CODE.MISSING) {
        message = `The ${field} is missing`;
    }
    return {
        code: code,
        field: field,
        message: message
    }
}

async function calculateDIV2(req,res) {
    try {
        const poolWrite = global.poolWrite
        const poolRead = global.poolRead
        const {contractNumber,pcbDebtGroup,pcbMonthlyPay,pcbCardDebt,cicInfo,realRevenue,cicInstallment,revenue,monthlyPaymentAtEc,assetsInfo} = req.body
        // const scoreDict = await productService.getCICConfig()
        const loanContractData = await loanContractRepo.getLoanContract(contractNumber)
        const cicScoreData = await loanCicScoreRepo.getCicScore(poolRead,contractNumber)
        let cic1 = 0;
		let cic2 = 0;
        let cicBody = {
            legalDebt: cicInfo.legalDebt,
            personDebt: cicInfo.personDebt,
            cicInstallment: cicInstallment,
            monthlyPaymentAtEc: monthlyPaymentAtEc,
            pcbMonthlyPay: pcbMonthlyPay,
            pcbCardDebt: pcbCardDebt
        }
        let countInclude = 0;
        let countInclude2 = 0;
        let netTurnOver = [];
        let turnOverSuperApp = [];
        let smePercent = [];
		let legalDebt = cicInfo.legalDebt
        let personDebt = cicInfo.personDebt
        cic2 = sum([parseFloat(legalDebt.shortTermInterest),
                    parseFloat(legalDebt.mediumTermInterest),
                    parseFloat(legalDebt.longTermInterest),
                    parseFloat(legalDebt.shortTermPrincipalAndInterest),
                    parseFloat(legalDebt.mediumTermPrincipalAndInterest),
                    parseFloat(legalDebt.longTermPrincipalAndInterest),
                    parseFloat(legalDebt.cardDebt2),
                ]) 
        cic1 = sum([parseFloat(personDebt.shortTerm),
                    parseFloat(personDebt.mortgageMediumTerm),
                    parseFloat(personDebt.creditMediumTerm),
                    parseFloat(personDebt.longTerm),
                    parseFloat(personDebt.cardDebt),
                    parseFloat(personDebt.otherDebt1),
                    parseFloat(monthlyPaymentAtEc||0)
        ]) 
        const data = await Promise.all([loanContractRepo.getLoanContractJoinLoanScore(contractNumber),
                    turnoverRepo.getRealTurnover(contractNumber),
                    turnoverRepo.getTurnOver(contractNumber)])
        const contractData = data[0]
        const turnoverData = data[1]
        const turnoverData2 = data[2]
        const partnerCode = contractData?.partner_code;
        if(contractData.partner_code == PARTNER_CODE.KOV){
            cic1 = 0;
            if(!utils.isNullOrEmpty(cicInstallment) && cicInstallment != '0') {
                cic2 = parseInt(cicInstallment)
            }
        }
        let netIncome ;
        
        for (let index = 1; index < 13; index++) {
            const element = turnoverData2[index];
            if(element!=undefined){
                if(element.hasOwnProperty("net_turnover")) {
                    countInclude++
                }
                if(element.hasOwnProperty("sme_percent")){
                    countInclude2++
                }
            }
        }
        revenue.map(x=>{
            netTurnOver.push({info_type:'net_turnover',month:x.month,amount:x.net_turnover})
            smePercent.push({info_type:'sme_percent',month:x.month,amount:x.sme_percent})
            turnOverSuperApp.push({info_type:'turnover',month:x.month,amount:x.turnover})
        });

        if(countInclude == 0 && Array.isArray(netTurnOver) && netTurnOver.length){
            await turnoverRepo.saveTurnOrTrans(poolWrite,netTurnOver,'net_turnover',contractNumber)
        }
        if(countInclude2 == 0 && Array.isArray(smePercent) && smePercent.length){
            turnoverRepo.saveTurnOrTrans(poolWrite,smePercent,'sme_percent',contractNumber)
        }
        if(assetsInfo?.assetsPrice) {
            loanAttributeRepo.update({contractNumber, field: 'assetsPrice', value: assetsInfo.assetsPrice})
        }

        await Promise.all([offerRepo.updateMainScore(contractNumber,'pcb_debt_group',parseInt(pcbDebtGroup)),
            offerRepo.updateMainScore(contractNumber,'pcb_score1',parseFloat(pcbMonthlyPay)),
            offerRepo.updateMainScore(contractNumber,'pcb_score2',parseFloat(pcbCardDebt)),
            offerRepo.updateMainScore(contractNumber,'cic_payment',parseFloat(cic1)),
            offerRepo.updateMainScore(contractNumber,'cic_payment_2',parseFloat(cic2)),
            turnoverRepo.updateRealTurnover(contractNumber,realRevenue),
            turnoverRepo.updateRevenue(contractNumber,revenue)
        ])

        if([PARTNER_CODE.KOV, PARTNER_CODE.SPL].includes(partnerCode)) {
            if (contractData.partner_code == PARTNER_CODE.KOV && contractData.contract_type === CONTRACT_TYPE.CASH_LOAN) {
                netIncome = await calculateDIAterCE({contractNumber, cicBody});
            } else {
                netIncome = computeNetIncome(contractNumber,contractData,turnoverData)
            }
        }
        else if([PARTNER_CODE.VSK, PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)){
            const diUrl = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].di;
            const headers = utils.initHeaders('DECISION', 'DI');
            let _turnover = partnerCode === PARTNER_CODE.SMA ? turnOverSuperApp : netTurnOver
            const diData = await deService.checkDeV3(contractNumber,diUrl,_turnover,cicBody, headers);
            if(diData?.data?.code == 0){
                netIncome = diData?.data?.data?.anuity ?? 0
            }
        }
        else if(contractData.partner_code == PARTNER_CODE.VPL){
            const pcbExpenses = computePCB(pcbMonthlyPay,pcbCardDebt);
            netIncome = Math.max(contractData?.monthly_income-(Math.max(pcbExpenses,cic1)+cic2)-Math.max(parseInt(loanContractData?.m_household_expenses),4000000),0)
        }
        else {
            const pcbExpenses = computePCB(pcbMonthlyPay,pcbCardDebt)
            netIncome = Math.max(contractData.net_income - Math.max(pcbExpenses,cic1) + cic2,0)
        }
        await offerRepo.updateMainScore(contractNumber,'di_after_ce',netIncome);
        res.status(200).json({
			"msg" : "compute DI success",
			"code" : 200,
			"data" : {
				"netIncome" : netIncome && netIncome != 0 ? netIncome.toFixed(0) : 0,
				"maxInstallMent" : 0
			}
		})
        
        if(cicScoreData.rowCount>0){
            loanCicScoreRepo.updateCicScore(poolWrite,contractNumber,cicInfo,monthlyPaymentAtEc,cicInstallment)
        }
        else{
            loanCicScoreRepo.saveCicScore(poolWrite,cicInfo,contractNumber,monthlyPaymentAtEc,cicInstallment)
        }
    }
    catch(error) {
        common.log(`compute DI error : ${error.message}`)
        return res.status(500).json({
            code : -1,
            msg : `${error.message}`
        })
    }
}

async function calculateDI(req,res) {
    try {
        const poolWrite = global.poolWrite
        const {contractNumber,debtGroup,score,realRevenue,pcbMonthlyPay,pcbCardDebt,cicInstallment,revenue} = req.body
        const scoreDict = await productService.getCICConfig()
        let cic1 = 0;
		let cic2 = 0
		// let cicScoreList=[]
		score.forEach(element => {
			const term = scoreDict[element.code].termType
			const loanType = scoreDict[element.code].loanType
			if(loanType == 1) {
				const tempScore = computeCIC1(element.value,term)
				// cicScoreList.push({
				// 	code : element.code,
				// 	value : tempScore
				// })
				let paramName = 'CIC-Thể_nhân-'
				if(term==1) {
					paramName += "Ngắn_hạn"
				}
				else if(term==2) {
					paramName += "Trung_hạn"
				}
				else {
					paramName += "Dài_hạn"	
				}
				cic1 += tempScore
				saveCIC(poolWrite,contractNumber,paramName,element.value,tempScore,'CIC')
			}
			else if(loanType == 2) {
				const tempScore = computeCIC2(element.value,term)
				cic2 += tempScore
				// cicScoreList.push({
				// 	code : element.code,
				// 	value : tempScore
				// })
				let paramName = 'CIC-Pháp_nhân-'
				if(term==1) {
					paramName += "Ngắn_hạn"
				}
				else if(term==2) {
					paramName += "Trung_hạn"
				}
				else {
					paramName += "Dài_hạn"	
				}
				saveCIC(poolWrite,contractNumber,paramName,element.value,tempScore,'CIC')
			}
			else{
				const tempScore = otherCIC(element.value)

				// cicScoreList.push({
				// 	code : element.code,
				// 	value : tempScore
				// })
				let paramName = 'CIC-'
				if(term == 4) {
					paramName += 'Nợ_khác-'
				}
				else {
					paramName += 'Nợ_thẻ-'
				}
				if(loanType==4) {
					cic1 += tempScore
					paramName += 'Thể_nhân'	
				}
				else {
					cic2 += tempScore
					paramName += 'Pháp_nhân'		
				}
				saveCIC(poolWrite,contractNumber,paramName,element.value,tempScore,'CIC')
			}
		})
        if(!utils.isNullOrEmpty(cicInstallment) && cicInstallment != '0') {
            cic2 = cicInstallment
        }
        await Promise.all([offerRepo.updateMainScore(contractNumber,'pcb_debt_group',parseInt(debtGroup.pcbDebtGroup)),
            offerRepo.updateMainScore(contractNumber,'pcb_score1',parseFloat(pcbMonthlyPay)),
            offerRepo.updateMainScore(contractNumber,'pcb_score2',parseFloat(pcbCardDebt)),
            offerRepo.updateMainScore(contractNumber,'cic_payment',parseFloat(cic1)),
            offerRepo.updateMainScore(contractNumber,'cic_payment_2',parseFloat(cic2)),
            turnoverRepo.updateRealTurnover(contractNumber,realRevenue)
        ])
        const data = await Promise.all([loanContractRepo.getLoanContractJoinLoanScore(contractNumber),turnoverRepo.getRealTurnover(contractNumber)])
        const contractData = data[0]
        const turnoverData = data[1]
        let netIncome ;
        if([PARTNER_CODE.KOV,PARTNER_CODE.SPL].includes(contractData.partner_code)) {
            netIncome = computeNetIncome(contractNumber,contractData,turnoverData)
            await turnoverRepo.updateRevenue(contractNumber,revenue)
        }
        else {
            let pcbExpenses = computePCB(pcbMonthlyPay,pcbCardDebt)
            netIncome = Math.max(contractData.net_income - Math.max(pcbExpenses,cic1) - cic2,0)
        }
        await offerRepo.updateMainScore(contractNumber,'di_after_ce',netIncome)
        return res.status(200).json({
			"msg" : "compute DI success",
			"code" : 200,
			"data" : {
				"netIncome" : netIncome,
				"maxInstallMent" : 0
			}
		})
    }
    catch(error) {
        common.log(`compute DI error : ${error.message}`)
        return res.status(500).json({
            code : -1,
            msg : `${error.message}`
        })
    }
}

function computePCB(pcbMonthlyPayment,pcbCardDebt) {
    pcbMonthlyPayment = parseInt(pcbMonthlyPayment)
	const pcbCardPay = 0.05 * parseInt(pcbCardDebt)
	const pcbTotal = pcbMonthlyPayment + pcbCardPay
	return pcbTotal
}


function computeCIC1(totalDebt,termType) {
	let ir
	let numMonth
	if(termType == 1) {
		ir = 0.105 
		numMonth =9
		
		if(totalDebt > 500000000) {
			return ir / 12 * totalDebt
		}
	}

	if(termType == 2) {
		ir = 0.22
		numMonth = 30
	}
	if(termType == 3) {
		ir = 0.125
		numMonth = 180
	}
	return -1 *  pmt(ir / 12 , numMonth, totalDebt )
}


function computeCIC2(totalDebt,termType) {
	let ir
	let numMonth
	if(termType == 1) {
		ir = 0.105 
		numMonth =9

		if(totalDebt > 500000000) {
			return ir / 12 * totalDebt
		}
	}
	if(termType == 2) {
		ir = 0.125
		numMonth = 36
	}
	if(termType == 3) {
		ir = 0.125
		numMonth = 180
	}
	return -1 *  pmt(ir / 12 , numMonth, totalDebt )
	
}


function otherCIC(amount) {
	return (amount * 0.05)
}

function saveCIC(poolWrite,contractNumber,cicName,cicInput,cicOutput,groupName) {
	const sql = "insert into loan_score_detail (contract_number,input_param_name,input_param_value,output_param_value,param_group) values ($1,$2,$3,$4,$5)"
	poolWrite.query(sql,[contractNumber,cicName,cicInput,cicOutput,groupName])
	.then()
	.catch(error => console.log(error))
}

async function calculateOffer(req,res) {
    try {
        const contractNumber = req.body.contractNumber;
        const contractData = await loanContractRepo.getLoanContractJoinMainScore(global.poolWrite, contractNumber);
        const partnerCode = contractData.partner_code;
        let offer;
        if((partnerCode === PARTNER_CODE.KOV && contractData.contract_type === CONTRACT_TYPE.CASH_LOAN)
            || [PARTNER_CODE.VTP, PARTNER_CODE.VSK, PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)){
                if (contractData.di_after_ce == null) {
                    return res.status(400).json({
                        code: 0,
                        msg: "Bạn cần phải tính DI cho sản phẩm này.",
                        data: {
                            maxLoan: 0
                        }
                    })
                }
        }
        if ([PARTNER_CODE.KOV, PARTNER_CODE.SPL].includes(partnerCode)) {
            if (partnerCode === PARTNER_CODE.KOV && contractData.contract_type === CONTRACT_TYPE.CASH_LOAN) {
                const maxLoanKov = await calculateOfferKovCash({ contractDataJoinMainScore: contractData });
                offer = { maxLoan: maxLoanKov };
                const annuity = await productService.getMonthlyInstallment(maxLoanKov, contractData.request_int_rate, contractData.request_tenor)
                await Promise.all([
                    offerRepo.saveOfferV3(contractNumber, parseInt(maxLoanKov / 1000000) * 1000000, contractData.request_tenor, annuity),
                    offerRepo.updateMainScore(contractNumber, 'max_loan', maxLoanKov)])
            } else {
                offer = await computeKOVOffer(contractNumber)
                console.log('offer', offer)
            }
        }
        if(partnerCode == PARTNER_CODE.VPL) {
            const offerData = await offerRepo.getMainScore(contractNumber)
            return res.status(200).json({
                code : 1,
                msg : "Offer ko thay đổi với sản phẩm VTPro.",
                data : {
                    maxLoan : offerData.max_loan
                }
            })
        }
        if(partnerCode == PARTNER_CODE.VTP) {
            let maxLoan;
            const offerRs = await getOffer(contractNumber,contractData.request_amt,contractData.request_tenor,null,contractData.empl_type,contractData.birth_date,contractData.partner_code,contractData.product_code,contractData.di_after_ce,1)
            if(offerRs.code == 1 || offerRs.code == 3) {
                maxLoan = parseFloat(offerRs.data[0].requestedAmount)
                let offerTenor = parseFloat(offerRs.data[0].tenor)
                const annuity = await productService.getMonthlyInstallment(maxLoan,contractData.request_int_rate,offerTenor)
                await Promise.all([offerRepo.saveOfferV3(contractNumber,maxLoan,offerTenor,annuity),offerRepo.updateMainScore(contractNumber,'max_loan',maxLoan)])
            }
            else {
                maxLoan = 0
            }
            
            offer = {
                maxLoan
            }   
        
        }
        if(partnerCode == PARTNER_CODE.VSK) {
            let maxLoan;
            const offerRs = await calculateOfferCeVSK(contractNumber,contractData.contract_type,contractData.request_int_rate,contractData.request_tenor,contractData.di_after_ce)
            maxLoan = parseFloat(offerRs)
            const annuity = await productService.getMonthlyInstallment(maxLoan,contractData.request_int_rate,contractData.request_tenor)
            await Promise.all([offerRepo.saveOfferV3(contractNumber,parseInt(maxLoan/1000000)*1000000,contractData.request_tenor,annuity),offerRepo.updateMainScore(contractNumber,'max_loan',maxLoan)])
            
            offer = {
                maxLoan
            }   
        }
        if([PARTNER_CODE.MCAPP, PARTNER_CODE.SMA].includes(partnerCode)) {
            let maxLoan;
            const offerRs = await calculateOfferMCA(contractNumber, partnerCode);
            maxLoan = parseFloat(offerRs) ?? 0;
            const annuity = await productService.getMonthlyInstallment(maxLoan,contractData.request_int_rate,contractData.request_tenor)
            await Promise.all([offerRepo.saveOfferV3(contractNumber,parseInt(maxLoan/1000000)*1000000,contractData.request_tenor,annuity),offerRepo.updateMainScore(contractNumber,'max_loan',maxLoan)])
            
            offer = {
                maxLoan
            }  
        }
        return res.status(200).json({
            "code":1,
            "msg":"compute offer successfully",
            "data":{
                "maxLoan":offer.maxLoan,
                diBeforeCe : "0",
                diAfterCe : "0",
                netIncome : "0"
            }
            })
    }
    catch(error) {
        common.log(`compute offer error : ${error.message}`, req.body.contractNumber);
        console.log(error);
        return res.status(500).json({
            code : -1,
            msg : `${error.message}`
        })
    }
}

async function getOfferScore(req,res) {
    const {contractNumber} = req.query
    const rs = await offerRepo.getMainScore(contractNumber);
    const loanContract = await loanContractRepo.getPartnerCodeAndProductCode(contractNumber);
    if(!rs) {
        return res.status(400).json({
            code : -1,
            msg : "Invalid contract number"
        })
    }
    return res.status(200).json({
        code : 1,
        msg : "get DI info successfully",
        data : {
            diBeforeCe : rs.di_before_ce,
            diAfterCe : rs.di_after_ce,
            maxLoan : (Number(rs.max_loan) < 10000000 && loanContract.partner_code == 'VSK') ? 0 : rs.max_loan,
            netIncome : rs.di_before_ce
        }
    })
}

async function validateOffer(req,res) {
    try {
        const amount = parseInt(req.body.amount);
        const tenor = parseInt(req.body.tenor)
        const contractNumber = req.body.contractNumber
        const data = await loanContractRepo.getLoanContractJoinLoanScore(contractNumber)
        const partnerCode = data?.partner_code
        const productData = await productService.getProductInfoV2(data.product_code)
        const minAmount = parseFloat(productData.productVar[0].minAmt);
        const maxAmount = parseFloat(productData.productVar[0].maxAmt);
        const minTenor = parseInt(productData.productVar[0].tenorFrm)
        const maxTenor = parseInt(productData.productVar[0].tenorTo)
        const defTenor = parseInt(productData.productVar[0].defTenor)
        if(data.contract_type == CONTRACT_TYPE.CREDIT_LINE && partnerCode != PARTNER_CODE.MISA) {
            if(tenor != defTenor) {
                return res.status(201).json({
                    code : 0,
                    msg : `Thông tin tenor phải bằng ${defTenor}`,
                    validateResult :false
                })
            }
        }
        if (utils.isNullOrEmpty(tenor) || utils.isNullOrEmpty(amount)) {
            return res.status(201).json({
                code : 0,
                msg : "Thông tin nhập không hợp lệ",
                validateResult :false
            })
        }

        if(amount < minAmount || amount > maxAmount || tenor < minTenor || tenor > maxTenor || amount > data.request_amt) {
            return res.status(201).json({
                code : 0,
                msg : "Offer không hợp lệ",
                validateResult :false
            })
        }

        if(utils.isNullOrEmpty(data.net_income) && utils.isNullOrEmpty(data.di_before_ce) && utils.isNullOrEmpty(data.di_after_ce) && partnerCode!=PARTNER_CODE.MISA) {
            return res.status(501).json({
                "code": -1,
                "msg": "netIncome is invalid"
            })    
        }

        return res.status(200).json({
            "code": 1,
            "msg": "offer is eligible",
            "validateResult": true
        })
    }
    catch(error) {
        console.log(error)
        return res.status(501).json({
            "code": -1,
            "msg": "amount is invalid"
        })
    }
}
//=min(Doanh thu ước tính * Hệ số,số tiền vay KH yêu cầu, số tiền vay tối đa theo scheme, 500tr)
async function calculateOfferCeVSK(contractNumber,type,rate,nper,diAfterCe){
    // console.log({
    //     contractNumber: contractNumber,
    //     type: type,
    //     rate: rate,
    //     nper: nper,
    //     diAfterCe: diAfterCe
    // })
    let offer = 0
    let hmtd1 = 0
    const contractData = await loanContractRepo.getLoanContract(contractNumber)
    const requestAmt = contractData.request_amt
    // console.log({rate: rate,nper:nper,diAfterCe:diAfterCe})
    // console.log('offerData',offerData)
    if(type==CONTRACT_TYPE.CASH_LOAN){//vay món
        const netTurnOver = await turnoverRepo.getNetTurnover(contractNumber)
        const offerDataCash = await vskOffer.computeEstimatedRevenue(contractNumber,netTurnOver)
        const estimateRevenue = offerDataCash?.data?.estimateRevenue || 0
        const ratio = offerDataCash?.data?.ratio || 0
        const maxAmountScheme = offerDataCash?.data?.maxAmountScheme
        // console.log({
        //     netTurnOver:netTurnOver,
        //     offerDataCash: offerDataCash,
        //     estimateRevenue: estimateRevenue,
        //     ratio: ratio,
        //     maxAmountScheme: maxAmountScheme
        // })
        hmtd1 = PV(rate/12,nper,diAfterCe)
        offer = Math.min(estimateRevenue*ratio,maxAmountScheme,requestAmt,hmtd1,500000000)
    }
    else if(type==CONTRACT_TYPE.CREDIT_LINE){// hạn mức
        // const offerDataCredit = await offerRepo.getOfferBeforeDI(contractNumber)
        // const hmtd2 = Number.parseFloat(offerDataCredit.offer_amt) || 0
        let hmtd2 = 0;
        const netTurnOverData = await turnoverRepo.getTurnOver(contractNumber)
        let netTurnOver = []
        netTurnOver.push({info_type:'net_turnover',month:1,amount:netTurnOverData['1'].net_turnover},
                        {info_type:'net_turnover',month:2,amount:netTurnOverData['2'].net_turnover},
                        {info_type:'net_turnover',month:3,amount:netTurnOverData['3'].net_turnover},
                        {info_type:'net_turnover',month:4,amount:netTurnOverData['4'].net_turnover},
                        {info_type:'net_turnover',month:5,amount:netTurnOverData['5'].net_turnover},
                        {info_type:'net_turnover',month:6,amount:netTurnOverData['6'].net_turnover}
        )
        // console.log('netTurnOver',netTurnOver)
        const offerData = await vskOffer.computeEstimatedRevenue(contractNumber,netTurnOver)
        hmtd2 = offerData?.data?.offer
        // console.log('offerData',offerData)
        hmtd1 = diAfterCe/(rate/12)
        offer = Math.min(hmtd1,hmtd2,requestAmt,500000000)
    }
    if(offer < 0) offer=0
    // console.log('offer',offer)
    return round_decimals(offer,0)
}

async function calculateOfferMCA(contractNumber, partnerCode) {
    const loanScore = await loanContractRepo.getLoanContractJoinMainScore(global.poolWrite, contractNumber);
    const offerRs = await computeOffer(contractNumber, partnerCode, 1, loanScore?.di_before_ce || 0,loanScore?.di_after_ce || 0);
    // const offer = Math.min(loanScore?.di_before_ce ?? 0, loanScore?.di_after_ce ?? 0);
    const offer = offerRs?.data?.offer || 0;
    return offer;
}

module.exports = {
    selectOffer,
    calculateDI,
    calculateDIV2,
    calculateOffer,
    getOfferScore,
    validateOffer,
    getOffer,
    calculateOfferKovCash
}