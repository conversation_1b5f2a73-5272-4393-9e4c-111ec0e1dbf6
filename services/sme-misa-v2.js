const common = require("../utils/common");
const axios = require('axios');
const querystring = require('querystring');
const { encryptDataMisa, decryptDataMisa, decryptFileMisa, encryptFileMisa, decryptFileMisaTest } = require("../utils/encrypt/encrypt");

const uuid = require('uuid');
const s3Service = require("../upload_document/s3-service");
const storageContractUnSignPath = "/mc-credit/unsigned-contract";
const storageContractSignedPath = "/mc-credit/signed-contract";
const moment = require('moment-timezone');
moment().tz('Asia/Ho_Chi_Minh').format();
const loanEsigningRepo = require("../repositories/loan-esigning");
const mime = require('mime-types');
const path = require('path');
const { PG_DATE_TODAY, LMS_DATE } = require("../utils/dateHelper");
const { getFileSizeFromUrl, getFileExtension } = require("../utils/helper");
const sqlHelper = require("../utils/sqlHelper");
const { CIC_STEP_CHECK, FILE_STORAGE, TYPE_COLLECTION, PARTNER_CODE, ERROR_CODE, CONTRACT_TYPE, SERVICE_NAME, DOC_TYPE, MisaStep } = require("../const/definition");
const loanContractRepo = require("../repositories/loan-contract-repo");
const { STATUS, KUNN_STEP, CONTENT_DISBURSE, SIMPLE_CONTENT_DISBURSE, KUNN_STATUS, WORKFLOW_STAGE, SME_CONTENT_DISBURSE } = require("../const/caseStatus");
const lmsService = require("../services/lms-service");
const loanContractDocumentRepo = require("../repositories/document");
const FormData = require('form-data');
const loggingService = require("../utils/loggingService");
// const misaKunnAppformService = require("../KUNN_V2/misa-kunn-service");
const kunnRepo = require("../repositories/kunn-repo")
const helper = require("../utils/helper");
const { checkAvailableAmountApi, cancelKunnApi, getKunnInstallmentApi, createDebtApi, getKunnInstallmentByKunnApiV2, checkRemainPrinAmountApi} = require("../apis/lms-api");
const { callbackKunnCicApi } = require("../apis/misa-api");
const { getProductByCodeApi } = require("../apis/product-api");
const kunnDisburInfoRepo = require("../repositories/kunn-disbursement-info-repo");
const offerRepo = require("../repositories/offer");
const productService = require("../utils/productService");
const { getValueCodeByCodeType, convertEvfLov } = require("../utils/masterdataService");
const { saveStepLog, saveWorkflow } = require("../repositories/logging-repo");
const { STEP, DOC_GROUP } = require("../const/variables-const");
const { MISA_ERROR_CODE } = require("../const/response-const");
const { checkBankAccountInfoApi } = require("../apis/disbursement-api");
const loggingRepo = require("../repositories/logging-repo");
const cicReportService = require("./cic-report-service");

const CIC_STEP_MISA_CALLBACK = {
  AF1: 'AF1',
  AF2: 'AF2',
  TC1: 'TC1',
  TC2: 'TC2',
}

const FILE_TYPE_MISA = {
  BCTD: 'BCTD', //Báo cáo thẩm định
  HDHM: 'HDHM' //Hợp đồng hạn mức
}

const getToken = async () => {
  try {
    const url = global.config.data.partnerCallback.misaTokenUrlV2;
    const form = {
      grant_type: global.config.data.partnerCallback.misaTokenGrantType,
      client_id: global.config.data.partnerCallback.misaTokenClientId,
      client_secret: global.config.data.partnerCallback.misaTokenClientSecret
    };
    const formBody = querystring.stringify(form);

    const response = await common.postApiV2(url, formBody, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    return response?.status === 200 ? response?.data?.access_token : undefined;
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

const getTokenTest = async (client_id, client_secret) => {
  try {
    const url = 'https://lendingapi.misa.vn/partner/connect/token';
    const form = {
      grant_type: 'client_credentials',
      client_id,
      client_secret
    };
    const formBody = querystring.stringify(form);

    const response = await common.postApiV2(url, formBody, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
    return response?.status === 200 ? response?.data?.access_token : undefined;
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

const addExtraDataToCicCallbackPayload = async (payload) => {
  const loan = await sqlHelper.findOne({
    table: `loan_contract`,
    whereCondition: {
      contract_number: payload.contractNumber
    },
    orderBy: {
      created_date: 'DESC'
    },
  })

  const remainAmountData = await checkRemainPrinAmountApi(loan.cust_id, PARTNER_CODE.MISA);
  payload.totalCreditLoanEVF = Number(remainAmountData.totalLimitAmount);
  payload.outstandingDebtEVF = Number(remainAmountData.totalPrinAmount);
  return payload
}

const callbackCicResult = async ({
  step = CIC_STEP_MISA_CALLBACK.AF1,
  contractNumber,
  isPass = false,
  totalCreditLoan,
  creditHistoryRank,
  loanEffectTime = LMS_DATE(),
  creditCardLimit
}) => {
  try {
    const url = global.config.data.partnerCallback.misaCicResultUrl;
    const token = await getToken();
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'text/plain'
    }
    let payload = {
      contractNumber,
      formStatus: step,
      loanEffectTime,
      result: isPass,
      creditInfo: {
        totalCreditLoan,
        creditHistoryRank,
        creditCardLimit: creditCardLimit ?? null
      }
    };
    payload = await addExtraDataToCicCallbackPayload(payload)
    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    const respPayload = await decryptDataMisa(response?.data);
    loggingService.saveRequestV2(
      global.poolWrite,
      JSON.stringify({
        url,
        payload
      }),
      JSON.stringify(respPayload),
      contractNumber,
      null,
      PARTNER_CODE.MISA
    );
    let stage;
    if (step == CIC_STEP_MISA_CALLBACK.AF1) {
      stage = MisaStep.SEND_RESULT_AF1;
    } else if (step == CIC_STEP_MISA_CALLBACK.AF2) {
      stage = MisaStep.SEND_RESULT_AF2;
    } else if (step == CIC_STEP_MISA_CALLBACK.TC1) {
      stage = MisaStep.SEND_RESULT_TC1;
    } else if (step == CIC_STEP_MISA_CALLBACK.TC2) {
      stage = MisaStep.SEND_RESULT_TC2;
    } else {
      stage = MisaStep.UNKNOWN_STEP;
    }

    await loggingRepo.saveWorkflow(stage, callbackStatusResult(respPayload), contractNumber, 'system');
    return respPayload;
  } catch (e) {
    console.error(e);
    return null;
  }
}

/**
 *
 * @param {*} file: {type, fileName, contentType, fileSize, fileUrl, contractValidDate}
 * @returns
 */
const callbackContractFile = async ({ contractNumber, files = [] }) => {
  try {
    const url = global.config.data.partnerCallback.misaFileContractUrl;
    const token = await getToken();
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'text/plain'
    }
    const payload = {
      contractNumber,
      files
    };

    const encryptedPayload = await encryptDataMisa(payload);
    const response = await common.putAPIV2(url, encryptedPayload, headers);
    const respPayload = await decryptDataMisa(response?.data);
    loggingService.saveRequestV2(
      global.poolWrite,
      JSON.stringify({
        url,
        payload
      }),
      JSON.stringify(respPayload),
      contractNumber,
      null,
      PARTNER_CODE.MISA
    );
    //save wf status
    let stage = files?.[0]?.type === FILE_TYPE_MISA.BCTD
      ? MisaStep.SEND_BCTD : files?.[0]?.type === FILE_TYPE_MISA.HDHM
        ? MisaStep.SEND_CONTRACT_FILE : 'UNKNOWN_FILE'
    await loggingRepo.saveWorkflow(stage, callbackStatusResult(respPayload), contractNumber, 'system');
    return respPayload;
  } catch (e) {
    console.error(e);
    return null;
  }
}

const fillEvfSignature = async (body, context = {}, isRefinance = false) => {
  try {
    const config = global.config;
    const poolWrite = global.poolWrite;
    const { contractNumber, unsignUrl } = body;

    const signUri = '/esigning/internal/misa/sign-contract';
    const signUrl = config.basic['bss-esigning-service'][config.env] + signUri;
    const misaFileUrl = unsignUrl;
    const bufferUnSign = await getMisaFile({ url: misaFileUrl, context });
    const fileExt = `.pdf`;
    // const fileName = path.basename(misaFileUrl);
    const fileName = `${contractNumber}-hop-dong-hm-tin-dung${fileExt}`;
    const s3UnSign = await s3Service.uploadV2(config.data, moment().format('yyyyMMDDHHmmss') + fileName, bufferUnSign, storageContractUnSignPath);
    const pathUnSign = s3UnSign.Key;
    const fileLocationUnSign = s3UnSign.Location;

    let sql = "insert into loan_esigning(contract_number,status,unsigned_contract,updated_date) values($1,$2,$3,$4)"
    await poolWrite.query(sql, [contractNumber, "not signed yet", fileLocationUnSign, new Date()])

    await loggingRepo.saveWorkflow(MisaStep.SIGNATURE, MisaStep.EVF_START_SIGNING, contractNumber, 'system')
    //call esigning
    const signedResp = await common.postApiV2(signUrl, {
      contractNumber: contractNumber,
      filePath: pathUnSign,
      partnerCode: "MISA_HDHM"
    })

    const signedBuffer = Buffer.from(signedResp.data.data, 'base64');
    const encryptSignedBuffer = await encryptFileMisa(signedBuffer);
    const encryptFileName = `encrypt-${fileName}.pgp`
    const [s3Signed, s3SignedEncrypt] = await Promise.all([
      s3Service.uploadV2(config.data, moment().format('yyyyMMDDHHmmss') + fileName, signedBuffer, storageContractSignedPath),
      s3Service.uploadV2(config.data, moment().format('yyyyMMDDHHmmss') + encryptFileName, encryptSignedBuffer, storageContractSignedPath, 'private')
    ])

    const pathSigned = s3Signed.Key;
    const fileLocationSigned = s3Signed.Location;
    const signedLocationEncrypt = s3SignedEncrypt.Location;
    const pathSignedEncrypt = s3SignedEncrypt.Key;
    console.log(`HDHM | ${contractNumber} | normal file: `, fileLocationSigned);
    console.log(`HDHM | ${contractNumber} | encrypt file: `, signedLocationEncrypt);

    let docBody = {
      type: 'LCT',
      url: fileLocationSigned,
      key: pathSigned,
      docId: uuid.v4(),
      bundleId: null,
      fileName: fileName
    }

    await Promise.all([
      loanEsigningRepo.saveSignedContract(fileLocationSigned, contractNumber),
      saveUpload(poolWrite, docBody.docId, docBody.url, docBody.type, docBody.bundleId, docBody.key, docBody.fileName, contractNumber),
      saveUpload(poolWrite, uuid.v4(), signedLocationEncrypt, 'LCT_ENC', docBody.bundleId, pathSignedEncrypt, encryptFileName, contractNumber),
      loanContractRepo.updateContractStatus(STATUS.SIGNED, contractNumber),
      loggingRepo.saveWorkflow(MisaStep.SIGNATURE, MisaStep.EVF_SIGNED, contractNumber, 'system')
    ])

    try {
      await updateDocGroup(contractNumber);
    } catch (e) {
      console.error(e);
    }

    const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);

    if (isRefinance) {
      // active HM, inactive HM cũ
      lmsService.misaRefinanceLms(
        contractNumber,
        loan?.old_contract_number,
        loan?.contract_type,
        loan?.loan_customer_representations?.phone_number,
        loan.partner_code
      ).catch(e => {
        console.error(`lmsService.misaRefinanceLms error: `, e);
      });
    } else {
      //active HM
      lmsService.misaCreateLms(
        contractNumber,
        loan?.contract_type,
        loan?.loan_customer_representations?.phone_number,
        loan.partner_code
      ).catch(e => {
        console.error(`lmsService.misaCreateLms error: `, e);
      });
    }

    //add gen cic report

    exportCicReportData({ contractNumber });


    //handle callback file to misa
    callbackContractFile({
      contractNumber,
      files: [
        {
          type: FILE_TYPE_MISA.HDHM,
          fileName: fileName,
          contentType: mime.lookup(fileName),
          fileSize: await getFileSizeFromUrl(fileLocationSigned),
          fileUrl: pathSignedEncrypt,
          contractValidDate: PG_DATE_TODAY()
        }
      ]
    });
  } catch (e) {
    console.error(e);
  }
}

const exportCicReportData = async ({ contractNumber }) => {
  try {
    await cicReportService.exportCicReportData({ contractNumber });
  } catch (error) {
    console.error(`gen cic report contract ${contractNumber}, error: ${error.message}`);
  }
}

async function saveUpload(poolWrite, docId, docUrl, docType, bundleId, fileKey, fileName, contractNumber, kunnId, fileSize) {
  const sql = `insert into loan_contract_document(doc_id,doc_type,url,doc_group,file_key,file_name,contract_number, kunn_contract_number, file_size)
   values ($1,$2,$3,$4,$5,$6,$7,$8,$9)`;
  poolWrite
    .query(sql, [docId, docType, docUrl, bundleId, fileKey, fileName, contractNumber, kunnId, fileSize])
    .then()
    .catch((error) => {
      common.log(error.message);
    });
}

const callbackCicDetailToMisa = async (contractNumber) => {
  try {
    const [
      cicLog,
      loan
    ] = await Promise.all([
      sqlHelper.findOne({
        table: `loan_cic_log`,
        whereCondition: {
          contract_number: contractNumber,
          step: CIC_STEP_CHECK.AF2_DETAIL
        },
        orderBy: {
          created_at: 'DESC'
        },
      }),
      sqlHelper.findOne({
        table: `loan_contract`,
        whereCondition: {
          contract_number: contractNumber
        },
        orderBy: {
          created_date: 'DESC'
        },
      })
    ])
    if (!cicLog?.id) {
      throw Error(`${contractNumber} | callbackCicDetailToMisa error: loan_cic_log not found`);
    }

    const responsePayload = JSON.parse(cicLog.response_payload);
    const isPass = responsePayload?.decision === 'D' ? false : true;
    const misaPayload = {
      step: CIC_STEP_MISA_CALLBACK.AF2,
      contractNumber,
      isPass,
      totalCreditLoan: calculateTotalDebt(responsePayload?.cicData?.enterprises, loan.sme_tax_id),
      creditHistoryRank: responsePayload?.decision,
      loanEffectTime: responsePayload?.nextTimeCanRequest ?? LMS_DATE(),
      creditCardLimit: calculateCreditCardLimit(responsePayload?.cicData?.enterprises, loan.sme_tax_id),
    };

    await callbackCicResult(misaPayload)
  } catch (e) {
    console.error(e);
  }
}

const callbackRefinanceCicDetailToMisa = async (contractNumber) => {
  try {
    const [
      cicLog,
      loan
    ] = await Promise.all([
      sqlHelper.findOne({
        table: `loan_cic_log`,
        whereCondition: {
          contract_number: contractNumber,
          step: CIC_STEP_CHECK.TC2_DETAIL
        },
        orderBy: {
          created_at: 'DESC'
        },
      }),
      sqlHelper.findOne({
        table: `loan_contract`,
        whereCondition: {
          contract_number: contractNumber
        },
        orderBy: {
          created_date: 'DESC'
        },
      })
    ])
    if (!cicLog?.id) {
      throw Error(`${contractNumber} | callbackCicDetailToMisaRefinance error: loan_cic_log not found`);
    }

    const responsePayload = JSON.parse(cicLog.response_payload);
    const isPass = responsePayload?.decision === 'D' ? false : true;

    // for MISA TC2
    const misaPayload = {
      step: CIC_STEP_MISA_CALLBACK.TC2,
      contractNumber,
      isPass,
      totalCreditLoan: calculateTotalDebt(responsePayload?.cicData?.enterprises, loan.sme_tax_id),
      creditHistoryRank: responsePayload?.decision,
      loanEffectTime: responsePayload?.nextTimeCanRequest ?? LMS_DATE(),
      creditCardLimit: calculateCreditCardLimit(responsePayload?.cicData?.enterprises, loan.sme_tax_id),
    };

    await callbackCicResult(misaPayload)
  } catch (e) {
    console.error(e);
  }
}

const calculateTotalDebt = (enterprisesCicInfo, smeTaxCode) => {
  const enterpriseCicInfo = enterprisesCicInfo.filter(item => item.taxCode === smeTaxCode)?.[0];
  let total = 0;
  const debts = enterpriseCicInfo?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG;
  if (!Array.isArray(debts) && !debts) {
    return total;
  }
  if (Array.isArray(debts) && debts?.length === 0) {
    return total;
  }

  let totalDolar = 0;
  const dollarRate = global.config.data.exchangeRate.dollarToVnd
  if (Array.isArray(debts)) {
    for (const debt of debts) {
      if (Array.isArray(debt.CTLOAIVAY.DONG)) {
        for (const d of debt.CTLOAIVAY.DONG) {
          total = total + +d.DUNO_VND;
          totalDolar = totalDolar + +d.DUNO_USD;
        }
      } else {
        total = total + +debt.CTLOAIVAY.DONG.DUNO_VND;
        totalDolar = totalDolar + +debt.CTLOAIVAY.DONG.DUNO_USD;
      }
    }
  } else {
    if (Array.isArray(debts.CTLOAIVAY.DONG)) {
      for (const d of debts.CTLOAIVAY.DONG) {
        total = total + +d.DUNO_VND;
        totalDolar = totalDolar + +d.DUNO_USD;
      }
    } else {
      total = total + +debts.CTLOAIVAY.DONG.DUNO_VND;
      totalDolar = totalDolar + +debts.CTLOAIVAY.DONG.DUNO_USD;
    }
  }
  return total * 1000000 + totalDolar * dollarRate; //DVT: triệu đồng
}

const calculateCreditCardLimit = (enterprisesCicInfo, smeTaxCode) => {
  const enterpriseCicInfo = enterprisesCicInfo.filter(item => item.taxCode === smeTaxCode)?.[0];
  let total = 0;
  const DUNO_THETD = enterpriseCicInfo?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG;
  if (!Array.isArray(DUNO_THETD) && !DUNO_THETD) {
    return total;
  }
  if (Array.isArray(DUNO_THETD) && DUNO_THETD?.length === 0) {
    return total;
  }

  if (Array.isArray(DUNO_THETD)) {
    for (const DUNO_THETD_DONG of DUNO_THETD) {
      total = total + +DUNO_THETD_DONG.HANMUC_THETD;
    }
  } else {
    total = total + +DUNO_THETD.HANMUC_THETD;
  }
  return total * 1000000; //DVT: triệu đồng
}

const getMisaFile = async ({ url, context }) => {
  try {
    if (context?.req?.headers?.env === 'dev' && context?.req?.headers?.decrypt === 'false' && process.env.NODE_ENV === 'dev') { // for test
      const response = await axios.get(url, { responseType: 'arraybuffer', timeout: 30000 });
      return response?.data;
    }

    const token = await getToken();
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'text/plain'
    }

    const response = await common.getApiTimeoutV2({ url, headers, timeout: 30000 });
    if (response?.data?.success == false) {
      return undefined
    }

    return await decryptFileMisa(response?.data); //buffer
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

/**
 *
 * @param {*} documents - list of object {fileName, fileType, docType, fileUrl}
 * @param {*} contractNumber
 * @returns
 */
const saveAf2Documents = async (documents, contractNumber, context) => {
  try {
    if (!documents || documents?.length === 0) {
      return;
    }
    let insertDocumentsTask = [];
    for (const doc of documents) {
      if (!doc?.fileUrl || !doc?.fileType) continue;
      let buffer
      try {
        buffer = await getMisaFile({ url: doc.fileUrl, context });
      } catch (e) {
        console.error(`ERROR | saveAf2Documents | getMisaFile not found | url: `, doc.fileUrl);
      }
      if (!buffer) continue;
      const fileExt = getFileExtension(doc.fileType);
      const misafileName = doc?.fileName ?? path.basename(doc.fileUrl);
      const fileName = moment().format('yyyyMMDDHHmmss') + `_${uuid.v4()}` + `${misafileName}.${fileExt}`;
      const fileStorage = FILE_STORAGE.storageMisaDocs + `/${contractNumber}`
      const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, fileStorage);
      const fileKey = s3Result.Key;
      const fileLocation = s3Result.Location;
      insertDocumentsTask.push(loanContractDocumentRepo.insert({
        contractNumber,
        docType: doc?.docType,
        docId: uuid.v4(),
        fileKey,
        fileName,
        url: fileLocation,
        typeCollection: TYPE_COLLECTION.DOC_EXTERNAL
      }))
    }
    await Promise.all(insertDocumentsTask);
  } catch (e) {
    console.error(e);
    throw new Error(e);
  }
}

const saveAf2DocumentsReplaceOnDuplicate = async (documents, contractNumber) => {
  try {
    if (!documents || documents?.length === 0) {
      return;
    }
    let insertDocumentsTask = [];
    let fileLocations = [];

    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract?.id || !loanContract.product_code) {
      console.log(`saveAf2DocumentsReplaceOnDuplicate | invalid contract_number: ${contractNumber}`);
      return;
    }
    let bundleInfo = await productService.getBundle(global.config, loanContract.product_code);
    const bundleData = bundleInfo.data;
    const docDict = {}
    bundleData.map(bundle => {
      let tmpDocList = bundle.docList
      tmpDocList.forEach(doc => {
        docDict[doc.docType] = bundle.bundleName
        docDict[doc.bundleNameVi] = bundle.bundleNameVi
      })
    })

    for (const doc of documents) {
      if (!doc?.fileUrl || !doc?.fileType) continue;
      let buffer
      try {
        buffer = await getMisaFile({ url: doc.fileUrl });
      } catch (e) {
        console.error(`ERROR | saveAf2DocumentsReplaceOnDuplicate | getMisaFile not found | url: `, doc.fileUrl);
      }
      if (!buffer) continue;
      const fileExt = getFileExtension(doc.fileType);
      const misafileName = doc?.fileName ?? path.basename(doc.fileUrl);
      const fileName = moment().format('yyyyMMDDHHmmss') + `_${uuid.v4()}` + `${misafileName}.${fileExt}`;
      const fileStorage = FILE_STORAGE.storageMisaDocs + `/${contractNumber}`
      const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, fileStorage);
      const fileKey = s3Result.Key;
      const fileLocation = s3Result.Location;
      fileLocations.push(fileLocation);//

      let deletedIds = await loanContractDocumentRepo.deleteDocumentsByContractNumberAndDocTypeAndPeriod(contractNumber, doc.docType, doc.period)

      let docId = uuid.v4()

      insertDocumentsTask.push(loanContractDocumentRepo.insert({
        contractNumber,
        docType: doc?.docType,
        docId,
        fileKey,
        fileName,
        url: fileLocation,
        typeCollection: TYPE_COLLECTION.DOC_EXTERNAL,
        period: doc?.period,
        docGroup: doc.hasOwnProperty('docType') ? docDict[doc.docType] : (doc.hasOwnProperty('docName') ? docDict[doc.docName] : docDict[doc.docType])
      }))

      if (deletedIds) {
        const historySql = "INSERT INTO loan_contract_document_upload_histories " +
            "(contract_number, kunn_contract_number, old_doc_id, new_doc_id, reason, created_by, contract_status, kunn_status, old_file_name, new_file_name) " +
            "values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)"

        for (const deletedId of deletedIds) {
          const oldSelectSql = "select type_collection, file_name, doc_name_vn_detail from loan_contract_document where doc_id =$1"
          const oldQueryRs = await poolRead.query(oldSelectSql,[deletedId])

          insertDocumentsTask.push(
              await poolWrite.query(historySql, [contractNumber, null, deletedId, docId, 'upload chứng từ sau ACTIVED khoản vay', 'Misa', loanContract?.status, null, oldQueryRs.rows[0]?.file_name, fileName ])
          )
        }
      }
    }
    await Promise.all(insertDocumentsTask);
    return fileLocations;
  } catch (e) {
    console.error(e);
    throw new Error(e);
  }
}

const scanDownloadDocuments = async (contractNumber, context) => {
  try {
    if (!contractNumber) {
      return;
    }
    const [
      revenues,
      vatForms
    ] = await Promise.all([
      sqlHelper.find({
        table: 'loan_revenues',
        whereCondition: {
          contract_number: contractNumber
        }
      }),
      sqlHelper.find({
        table: 'loan_vat_forms',
        whereCondition: {
          contract_number: contractNumber
        }
      })
    ])

    let insertDocumentsTask = [];
    let updateRevenueDocsTask = [];
    let updateVatFormDocsTask = [];
    if (revenues?.length > 0) {
      for (const revenue of revenues) {
        const [
          revenueDocs
        ] = await Promise.all([
          sqlHelper.find({
            table: 'revenue_documents',
            whereCondition: {
              loan_revenues_id: revenue.id
            }
          })
        ])

        for (const doc of revenueDocs) {
          if (!doc?.file_url || !doc?.file_type || doc.evf_file_url) continue;
          let buffer
          try {
            buffer = await getMisaFile({ url: doc.file_url, context });
          } catch (e) {
            console.error(`ERROR | scanDownloadDocuments | getMisaFile not found | url: `, doc.file_url);
          }
          if (!buffer) continue;
          const fileExt = getFileExtension(doc.file_type);
          const misafileName = doc?.file_name ?? path.basename(doc.file_url);
          const fileName = moment().format('yyyyMMDDHHmmss') + `_${uuid.v4()}` + `${misafileName}.${fileExt}`;
          const fileStorage = FILE_STORAGE.storageMisaDocs + `/${contractNumber}`
          const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, fileStorage);
          const fileKey = s3Result.Key;
          const fileLocation = s3Result.Location;
          insertDocumentsTask.push(loanContractDocumentRepo.insert({
            contractNumber,
            docType: doc?.doc_type,
            docId: uuid.v4(),
            fileKey,
            fileName,
            url: fileLocation,
            typeCollection: TYPE_COLLECTION.DOC_EXTERNAL,
            period: revenue?.year
          }));
          updateRevenueDocsTask.push(sqlHelper.patchUpdate({
            table: 'revenue_documents',
            columns: ['evf_file_url'],
            values: sqlHelper.generateValues({ evf_file_url: fileLocation }, ['evf_file_url']),
            conditions: {
              id: doc.id
            }
          }));
        }
      }
    }

    if (vatForms?.length > 0) {
      for (const vatForm of vatForms) {
        const [
          vatFormDocs
        ] = await Promise.all([
          sqlHelper.find({
            table: 'vat_forms_documents',
            whereCondition: {
              loan_vat_forms_id: vatForm.id
            }
          })
        ])

        for (const doc of vatFormDocs) {
          if (!doc?.file_url || !doc?.file_type || doc.evf_file_url) continue;
          let buffer
          try {
            buffer = await getMisaFile({ url: doc.file_url, context });
          } catch (e) {
            console.error(`ERROR | scanDownloadDocuments | getMisaFile not found | url: `, doc.file_url);
          }
          if (!buffer) continue;
          const fileExt = getFileExtension(doc.file_type);
          const misafileName = doc?.file_name ?? path.basename(doc.file_url);
          const fileName = moment().format('yyyyMMDDHHmmss') + `_${uuid.v4()}` + `${misafileName}.${fileExt}`;
          const fileStorage = FILE_STORAGE.storageMisaDocs + `/${contractNumber}`
          const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, fileStorage);
          const fileKey = s3Result.Key;
          const fileLocation = s3Result.Location;
          insertDocumentsTask.push(loanContractDocumentRepo.insert({
            contractNumber,
            docType: doc?.doc_type,
            docId: uuid.v4(),
            fileKey,
            fileName,
            url: fileLocation,
            typeCollection: TYPE_COLLECTION.DOC_EXTERNAL
          }));
          updateVatFormDocsTask.push(sqlHelper.patchUpdate({
            table: 'vat_forms_documents',
            columns: ['evf_file_url'],
            values: sqlHelper.generateValues({ evf_file_url: fileLocation }, ['evf_file_url']),
            conditions: {
              id: doc.id
            }
          }));
        }
      }
    }
    await Promise.all([...insertDocumentsTask, ...updateRevenueDocsTask, ...updateVatFormDocsTask]);
  } catch (e) {
    console.error(e);
    throw new Error(e);
  }
}

const isValidMisaSignature = async (body) => {
  const config = global.config;
  const { unsignUrl } = body;
  const checkSignUri = '/esigning/internal/misa/check-signature';
  const checkSignUrl = config.basic['bss-esigning-service'][config.env] + checkSignUri;
  const misaFileUrl = unsignUrl;
  const bufferUnSign = await getMisaFile({ url: misaFileUrl });
  let dataCheckSign = new FormData();
  let bufferNode = Buffer.from(bufferUnSign);
  dataCheckSign.append('file', bufferNode);
  const rsCheck = await common.postApiV2(checkSignUrl, dataCheckSign, dataCheckSign.getHeaders());
  if (rsCheck?.data?.code != 0) {
    return false;
  }
  if (rsCheck?.data?.data?.length < 2) {
    return false;
  }
  return true;
}

/**
 *
 * @param {*} documents - list of object {fileName, fileType, docType, fileUrl}
 * @param {*} contractNumber
 * @returns
 */
const downloadAndPushDocS3 = async (doc, contractNumber, context = {}) => {
  try {
    if (!doc) {
      return;
    }
    if (!doc?.fileUrl || !doc?.fileType) return;
    let buffer
    try {
      buffer = await getMisaFile({ url: doc.fileUrl, context });
    } catch (e) {
      console.error(`ERROR | downloadAndPushDocS3 | getMisaFile not found | url: `, doc.fileUrl);
    }
    if (!buffer) return;
    const fileExt = getFileExtension(doc.fileType);
    const misafileName = doc?.fileName ?? path.basename(doc.fileUrl);
    const fileName = moment().format('yyyyMMDDHHmmss') + `${helper.removeVietnameseTones(misafileName.replace(/\s+/g, ''))}.${fileExt}`;
    const fileStorage = FILE_STORAGE.storageMisaDocs + `/${contractNumber}`
    const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, fileStorage);
    return s3Result;
  } catch (e) {
    console.error(e);
    throw new Error(e);
  }
}

/**
 * handle callback kunn
 */
const handleCallbackCicKunn = async ({
  requestId,
  kunnId,
  contractType,
  status,
  decision,
  cicData,
  callbackAmount
}) => {
  try {
    const { availableAmount, lastestLoanAmount } = callbackAmount;
    const loanStatus = ["A", "B", "C"].includes(decision)
      ? STATUS.WAITING_CUSTOMER_SIGNATURE
      : STATUS.REFUSED;

    const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(kunnId, true));
    if (kunn?.status !== STATUS.WAITING_CIC_RESULT) {
      console.log(`[MISA][KUNN][V2] handleCallbackCicKunn status kunn ${kunn.kunnId} invalid ${kunn.status}`);
      return;
    };

    //callback misa

    const cicLog = await sqlHelper.findOne({
      table: `loan_cic_log`,
      whereCondition: {
        contract_number: kunn.kunnId,
        step: CIC_STEP_CHECK.KUNN
      },
      orderBy: {
        created_at: 'DESC'
      }

    })
    if (!cicLog?.id) {
      throw Error(`${kunnId} | callbackCicDetailToMisa error: loan_cic_log not found`);
    }
    //check avaible limit
    const expiredDate = (moment().add(1, 'days')).format('YYYY-MM-DD');
    const responsePayload = JSON.parse(cicLog.response_payload ?? '{}');

    await kunnRepo.update(kunnId,
      {
        status: loanStatus,
        expired_date: expiredDate,
        loan_effect_time: responsePayload?.nextTimeCanRequest ?? LMS_DATE(),

      });
    await saveWorkflow(WORKFLOW_STAGE.CIC_RESULT, loanStatus, kunn.contractNumber, 'system', kunn.kunnId);
    const result = responsePayload?.decision === 'D' ? false : true;
    //callback to misa
    const callbackRs = await callBackKunnCic(kunn.kunnId, result, { availableAmount, lastestLoanAmount });
    return callbackRs;
  } catch (error) {
    console.log(`[MISA][KUNN][V2]handleCallbackCIcKunn error kunnId ${kunnId}, error ${error}`);
  }
}

const getKunnPresignInfo = async (debtContractNumber, { availableAmount, lastestLoanAmount}) => {
  try {
    const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(debtContractNumber));
    if (!kunn?.status) {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} not found`, MISA_ERROR_CODE.E412);
    }
    if (kunn?.status !== STATUS.WAITING_CUSTOMER_SIGNATURE) {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} status invalid ${kunn?.status || ''}`, MISA_ERROR_CODE.E413);
    }
    // const avalibleAmountRs = await checkAvailableAmountApi(kunn.contractNumber);
    
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: {
        contractNumber: kunn.contractNumber,
        debtContractNumber: kunn.kunnId,
        availableCreditLimit: availableAmount,
        outstandingPrinBalance: lastestLoanAmount,
        expiredDate: kunn.expiredDate
      },
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2] getKunnPresignInfo error kunnId ${debtContractNumber}, error ${error}`);
    throw error;
  }
}

const signKunnContract = async (payload, context = {}, isTest = false) => {
  const {
    requestId,
    debtContractNumber,
    fileUrl,
    fileType,
    fileSize,
    fileName,
  } = payload;
  let rs;
  try {
    const kunn = helper.snakeToCamel(
      await kunnRepo.getKunnData(debtContractNumber)
    );
    if (!kunn) {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} not found`, MISA_ERROR_CODE.E412);
    }
    if (kunn.status !== STATUS.WAITING_CUSTOMER_SIGNATURE) {
      helper.throwBadReqError(`debtContractNumber`, `Trạng thái hợp Kunn không hợp lệ`, MISA_ERROR_CODE.E413);
    }
    // const expiredDate = moment(kunn.expiredDate, "YYYY-MM-DD");
    // const today = moment().startOf("day");
    // if (today > expiredDate) {
    //   helper.throwBadReqError(`debtContractNumber`,`Kunn ${debtContractNumber} expired ${kunn.expiredDate}`,MISA_ERROR_CODE.E413);
    // }
    const loanEsigning = await loanEsigningRepo.findOne(debtContractNumber);
    if (loanEsigning?.status == "SIGNED") {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} has been signed`, MISA_ERROR_CODE.E413);

    }
    // if (!isTest) {
    //   const isValidSignature = await isValidMisaSignature({
    //     unsignUrl: fileUrl,
    //   });
    //   if (!isValidSignature) {
    //     helper.throwBadReqError(`debtContractNumber`,`Kunn ${debtContractNumber} EC kiểm tra thấy file hợp đồng chưa có chữ ký điện tử của MISA`,MISA_ERROR_CODE.E414);
    //   }
    // }

    //download file contract from misa
    const s3Result = await downloadAndPushDocS3(
      {
        fileUrl,
        fileType,
        fileSize,
        fileName,
      },
      debtContractNumber,
      context
    );
    const fileLocation = s3Result.Location;
    await Promise.all([
      kunnRepo.update(debtContractNumber, {
        status: STATUS.SIGNING_IN_PROGRESS,
        step: KUNN_STEP.ESIGNING,
        esign_contract: fileLocation,
      }),
      saveWorkflow(WORKFLOW_STAGE.SIGNING, STATUS.SIGNING_IN_PROGRESS, kunn.contractNumber, 'misa.api', debtContractNumber)
    ])

    //create debt
    signAndDisburKunn({
      requestId,
      debtContractNumber,
      fileUrl,
      fileName,
      fileSize,
      fileType,
      context,
      isTest,
    });
    rs = {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
    };
    return rs;
  } catch (error) {
    console.log(
      `[MISA][KUNN][V2] signKunnContract error kunnId ${debtContractNumber}, error ${error}`
    );
    rs = error.data || error.message;
    throw error;
  } finally {
    await saveStepLog(
      debtContractNumber,
      SERVICE_NAME.MC_LOS,
      STEP.REQUEST_SIGN_KUNN,
      payload,
      rs
    );
  }
};

const signAndDisburKunn = async ({
  requestId,
  debtContractNumber,
  fileUrl,
  fileType,
  fileSize,
  fileName,
  context = {},
  isTest = false,
}) => {
  try {
    const kunn = helper.snakeToCamel(
      await kunnRepo.getKunnData(debtContractNumber)
    );
    if (kunn?.status !== STATUS.SIGNING_IN_PROGRESS) {
      helper.throwBadReqError(`debtContractNumber`, `Kunn ${debtContractNumber} trạng thái không hợp lệ`, MISA_ERROR_CODE.E413);
    }

    let signedLocationEncrypt = await fillEvfSignatureKunn({
      kunnId: debtContractNumber,
      contractNumber: kunn.contractNumber,
      unsignUrl: fileUrl,
      type: 'VVNH'
    }, context);
    //handle disbursement
    if (!signedLocationEncrypt) {
      helper.throwServerError(`KUNN ${debtContractNumber} ký số thất bại`);
    }
    //createDisbursementRequest
    await createKunnDebt({ kunnId: debtContractNumber });
  } catch (error) {
    console.log(
      `[MISA][KUNN][V2] signAndDisburKunn error kunnId ${debtContractNumber}, error ${error}`
    );
  }
};

const fillEvfSignatureKunn = async (signData, context = {}) => {
  const { kunnId, unsignUrl, contractNumber, type } = signData;
  let pathUnSign = "";
  try {
    const config = global.config;
    const poolWrite = global.poolWrite;

    const signUri = "/esigning/internal/misa/sign-contract";
    const signUrl = config.basic["bss-esigning-service"][config.env] + signUri;
    const misaFileUrl = unsignUrl;
    const bufferUnSign = await getMisaFile({ url: misaFileUrl, context });
    const fileExt = `.pdf`;
    // const fileName = path.basename(misaFileUrl);
    const fileName = `${kunnId}-hop-dong-ku-tin-dung${fileExt}`;
    const s3UnSign = await s3Service.uploadV2(
      config.data,
      moment().format("yyyyMMDDHHmmss") + fileName,
      bufferUnSign,
      storageContractUnSignPath
    );
    pathUnSign = s3UnSign.Key;
    const fileLocationUnSign = s3UnSign.Location;

    const sql =
      "insert into loan_esigning(contract_number,status,unsigned_contract,updated_date) values($1,$2,$3,$4)";
    await poolWrite.query(sql, [
      kunnId,
      "not signed yet",
      fileLocationUnSign,
      new Date(),
    ]);

    //call esigning
    const signedResp = await common.postApiV2(signUrl, {
      contractNumber: kunnId,
      filePath: pathUnSign,
      type
    });

    const signedBuffer = Buffer.from(signedResp.data.data, "base64");
    const encryptSignedBuffer = await encryptFileMisa(signedBuffer);
    const encryptFileName = `encrypt-${fileName}.pgp`;
    const [s3Signed, s3SignedEncrypt] = await Promise.all([
      s3Service.uploadV2(
        config.data,
        moment().format("yyyyMMDDHHmmss") + fileName,
        signedBuffer,
        storageContractSignedPath
      ),
      s3Service.uploadV2(
        config.data,
        moment().format("yyyyMMDDHHmmss") + encryptFileName,
        encryptSignedBuffer,
        storageContractSignedPath,
        "private"
      ),
    ]);

    const pathSigned = s3Signed.Key;
    const fileLocationSigned = s3Signed.Location;
    const signedLocationEncrypt = s3SignedEncrypt.Location;
    const pathSignedEncrypt = s3SignedEncrypt.Key;

    let docBody = {
      type: DOC_TYPE.LCTKU,
      url: fileLocationSigned,
      key: pathSigned,
      docId: uuid.v4(),
      bundleId: DOC_GROUP.MISA_CONTRACT_KUNN,
      fileName: fileName,
    };
    const fileSize = Buffer.from(encryptSignedBuffer).length;
    await Promise.all([
      loanEsigningRepo.saveSignedContract(fileLocationSigned, kunnId),
      saveUpload(
        poolWrite,
        docBody.docId,
        docBody.url,
        docBody.type,
        docBody.bundleId,
        docBody.key,
        docBody.fileName,
        contractNumber,
        kunnId
      ),
      saveUpload(
        poolWrite,
        uuid.v4(),
        signedLocationEncrypt,
        "LCTKU_ENC",
        docBody.bundleId,
        pathSignedEncrypt,
        encryptFileName,
        contractNumber,
        kunnId,
        fileSize
      ),
      kunnRepo.update(kunnId, {
        status: STATUS.SIGNED,
        esigned_contract: fileLocationSigned,
      }),
      saveWorkflow(WORKFLOW_STAGE.SIGNING, STATUS.SIGNED, contractNumber, 'system', kunnId)
    ]);
    return signedLocationEncrypt;
  } catch (e) {
    console.error(
      `[MISA][KUNN][V2][fillEvfSignatureKunn] kunn ${kunnId}, path ${pathUnSign}, error ${e}`
    );
    throw e;
  }
};

const cancelKunn = async ({ debtContractNumber, requestId, reason }) => {
  let rs;
  let kunn;
  try {
    kunn = helper.snakeToCamel(await kunnRepo.getKunnData(debtContractNumber));
    if (!kunn) {
      helper.throwBadReqError(`debtContractNumber`, `kunn ${debtContractNumber} not found`, MISA_ERROR_CODE.E412);

    }
    if (!kunn || kunn.status === STATUS.CANCELLED) {
      helper.throwBadReqError(`debtContractNumber`, `kunn ${debtContractNumber} status invalid or canceled`, MISA_ERROR_CODE.E413)
    }
    //call lms cancel
    await cancelKunnApi(debtContractNumber);
    //
    await kunnRepo.updateKUStatus(debtContractNumber, KUNN_STATUS.CANCELLED);
    rs = {
      code: 0,
      msg: "update contract success",
    };
    return rs;
  } catch (error) {
    console.log(
      `[MISA][KUNN][V2] cancelKunn ${debtContractNumber} error: ${error}`
    );
    rs = error.data || error.message;
    throw error;
  } finally {
    if (kunn?.kunn_id) {
      await saveStepLog(
        debtContractNumber,
        SERVICE_NAME.MC_LOS,
        STEP.CANCEL_LOAN,
        { debtContractNumber, requestId, reason },
        rs
      );
      await saveWorkflow(WORKFLOW_STAGE.CANCELLED, STATUS.CANCELLED, kunn.contractNumber, 'misa.api', debtContractNumber, reason)
    }
  }
};

const getKunnInstallment = async (contractNumber, { availableAmount, lastestLoanAmount } = {}) => {
  try {
    if (!contractNumber) {
      helper.throwBadReqError(`contractNumber`, `contractNumber ${contractNumber} missing`, MISA_ERROR_CODE.E400);
    }
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan) {
      helper.throwBadReqError(`contractNumber`, `contractNumber ${contractNumber} not found`, MISA_ERROR_CODE.E401);

    }
    //call lms info
    const installment = await getKunnInstallmentApi(contractNumber);
    if (!installment) {
      helper.throwServerError(`cannot get installment info`)
    }
    
    const rs = {
      code: ERROR_CODE.SUCCESS,
      message: "success",
      data: {
        contractNumber,
        availableCreditLimit: availableAmount,
        outstandingPrinBalance: lastestLoanAmount,
        paymentSchedules: installment.map(e => mappingInstallment(e))
      }
    }
    console.log(`[MISA][KUNN][V2] getKunnInstallment contractNumber: ${contractNumber}, rs ${JSON.stringify(rs)}`);
    return rs;
  } catch (error) {
    console.log(`[MISA][KUNN][V2] getKunnInstallment ${contractNumber} error: ${error}`);
    throw error;
  }
}

const getUnpaiAmount = (e) => {
  const unpaid = Number(e.totalCycle || 0) - Number(e.paidPeriodAmount || 0);
  return unpaid;
}

const mappingInstallment = (installment, isFormat = false) => {
  const listInstallment = installment.listInstallment?.filter(
    (e) =>
      e.paymentStatus != 1 ||
      getUnpaiAmount(e) > 0 ||
      Number(e.remainingDueInterest) +
      Number(e.remainingDueCapital) +
      Number(e.remainFee) > 0
  ) || [];
  const schedules = [];
  for (const ins of listInstallment) {
    const prinAmt =
      Number(ins.capitalRefunded || 0) - Number(ins.paidDueCapital || 0);
    const lpi =
      Number(ins.lpi || 0) -
      Number(ins.paidLpiPrin || 0) -
      Number(ins.paidLpiIr || 0);
    const feeAmt = Number(ins.fee || 0) - Number(ins.paidFeeAmount || 0);
    const intAmt =
      Number(ins.interest || 0) - Number(ins.paidIrAmount || 0) + lpi;
    const total = getUnpaiAmount(ins) + lpi;
    schedules.push({
      dueDate: moment(ins.dueDate).tz("Asia/Ho_Chi_Minh").format("YYYY-MM-DD"),
      prinAmt: !isFormat ? prinAmt : helper.formatNumberVietnamese(prinAmt),
      intAmt: !isFormat ? intAmt : helper.formatNumberVietnamese(intAmt),
      fee: !isFormat ? feeAmt : helper.formatNumberVietnamese(feeAmt),
      totalPaymentAmt: !isFormat ? total : helper.formatNumberVietnamese(total),
    });
  }

  return {
    debtContractNumber: installment.debtAckContractNumber,
    schedules,
  };
};

const createKunnDebt = async ({ kunnId }) => {
  try {
    const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(kunnId));
    const loanContract = helper.snakeToCamel(
      await loanContractRepo.getLoanContract(kunn.contractNumber)
    );
    const offer = helper.snakeToCamel(
      await offerRepo.getSelectedOfferByKunn(kunnId)
    );
    const productCode = loanContract.productCode;
    const partnerCode = loanContract.partnerCode;
    const contractType = loanContract.contractType;
    const productInfo = await getProductByCodeApi(productCode);
    // let ir = parseFloat(productInfo.productVar[0].intRate / 100)
    let irChargeSmes = [];
    let ir = Number(kunn.ir);
    const rates = productInfo.rate;
    const irType = "4";
    for await (const rate of rates) {
      if (rate.intRateName === "EARLY_TERMINATION_RATE") {
        let irChargeSme = {};
        irChargeSme.irName = rate.intRateName;
        irChargeSme.irType = irType;
        irChargeSme.irValue = rate.intRateVal / 100;
        irChargeSme.tenorFrom = rate.tenorFrom;
        irChargeSme.tenorTo = rate.tenorTo;
        irChargeSme.installmentFrom = rate.installmentFrom;
        irChargeSme.installmentTo = rate.installmentTo;
        irChargeSmes.push(irChargeSme);
      }
    }

    let irCharge = [
      {
        irName: "Lãi xuất lãi quá hạn",
        irType: "3",
        irValue: "0.1",
      },
      {
        irName: "Lãi xuất gốc quá hạn",
        irType: "2",
        irValue: (ir * 1.5).toFixed(4)
      },
      {
        irName: "Lãi xuất gốc",
        irType: "1",
        irValue: `${ir}`,
      },
    ];
    // const contents = CONTENT_DISBURSE;
    // for (const key of Object.keys(contents)) {
    //   contents[key] = contents[key].replace("kunn", kunnId);
    // }
    const kunnDisburInfos = helper.snakeToCamel(
      await kunnDisburInfoRepo.findByKunnId(kunnId)
    );
    const bankInfos = [];
    for (let i = 0; i < kunnDisburInfos.length; i++) {
      const content = SME_CONTENT_DISBURSE.replace("{KUNN}", kunnId).replace(
        "{HD}",
        loanContract.contractNumber
      );
      const bankInfo = kunnDisburInfos[i];
      bankInfos.push({
        accountName: bankInfo.accountName,
        beneficiaryName: bankInfo.beneficiary || bankInfo.accountName,
        bankCode: bankInfo.bankCode,
        bankName: bankInfo.bankName,
        bankAccount: bankInfo.bankAccount,
        disbursedAmount: Number(bankInfo.amount || "0"),
        content: `${content}: ${helper
          .removeVietnameseTones(bankInfo.transferContent || "")
          .trim()}`.substring(0, 255),
      });
    }

    const acronymSmeName = kunn.acronymSmeName;
    if (Object.entries(irChargeSmes).length != 0) {
      irCharge = [...irCharge, ...irChargeSmes];
    }
    const periodicity =
      [PARTNER_CODE.MISA, PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(
        partnerCode
      ) && [CONTRACT_TYPE.CASH_LOAN, "PIM"].includes(contractType)
        ? 1
        : [PARTNER_CODE.MISA, PARTNER_CODE.SMA].includes(partnerCode)
          ? kunn.tenor
          : kunn.method || 6;
    // const dataProduct = await getProduct(kunnInfo.contract.product_code, req);

    const createDebtPayload = {
      contractNumber: kunn.contractNumber,
      debtAckContractNumber: kunnId,
      productCode,
      amount: offer.offerAmt || 0,
      tenor: offer.tenor || 6,
      fee: productInfo.fee,
      periodicity,
      partnerCode: loanContract.partnerCode,
      /**
       * change to list bankInfos when lms fix
       */
      // beneficiaryName: bankInfos[0].beneficiaryName || bankInfos[0].accountName,
      // bankCode: bankInfos[0].bankCode,
      // bankName: bankInfos[0].bankName,
      // bankAccount: bankInfos[0].bankAccount,
      // branchCode: bankInfos[0].bankBranchCode,
      /**
       * change to list bankInfos when lms fix
       */
      partnerTranNo: loanContract.partnerCode,
      irCharge,
      billDay: kunn.billDay || 5,
      graceDayNumber: productInfo.graceDayNumber,
      email: loanContract.smeRepresentationEmail || loanContract.email || "",
      ccycd: "VND",
      contractType,
      bankInfo: bankInfos,
      description: `Easy SME giai ngan KUNN ${kunnId} cho ${acronymSmeName} de thanh toan tien hang`,
    };

    const lmsRs = await createDebtApi(kunnId, createDebtPayload);
    if (lmsRs?.code === 0) {
      await kunnRepo.update(kunnId, {
        status: STATUS.SIGNED_TO_BE_DISBURED,
        step: KUNN_STEP.SIGNED_TO_BE_DISBURED,
      });
      await saveWorkflow(WORKFLOW_STAGE.DISBURSEMENT, STATUS.SIGNED_TO_BE_DISBURED, loanContract.contractNumber, 'system', kunnId);
    } else throw new Error(`LMS ERROR: ${lmsRs?.message}`);
  } catch (error) {
    console.log(
      `[MISA][KUNN][V2][createKunnDebt] kunn ${kunnId}, error ${error}`
    );
  }
};

const callBackKunnCic = async (kunnId, result, { availableAmount, lastestLoanAmount } = {}) => {
  try {
    const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(kunnId));
    // const avalibleAmountRs = await checkAvailableAmountApi(kunn.contractNumber);
    
    // const expiredDate = (moment().add(1,'days')).format('YYYY-MM-DD');
    const expiredDate = helper.formatDate(kunn.endDate, 'YYYY-MM-DD');
    const loanEffectTime = kunn.loanEffectTime ?? LMS_DATE();
    await kunnRepo.update(kunnId,
      {
        expired_date: expiredDate,
        loan_effect_time: loanEffectTime,
        available_amount: availableAmount,
        date_approval: result == true ? moment() : null,
        step: 'SIGN'
      });
    const misaPayload = {
      contractNumber: kunn.contractNumber,
      debtContractNumber: kunnId,
      result,
      loanEffectTime,
      creditInfo: {
        lastestLoanAmount,
        availableAmount,
        expiredDate
      }
    };
    //callback to misa
    const callbackRs = await callbackKunnCicApi(kunnId, misaPayload);
    return callbackRs;
  } catch (error) {
    console.log(`[MISA][KUNN][V2]callBackKunnCic ${kunnId} error ${error} `)
  }
}

const getAvailableLimit = async (contractNumber, { availableAmount, lastestLoanAmount } = {}) => {
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan) {
      helper.throwBadReqError(`contractNumber`, `ContractNumber ${contractNumber} not found`, MISA_ERROR_CODE.E401);
    }
    if (loan.status !== STATUS.ACTIVATED) {
      helper.throwBadReqError(`contractNumber`, `ContractNumber ${contractNumber} status invalid ${loan.status}`, MISA_ERROR_CODE.E401);

    }
    
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: {
        contractNumber: contractNumber,
        availableCreditLimit: availableAmount,
        outstandingPrinBalance: lastestLoanAmount
      },
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2] getAvailableLimit ${contractNumber} error: ${error}`);
    throw error;
  }
}

const isExistedFile = async (fileUrl, context) => {
  try {
    let buffer = await getMisaFile({ url: fileUrl, context });
    if (!buffer || buffer?.length === 0) {
      return false;
    }
    return true;
  } catch (e) {
    console.error(`func | isExistedFile | error:`, e);
    return false;
  }
}

const updateDocGroup = async (contractNumber) => {
  try {
    const loanContract = await loanContractRepo.getLoanContract(contractNumber);
    if (!loanContract?.id) {
      console.log(`updateDocGroup | not found loan_contract with contract_number: ${contractNumber}`);
      return;
    }
    let bundleInfo = await productService.getBundle(global.config, loanContract.product_code ?? 'SME_MISA_HM_SILVER');
    const bundleData = bundleInfo.data;
    const docDict = {}
    bundleData.forEach(bundle => {
      let tmpDocList = bundle.docList
      tmpDocList.forEach(doc => {
        docDict[doc.docType] = bundle.bundleName
        docDict[doc.bundleNameVi] = bundle.bundleNameVi
      })
    })
    const documents = await sqlHelper.find({
      table: 'loan_contract_document',
      whereCondition: {
        is_deleted: 0,
        contract_number: contractNumber
      }
    })

    const documentRs = await getValueCodeByCodeType("DOCUMENT");
    let updatePromises = [];
    documents.map(doc => {
      if (doc?.doc_group) {
        return;
      }
      let docNameVnDetail;
      if (Array.isArray(documentRs) && documentRs.length > 0) {
        docNameVnDetail = documentRs.find(masDoc => masDoc.code == doc.doc_type)?.value
      }
      updatePromises.push(sqlHelper.patchUpdate({
        table: 'loan_contract_document',
        columns: ['doc_group', 'doc_name_vn', 'doc_name_vn_detail'],
        values: sqlHelper.generateValues({
          doc_group: doc.hasOwnProperty('doc_type') ? docDict[doc.doc_type] : (doc.hasOwnProperty('docName') ? docDict[doc.doc_name] : docDict[doc.doc_type]),
          doc_name_vn: docDict[doc.bundleNameVi],
          doc_name_vn_detail: docNameVnDetail
        }, ['doc_group', 'doc_name_vn', 'doc_name_vn_detail']),
        conditions: {
          id: doc.id,
          contract_number: contractNumber
        }
      }))
    })
    await Promise.all(updatePromises);
  } catch (e) {
    console.error(e);
  }
}

const downloadMisaFile = async (url) => {
  let buffer
  try {
    buffer = await getMisaFile({ url });
  } catch (e) {
    console.error(`ERROR | downloadMisaFile | getMisaFile not found | url: `, url);
  }
  return buffer;
}

const downloadMisaFileTest = async (url, clientId, clientSecret, key, passphrase) => {
  let buffer
  try {
    buffer = await getMisaFileTest({ url, clientId, clientSecret, key, passphrase });
  } catch (e) {
    console.error(`ERROR | downloadMisaFile | getMisaFile not found | url: `, url);
  }
  return buffer;
}

const getMisaFileTest = async ({ url, clientId, clientSecret, key, passphrase }) => {
  try {
    const token = await getTokenTest(clientId, clientSecret);
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'text/plain'
    }
    console.log(`[MISA][TEST] bat dau download file`);
    const response = await common.getApiTimeoutV2({ url, headers });
    if (response?.data?.success == false) {
      return undefined
    }
    // const data = fs.readFileSync('/home/<USER>/Documents/misa.txt');
    console.log(`[MISA][TEST] downfile xong`);
    console.log(`[MISA][TEST] giai ma file`);
    key = Buffer.from(key, "base64").toString("utf-8");
    return await decryptFileMisaTest(response?.data, key, passphrase); //buffer
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

const getBankInfo = async ({ accountNumber, beneficiaryName, bankCode }) => {
  let bankInfo = { accountNumber, beneficiaryName, bankCode };
  try {
    bankInfo = await convertEvfLov({
      partnerCode: PARTNER_CODE.MISA,
      convertObject: bankInfo,
    });
    let res = {
      actualName: '',
      ...bankInfo,
    }
    const checkAcountInfo = await checkBankAccountInfoApi({
      bankCode: bankInfo.bankCode,
      accountNumber: bankInfo.accountNumber,
      customerName: bankInfo.beneficiaryName,
    });
    if (!checkAcountInfo) {
      helper.throwServerError(`Check bank account error`);
    }
    res.actualName = checkAcountInfo.actualName || '';//(process.env.NODE_ENV !=='prod' ? 'UAT_TEST NAME' :'');
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: res,
    };
  } catch (error) {
    console.log(`[MISA][getBankInfo] payload: ${JSON.stringify(bankInfo)}, error: ${error}`);
    throw error;
  }
}

const uploadFinancialReport = async ({ contractNumber, revenues }) => {
  try {
    const loan = helper.snakeToCamel(
      await loanContractRepo.getLoanContract(contractNumber)
    );
    if (loan?.status !== STATUS.ACTIVATED) {
      helper.throwServerError(
        `contractNumber ${contractNumber} not found`,
        MISA_ERROR_CODE.E400
      );
    }
    for await (const revenue of revenues) {
      //handle save file to loan revenue
      //download file contract from misa
      for (const doc of revenue.financialReportDocs) {
        const s3Result = await downloadAndPushDocS3(doc, contractNumber);
        // const fileKey = s3Result.Key;
        const fileLocation = s3Result.Location;
        doc.evfFileUrl = fileLocation;
      }
      //save revenu
    }
    await loanContractRepo.insertLoanRevenues(contractNumber, revenues, async (results) => {
      for (const revenueDoc of results || []) {
        revenueDoc.docId = revenueDoc.id;
        await cicReportService.handleExportDocumentData({
          doc: revenueDoc,
          custId: loan.custId,
          contractNumber,
        });
      }
    });
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
    };
  } catch (error) {
    throw error;
  }
};

const uploadDocumentReport = async ({
    contractNumber,
    period,
    financialReportType,
    files
}) => {
  try {
    if (contractNumber) {
      const loan = helper.snakeToCamel(
          await loanContractRepo.getLoanContract(contractNumber)
      );
      if (loan?.status !== STATUS.ACTIVATED) {
        return helper.throwServerError(
            `contractNumber ${contractNumber} not found`,
            MISA_ERROR_CODE.E400
        );
      }

      const cb = async (results) => {
        for (const revenueDoc of results || []) {
          revenueDoc.docId = revenueDoc.id;
          await cicReportService.handleExportDocumentData({
            doc: revenueDoc,
            custId: loan.custId,
            contractNumber,
          });
        }
      };

      for (const file of files) {
        const { fileType, fileUrl, docType } = file

        const fileLocations= await saveAf2DocumentsReplaceOnDuplicate([{
          fileUrl,
          fileType,
          docType,
          period,
        }], contractNumber);

        const evfFileUrl = fileLocations && fileLocations.length > 0 ? fileLocations[0] : "";

        // Nếu docType là SFSTD2 hoặc SNFS2 thì sẽ thực hiện lưu thông tin vào bảng loan_revenues và revenue_documents. Riêng loan_revenues thì chỉ tạo mới khi cặp year(period) và contract_number chưa tồn tại.
        if (docType === DOC_TYPE.SFSTD2 || docType === DOC_TYPE.SNFS2) {
          const revenue = await loanContractRepo.getLoanRevenueByPeriod(contractNumber, period);
          if (!revenue) {
            await loanContractRepo.insertLoanRevenues(contractNumber, [{
              year: period,
              netRevenue: 0,
              totalAssets: 0,
              financialReportType,
              financialReportDocs: [
                {
                  fileUrl,
                  fileType,
                  docType,
                  evfFileUrl,
                }
              ]
            }], cb);
          } else {
            await loanContractRepo.insertRevenueDocuments(revenue.id, [
              {
                fileUrl,
                fileType,
                docType,
                evfFileUrl,
              }
            ], cb);
          }
        }
      }

      return {
        code: ERROR_CODE.SUCCESS,
        message: "success",
      };
    } else {
      return helper.throwServerError(
          `missing contract number`,
          MISA_ERROR_CODE.E400
      );
    }
  } catch (error) {
    throw error;
  }
};

const getInstallments = async ({
    kunnContractNumber,
    date,
}) => {
  try {
    if (kunnContractNumber) {
      const kunn = helper.snakeToCamel(
          await kunnRepo.getKunnData(kunnContractNumber)
      );
      if (![KUNN_STATUS.ACTIVATED, KUNN_STATUS.ACTIVATED_WITH_DIS_DOCS, KUNN_STATUS.TERMINATED].includes(kunn?.status)) {
        return helper.throwServerError(
            `kunnContractNumber ${kunnContractNumber} not found`,
            MISA_ERROR_CODE.E400
        );
      }
    } else {
      return helper.throwServerError(
          `missing contract number`,
          MISA_ERROR_CODE.E400
      );
    }

    let {data, nonAllocationAmt}  = await getKunnInstallmentByKunnApiV2(kunnContractNumber);
    const installments = mappingInstallmentV2(data);

    const trackingDate = date ? moment(date).tz("Asia/Ho_Chi_Minh").format("YYYY-MM-DD") : moment().tz("Asia/Ho_Chi_Minh").format("YYYY-MM-DD");
    const overDueInstallment = getOverDueInstallmentData(
        installments,
        trackingDate
    );
    const todayDueInstallment = getNextDueInstallmentData(
        installments,
        trackingDate
    );

    const rs = {
      kunnContractNumber: kunnContractNumber,
      nonAllocationAmt,
      nextDueInstallment: todayDueInstallment,
      installments,
      overDueInstallment,
    };

    return {
      code: ERROR_CODE.SUCCESS,
      message: "success",
      data: rs
    };
  } catch (error) {
    throw error;
  }
};

const getOverDueInstallmentData = (installments, trackingDate) => {
  const overDueInstallments = installments.filter(
      (e) => e.dueDate < trackingDate && e.paymentStatus == 0
  );
  return mappingDueObj(overDueInstallments);
};

const getNextDueInstallmentData = (
    installments,
    trackingDate,
    isEqDate = false
) => {
  const nextDueInstallment = installments.find((e) =>
      !isEqDate
          ? e.dueDate >= trackingDate && e.isInvoiced
          : e.dueDate == trackingDate && e.isInvoiced
  );
  if (nextDueInstallment) {
    return mappingDueObj([nextDueInstallment]);
  }
  return mappingDueObj([]);
};

const mappingDueObj = (installments) => {
  const _ = {};
  _.prinAmt =
      installments.reduce((sum, item) => sum + item.prinAmt, 0) -
      installments.reduce((sum, item) => sum + item.paidPrinAmt, 0);
  _.interest =
      installments.reduce((sum, item) => sum + item.interest, 0) -
      installments.reduce((sum, item) => sum + item.paidIrAmt, 0);
  _.fee =
      installments.reduce((sum, item) => sum + item.fee, 0) -
      installments.reduce((sum, item) => sum + item.paidFeeAmt, 0);
  _.lpi =
      installments.reduce((sum, item) => sum + item.lpi, 0) -
      installments.reduce((sum, item) => sum + item.paidLpiAmt, 0);
  return _;
};

const mappingInstallmentV2 = (installments) => {
  const schedules = [];
  for (const ins of installments) {
    const _ = {};
    _.dueDate = moment(ins.dueDate).tz("Asia/Ho_Chi_Minh").format("YYYY-MM-DD");
    _.isInvoiced = ins.invoiced ? true : false;
    _.prinAmt = Number(ins.capitalRefunded || 0);
    _.interest = ins.interest;
    _.fee = ins.fee;
    _.lpi = ins.lpi;
    _.paidPrinAmt = Number(ins.paidDueCapital || 0);
    _.paidIrAmt = Number(ins.paidIrAmount || 0);
    _.paidFeeAmt = Number(ins.paidFeeAmount || 0);
    _.paidLpiAmt = Number(ins.paidLpiPrin || 0) + Number(ins.paidLpiIr || 0);
    _.remainPrinAmt = _.prinAmt - _.paidPrinAmt;
    _.remainIrAmt = _.interest - _.paidIrAmt;
    _.remainFeeAmt = _.fee - _.paidFeeAmt;
    _.remainLpi = _.lpi - _.paidLpiAmt;
    _.paymentStatus = ins.paymentStatus;
    schedules.push(_);
  }

  return schedules;
};

/**
 * upload bao cao tham dinh cic noi bo evf
 * @param {*} contractNumber
 * @param {*} revenues
 * @returns
 */
const uploadFinancialReportInternal = async ({ contractNumber, revenues, createdBy }) => {
  try {
    const loan = helper.snakeToCamel(
      await loanContractRepo.getLoanContract(contractNumber)
    );
    if (loan?.status !== STATUS.ACTIVATED) {
      helper.throwServerError(
        `contractNumber ${contractNumber} not found`,
        MISA_ERROR_CODE.E400
      );
    }
    for await (const revenue of revenues) {
      //handle save file to loan revenue
      //download file contract from misa
      for (const doc of revenue.financialReportDocs) {
        let { fileType, fileData } = doc;
        const buffer = Buffer.from(fileData, "base64");
        const fileID = uuid.v4();
        const fileExt = getFileExtension(fileType);
        const fileName = `BCTC_${fileID}_${new Date().getTime()}.${fileExt}`;
        const fileStorage = FILE_STORAGE.storageMisaDocs + `/${contractNumber}`
        const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, fileStorage);
        // const fileKey = s3Result.Key;
        const fileLocation = s3Result.Location;
        doc.evfFileUrl = fileLocation;
        doc.createdBy = createdBy;
      }
      //save revenu
    }
    await loanContractRepo.insertLoanRevenues(contractNumber, revenues, async (results) => {
      for (const revenueDoc of results || []) {
        revenueDoc.docId = revenueDoc.id;
        await cicReportService.handleExportDocumentData({
          doc: revenueDoc,
          custId: loan.custId,
          contractNumber,
        });
      }
    });
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
    };
  } catch (error) {
    throw error;
  }
};

const callbackStatusResult = (respPayload) => {
  const isSuccess = respPayload?.Success == true && respPayload?.StatusCode == 200;
  const statusResult = isSuccess ? 'SUCCESS' : 'FAIL'
  return statusResult;
}

module.exports = {
  getToken,
  callbackCicResult,
  callbackContractFile,
  CIC_STEP_MISA_CALLBACK,
  fillEvfSignature,
  FILE_TYPE_MISA,
  callbackCicDetailToMisa,
  getMisaFile,
  saveAf2Documents,
  saveAf2DocumentsReplaceOnDuplicate,
  scanDownloadDocuments,
  isValidMisaSignature,
  handleCallbackCicKunn,
  downloadAndPushDocS3,
  getKunnPresignInfo,
  signKunnContract,
  cancelKunn,
  getKunnInstallment,
  createKunnDebt,
  callBackKunnCic,
  getAvailableLimit,
  mappingInstallment,
  isExistedFile,
  updateDocGroup,
  downloadMisaFile,
  downloadMisaFileTest,
  getTokenTest,
  getBankInfo,
  uploadFinancialReport,
  uploadDocumentReport,
  getInstallments,
  callbackRefinanceCicDetailToMisa,
  uploadFinancialReportInternal
}
