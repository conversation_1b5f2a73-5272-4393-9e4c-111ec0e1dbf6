
const loanContractRepo = require("../repositories/loan-contract-repo");
const documentRepo = require("../repositories/document");
const sqlHelper = require("../utils/sqlHelper");
const { PARTNER_CODE, SERVICE_NAME, LIST_PARTNER_CODE, request_type, REQUEST_TYPE, TASK_FLOW, DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const { STATUS, CALLBACK_STAUS, KUNN_STATUS } = require("../const/caseStatus");
const { callbackPartner } = require("./callback-service");
const { checkAllAF3DocumentsExist } = require("./creditlimit-service");
const { getValueByCodeType, getValueCodeMasterdataV2, getValueCodeByCodeType } = require("../utils/masterdataService");
const { BadRequestResponse, SuccessResponse, BadRequestResponseV2, ServerErrorResponse } = require("../base/response");
const excelHelper = require("../utils/excel-helper");

const { goNextStep } = require("./workflow-continue");
const kunnRepo = require("../repositories/kunn-repo");
const _ = require("lodash");

const mappingUiFilter = (pagingDto) => {
  const mapping = {
    "representative.identityCard": "sme_representation_id",
  };
  for (let key in pagingDto.filter) {
    if (mapping[key]) {
      pagingDto.filter[mapping[key]] = pagingDto.filter[key];
      delete pagingDto.filter[key];
    }
  }
};

async function getLoanRequest(req, res) {
  const paging = req.paging;
  mappingUiFilter(paging);
  paging.setAllowFilter(["contract_number", "status", "registration_number", "created_date", "from_date", "to_date", "channel"]);
  paging.setAllowSearch(["contract_number", "status", "registration_number"]);
  paging.setDateFilterKey("created_date");
  paging.addFilter({
    channel: req?.query?.channel || [PARTNER_CODE.FINV, PARTNER_CODE.BIZZ, PARTNER_CODE.BZHM],
  });
  let selectKey = ["id", "request_id", "partner_code", "contract_number", "status", "registration_number", "tax_id", "sme_name", "created_date", "channel", "updated_date", "request_amt", "request_int_rate"];

  if (req.query?.channel === PARTNER_CODE.FINV) {
    selectKey.push("product_code", "id_number", "other_id_number", "cust_full_name", "phone_number1", "phone_number2", "phone_number3", "email", "sme_tax_id", "sme_name");
  }
  const data = await loanContractRepo.findAllAndCount(paging, selectKey);
  return new SuccessResponse(data);
}

async function searchKunnLender(req, res) {
  try {
    const { body } = req;
    body.partnerCodes = ["BIZZ", "BZHM", "FINV"];
    body.columns = ["kunn_id", "kunn_code", "partner_code", "contract_number", "beneficiary_name", "bank_account", "bank_code", "bank_branch_code", "total_receivable_amount", "created_date", "updated_date", "with_draw_amount", "status", "available_amount"];
    const result = await kunnRepo.searchKunn(body);
    return new SuccessResponse(result);
  } catch (error) {
    throw new BadRequestResponseV2([], error.message || "Failed to search KUNN");
  }
}

async function handleDocumentsGenerated(req, res) {
  const { contract_number } = req.body;

  if (!contract_number) {
    throw new BadRequestResponse("Missing contract_number");
  }

  const loan = await sqlHelper.findOne({
    table: "loan_contract",
    whereCondition: {
      contract_number: contract_number,
    },
  });

  if (!loan?.id) {
    throw new BadRequestResponse(`Loan not found for contract_number: ${contract_number}`);
  }

  let completed = false;
  if (loan) {
    completed = await checkAllAF3DocumentsExist(contract_number);
  }

  if (completed && loan.current_task === TASK_FLOW.BIZZ_LIMIT_GENERATE_TEMPLATE && loan.status === STATUS.SIGNING_IN_PROGRESS) {
    console.log(`${contract_number} go next step afther check BIZZ_LIMIT_GENERATE_TEMPLATE`);

    //callback to biszi partner
    goNextStep(contract_number);
  } else {
    //log step attempt to generate documents
    console.log(`${contract_number} check documents not generated enough for callback to bizzi partner`);
  }

  return new SuccessResponse();
}

const exportLoanRequest = async (req, res) => {
  const paging = req.paging;
  mappingUiFilter(paging);
  paging.setAllowFilter(["contract_number", "status", "registration_number", "created_date", "from_date", "to_date"]);
  paging.setAllowSearch(["contract_number", "status", "registration_number"]);
  paging.setDateFilterKey("created_date");

  const selects = "contract_number, status,sme_tax_id,sme_name, registration_number, created_date, updated_date";
  const selectKey = selects.split(",").map((item) => item.trim());

  const data = await loanContractRepo.findAllAndCount(paging, selects);

  //export to excel or csv
  const buffer = await excelHelper.jsonToExcelBuffer(data.rows, selectKey);

  return buffer;
};

async function getMasterdata(req, res) {
  const { code_type } = req.query;

  const masterdata = await getValueByCodeType(req, code_type);
  if (!masterdata) {
    throw new BadRequestResponse([], "Masterdata not found for code_type: " + code_type);
  }

  return new SuccessResponse(masterdata, "get masterdata success");
}

module.exports = {
  getLoanRequest,
  searchKunnLender,
  getMasterdata,
  exportLoanRequest,
  handleDocumentsGenerated,
};
