const offerRepo = require("../repositories/offer");
const { getTurnOver } = require("../repositories/turnover-repo");
const loanContractRepo = require("../repositories/loan-contract-repo");
const turnOverRepo = require("../repositories/turnover-repo");
const masterdataService = require("../utils/masterdataService");
const common = require("../utils/common");
const productService = require("../utils/productService");
const getData = require("../utils/getData");
const kunnRepo = require("../repositories/kunn-repo");
const { PARTNER_CODE, SERVICE_NAME, DATE_FORMAT } = require("../const/definition");
const _ = require("underscore");
const dateHelper = require("../utils/dateHelper");
const { CONTRACT_TYPE, TURNOVER_TYPE } = require("../const/definition");
const loanCicScoreRepo = require("../repositories/loan-cic-score");
const pmt = require("formula-pmt");
const helpers = require("../utils/helper");
const loanAttributeRepo = require("../repositories/loan-atribute-repo");
const loanContractDocumentRepo = require("../repositories/document");
const { STATUS, WORKFLOW_STAGE } = require("../const/caseStatus");
const { find, findOne } = require("../utils/sqlHelper");
const moment = require("moment-timezone");
const { LOAN_CUSTOMER_SUBJECT, MASTER_DATA } = require("../const/variables-const");
const { findByContract, insert } = require("../repositories/ui-review-repo");
const { finvA2 } = require("../A2_BASE/a2-app-form-service");
const { saveStepLog, saveWorkflow } = require("../repositories/logging-repo");
const { getProductByCodeApi } = require("../apis/product-api");
const { date } = require("joi/lib");
moment().tz("Asia/Ho_Chi_Minh");
const financialStatementDetailsRepo = require("../repositories/financial_statement_details-repo");

async function getVdsTurnOver(req, res) {
  try {
    const contractNumber = req.query.contractNumber;
    const data = await Promise.all([getTurnOver(contractNumber), offerRepo.getVPLSCore(contractNumber)]);
    const vplScore = data[1];
    const turnoverData = data[0];
    if (!vplScore || !turnoverData) {
      common.log("get vpl error", contractNumber);
    }

    let body = {
      avgIncome: vplScore.average_income || 0,
      totalTurnover: vplScore.total_turn_over || 0,
      offer_1: vplScore.offer_1 || 0,
      maxAnnuity: vplScore.max_annuity || 0,
      turnOver: turnoverData,
    };
    return res.status(200).json({
      code: 1,
      msg: "get viettel pay pro data successfully",
      data: body,
    });
  } catch (err) {
    console.log(err);
    common.log("get vpl error", req.query.contractNumber);
    return res.status(500).json({
      code: -1,
      msg: `get vpl error : ${err.message}`,
    });
  }
}

async function getVTPTurnover(req, res) {
  try {
    const contractNumber = req.query.contractNumber;
    const data = await getTurnOver(contractNumber);
    return res.status(200).json({
      code: 1,
      msg: "get viettel COD data successfully",
      data: data,
    });
  } catch (err) {
    console.log(err);
    common.log("get VTP error", req.query.contractNumber);
    return res.status(500).json({
      code: -1,
      msg: `get vpl error : ${err.message}`,
    });
  }
}

async function getKOVTurnover(req, res) {
  try {
    const contractNumber = req.query.contractNumber;
    const data = await getTurnOver(contractNumber);
    const turnDataList = [];
    for (let key in data) {
      const monthData = data[key];
      monthData.month = key;
      turnDataList.push(monthData);
    }
    return res.status(200).json({
      code: 1,
      msg: "get KOV data successfully",
      data: turnDataList,
    });
  } catch (err) {
    console.log(err);
    common.log("get KOV error", req.query.contractNumber);
    common.responseErrorInternal(res, errs);
  }
}

async function getDIScore(req, res) {
  try {
    const contractNumber = req.query.contractNumber;
    const poolWrite = global.poolWrite;
    const poolRead = global.poolRead;
    const data = await Promise.all([loanContractRepo.getLoanContractJoinMainScore(poolWrite, contractNumber), turnOverRepo.getTurnOverTransaction(contractNumber), loanCicScoreRepo.getCicScore(poolRead, contractNumber), loanAttributeRepo.getDataByContractNumber(contractNumber)]);
    const loanData = data[0];
    const turnTranData = data[1];
    const cicInfor = data[2].rows;
    const loanAttributeData = data[3];
    let longTerm, cardDebt, otherDebt1, debtGroup, shortTermInterest, mediumTermInterest, creditMediumTerm, longTermInterest, shortTermPrincipalAndInterest, mortgageMediumTerm, shortTerm, mediumTermPrincipalAndInterest, cardDebt2, longTermPrincipalAndInterest, monthlyPaymentAtEc, cicInstallment;
    let assetsInfo = {};
    if (loanData?.partner_code == PARTNER_CODE.SMA) {
      assetsInfo.assetsType = loanAttributeData?.assetsType == "PROPERTY" ? "Bất động sản" : loanAttributeData?.assetsType == "CAR" ? "Ô tô" : null;
      assetsInfo.propertyType = loanAttributeData?.propertyType == "HOUSE" ? "Nhà đất" : loanAttributeData?.propertyType == "APARTMENTS" ? "Chung cư" : null;
      assetsInfo.assetsPrice = parseFloat(loanAttributeData?.assetsPrice || 0) || 0;
    }
    cicInfor.map((x) => {
      if (x.score_type == "longTerm") longTerm = x.score_value;
      if (x.score_type == "cardDebt") cardDebt = x.score_value;
      if (x.score_type == "otherDebt1") otherDebt1 = x.score_value;
      if (x.score_type == "shortTerm") shortTerm = x.score_value;
      if (x.score_type == "debtGroup") debtGroup = x.score_value;
      if (x.score_type == "shortTermInterest") shortTermInterest = x.score_value;
      if (x.score_type == "mediumTermPrincipalAndInterest") mediumTermPrincipalAndInterest = x.score_value;
      if (x.score_type == "mediumTermInterest") mediumTermInterest = x.score_value;
      if (x.score_type == "creditMediumTerm") creditMediumTerm = x.score_value;
      if (x.score_type == "longTermInterest") longTermInterest = x.score_value;
      if (x.score_type == "shortTermPrincipalAndInterest") shortTermPrincipalAndInterest = x.score_value;
      if (x.score_type == "mortgageMediumTerm") mortgageMediumTerm = x.score_value;
      if (x.score_type == "cardDebt2") cardDebt2 = x.score_value;
      if (x.score_type == "longTermPrincipalAndInterest") longTermPrincipalAndInterest = x.score_value;
      if (x.score_type == "monthlyPaymentAtEc") monthlyPaymentAtEc = x.score_value;
      if (x.score_type == "cicInstallment") cicInstallment = x.score_value;
    });
    // console.log('cicInfor',cicInfor)
    const turnDataList = [];
    for (let key in turnTranData) {
      const monthData = turnTranData[key];
      monthData.month = key;
      turnDataList.push(monthData);
    }
    const cicInfo = {
      personDebt: {
        shortTerm: shortTerm || 0,
        mortgageMediumTerm: mortgageMediumTerm || 0,
        creditMediumTerm: creditMediumTerm || 0,
        longTerm: longTerm || 0,
        cardDebt: cardDebt || 0,
        otherDebt1: otherDebt1 || 0,
        debtGroup: debtGroup || null,
      },
      legalDebt: {
        shortTermInterest: shortTermInterest || 0,
        mediumTermInterest: mediumTermInterest || 0,
        longTermInterest: longTermInterest || 0,
        shortTermPrincipalAndInterest: shortTermPrincipalAndInterest || 0,
        mediumTermPrincipalAndInterest: mediumTermPrincipalAndInterest || 0,
        longTermPrincipalAndInterest: longTermPrincipalAndInterest || 0,
        cardDebt2: cardDebt2 || 0,
      },
      monthlyPaymentAtEc: monthlyPaymentAtEc || 0,
      cicInstallment: cicInstallment || 0,
    };
    //dat ten transaction12m nhung gia tri cua no la 6m (co the thay doi)
    const finalRs = {
      fullName: loanData.cust_full_name,
      id: loanData.id_number,
      currentProvince: (await masterdataService.getValueCode_v3(loanData.province_cur, "PROVINCE")) || "",
      timeDuration: loanData.time_duration,
      revenueLast6Month: loanData.turnover_12m || 0,
      transactionLast6Month: loanData.transaction12m || 0,
      revenueLast12Month: loanData.turnover_12m || 0,
      transactionLast12Month: loanData.transaction12m || 0,
      numOfDependants: loanData.num_of_dependants || 0,
      monthlyExpenses: loanData.monthly_expenses || loanData.m_household_expenses,
      requestAmt: loanData.request_amt || 0,
      revenue: turnDataList,
      pcbMonthlyPay: !helpers.isNullOrEmptyV2(loanData.pcb_score1) ? loanData.pcb_score1 : 0,
      pcbCardDebt: !helpers.isNullOrEmptyV2(loanData.pcb_score2) ? loanData.pcb_score2 : 0,
      pcbDebtGroup: loanData.pcb_debt_group || 0,
      cicInfo: cicInfo,
      assetsInfo,
    };
    res.status(200).json({
      code: 200,
      msg: "get DI info success",
      data: finalRs,
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      code: 500,
      msg: "get DI info error",
      data: error,
    });
  }
}

function saveChecklistScore(req, res) {
  try {
    const contractNumber = req.body.contractNumber;
    const scoreType = "CHECKLIST";
    // const userName = req.body.userName;
    // const userRole = req.body.userRole;
    const score = req.body.score;
    const totalScore = req.body.totalScore;

    offerRepo.saveScoreDetail(contractNumber, score, scoreType);
    offerRepo.updateMainScore(contractNumber, "total_score", totalScore);

    return res.status(200).json({
      msg: "save score info success",
      code: "200",
    });
  } catch (error) {
    console.log(error);
    res.status(500).json({
      msg: "save score error",
      code: "500",
    });
  }
}

async function getOfferList(req, res) {
  const contractNumber = req.query.contractnumber;
  try {
    const role = req.query.role;
    const data = await Promise.all([loanContractRepo.getPartnerCodeAndProductCode(contractNumber), offerRepo.getOfferList(contractNumber, role)]);

    const productCode = data[0].product_code;
    const partnerCode = data[0].partner_code;
    const offerData = data[1];
    console.log("getOfferList ", JSON.stringify(offerData));
    for (let offer of offerData) {
      offer.offer_amt = partnerCode == "VSK" && Number(offer.offer_amt) < 10000000 ? "0" : offer.offer_amt;
      let intRate = helpers.isNullOrEmpty(offer.int_rate) ? 0 : offer.int_rate;
      offer.emi = Math.round(pmt(Number(intRate) / 12, offer.tenor, -Number(offer.offer_amt)));
    }

    const productData = await productService.getProductInfoV2(productCode);
    // console.log(productData)

    return res.status(200).json({
      msg: "get offer list succes",
      code: 200,
      data: {
        contractnumber: contractNumber,
        offerList: offerData,
        offerInfo: {
          rate: parseFloat(productData.productVar[0].intRate) / 100,
          tenor: productData.productVar[0].defTenor,
        },
      },
    });
  } catch (error) {
    common.log(`get offer error : ${error.message}`, contractNumber);
    res.status(500).json({
      msg: "get offer list error",
      code: 200,
      dadta: error,
    });
  }
}

async function getAllContact(req, res) {
  const poolRead = req.poolRead;
  const contractNumber = req.query.contractNumber;
  if (contractNumber == undefined) {
    return res.status(400).json({
      code: 0,
      msg: "contractNumber is missing.",
    });
  }

  const contractData = await getData.getAllContractData(poolRead, contractNumber);
  if (contractData == null || contractData == undefined) {
    return res.status(400).json({
      code: 0,
      msg: "contractNumber is invalid.",
    });
  }
  const phoneNumberList = [
    {
      type: "Khach hang",
      value: contractData.phone_number1 || "",
      title: contractData.cust_full_name || "",
    },
    {
      type: contractData.reference_type_1 || "",
      value: contractData.reference_phone_1 || "",
      title: contractData.reference_name_1 || "",
    },
    {
      type: contractData.reference_type_2 || "",
      value: contractData.reference_phone_2 || "",
      title: contractData.reference_name_2 || "",
    },
  ];
  return res.status(200).json({
    code: 1,
    msg: "Lấy thông tin contact thành công.",
    data: {
      custId: contractData.cust_id,
      phoneNumberList,
    },
  });
}

async function getCaseSummary(req, res) {
  try {
    let contractNumber = req.query.contractNumber;
    let kunnNumber = contractNumber;
    const isKunn = await kunnRepo.checkIsKU(contractNumber);
    let kunnData;
    if (isKunn) {
      contractNumber = await kunnRepo.getContractByKU(kunnNumber);
      kunnData = await kunnRepo.getKunnData(kunnNumber);
    }

    const contractData = await loanContractRepo.getLoanContract(contractNumber);
    const {
      product_code: productCode
    } = contractData;
    const bodyCheckAvailableAmt = {
      contractNumber,
    };
    const checkAvailableUrl = global.config.basic.lmsMc[req.config.env] + global.config.data.lms.checkAvailable;
    const [
      availableRs,
      productInfo,
      loan,
      businessTypeName,
      businessIndustryName,
      bankName,
      bankBranchName,
      newProvinceOnLicenseName,
      newWardOnLicenseName,
      repIssuePlaceName
    ] = await Promise.all([
      common.postApiV2(checkAvailableUrl, bodyCheckAvailableAmt),
      getProductByCodeApi(productCode),
      loanContractRepo.findLoan(contractNumber),
      masterdataService.getValueCode_v3(contractData?.business_type || "", MASTER_DATA.CODE_TYPE.ENTERPRISE_TYPE),
      masterdataService.getValueCode_v3(contractData?.business_industry || "", MASTER_DATA.CODE_TYPE.SME_EMPLOYMENT_TYPE_4),
      masterdataService.getValueCode_v3(contractData?.bank_code || "", MASTER_DATA.CODE_TYPE.BANK),
      masterdataService.getValueCode_v3(contractData?.bank_branch || "", MASTER_DATA.CODE_TYPE.BANK_BRANCH),
      masterdataService.getValueCode_v3(contractData?.sme_headquarters_province || "", MASTER_DATA.CODE_TYPE.NEW_PROVINCE),
      masterdataService.getValueCode_v3(contractData?.sme_headquarters_ward || "", MASTER_DATA.CODE_TYPE.NEW_WARD),
      masterdataService.getValueCode_v3(contractData?.sme_representation_issue_place || "", MASTER_DATA.CODE_TYPE.ISSUE_PLACE_VN)
    ]);

    const [
      repManagementExperience,
      managerIssuePlaceName,
      businessOwnerIssuePlaceName,
      businessOwnerMarriedStatusName,
      businessOwnerPartnerIssuePlaceName,
      revenue,
      totalAssets
    ] = await Promise.all([
      masterdataService.getValueCode_v3(loan?.loan_customer_representations?.[0]?.management_experience || "", MASTER_DATA.CODE_TYPE.MANAGEMENT_EXPERIENCE),
      masterdataService.getValueCode_v3(loan?.loan_customer_managers?.[0]?.issue_place || "", MASTER_DATA.CODE_TYPE.ISSUE_PLACE_VN),
      masterdataService.getValueCode_v3(loan?.loan_business_owner?.issue_place || "", MASTER_DATA.CODE_TYPE.ISSUE_PLACE_VN),
      masterdataService.getValueCode_v3(loan?.loan_business_owner?.married_status || "", MASTER_DATA.CODE_TYPE.MARRIED_STATUS),
      masterdataService.getValueCode_v3(loan?.loan_business_owner?.partner_issue_place || "", MASTER_DATA.CODE_TYPE.ISSUE_PLACE_VN),
      financialStatementDetailsRepo.getRevenue({ contractNumber }),
      financialStatementDetailsRepo.getTotalAssets({ contractNumber })
    ]);

    // let netRevenueThisYear = null;
    // if (revenue) {
    //   netRevenueThisYear = revenue?.num_of_second_year;
    // }

    let companyInfo = {
      companyName: loan?.sme_name || loan?.company_name,
      taxId: loan?.tax_id,
      registrationNumber: loan?.registration_number || loan?.tax_id || null,
      registrationCertIssueDate: loan?.registration_cert_issue_date ? 
        (helpers.formatDate(loan?.registration_cert_issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
      registrationCertIssuePlace: loan?.registration_cert_issue_place || null,
      addressOnLicense: loan?.address_on_license || null,
      newAddressOnLicense: [contractData?.sme_headquarters_address, newWardOnLicenseName, newProvinceOnLicenseName].join(', ') || null,
      charterCapital: loan?.charter_capital || null,
      businessTypeName: businessTypeName|| null,
      businessIndustryName: businessIndustryName|| null,
      conditionalBusinessIndustry: loan?.conditional_business_industry || null,
      numberOfStaffs: loan?.number_of_staffs || null,
      companyPhoneNumber: loan?.sme_phone_number || loan?.company_phone_number || null,
      smeEmail: loan?.sme_email || loan?.company_email || null,
    };
    let keyFinancialIndicator = {

    };
    let bankInfo = {
      bankName: bankName || null,
      bankAccount: loan?.bank_account || null,
      bankAccountOwner: loan?.bank_account_owner || null,
      bankBranchName: bankBranchName || null,
    };
    let contractCreator = {
      contractCreatorName: loan?.contract_creator_name,
      contractCreatorPosition: loan?.contract_creator_position || null,
      contractCreatorPhoneNumber: loan?.contract_creator_phone_number || null,
      contractCreatorEmail: loan?.contract_creator_email || null
    };
    let smeInfo = {
      representations: {
      fullName: loan?.sme_representation_name || null,
      dateOfBirth: loan?.sme_representation_dob ?
        (helpers.formatDate(loan?.sme_representation_dob, DATE_FORMAT.DDMMYYYY) || null) : null,
      idCardNumber: loan?.sme_representation_id || null,
      issueDate: loan?.sme_representation_issue_date ?
        (helpers.formatDate(loan?.sme_representation_issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
      issuePlace: repIssuePlaceName || null,
      phoneNumber: loan?.sme_representation_phone_number || null,
      email: loan?.sme_representation_email || null,
      perAddress: loan?.sme_representation_per_address || null,
      managementExperience: repManagementExperience || null
      },
      manager: {
      fullName: loan?.sme_manager_name || null,
      idCardNumber: loan?.sme_manager_id || null,
      dateOfBirth: loan?.loan_customer_managers?.[0]?.dob ?
        (helpers.formatDate(loan?.loan_customer_managers?.[0]?.dob, DATE_FORMAT.DDMMYYYY) || null) : null,
      issueDate: loan?.loan_customer_managers?.[0]?.issue_date ?
        (helpers.formatDate(loan?.loan_customer_managers?.[0]?.issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
      issuePlace: managerIssuePlaceName || null,
      phoneNumber: loan?.loan_customer_managers?.[0]?.phone_number || null,
      email: loan?.loan_customer_managers?.[0]?.email || null,
      perAddress: loan?.sme_manager_address_per || null
      },
      owner:{
      subject: loan?.loan_business_owner?.subject || null,
      individual: loan?.loan_business_owner?.subject == LOAN_CUSTOMER_SUBJECT.INDIVIDUAL ? {
        fullName: loan?.loan_business_owner?.full_name || null,
        idCardNumber: loan?.loan_business_owner?.id_number || null,
        dateOfBirth: loan?.loan_business_owner?.dob ?
        (helpers.formatDate(loan?.loan_business_owner?.dob, DATE_FORMAT.DDMMYYYY) || null) : null,
        issueDate: loan?.loan_business_owner?.issue_date ?
        (helpers.formatDate(loan?.loan_business_owner?.issue_date, DATE_FORMAT.DDMMYYYY) || null) : null, 
        issuePlace: businessOwnerIssuePlaceName || null,
        phoneNumber: loan?.loan_business_owner?.phone_number || null,
        email: loan?.loan_business_owner?.email || null,
        perAddress: loan?.loan_business_owner?.address_per || null,
        marriedStatus: businessOwnerMarriedStatusName || null,
        partner: {
        fullName: loan?.loan_business_owner?.partner_full_name || null,
        idCardNumber: loan?.loan_business_owner?.partner_id_number || null,
        dateOfBirth: loan?.loan_business_owner?.partner_dob ? 
          (helpers.formatDate(loan?.loan_business_owner?.partner_dob, DATE_FORMAT.DDMMYYYY) || null) : null,
        issueDate: loan?.loan_business_owner?.partner_issue_date ?
          (helpers.formatDate(loan?.loan_business_owner?.partner_issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
        issuePlace: businessOwnerPartnerIssuePlaceName || null,
        phoneNumber: loan?.loan_business_owner?.partner_phone_number || null,
        email: loan?.loan_business_owner?.partner_email || null,
        perAddress: loan?.loan_business_owner?.partner_address_per || null
        }
      } : null,
      organization: loan?.loan_business_owner?.subject == LOAN_CUSTOMER_SUBJECT.ORGANIZATION ? {
        taxId: loan?.loan_business_owner?.tax_id || null,
        companyName: loan?.loan_business_owner?.company_name || null,
        headquartersAddress: loan?.loan_business_owner?.headquarters_address || null,
        companyPhoneNumber: loan?.loan_business_owner?.business_phone_number || null,
        companyEmail: loan?.loan_business_owner?.business_email || null,
        representation: {
        entityType: loan?.loan_business_owner?.entity_type || null,
        fullName: loan?.loan_business_owner?.full_name || null,
        dateOfBirth: loan?.loan_business_owner?.dob ?
          (helpers.formatDate(loan?.loan_business_owner?.dob, DATE_FORMAT.DDMMYYYY) || null) : null,
        idCardNumber: loan?.loan_business_owner?.id_number || null,
        issueDate: loan?.loan_business_owner?.issue_date ?
          (helpers.formatDate(loan?.loan_business_owner?.issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
        issuePlace: businessOwnerIssuePlaceName || null,
        phoneNumber: loan?.loan_business_owner?.phone_number || null,
        email: loan?.loan_business_owner?.email || null,
        perAddress: loan?.loan_business_owner?.address_per || null
        }
      } : null
      },
      shareholders: [
      ...(loan?.loan_customer_shareholders || []).map(s => ({
        subject: s.subject || null,
        individual: s.subject == LOAN_CUSTOMER_SUBJECT.INDIVIDUAL ? {
            fullName: s.full_name || null,
            idCardNumber: s.id_number || null,
            dateOfBirth: s.dob ? (helpers.formatDate(s.dob, DATE_FORMAT.DDMMYYYY) || null) : null,
            issueDate: s.issue_date ? (helpers.formatDate(s.issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
            issuePlace: s.issue_place || null,
            phoneNumber: s.phone_number || null,
            email: s.email || null,
            perAddress: s.address_per || null,
            capitalContributionRatio: s.capital_contribution_ratio || null
          } : null,
          organization: s.subject == LOAN_CUSTOMER_SUBJECT.ORGANIZATION ? {
            taxId: s.tax_id || null,
            companyName: s.company_name || null,
            headquartersAddress: s.headquarters_address || null,
            companyPhoneNumber: s.business_phone_number || null,
            companyEmail: s.business_email || null,
            capitalContributionRatio: s.capital_contribution_ratio || null,
            representation: {
              fullName: s.full_name || null,
              dateOfBirth: s.dob ? (helpers.formatDate(s.dob, DATE_FORMAT.DDMMYYYY) || null) : null,
              idCardNumber: s.id_number || null,
              issueDate: s.issue_date ? (helpers.formatDate(s.issue_date, DATE_FORMAT.DDMMYYYY) || null) : null,
              issuePlace: s.issue_place || null,
              phoneNumber: s.phone_number || null,
              email: s.email || null
            }
          } : null
        }))
      ],
      branchesAndLoanCustomerWarehouses: [
        ...(loan?.loan_branch_address || []).map(b => ({
          name: b.branch_name || null,
          address: b.branch_address || null,
          type: 'Chi nhánh'
        })),
        ...(loan?.loan_customer_warehouses || []).map(w => ({
          name: w.warehouse_name || null,
          address: w.warehouse_address || null,
          type: 'Kho hàng'
        }))
      ],
      financial: [
        {
          year: revenue?.report_year || null,
          netRevenue: revenue?.num_of_second_year ? parseFloat(revenue?.num_of_second_year) : null,
          totalAssets: totalAssets?.report_year == revenue?.report_year && totalAssets?.num_of_second_year
            ? parseFloat(totalAssets?.num_of_second_year) : null,
        }
      ],
      inputPartners: [
        ...(loan?.loan_customer_partners || [])
          .filter(p => p?.partner_type === 'IN')
          .map(p => ({
            tax_id: p.tax_id || null,
            companyName: p.company_name || null
          }))
      ],
      outPartners: [
        ...(loan?.loan_customer_partners || [])
          .filter(p => p?.partner_type === 'OUT')
          .map(p => ({
            tax_id: p.tax_id || null,
            companyName: p.company_name || null
          }))
      ]
    };

    const availableAmount = parseFloat(availableRs?.data?.data?.avalibleAmount) || 0;
    const remainPrincipalAmount = parseFloat(availableRs?.data?.data?.remainPrincipalAmount) || 0;

    if (isKunn) {
      if (helpers.isNullOrEmpty(kunnData.ir)) kunnData.ir = 0;
    } else {
      if (helpers.isNullOrEmpty(contractData.approval_int_rate)) contractData.approval_int_rate = 0;
    }
    const responseData = {
      debtor: {
        custId: contractData.cust_id,
        name: contractData.cust_full_name,
        firstName: contractData.cust_full_name,
        fullName: contractData.cust_full_name,
        address: contractData.address_cur,
        next: "",
        phones: [
          {
            num: 1,
            phoneNumber: contractData.phone_number1,
          },
        ],
        language: "vietnamese",
        insolvent: "",
      },
      caseInformation: {
        financingType: contractData.contract_type || CONTRACT_TYPE.CASH_LOAN,
        productType: contractData.contract_type || CONTRACT_TYPE.CASH_LOAN,
        productCode: contractData.product_code,
        kunnProductCode: kunnData?.kunn_code || "",
        partnerCode: contractData.partner_code,
        requestedAmount: isKunn ? parseInt(kunnData.with_draw_amount) : parseInt(contractData.request_amt),
        tenor: isKunn ? parseInt(kunnData.tenor) : parseInt(contractData.approval_tenor) || parseInt(contractData.request_tenor),
        status: isKunn ? kunnData.status : contractData.status,
        contractNumber: kunnNumber,
        startDate: isKunn ? kunnData.date_approval : contractData.start_date,
        activedDate: isKunn ? kunnData.date_approval : contractData.start_date,
        endDate: isKunn ? kunnData.end_date : contractData.end_date,
        signedDate: isKunn ? kunnData.date_approval : contractData.approval_date,
        financedAmount: isKunn ? parseInt(kunnData.with_draw_amount) : parseInt(contractData.approval_amt) || parseInt(contractData.request_amt),
        lmsType: contractData.lms_type || contractData.contract_type,
        rate: isKunn ? ((parseFloat(kunnData.ir) * 10000) / 100).toFixed(2) + " %" : !helpers.isNullOrEmpty(contractData.approval_int_rate) ? ((parseFloat(contractData.approval_int_rate) * 10000) / 100).toFixed(2) + " %" : ((parseFloat(contractData.request_int_rate) * 10000) / 100).toFixed(2) + " %",
        channel: "MCC",
        remainAmt: availableAmount,
        factoringTermDays: productInfo?.factoringTermDays || null,
        advanceRate: productInfo?.advanceRate || null,
        maxDurationDay: productInfo?.maxDurationDay || null,
        limitApprovalAmt: contractData.approval_amt ? parseFloat(contractData.approval_amt) : null,
        factoringAmount: isKunn
          ? (kunnData.with_draw_amount
            ? (parseFloat(kunnData.with_draw_amount) ?? null)
            : null)
          : parseFloat(remainPrincipalAmount),
        remainingFactoringLimit: availableAmount ? parseFloat(availableAmount) : null,
      },
      companyInfo: companyInfo || null,
      keyFinancialIndicator: keyFinancialIndicator || null,
      bankInfo: bankInfo || null,
      contractCreator: contractCreator || null,
      smeInfo: smeInfo || null,
    };

    return res.status(200).json({
      code: 1,
      msg: "get case summary successfully.",
      data: responseData,
    });
  } catch (err) {
    common.log(`get /case/summary-info error : ${err.message}`);
    return res.status(500).json({
      code: 0,
      msg: `${err.message}`,
    });
  }
}

async function getKunnDataByCustId(req, res) {
  try {
    const contractNumber = req.query.custId;
    const isAppMc = helpers.isNullOrEmpty(req?.query?.isAppMc) ? false : true;
    const datas = await kunnRepo.getKunnDataByCustId(contractNumber, isAppMc);
    // console.log('datas.length',datas.length)
    let resBody = {};
    let debtAckContractData = [];
    if (datas.length > 0) {
      for (let data of datas) {
        // console.log('data',data)
        resBody.contractNumber = data.contract_number;
        resBody.debtAckContractNumber = data.kunn_id;
        resBody.status = data.status;
        resBody.aprLimitAmt = data.with_draw_amount;
        resBody.startDate = data.start_date ? dateHelper.formatDate(data.start_date, "DD/MM/YYYY") : null;
        resBody.endDate = data.end_date ? dateHelper.formatDate(data.end_date, "DD/MM/YYYY") : null;
        resBody.monthyAmt = 0;
        resBody.remainPriAmt = 0;
        resBody.remainTenor = "6";
        resBody.tenor = "6";
        debtAckContractData.push(resBody);
        resBody = {};
        // console.log('resBody',resBody)
        // console.log('debtAckContractData',debtAckContractData)
      }
    }
    return res.status(200).json({
      code: 1,
      msg: "get KUNN data successfully",
      data: debtAckContractData,
    });
  } catch (err) {
    console.log(err);
    common.log("get KUNN data error", req.query.contractNumber);
  }
}

async function getKunnDataByContractNumber(req, res) {
  try {
    const contractNumber = req.query.contractNumber;
    const datas = await kunnRepo.getKunnDataByContractNumber(contractNumber);
    // console.log('datas.length',datas.length)
    let resBody = {};
    let debtAckContractData = [];
    if (datas.length > 0) {
      for (let data of datas) {
        // console.log('data',data)
        resBody.contractNumber = data.contract_number;
        resBody.debtAckContractNumber = data.kunn_id;
        resBody.status = data.status;
        resBody.aprLimitAmt = data.with_draw_amount;
        resBody.startDate = data.start_date ? dateHelper.formatDate(data.start_date, "DD/MM/YYYY") : null;
        resBody.endDate = data.end_date ? dateHelper.formatDate(data.end_date, "DD/MM/YYYY") : null;
        resBody.monthyAmt = 0;
        resBody.remainPriAmt = 0;
        resBody.remainTenor = "6";
        resBody.tenor = "6";
        resBody.requestId = data?.request_id || "";
        debtAckContractData.push(resBody);
        resBody = {};
      }
    }
    return res.status(200).json({
      code: 1,
      msg: "get KUNN data successfully",
      data: debtAckContractData,
    });
  } catch (err) {
    console.log(err);
    common.log("get KUNN data error", req.query.contractNumber);
  }
}

async function getKunnDataByKunnId(req, res) {
  try {
    const KunnId = req.query.KunnId;
    const data = await kunnRepo.getKunnData(KunnId);
    // console.log('data',data)
    let resBody = {};
    if (data) {
      resBody.debtAckContractNumber = data.kunn_id;
      resBody.status = data.status;
      resBody.aprLimitAmt = data.with_draw_amount;
      resBody.startDate = data.start_date ? dateHelper.formatDate(data.start_date, "DD/MM/YYYY") : null;
      resBody.endDate = data.end_date ? dateHelper.formatDate(data.end_date, "DD/MM/YYYY") : null;
      resBody.monthyAmt = 0;
      resBody.remainPriAmt = data.available_amount || 0;
      resBody.remainTenor = "6";
      resBody.tenor = "6";
      resBody.contractNumber = data.contract_number;
    }
    return res.status(200).json({
      code: 0,
      msg: "get KUNN data successfully",
      data: resBody,
    });
  } catch (err) {
    console.log(err);
    common.log("get KUNN data error", req.query.custId);
  }
}

async function getTurnoverAfterLoan(req, res) {
  try {
    const { contractNumber } = req.query;
    const dataObject = await turnOverRepo.getTurnoverByInfoType(contractNumber, [TURNOVER_TYPE.TURNOVER_AFTER_LOAN, TURNOVER_TYPE.PROFIT_AFTER_LOAN]);
    const dataArray = [];
    for (let key in dataObject) {
      dataArray.push(dataObject[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get turnover data successfully.",
      data: dataArray,
    });
  } catch (err) {
    console.log(err);
    return false;
  }
}

const getReviewDetail = async (req, res) => {
  const contractNumber = req.query.contractNumber;
  if (!contractNumber) {
    return res.status(400).json({
      code: -1,
      msg: "Missing contract number",
    });
  }
  try {
    const loan = helpers.snakeToCamel(await loanContractRepo.getLoanContract(contractNumber));
    if (!loan || ![STATUS.RECEIVED, STATUS.APPROVED].includes(loan.status)) {
      return res.status(400).json({
        code: -1,
        msg: "contractNumber invalid",
      });
    }

    const docs = helpers.snakeToCamel(await loanContractDocumentRepo.getAllDocumentByContractNumber(contractNumber));
    loan.docs = docs;
    return res.status(200).json({
      code: 0,
      msg: "Success",
      data: loan,
    });
  } catch (error) {
    return res.status(500).json({
      code: -1,
      msg: "Internal server error",
    });
  }
};

const getReviewContract = async (req, res) => {
  try {
    const paging = req.paging;
    mappingUiFilter(paging);
    paging.setAllowFilter(["contract_number", "status", "registration_number", "created_date", "from_date", "to_date"]);
    paging.setAllowSearch(["contract_number", "status", "registration_number"]);
    paging.setDateFilterKey("created_date");
    let { count, rows } = await loanContractRepo.findAllAndCount(paging);
    rows = rows.map((e) => mappingReviewUiData(e));
    return res.status(200).json({
      code: 0,
      msg: "Success",
      data: { count, rows },
    });
  } catch (error) {
    console.log(`[UI-SERVICE] getReviewContract error: ${error.message}`);
    return res.status(500).json({
      code: -2,
      msg: "Internal server error",
    });
  }
};

const getReviewContractDetails = async (req, res) => {
  try {
    const contractNumber = req.query.contractNumber;
    if (!contractNumber) {
      return res.status(400).json({
        code: -1,
        msg: "Missing contract number",
      });
    }
    let loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan) {
      return res.status(400).json({
        code: -1,
        msg: "contractNumber invalid",
      });
    }
    const partnerInfo = await findOne({
      table: "loan_customer_shareholders",
      whereCondition: { contract_number: contractNumber, subject: LOAN_CUSTOMER_SUBJECT.PARTNER },
    });
    partnerInfo.docs = [];
    loan.partnerInfo = partnerInfo;
    loan.docs = await loanContractDocumentRepo.getAllDocumentByContractNumber(contractNumber);
    loan = mappingReviewUiData(loan);
    const comments = await findByContract(contractNumber);
    loan = helpers.transformObject(loan, comments);
    return res.status(200).json({
      code: 0,
      msg: "Success",
      data: loan,
    });
  } catch (error) {
    console.log(`[UI-SERVICE] getReviewContractDetails error: ${error.message}`);
    return res.status(500).json({
      code: -2,
      msg: "Internal server error",
    });
  }
};

const mappingUiFilter = (pagingDto) => {
  const mapping = {
    "representative.identityCard": "sme_representation_id",
  };
  for (let key in pagingDto.filter) {
    if (mapping[key]) {
      pagingDto.filter[mapping[key]] = pagingDto.filter[key];
      delete pagingDto.filter[key];
    }
  }
};

const mappingReviewUiData = (loan) => {
  if (!loan) return;
  const partnerInfo = loan.partnerInfo || {};
  const result = {
    createdDate: helpers.formatDateIsoVN(loan.created_date),
    contractNumber: loan.contract_number,
    status: loan.status,
    registrationNumber: loan.registration_number,
    companyName: loan.sme_name || loan.company_name,
    channel: loan.partner_code,
    taxCode: loan.tax_id,
    partnerLoanCode: loan.misaLoanCode || loan.requestId,
    partnerCode: loan.partner_code,
    capitalNeed: loan.capital_need,
    ownerEquity: loan.owner_equity,
    otherCapital: loan.other_capital,
    loanPurpose: loan.loan_purpose,
    businessType: loan.business_type,
    businessIndustry: loan.sector_industry,
    conditionalBusinessIndustry: loan.conditional_business_industry,
    companyPhoneNumber: loan.sme_phone_number,
    companyEmail: loan.sme_email,
    workplaceName: loan.workplace_name,

    loanAmount: loan.request_amt,
    tenor: loan.request_tenor,
    repaymentMethod: loan.repayment_method,
    repaymentSources: loan.repayment_sources,
    billDay: loan.bill_day,
    businessData: JSON.parse(loan.business_data || "{}"),
    workplaceAddress: {
      provinceCode: loan.workplace_province,
      districtCode: loan.workplace_district,
      wardCode: loan.workplace_ward,
      address: loan.workplace_address,
    },
    refPerson1: {
      relationType: loan.reference_type_1,
      fullname: loan.reference_name_1,
      phoneNumber: loan.reference_phone_1,
      identityCard: loan.reference_id_number_1,
    },
    refPerson2: {
      relationType: loan.reference_type_2,
      fullname: loan.reference_name_2,
      phoneNumber: loan.reference_phone_2,
      identityCard: loan.reference_id_number_2,
    },
    customerInfo: {
      fullname: loan.sme_representation_name,
      identityCard: loan.sme_representation_id,
      gender: loan.sme_representation_gender,
      dateOfBirth: helpers.formatDateIsoVN(loan.sme_representation_dob),
      email: loan.sme_representation_email,
      marriedStatus: loan.sme_married_status,
      issueDate: helpers.formatDateIsoVN(loan.sme_representation_issue_date),
      issuePlace: loan.sme_representation_issue_place,
      posiition: loan.sme_representation_position || "",
      yearsOfWork: loan.sme_years_of_work || 0,
      monthlyIncome: Number(loan.monthly_income) || 0,
      otherIncome: Number(loan.other_income) || 0,
      monthlyExpenses: Number(loan.monthly_expenses) || 0,
      perAddress: {
        provinceCode: loan.province_per,
        districtCode: loan.district_per,
        wardCode: loan.ward_per,
        address: loan.address_per,
      },
      curAddress: {
        provinceCode: loan.province_cur,
        districtCode: loan.district_cur,
        wardCode: loan.ward_cur,
        address: loan.address_cur,
      },
    },
    bankInfos: [
      {
        bankAccount: loan.bank_account || "",
        bankCode: loan.bank_code || "",
        bankAccountOwner: loan.bank_account_owner || "",
        bankName: loan.bank_name || "",
      },
    ],
    partnerInfo: {
      fullname: partnerInfo.full_name || "",
      identityCard: partnerInfo.id_number || "",
      dateOfBirth: helpers.formatDateIsoVN(partnerInfo.dob),
      issueDate: helpers.formatDateIsoVN(partnerInfo.issue_date),
      issuePlace: partnerInfo.issue_place || "",
      phoneNumber: partnerInfo.phone_number || "",
      gender: partnerInfo.gender || "",
      perAddress: {
        provinceCode: partnerInfo.per_province_code || "",
        districtCode: partnerInfo.per_district_code || "",
        wardCode: partnerInfo.per_ward_code || "",
        detailAddress: partnerInfo.per_detail_address || "",
      },
    },
    docs: loan.docs?.map((e) => ({
      docType: e.doc_type,
      fileKey: e.file_key,
      fileUrl: e.url,
    })),
  };

  return result;
};

const createReviewContractComments = async (req, res) => {
  try {
    const { contractNumber, comment, paramLocation } = req.body;
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan) {
      return res.status(400).json({
        code: -1,
        msg: "contractNumber invalid",
      });
    }

    const rs = await insert({ contractNumber, comment, paramLocation, step: "AF2" });
    if (!rs) throw Error(`update error`);
    return res.status(200).json({
      code: 0,
      msg: "Success",
    });
  } catch (error) {
    console.log(`[UI-SERVICE] createReviewContractComments error: ${error.message}`);
    return res.status(500).json({
      code: -2,
      msg: "Internal server error",
    });
  }
};

const updateReviewContractStatus = async (req, res) => {
  const { contractNumber, status, createdBy } = req.body;
  if (!contractNumber || !status) {
    return res.status(400).json({
      code: -1,
      msg: "Missing contract number or status",
    });
  }
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (![STATUS.IN_MANUAL_PROCESS, STATUS.IN_MANUAL_PROCESS_A3].includes(loan?.status)) {
      return res.status(400).json({
        code: -1,
        msg: "contractNumber or contact status invalid",
      });
    }
    switch (status) {
      case STATUS.APPROVED:
        const finv = new finvA2(req, res);
        if (loan.status == STATUS.IN_MANUAL_PROCESS) {
          finv.processFlowA2();
        } else {
          //process a3
          finv.processFlowA3();
        }
        break;
      case STATUS.REFUSED:
        await loanContractRepo.updateContractStatus(STATUS.CANCELLED, contractNumber);
        break;
      case STATUS.CP_RESUBMIT:
        await loanContractRepo.updateContractStatus(STATUS.CP_RESUBMIT, contractNumber);
        break;
      default:
        return res.status(400).json({
          code: -1,
          msg: "status invalid",
        });
    }
    return res.status(200).json({
      code: 0,
      msg: "contract update status success",
    });
  } catch (error) {
    console.log(`[UI-SERVICE] updateReviewContractStatus contractNumber ${contractNumber}, status ${status} error: ${error.message}`);
    return res.status(500).json({
      code: -2,
      msg: "Internal server error",
    });
  } finally {
    await saveStepLog(contractNumber, SERVICE_NAME.UI_REVIEW, WORKFLOW_STAGE.UI_REVIEW, req.body || {}, {}, req.originalUrl);
    await saveWorkflow(WORKFLOW_STAGE.UI_REVIEW, status, contractNumber, createdBy);
  }
};

module.exports = {
  getVdsTurnOver,
  getDIScore,
  saveChecklistScore,
  getOfferList,
  getAllContact,
  getVTPTurnover,
  getKOVTurnover,
  getCaseSummary,
  getKunnDataByCustId,
  getKunnDataByContractNumber,
  getKunnDataByKunnId,
  getTurnoverAfterLoan,
  getReviewDetail,
  getReviewContract,
  getReviewContractDetails,
  createReviewContractComments,
  updateReviewContractStatus,
};
