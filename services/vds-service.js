const helper = require("../utils/helper")
const common = require("../utils/common")
const {MANUAL_TASK_CODE, roleCode} = require("../const/definition")
const {STATUS,caseStatusCode} = require("../const/caseStatus")

const offerService = require("../repositories/offer")
const documentRepo = require("../repositories/document")
const loggingRepo = require("../repositories/logging-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")

const productService = require("../utils/productService")
const loggingService = require("../utils/loggingService")
const callbackService = require("../utils/callbackService")
const {isValidResubmit} = require("../utils/validator/logic-validator")
const uuid = require("uuid")
const wfService = require("./workflow-service")
const aadService = require("../utils/aadService")
const contractGw = require("../services/contract-service")

async function checkStatus(req,res) {
    try {
        const poolWrite = global.poolWrite
        const requestId = req.query.request_id
        if(helper.isNullOrEmpty(requestId)) {
            return res.status(400).json({
                code : 'ERROR',
                msg : "request_id is missing"
            })
        }

        const sql = "select contract_number,request_response,request_callback from request_log rl where request_id = $1 order by created_date desc limit 1"
        const queryRs = await poolWrite.query(sql,[requestId])
        if(queryRs.rowCount == 0) {
            return res.status(200).json({
                code : 'DOES_NOT_EXIST',
                msg : 'request_id is not exists.'
            })
        }
        else {
            if(queryRs.rows[0].request_callback == null || queryRs.rows[0].request_callback == '') {
                return res.status(200).json({
                    code : 'EXIST_NOT_CALLBACK',
                    msg : 'The request id is existed.'
                })
            }
            else {
                return res.status(200).json({
                    code : 'EXIST_CALLBACK',
                    "message": "The request id is existed.",
                    data : JSON.parse(queryRs.rows[0].request_callback)
                })
            }
        }
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : 'ERROR',
            msg : "INTERNAL SERVER ERROR"
        })
    }
}

async function selectOffer(req,res) {
    try {
        const {request_id,contract_number,selected_offer_id} = req.body
        const poolWrite = global.poolWrite
        const contractStatus = await loanContractRepo.getContractStaus(contract_number)
        if(contractStatus !== caseStatusCode.KH11 && contractStatus !== STATUS.ACCEPTED_WITH_OFFERS) {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : "Invalid contract_number",
                data : {
                    request_id,
                    contract_number
                }
            })
        }
        const selectOfferRs = await offerService.selectOffer(poolWrite,selected_offer_id,contract_number)
        if(selectOfferRs.result) {
            const responseBody = {
                code : 'RECEIVED',
                msg : 'The offer selection has been received and is being processed.',
                data : {
                    request_id,
                    contract_number
                }
            }
            await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contract_number,request_id,"")
            await loanContractRepo.updateContractStatus(STATUS.SELECTED_OFFER,contract_number)
            contractGw.generateContract(contract_number)
            return res.status(200).json(responseBody)
        }
        else {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : selectOfferRs.msg,
                data : {
                    request_id,
                    contract_number
                }
            })
        }
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : 'SERVER_ERROR',
            msg : "INTERNAL SERVER ERROR"
        })
    }
}

async function resubmitDoc(req,res) {
    try {
        const poolWrite = global.poolWrite
        const {contract_number,request_id,list_doc_resubmit} = req.body
        const contractData = await loanContractRepo.getLoanContract(contract_number)
        const waitingResubmitList = await documentRepo.getResubmitList(contract_number)
        const validResubmit = isValidResubmit(waitingResubmitList,list_doc_resubmit)
        
        if(!waitingResubmitList || !validResubmit) {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : "Invalid list_doc_resubmit",
                data : {
                    contract_number,
                    request_id
                }
            })
        }
        
        const bundleInfo = await productService.getBundle(global.config,contractData.product_code)
		const newDocList = productService.mapBundleGroup(list_doc_resubmit,bundleInfo.data)
        
        const resubmitRs = await documentRepo.resubmitDoc(contract_number,newDocList)
        
        if(resubmitRs.result) {
            req.body.product_code = contractData.product_code
            req.body.currentTask = MANUAL_TASK_CODE.CP_MANUAL.CP_RESUBMIT
            if(contractData.status == STATUS.CP_RESUBMIT) {
                await aadService.pushTaskMcV2(roleCode.CP,contract_number,contractData.contract_type,STATUS.IN_CP_QUEUE)
                await loanContractRepo.updateContractStatus(STATUS.IN_CP_QUEUE,contract_number)
            }
            else if (contractData.status == STATUS.CE_RESUBMIT) {
                await aadService.pushTaskMcV2(roleCode.CE,contract_number,contractData.contract_type,STATUS.IN_CE_QUEUE)
                await loanContractRepo.updateContractStatus(STATUS.IN_CE_QUEUE,contract_number)
            }
            const responseBody = {
                code : 'SUCCESS',
                msg : resubmitRs.msg,
                data : {
                    contract_number,
                    request_id
                }
            }
            await loggingService.saveRequestV2(poolWrite,req.body,responseBody,contract_number,request_id,"")
            return res.status(200).json(responseBody)
        }else {
            return res.status(400).json({
                code : 'INVALID_REQUEST',
                msg : resubmitRs.msg,
                data : {
                    contract_number,
                    request_id
                }
            })
        }
    }
    catch(err) {
        console.log(err)
        return res.status(500).json({
            code : -1,
            msg : "INTERNAL SERVER ERROR"
        })
    }
}

async function getVdsToken() {
    try {
        const config = global.config
        //const url = 'http://125.235.38.229:8080/loan/loan-product-management/public/api/v1/evn_fc/auth/login'
        const url = config.data.partnerCallback.vdsTokenUrl
        const body = {
            "clientId": "vay_pro_evn",
            "clientSecret": "NHFJU%$#dkv124"
        }
        const rs = await common.postApiV2(url,body,{
            'apiKey' : 'F9rxVQlvAtvIfSha'
        })
        common.serviceLog('VDS_TOKEN','',rs)
        if(rs.data.status.code == 'SUCCESS') {
            return rs.data.data.authenticationToken
        }
        else {
            return false
        }
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function callbackVds(body,contractNumber) {
    const token = await getVdsToken()
    if(!token) {
        console.log("callback to VDS result : get token error")
    }
    const headers = { 
            'Authorization': 'Bearer ' + token, 
            'Content-Type': 'application/json',
            'apiKey' : 'F9rxVQlvAtvIfSha'
        }
    const url = config.data.partnerCallback.vdsCallbackUrl
    common.bodyLog('VDS_CALLBACK',contractNumber,body)
    const rs = await common.postApiV2(url,body,headers)
    common.serviceLog('VDS_CALLBACK',contractNumber,rs)
    loggingRepo.saveCallback(contractNumber,body,rs)
    loggingRepo.updateCallback(contractNumber,body)
}



async function callbackRejectVds(contractNumber,partnerCode) {
    const callbackRejectBody = callbackService.bodyCallbackReject(contractNumber,partnerCode)
    loanContractRepo.updateContractStatus(STATUS.REFUSED,contractNumber)
    callbackVds(callbackRejectBody,contractNumber)    
}

async function callbackCancelVds(contractNumber,partnerCode) {
    const callbackCancelBody = callbackService.bodyCallbackCancel(contractNumber,partnerCode)
    loanContractRepo.updateContractStatus(STATUS.CANCELLED,contractNumber)
    callbackVds(callbackCancelBody,contractNumber)    
}

async function callbackApprove(contractNumber,partnerCode) {
    const callbackApprovedBody = await callbackService.bodyCallbackApproved(contractNumber,partnerCode)
    callbackVds(callbackApprovedBody,contractNumber)
}

async function callbackResubmit(contractNumber,resubmitList) {
    const callbackResubmitBody = callbackService.bodyCallBackResubmit(contractNumber,resubmitList)
    callbackVds(callbackResubmitBody,contractNumber)
}

async function callbackOffer(contractNumber) {
    const callbackAltOfferBody = await callbackService.bodyCallbackOffer(contractNumber)
    callbackVds(callbackAltOfferBody,contractNumber)
}

async function callbackActivated(contractNumber) {
    const request_id = 'EC' + uuid.v4()
    
    const body = {
        "request_id": request_id,
        "contract_number": contractNumber,
        "application_status": "ACTIVATED",
        "message": "Activated",
        "reject_reason": ""
    }

    callbackVds(body,contractNumber)
}

async function callbackTerminated(contractNumber) {
    const request_id = 'EC' + uuid.v4()
    
    const body = {
        "request_id": request_id,
        "contract_number": contractNumber,
        "application_status": "TERMINATED",
        "message": "Terminated",
        "reject_reason": ""
    }

    callbackVds(body,contractNumber)
}

module.exports = {
    checkStatus,
    selectOffer,
    resubmitDoc,
    callbackVds,
    callbackRejectVds,
    callbackCancelVds,
    callbackApprove,
    callbackResubmit,
    callbackOffer,
    callbackActivated,
    callbackTerminated
}