const loanContractRepo = require("../repositories/loan-contract-repo");

let routingHandler = null;

const initRouting = (fn) => {
    routingHandler = fn;
};

const goNextStep = async (contract_number) => {
    if (!contract_number) {
        throw new Error("Missing contract number");
    }

    const loan = await loanContractRepo.getLoanContract(contract_number);
    if (!loan) {
        throw new Error(`Loan not found for contract_number: ${contract_number}`);
    }

    if (typeof routingHandler !== 'function') {
        throw new Error("Routing handler not initialized");
    }

    routingHandler(loan);
};

module.exports = {
    goNextStep,
    initRouting,
};