/**
 * Workflow Router Module
 * Giải quyết circular dependency bằng cách tách routing function
 * và sử dụng lazy loading pattern
 */

let workflowService = null;

/**
 * Lazy load workflow service để tránh circular dependency
 * Chỉ load khi thực sự cần sử dụng
 */
function getWorkflowService() {
    if (!workflowService) {
        workflowService = require('./workflow-service');
    }
    return workflowService;
}

/**
 * Routing function wrapper để tránh circular dependency
 * @param {Object} body - Workflow body containing task information
 * @returns {Promise} - Result from workflow routing
 */
function routing(body) {
    const wfService = getWorkflowService();
    return wfService.routing(body);
}

/**
 * Initialize workflow cache
 * @param {Object} poolWrite - Database pool for writing
 * @returns {Promise<boolean>} - Success status
 */
function initWFCache(poolWrite) {
    const wfService = getWorkflowService();
    return wfService.initWFCache(poolWrite);
}

/**
 * Clear the cached workflow service instance
 * Useful for testing or when service needs to be reloaded
 */
function clearCache() {
    workflowService = null;
}

module.exports = {
    routing,
    initWFCache,
    clearCache
};
