const common = require("../utils/common");
const { TASK_FLOW, roleCode, PARTNER_CODE, CONTRACT_TYPE, DOC_TYPE} = require("../const/definition");
const aadGw = require("./aad-service");
const crmGw = require("./crm-service");
const deGw = require("./de-service");
const antiFrauGw = require("./anti-fraud");
const VPLOffer = require("../offer/VPL-offer");
const VTPOffer = require("../offer/VTP-offer");
const VSKOffer = require("../offer/VSK-offer");
const contractGw = require("./contract-service");
const kovDeService = require("./kov-de-service");
const { getLoanContract, getLoanContractJoinMainScore } = require("../repositories/loan-contract-repo");
const { computeOffer, computeOfferFinv } = require("../offer/offer");
const moment = require("moment-timezone");
const { genRequestId, snakeToCamel } = require("../utils/helper");
const loanContractRepo = require("../repositories/loan-contract-repo");
const { callbackPartner, callbackFinv } = require("./callback-service");
const { CALLBACK_STAUS, STATUS, KUNN_STATUS } = require("../const/caseStatus");
const esigningService = require("./esigning-service");
moment().tz("Asia/Ho_Chi_Minh").format();
const cicReportService = require("./cic-report-service");
const lmsService = require("./lms-service");
const { signedTobeDisburseAndCreateLms, checkTD2, checkTD1 } = require("./bizzi-workflow");
const { initRouting } = require("./workflow-continue");
const bizziService = require("../services/bizzi-service.js");
const { checkSumFile } = require("../upload_document/document-checksum");
const loanContractDocumentRepo = require("../repositories/document");
const kunnRepo = require("../repositories/kunn-repo");

async function initWFCache(poolWrite) {
  const sql = "select * from workflow_detail wd join workflow wf on wd.workflow_code = wf.workflow_code;";
  const workflowRs = await poolWrite.query(sql);
  if (workflowRs.rowCount == 0) {
    console.log("Init workflow cache error");
    return false;
  }
  const workflowCache = {};
  for (let i in workflowRs.rows) {
    const wf = workflowRs.rows[i];
    if (!workflowCache.hasOwnProperty(wf.partner_code)) {
      workflowCache[wf.partner_code] = {};
    }
    if (!workflowCache[wf.partner_code].hasOwnProperty(wf.product_code)) {
      workflowCache[wf.partner_code][wf.product_code] = {};
    }
    if (!workflowCache[wf.partner_code][wf.product_code].hasOwnProperty(wf.current_task)) {
      workflowCache[wf.partner_code][wf.product_code][wf.current_task] = wf.next_task;
    }
  }
  return workflowCache;
}

const initWFCacheV2 = async (poolWrite) => {
  const sql = "select * from workflow_detail wd join workflow wf on wd.workflow_code = wf.workflow_code;";
  const workflowRs = await poolWrite.query(sql);
  if (workflowRs.rowCount == 0) {
    console.log("Init workflow cache V2 error");
    return false;
  }
  const workflowCache = {};
  for (const wf of workflowRs.rows) {
    if (!workflowCache[wf.partner_code]) {
      workflowCache[wf.partner_code] = {};
    }
    if (!workflowCache[wf.partner_code][wf.workflow_code]) {
      workflowCache[wf.partner_code][wf.workflow_code] = {};
    }
    if (!workflowCache[wf.partner_code][wf.workflow_code][wf.product_code]) {
      workflowCache[wf.partner_code][wf.workflow_code][wf.product_code] = {};
    }
    workflowCache[wf.partner_code][wf.workflow_code][wf.product_code][wf.current_task] = wf.next_task;
  }
  return workflowCache;
}

async function routing(body) {
  try {
    const workflowCache = global.workflowCache;
    const workflowCacheV2 = global.workflowCacheV2;
    const currentTask = body.current_task || body.currentTask;
    const partnerCode = body.partner_code || body.partnerCode;
    const contractNumber = body.contract_number || body.contractNumber;
    const kunnNumber = body.kunn_id || body.kunnId;
    let productCode = body.product_code || body.partnerCode;
    let loopFlag = true;
    if (!workflowCache[partnerCode].hasOwnProperty(productCode)) {
      productCode = "";
    }
    const contractData = await getLoanContract(contractNumber);
    let nextTask = workflowCache[partnerCode][productCode][currentTask];

    if (partnerCode === PARTNER_CODE.FINV && contractData?.id) {
      const workflowCode = body.workflow_code || body.workflowCode || contractData.workflow_code;
      nextTask = workflowCacheV2[partnerCode][workflowCode][productCode][currentTask];
    }

    body.currentTask = nextTask;
    body.current_task = nextTask;
    switch (nextTask) {
      case TASK_FLOW.CHECK_DEDUP_MC:
        const custId = await crmGw.checkDedupMcV2(body);
        if (custId) {
          loopFlag = true;
          body.cust_id = custId;
        }
        break;
      case TASK_FLOW.CHECK_ELIGIBLE:
        loopFlag = await deGw.checkEligibleMcV2(body);
        break;
      case TASK_FLOW.CHECK_ELIGIBLE_INDIVIDUAL: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_ELIGIBLE_INDIVIDUAL);
        loopFlag = await antiFrauGw.checkEligibleApiIndividual(contractNumber);
        break;
      }
      case TASK_FLOW.CHECK_FULL_LOAN_INDIVIDUAL: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_FULL_LOAN_INDIVIDUAL);
        loopFlag = await antiFrauGw.checkFullLoan(contractNumber);
        break;
      }
      case TASK_FLOW.CHECK_CIC:
        loopFlag = await deGw.baseCheckS37(body);
        break;
      case TASK_FLOW.CHECK_PCB:
        loopFlag = await deGw.checkPCBMcV2(body);
        break;
      case TASK_FLOW.CHECK_DEDUP: {
        common.log("check Dedup ", contractNumber, 1);
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_DEDUP);
        loopFlag = await crmGw.checkDedupCash(contractNumber);
        break;
      }
      case TASK_FLOW.CHECK_DEDUP_SME:
        common.log("check Dedup Sme ", contractNumber, 1);
        loopFlag = await crmGw.checkDedupSme(contractNumber);
        break;
      case TASK_FLOW.CHECK_EKYC:
        common.log("check EKYC ", contractNumber, 1);
        if (partnerCode == PARTNER_CODE.MISA) {
          loopFlag = await deGw.checkEKYCGateWay(contractNumber, partnerCode, true);
        } else {
          loopFlag = await deGw.checkEKYCGateWay(contractNumber, partnerCode);
        }
        break;
      case TASK_FLOW.PUSH_TASK_CP:
        common.log("push CP task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDoc(contractNumber, roleCode.CP);
        break;
      case TASK_FLOW.PUSH_TASK_CE:
        common.log("push CE task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDoc(contractNumber, roleCode.CE);
        break;
      case TASK_FLOW.PUSH_TASK_DE:
        common.log("push DE task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDoc(contractNumber, roleCode.DE);
        break;
      case TASK_FLOW.CE_RESUBMIT_DE:
        common.log("push CE task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDoc(contractNumber, roleCode.DE);
        break;
      case TASK_FLOW.CHECK_DE_PRESCORE:
        common.log("check prescore DE ", contractNumber, 1);
        loopFlag = await deGw.checkPrescore(contractNumber);
        break;
      case TASK_FLOW.CHECK_DE_INTERNAL_SCORE:
        common.log("check internal score DE ", contractNumber, 1);
        loopFlag = await deGw.checkInternalScore(contractNumber);
        break;
      case TASK_FLOW.CHECK_AF1:
        loopFlag = await deGw.checkAf1(contractNumber, partnerCode);
        break;
      case TASK_FLOW.CHECK_AF2:
        loopFlag = await deGw.checkAf2(contractNumber, partnerCode);
        break;
      case TASK_FLOW.COMPUTE_OFFER_VDS:
        common.log("compute offer ", contractNumber, 1);
        loopFlag = await VPLOffer.computeVPLOffer(contractNumber);
        break;
      case TASK_FLOW.COMPUTE_OFFER_VTP:
        case TASK_FLOW.COMPUTE_OFFER_KOV:
        common.log("compute offer ", contractNumber, 1);
        loopFlag = await VTPOffer.computeVTPOffer(contractNumber);
        break;
      case TASK_FLOW.GENERATE_CONTRACT:
        common.log("generate contract ", contractNumber, 1);
        loopFlag = await contractGw.generateContract(contractNumber, partnerCode);
        break;
      case TASK_FLOW.MANUAL_PROCESS:
        common.log("[WORKFLOW] Stop by manual process ", contractNumber, 1);
        loopFlag = false;
        break;
      case TASK_FLOW.CALL_BACK:
        common.log("[WORKFLOW] Stop by call back process ", contractNumber, 1);
        if (partnerCode == PARTNER_CODE.FINV) {
          callbackFinv(contractNumber);
        }
        loopFlag = false;
        break;
      case TASK_FLOW.COMPUTE_OFFER_VSK:
        common.log("compute offer ", contractNumber, 1);
        loopFlag = await VSKOffer.computeVSKOffer(contractNumber);
        break;
      case TASK_FLOW.CHECK_CIC_KU:
        common.log("check CIC KU ", kunnNumber, 1);
        if (partnerCode == PARTNER_CODE.MISA) {
          loopFlag = await deGw.checkCICKUNN(contractNumber, kunnNumber, body.sme_tax_id, true);
        } else {
          loopFlag = await deGw.checkCICKUNN(contractNumber, kunnNumber, body.id_number);
        }
        break;
      case TASK_FLOW.CHECK_PCB_KU:
        common.log("check PCB KU ", kunnNumber, 1);
        if (partnerCode != PARTNER_CODE.MISA) {
          loopFlag = await deGw.checkPCBKUNN(contractNumber, kunnNumber, partnerCode);
        } else {
          loopFlag = await deGw.checkPcbSME(body, kunnNumber);
        }
        break;
      case TASK_FLOW.PUSH_TASK_CP_KU:
        common.log("push CP task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDocKU(kunnNumber, roleCode.CP);
        break;
      case TASK_FLOW.PUSH_TASK_SS_KU:
        common.log("push SS task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDocKU(kunnNumber, roleCode.SS);
        break;
      case TASK_FLOW.PUSH_TASK_CE_KU:
        common.log("push CE task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDocKU(kunnNumber, roleCode.CE);
        break;
      case TASK_FLOW.KOV_CASH_CALCULATE_DI:
        common.log("KOV_CASH_CALCULATE_DI ", contractNumber, 1);
        loopFlag = await kovDeService.gwDICalculation({contractNumber});
        break;
      case TASK_FLOW.PUSH_TASK_SS:
        common.log("push SS task ", contractNumber, 1);
        loopFlag = await aadGw.pushTaskCheckDoc(contractNumber, roleCode.SS);
        break;
      case TASK_FLOW.CHECK_ELIGIBLE_KUNN:
        common.log("check Eligible KUNN: ", kunnNumber, 1);
        body.request_id = genRequestId(partnerCode);
        body.id_number = contractData.id_number;
        body.phone_number1 = contractData.phone_number1;
        body.birth_date = moment(contractData.birth_date).format("YYYY-MM-DD");
        body.id_issue_dt = moment(contractData.id_issue_dt).format("YYYY-MM-DD");
        body.email = contractData?.email;
        body.account_number = contractData?.bank_account;
        body.gender = contractData?.gender;
        body.cust_full_name = contractData?.cust_full_name;
        body.kunn_number = kunnNumber;
        loopFlag = await deGw.checkEligibleKUNN(false, body);
        break;
      case TASK_FLOW.COMPUTE_OFFER:
        common.log("compute offer ", contractNumber, 1);
        if (partnerCode == PARTNER_CODE.FINV) {
          loopFlag = await computeOfferFinv(contractNumber);
        } else {
          const loanMainScore = await getLoanContractJoinMainScore(global.poolWrite, contractNumber);
          loopFlag = await computeOffer(contractNumber, partnerCode, 0, loanMainScore?.di_before_ce || 0);
        }
        break;
      case TASK_FLOW.CHECK_NFC_WITHOUT_C06: {
        loopFlag = await antiFrauGw.checkNfc(contractNumber, false);
        break;
      }
      case TASK_FLOW.CHECK_NFC_WITH_C06: {
        loopFlag = await antiFrauGw.checkNfc(contractNumber, true);
        break;
      }
      case TASK_FLOW.MANUAL_REVIEW: {
        await loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_PROCESS, contractNumber);
        loopFlag = false;
        break;
      }
      case TASK_FLOW.MANUAL_REVIEW_A3: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.MANUAL_REVIEW_A3);
        await loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_PROCESS_A3, contractNumber);
        await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.IN_MANUAL_PROCESS_A3);
        loopFlag = false;
        break;
      }
      case TASK_FLOW.CHECK_EKYC_ANTI_FRAUD: {
        loopFlag = await antiFrauGw.checkEKYC(contractNumber);
        break;
      }
      case TASK_FLOW.CHECK_ELIGIBLE_SME: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_ELIGIBLE_SME);
        loopFlag = await antiFrauGw.checkEligibleSmeV2Gw(body);
        break;
      }
      case TASK_FLOW.CHECK_DEDUP_SME_V2: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_DEDUP_SME_V2);
        loopFlag = await crmGw.checkDedupEnterpriseGw(body);
        break;
      }
      case TASK_FLOW.CHECK_DEDUP_SME_FD: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_DEDUP_SME_FD);
        loopFlag = await crmGw.checkDedupEnterpriseFD(body);
        break;
      }
      case TASK_FLOW.CHECK_CIC_DETAIL_SME: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CIC_DETAIL_SME);
        loopFlag = await antiFrauGw.checkCicDetailSmeGw(body);
        break;
      }
      case TASK_FLOW.KUNN_TD1:
        loopFlag = await checkTD1(body);
        break;
      case TASK_FLOW.KUNN_TD2:
        loopFlag = await checkTD2(body);
        break;
      case TASK_FLOW.CHECK_CIC_B11T_SME: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CIC_B11T_SME);
        loopFlag = await antiFrauGw.checkCicB11tSmeGw(body);
        break;
      }
      case TASK_FLOW.CHECK_CONTRACT_RENEW_DATE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CONTRACT_RENEW_DATE);
        loopFlag = await antiFrauGw.checkContractRenewDateGw(body);
        break;
      }
      case TASK_FLOW.CHECK_MODEL: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_MODEL);
        if (partnerCode == PARTNER_CODE.FINV) {
          loopFlag = await antiFrauGw.checkModelFinvGw(body);
        } else if (partnerCode == PARTNER_CODE.BZHM) {
          loopFlag = await antiFrauGw.checkModelHm(body);
        } else {
          loopFlag = await antiFrauGw.checkModelGw(body);
        }
        break;
      }
      case TASK_FLOW.CHECK_WHITELIST_FULL_LOAN: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_WHITELIST_FULL_LOAN);
        if (partnerCode == PARTNER_CODE.BZHM) {
          loopFlag = await antiFrauGw.checkWhitelistFullLoanHm(body);
        } else {
          loopFlag = await antiFrauGw.checkWhitelistFullLoanGw(body);
        }
        break;
      }
      case TASK_FLOW.MANUAL_REVIEW_A2: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.MANUAL_REVIEW_A2);
        await loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_REVIEW_A2, contractNumber);
        await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.IN_MANUAL_REVIEW_A2);
        loopFlag = false;
        break;
      }
      case TASK_FLOW.BIZZ_LIMIT_GENERATE_TEMPLATE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.BIZZ_LIMIT_GENERATE_TEMPLATE);
        await loanContractRepo.updateContractStatus(STATUS.SIGNING_IN_PROGRESS, contractNumber);
        await contractGw.generateAF3ContractFile(contractNumber);
        loopFlag = false; // Stop workflow after generate AF3 contract, next task will be trigger in af3/handler-documents-generated
        break;
      }
      case TASK_FLOW.SIGN_EVF_SIGNATURE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.SIGN_EVF_SIGNATURE);
        if (partnerCode == PARTNER_CODE.BIZZ) {
          loopFlag = await esigningService.fillEvfSignature({contractNumber,docTypes:['BTTHDTD', 'BTTQDCV', 'BTTCNKPT']});
        } else if (partnerCode == PARTNER_CODE.FINV) {
          loopFlag = await esigningService.fillEvfSignatureFinv({contractNumber});
        }
        else if (partnerCode == PARTNER_CODE.BZHM) {
          // loopFlag = await esigningService.fillEvfSignature({contractNumber,docTypes:['VLDHDTD', 'VLDQDCV']});
          loopFlag = true; //tamj thời chưa có nên set loopflag = true để không dừng workflow
        }
        break;
      }
      case TASK_FLOW.SIGN_EVF_SIGNATURE_KUNN: {
        if (partnerCode == PARTNER_CODE.BIZZ) {
          loopFlag = await esigningService.fillEvfSignatureKunn({contractNumber, debtContractNumber: kunnNumber});
        } else if (partnerCode === PARTNER_CODE.BZHM) {
          loopFlag = await esigningService.fillEvfSignatureKunnV2({contractNumber, debtContractNumber: kunnNumber, partnerCode});
        }
        break;
      }
      case TASK_FLOW.EXPORT_FINANCIAL_REPORT: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.EXPORT_FINANCIAL_REPORT);
        loopFlag = await cicReportService.exportFinancialReportDataGw({contractNumber});
        break;
      }

      case TASK_FLOW.KUNN_GENERATE_TEMPLATE:
        loopFlag = await contractGw.generateKunnContractFile(kunnNumber);
        break;

      case TASK_FLOW.KUNN_CHECK_TEMPLATE_EXIST:
        console.log('Begin step KUNN_CHECK_TEMPLATE_EXIST');
        if (partnerCode === PARTNER_CODE.BZHM) {
          loopFlag = await contractGw.checkGenerateKunnTemplateSuccessV2(kunnNumber, partnerCode)
        } else {
          loopFlag = await contractGw.checkGenerateKunnTemplateSuccess(kunnNumber);
        }
        break;
      case TASK_FLOW.ACTIVE_CREDIT_LIMIT: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.ACTIVE_CREDIT_LIMIT);
        loopFlag = await lmsService.createActiveCreditLimit({contractNumber});
        break;
      }

      case TASK_FLOW.KUNN_SIGNED_TO_BE_DISBURSED:
        loopFlag = await signedTobeDisburseAndCreateLms(kunnNumber);
        break;

      case TASK_FLOW.KUNN_CREATE_LMS_DEBT:
        loopFlag = await lmsService.createDebtV3(kunnNumber);
        break;

      case TASK_FLOW.SEND_LIMIT_FILES_TO_BIZZI: {
        await loanContractRepo.updateLoanContract({
          status: STATUS.WAITING_CUSTOMER_SIGNATURE,
          current_task: TASK_FLOW.SEND_LIMIT_FILES_TO_BIZZI,
          contract_number: contractNumber,
          approval_date: moment().tz("Asia/Ho_Chi_Minh").format("YYYY-MM-DD HH:mm:ss")
        })
        await bizziService.handleCallbackPartner(contractNumber);
        loopFlag = false;
        break;
      }
      case TASK_FLOW.MERCHANT_LIMIT_CREATE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.MERCHANT_LIMIT_CREATE);
        loopFlag = await crmGw.createMerchantLimitGw(body);
        break;
      }

      case TASK_FLOW.KUNN_CREATE_CRM_SERVICE: {
        loopFlag = await crmGw.createKunnGw(body);
        break;
      }
      case TASK_FLOW.CHECK_A3_FILE_INTEGRITY: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_A3_FILE_INTEGRITY);
        await checkSumFile(contractNumber);
        loopFlag = true;
        break;
      }
      case TASK_FLOW.CHECK_C06_WITH_FALSE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_C06_WITH_FALSE);
        loopFlag = await antiFrauGw.checkNfcFinv(contractNumber, false);
        break;
      }
      case TASK_FLOW.CHECK_C06_WITH_TRUE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_C06_WITH_TRUE);
        loopFlag = await antiFrauGw.checkNfcFinv(contractNumber, true);
        break;
      }
      case TASK_FLOW.CHECK_CIC_B11T_INDIVIDUAL: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CIC_B11T_INDIVIDUAL);
        loopFlag = await antiFrauGw.checkCicB11tInvidual(body);
        break;
      }
      case TASK_FLOW.CHECK_CONTRACT_RENEW_DATE_INDIVIDUAL: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CONTRACT_RENEW_DATE_INDIVIDUAL);
        loopFlag = await antiFrauGw.checkRenewDateByIdentityCard(
          body.request_id,
          body.partner_code,
          body.id_number,
          contractNumber,
          TASK_FLOW.CHECK_CONTRACT_RENEW_DATE_INDIVIDUAL
        );
        break;
      }
      case TASK_FLOW.READ_OCR: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.READ_OCR);
        loopFlag = await antiFrauGw.ocrIdCard(body, TASK_FLOW.READ_OCR);
        break;
      }
      case TASK_FLOW.CHECK_EKYC_V2: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_EKYC_V2);
        loopFlag = await antiFrauGw.checkEKYC(body, TASK_FLOW.CHECK_EKYC_V2);
        break;
      }
      case TASK_FLOW.CHECK_BLACK_LIST: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_BLACK_LIST);
        loopFlag = await antiFrauGw.checkBlacklistGw({ contractNumber });
        break;
      }
      case TASK_FLOW.GENERATE_CONTRACT_FINV: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.GENERATE_CONTRACT_FINV);
        loopFlag = await contractGw.generateContract(contractNumber, partnerCode);
        break;
      }
      case TASK_FLOW.APPROVE_AF1: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.APPROVE_AF1);
        await loanContractRepo.updateContractStatus(STATUS.PASSED_REVIEW_A1, contractNumber);
        await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.PASSED_REVIEW_A1);
        loopFlag = false;
        break;
      }

      case TASK_FLOW.CHECK_RENEW_DATE_KUNN: {
        loopFlag = await antiFrauGw.checkRenewDateKunn(kunnNumber);
        if (loopFlag) {
          await kunnRepo.updateKUStatusV2(kunnNumber, KUNN_STATUS.PASSED_CHECK_RENEW_DATE);
        }
        break;
      }

      case TASK_FLOW.CHECK_BLACKLIST_KUNN: {
        loopFlag = await antiFrauGw.checkBlacklistKunn(kunnNumber);
        break;
      }

      case TASK_FLOW.CHECK_EKYC_KUNN: {
        loopFlag = await antiFrauGw.checkEkycKunn(kunnNumber);
        break;
      }

      case TASK_FLOW.CHECK_FULL_LOAN_KUNN: {
        loopFlag = await antiFrauGw.checkFullLoanKunn(kunnNumber);
        break;
      }

      case TASK_FLOW.CHECK_CONTRACT_PROGRESS: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CONTRACT_PROGRESS);
        loopFlag = await antiFrauGw.checkContractProgressGw(contractData, TASK_FLOW.CHECK_CONTRACT_PROGRESS);
        break;
      }
      case TASK_FLOW.CHECK_CONTRACT_ACTIVE: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.CHECK_CONTRACT_ACTIVE);
        loopFlag = await antiFrauGw.checkContractActiveGw(contractData, TASK_FLOW.CHECK_CONTRACT_ACTIVE);
        break;
      }
      case TASK_FLOW.FINV_CHECK_ELIGIBLE_KUNN: {
        loopFlag = await antiFrauGw.checkEligibleKunnFinV(kunnNumber);
        break;
      }
      case TASK_FLOW.APPROVE_AF2: {
        await loanContractRepo.updateCurrentTask(contractNumber, TASK_FLOW.APPROVE_AF2);
        await loanContractRepo.updateContractStatus(STATUS.PASSED_REVIEW_A2, contractNumber);
        await callbackPartner(contractNumber, partnerCode, CALLBACK_STAUS.PASSED_REVIEW_A2);
        loopFlag = true;
        break;
      }
      default:
        common.log("[WORKFLOW] Stop by end task", contractNumber, 1);
        loopFlag = false;
        break;
    }
    // try {
    //     await loanContractRepo.updateLoanContract({
    //         contract_number: contractNumber,
    //         updated_date: new Date(),
    //         current_task: nextTask
    //       })
    // } catch (error) {
    //     console.log(`update currentTask error ${contractNumber}, error ${error.message}`)
    // }
    if (loopFlag) {
      routing(body);
    } else {
      return;
    }
  } catch (err) {
    common.log(`workflow error : ${err.message}`);
    console.log(err);
    return false;
  }
}

initRouting(routing);

module.exports = {
  initWFCache,
  routing,
  initWFCacheV2
};
