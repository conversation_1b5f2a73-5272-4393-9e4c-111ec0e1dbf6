const { generateContractNumber_v2 } = require("../a1_application/a1-input-validator")
const { REQUEST_TYPE, PARTNER_CODE, CONTRACT_TYPE, CHANNEL, TASK_FLOW, RESPONSE_CODE } = require("../const/definition")
const { RESPONSE_MSG, RES_STT_CODE } = require("../const/response-const")
const { convertBody } = require("../utils/converter/convert")
const { isNullOrEmpty, genRequestId } = require("../utils/helper")
const loanContractRepo = require("../repositories/loan-contract-repo")
const loggingService = require("../utils/loggingService")
const { STATUS, KUNN_STATUS } = require("../const/caseStatus")
const { routing } = require("../services/workflow-service")
const productService = require("../utils/productService")
const documentRepo = require("../repositories/document")
const offerRepo = require("../repositories/offer")
const { SuperAppA1 } = require("../A1_BASE/a1-app-form-service")
const loanAttributeRepo = require("../repositories/loan-atribute-repo")
const kunnRepo = require("../repositories/kunn-repo")
const kunnPrepareRepo = require("../repositories/kunn-prepare-attribute-repo")
const { postApiV2 } = require("../utils/common")
const { checkDeWithDrawRequest, computeOfferWithDrawRequest } = require("../services/de-service")
const { serviceEndpoint } = require("../const/config")

async function submitLoanLimit(req) {
    let responseBody = {
        statusCode: RES_STT_CODE.SUCCESS,
        code: RESPONSE_CODE.SUCCESS,
        message: RESPONSE_MSG.APPLICATION_RECEIVED,
        data: {}
    }
    try {
        let body = req.body
        const isWithdrawRequest = body.isWithdrawRequest
        const poolWrite = req.poolWrite
        const requestId = genRequestId(PARTNER_CODE.SMA) 
        body.partnerCode = PARTNER_CODE.SMA
        body.channel = CHANNEL.SMA
        body.lms_type = CONTRACT_TYPE.CREDIT_LINE
        body.requestId = requestId
        body.contractType = CONTRACT_TYPE.CREDIT_LINE
        const convertedBody = convertBody(body, REQUEST_TYPE.BASIC, req.convertCache)
        const contractNumber = await generateContractNumber_v2(req)
        convertedBody.contract_number = contractNumber
        const insertLoanRs = await loanContractRepo.insertLoanContract(convertedBody)
        loggingService.saveRequestV2(poolWrite, convertBody, responseBody, contractNumber, requestId, body.partnerCode)
        if (!insertLoanRs) {
            responseBody.code = RESPONSE_CODE.ERROR
            responseBody.message = RESPONSE_MSG.INSERT_LOAN_ERROR
            responseBody.statusCode = RES_STT_CODE.BAD_REQUEST
            return responseBody
        }
        let docList = body.listDocCollecting;
        const productCode = convertedBody.product_code
        let bundleInfo = await productService.getBundle(req.config, productCode);
        const bundleData = bundleInfo.data;
        docList = productService.mapBundleGroup(docList, bundleData);
        const updateDoc = await documentRepo.saveUploadedDocumentKOV(poolWrite, contractNumber, docList);
        if (!updateDoc) {
            responseBody.code = RESPONSE_CODE.ERROR
            responseBody.message = RESPONSE_MSG.UPLOAD_DOC_ERROR
            responseBody.statusCode = RES_STT_CODE.BAD_REQUEST
            return responseBody
        }
        if (!isNullOrEmpty(body.branchAddress)) {
            loanContractRepo.saveBranchAddress(contractNumber, body.branchAddress);
        }
        // const productRate = await productService.getRateByRequestAmount(500000000);
        // if (productRate != 0) {
        //     loanContractRepo.updateFieldLoanContract(contractNumber, 'request_int_rate', productRate / 100)
        // }
        Promise.all([
            loggingService.saveRequestV2(poolWrite, convertedBody, responseBody, contractNumber, requestId, PARTNER_CODE.SMA),
            offerRepo.createLoanMainScore(contractNumber)
        ])
        responseBody.data.contractNumber = contractNumber
        loanAttributeRepo.save({ contractNumber, field: 'isWithdrawRequest', value: body.isWithdrawRequest })
        if (!isNullOrEmpty(body.carInfo)) {
            const carInfo = body.carInfo
            Promise.all([
                loanAttributeRepo.save({ contractNumber, field: 'carBrand', value: carInfo.carBrand }),
                loanAttributeRepo.save({ contractNumber, field: 'carModel', value: carInfo.carModel }),
                loanAttributeRepo.save({ contractNumber, field: 'carKmTraveled', value: carInfo.carKmTraveled }),
                loanAttributeRepo.save({ contractNumber, field: 'carFuel', value: carInfo.carFuel }),
                loanAttributeRepo.save({ contractNumber, field: 'carManufactureOrigin', value: carInfo.carManufactureOrigin }),
                loanAttributeRepo.save({ contractNumber, field: 'carManufactureYear', value: carInfo.carManufactureYear }),
            ])
            const bodyPredict = {
                "brand": carInfo.carBrand,
                "phien_ban": carInfo.carModel,
                "fuel": carInfo.carFuel,
                "origin": carInfo.carManufactureOrigin,
                "year_manufacture": carInfo.carManufactureYear,
                "odometer": carInfo.carKmTraveled
            }
            const url = req.config.basic.pucpCore[req.config.env] + "/pucp-core/v1/car-model/predict"
            const rsPredict = await postApiV2(url,bodyPredict)
            const priceUpdate = !isNullOrEmpty(rsPredict?.data?.car_price) ? parseFloat(rsPredict?.data?.car_price)*1000000 : 0
            loanAttributeRepo.update({ contractNumber, field: 'assetsPrice', value: priceUpdate > 0 ? priceUpdate : 0 })
        }
        if (!isWithdrawRequest) {
            const curTaskCode = TASK_FLOW.SUBMITED_LOAN_LIMIT
            convertedBody.currentTask = curTaskCode
            routing(convertedBody)
        } else {
            loanContractRepo.updateContractStatus(STATUS.LOAN_LIMIT_PASSED, contractNumber)
        }
        return responseBody
    } catch (error) {
        console.log(error)
        console.log("error when submitLoanLimit: " + error?.message)
        responseBody.code = RESPONSE_CODE.SERVER_ERROR
        responseBody.statusCode = RES_STT_CODE.SERVER_ERROR
        responseBody.message = RESPONSE_MSG.INTERNAL_SERVER_ERROR
        return responseBody
    }
}

async function createKunnWithoutLimit({contractNumber, tenor, ir, bill_day, with_draw_amount, kunnCode}){
    try {
        const kunn_id = await kunnRepo.genKunnNumber()
        // const prepareData = await kunnPrepareRepo.getByContractNumber(contractNumber)
        // const prepareAttribute = await kunnPrepareRepo.getkunnPrepareAttribute()
        // const obj = {}
        // prepareAttribute.map(pa => {
        //     prepareData.map(pd => {
        //         if(pd.code == pa.code) {
        //             obj[pa.code] = pd.value
        //         }
        //     })
        // })
        const loanData = await loanContractRepo.getLoanContract(contractNumber)
        const kunnBody = {
            request_id: genRequestId(`${PARTNER_CODE.SMA}_${PARTNER_CODE.SMA}`),
            partner_code: PARTNER_CODE.SMA,
            customer_name: loanData.cust_full_name,
            contract_number: contractNumber,
            kunn_id,
            tenor,
            ir,
            bill_day,
            with_draw_amount,
            beneficiary_name: loanData.bank_account_owner,
            bank_code: loanData.bank_code,
            bank_account: loanData.bank_account,
            kunn_code: kunnCode
        }
        const listDocCollecting = await documentRepo.getPrepareDocKunnByContractNumber(contractNumber)
        let docObj = []
        listDocCollecting.map(d => {
            let obj = {
                docName: d.doc_type,
                docId: d.doc_id
            }
            docObj.push(obj)
        })
        let documentList = docObj
        const bundleInfo = await productService.getBundle(global.config,kunnCode,undefined,true)
        documentList = productService.mapBundleGroup(documentList,bundleInfo.data)
        const prepareKunnData = await kunnPrepareRepo.getDataByContractNumber(contractNumber)
        let offerBody = {
            contractNumber: kunnBody.contract_number,
            kunnNumber: kunnBody.kunn_id,
            offerAmt: kunnBody.with_draw_amount,
            offerRate: kunnBody.ir,
            offerTenor: kunnBody.tenor,
            offerType: 'STANDARD',
            productCode: prepareKunnData?.kunnCode,
            requestAmt: kunnBody.with_draw_amount,
            available_amount: kunnBody.with_draw_amount,
            requestTenor: kunnBody.tenor,
            status: KUNN_STATUS.RECIEVE
        }
        
        const rsInsert = await Promise.all([
            kunnRepo.insertKunn(kunnBody),
            documentRepo.saveUploadedDocumentKunn(global.poolWrite,kunn_id,documentList),
            offerRepo.saveKUOfferV2(offerBody)
        ])
        if (!rsInsert[0] || !rsInsert[1] || !rsInsert[2]) 
            return false
        return kunnBody
    } catch (error) {
        console.log(error)
        console.log("error when createKunnWithoutLimit: " + error?.message)
        return false
    }
}

async function submitIncreaseLoanLimit(req) {
    let responseBody = {
        statusCode: RES_STT_CODE.SUCCESS,
        code: RESPONSE_CODE.SUCCESS,
        message: RESPONSE_MSG.APPLICATION_RECEIVED,
        data: {}
    }
    try {
        let body = req.body
        // const isWithdrawRequest = body.isWithdrawRequest
        const poolWrite = req.poolWrite
        const requestId = genRequestId(PARTNER_CODE.SMA) 
        const limitContractNumber = body.limitContractNumber
        body.partnerCode = PARTNER_CODE.SMA
        body.channel = CHANNEL.SMA
        body.lms_type = CONTRACT_TYPE.CREDIT_LINE
        body.requestId = requestId
        body.contractType = CONTRACT_TYPE.CREDIT_LINE
        const convertedBody = convertBody(body, REQUEST_TYPE.BASIC, req.convertCache)
        const contractNumber = await generateContractNumber_v2(req)
        convertedBody.contract_number = contractNumber
        const insertLoanRs = await loanContractRepo.insertLoanContract(convertedBody)
        loggingService.saveRequestV2(poolWrite, convertBody, responseBody, contractNumber, requestId, body.partnerCode)
        if (!insertLoanRs) {
            responseBody.code = RESPONSE_CODE.ERROR
            responseBody.message = RESPONSE_MSG.INSERT_LOAN_ERROR
            responseBody.statusCode = RES_STT_CODE.BAD_REQUEST
            return responseBody
        }
        let docList = body.listDocCollecting;
        const productCode = convertedBody.product_code
        let bundleInfo = await productService.getBundle(req.config, productCode);
        const bundleData = bundleInfo.data;
        docList = productService.mapBundleGroup(docList, bundleData);
        const updateDoc = await documentRepo.saveUploadedDocumentKOV(poolWrite, contractNumber, docList);
        if (!updateDoc) {
            responseBody.code = RESPONSE_CODE.ERROR
            responseBody.message = RESPONSE_MSG.UPLOAD_DOC_ERROR
            responseBody.statusCode = RES_STT_CODE.BAD_REQUEST
            return responseBody
        }
        if (!isNullOrEmpty(body.branchAddress)) {
            loanContractRepo.saveBranchAddress(contractNumber, body.branchAddress);
        }
        // const productRate = await productService.getRateByRequestAmount(500000000);
        // console.log({productRate})
        // if (!isNullOrEmpty(productRate)) {
        //     loanContractRepo.updateFieldLoanContract(contractNumber, 'request_int_rate', productRate / 100)
        // }
        Promise.all([
            loggingService.saveRequestV2(poolWrite, convertedBody, responseBody, contractNumber, requestId, PARTNER_CODE.SMA),
            offerRepo.createLoanMainScore(contractNumber),
            loanAttributeRepo.save({ contractNumber, field: 'limitContractNumber', value: limitContractNumber })
        ])
        responseBody.data.contractNumber = contractNumber
        // loanAttributeRepo.save({ contractNumber, field: 'isWithdrawRequest', value: body.isWithdrawRequest })
        if (!isNullOrEmpty(body.carInfo)) {
            const carInfo = body.carInfo
            Promise.all([
                loanAttributeRepo.save({ contractNumber, field: 'carBrand', value: carInfo.carBrand }),
                loanAttributeRepo.save({ contractNumber, field: 'carModel', value: carInfo.carModel }),
                loanAttributeRepo.save({ contractNumber, field: 'carKmTraveled', value: carInfo.carKmTraveled }),
                loanAttributeRepo.save({ contractNumber, field: 'carFuel', value: carInfo.carFuel }),
                loanAttributeRepo.save({ contractNumber, field: 'carManufactureOrigin', value: carInfo.carManufactureOrigin }),
                loanAttributeRepo.save({ contractNumber, field: 'carManufactureYear', value: carInfo.carManufactureYear }),
            ])
            const bodyPredict = {
                "brand": carInfo.carBrand,
                "phien_ban": carInfo.carModel,
                "fuel": carInfo.carFuel,
                "origin": carInfo.carManufactureOrigin,
                "year_manufacture": carInfo.carManufactureYear,
                "odometer": carInfo.carKmTraveled
            }
            const url = req.config.basic.pucpCore[req.config.env] + "/pucp-core/v1/car-model/predict"
            const rsPredict = await postApiV2(url,bodyPredict)
            loanAttributeRepo.save({ contractNumber, field: 'carAmount', value: rsPredict?.data?.car_price || 0 })
        }
        // if (!isWithdrawRequest) {
        const curTaskCode = TASK_FLOW.SUBMITED_LOAN_LIMIT
        convertedBody.currentTask = curTaskCode
        routing(convertedBody)
        // } else {
        //     loanContractRepo.updateContractStatus(STATUS.LOAN_LIMIT_PASSED, contractNumber)
        // }
        return responseBody
    } catch (error) {
        console.log(error)
        console.log("error when submitIncreaseLoanLimit: " + error?.message)
        responseBody.code = RESPONSE_CODE.SERVER_ERROR
        responseBody.statusCode = RES_STT_CODE.SERVER_ERROR
        responseBody.message = RESPONSE_MSG.INTERNAL_SERVER_ERROR
        return responseBody
    }
}

async function calculateOfferWithDrawRequest({contractNumber, kunnCode, paymentMethod, withDrawAmount}){
    let responseBody = {
        statusCode: 400,
        code: RESPONSE_CODE.ERROR,
        message: RESPONSE_MSG.COMPUTE_OFFER_ERROR,
        data: {
            offer: null
        }
    }
    try {
        const config = global.config
        const partnerCode = PARTNER_CODE.SMA
        const urlCheckDe = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].di
        const deRs = await checkDeWithDrawRequest(contractNumber, urlCheckDe, kunnCode)
        const di = deRs?.data?.data?.anuity || 0
        if (di > 0) {
            const urlComputeOffer = config.basic.decisionsV02[config.env] + serviceEndpoint[partnerCode].offer
            let offer = await computeOfferWithDrawRequest(contractNumber, {paymentMethod, diBeforeCE: di, kunnCode, url: urlComputeOffer})
            offer = offer != null ? offer : 0
            console.log(`info check request: offer = ${offer}, withDrawAmount = ${withDrawAmount}`)
            if (parseInt(withDrawAmount) <= offer) {
                responseBody.code = RESPONSE_CODE.SUCCESS
                responseBody.message = RESPONSE_MSG.COMPUTE_OFFER_SUCCESS
            }
            responseBody.data.offer = offer
            responseBody.statusCode = 200
        }
        return responseBody

    } catch (error) {
        responseBody.statusCode = 500
        responseBody.code = RESPONSE_CODE.SERVER_ERROR
        responseBody.message = RESPONSE_MSG.INTERNAL_SERVER_ERROR
        console.log("error when calculateOfferWithDrawRequest: " + error?.message)
    }
}

module.exports = {
    submitLoanLimit,
    createKunnWithoutLimit,
    submitIncreaseLoanLimit,
    calculateOfferWithDrawRequest
}