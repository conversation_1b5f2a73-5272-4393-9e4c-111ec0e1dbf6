const axios = require("axios");
const pdfParse = require("pdf-parse");
const documentRepo = require("../repositories/document");
const { AF3_TEMPLATE_DOCTYPE_SIGNED } = require("../const/variables-const");

async function fetchPDFBuffer(url) {
  const response = await axios.get(url, { responseType: "arraybuffer" });
  return Buffer.from(response.data);
}

function normalizeText(text) {
  return text
    .replace(/\r/g, "")
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .join("\n");
}

async function checkSumFile(contract_number) {
  const docs = await documentRepo.findByContractAndTypes(contract_number, AF3_TEMPLATE_DOCTYPE_SIGNED);
  // let result = [];
  if (docs.length > 0) {
    await Promise.all(
      docs.map(async (doc) => {
        try {
          const docshistory = await documentRepo.findByContractDeletedAndTypes(contract_number, doc.doc_type);
          if (docshistory && doc?.url && docshistory?.url) {
            const result = await comparePDFsFromS3(doc.url, docshistory.url);
            if (result) {
              await documentRepo.updateCommentDocument({ comment: result, id: doc.id });
            }
          }
        } catch (error) {
          console.log(`Error processing contract_number: ${contract_number} with doc ID ${doc?.id}:`, error);
        }
      })
    );
  }
}

async function comparePDFsFromS3(url1, url2) {
  try {
    let message = "";

    const [buffer1, buffer2] = await Promise.all([fetchPDFBuffer(url1), fetchPDFBuffer(url2)]);

    const [pdf1, pdf2] = await Promise.all([pdfParse(buffer1), pdfParse(buffer2)]);

    const text1 = normalizeText(pdf1.text);
    const text2 = normalizeText(pdf2.text);

    if (text1 === text2) {
      message = "2 file PDF giống nhau.";
    } else {
      message = "2 file PDF KHÁC nhau.";

      const lines1 = text1.split("\n");
      const lines2 = text2.split("\n");
      const maxLines = Math.max(lines1.length, lines2.length);

      for (let i = 0; i < maxLines; i++) {
        if (lines1[i] !== lines2[i]) {
          message += `\nDòng ${i + 1} khác nhau:\n`;
          message += `File1: ${lines1[i] || "[trống]"}\n`;
          message += `File2: ${lines2[i] || "[trống]"}\n`;

          // console.log(message);

          // break;
        }
      }
    }

    return message;
  } catch (error) {
    console.log("Lỗi khi tải hoặc xử lý PDF:", error.message);
    return "";
  }
}

module.exports = {
  comparePDFsFromS3,
  checkSumFile,
};
