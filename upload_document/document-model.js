const common = require("../utils/common");
const pgp = require("pg-promise")({
  /* initialization options */
  capSQL: true, // capitalize all generated SQL
});

function getDocList(poolRead, contracNumber) {
  return new Promise(function (resolve, reject) {
    const sql = "select doc_type from loan_contract_document where contract_number = $1 and is_deleted=0";
    poolRead
      .query(sql, [contracNumber])
      .then((result) => {
        if (result.rows === undefined) {
          reject(false);
        } else {
          resolve(result.rows);
        }
      })
      .catch((error) => {
        common.log(error);
        reject(error);
      });
  });
}

function updateDeletedDoc(poolWrite, docList, contracNumber) {
  let promiseQuery = [];
  const sql = "update loan_contract_document set is_deleted=$1 where contract_number=$2 and doc_type = $3";
  docList.forEach((element) => {
    promiseQuery.push(poolWrite.query(sql, [1, contracNumber, element]));
  });

  Promise.all(promiseQuery)
    .then((result) => console.log("updated existed doc to is_deleted"))
    .catch((error) => console.log("upate error " + error));
}

function saveListDocCollecting(poolWrite, docCollecting, contractNumber) {
  let promiseQuery = [];
  const currentDate = new Date();
  let sql = "update loan_contract_document set contract_number=$1,updated_date=$2,is_resubmit=$3 where doc_id = $4";
  docCollecting.forEach((element) => {
    promiseQuery.push(poolWrite.query(sql, [contractNumber, currentDate, 1, element.docId]));
  });

  Promise.all(promiseQuery)
    .then(() => common.log("UPDATE - loan_contract_document : success"))
    .catch((error) => console.log("UPDATE - loan_contract_document : " + error));
}

module.exports = {
  getDocList,
  saveListDocCollecting,
  updateDeletedDoc,
};
