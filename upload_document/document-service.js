const documentModel = require("./document-model");
const common = require("../utils/common");
const { saveRequest } = require("../utils/loggingService");
const productService = require("../utils/productService");
const loanContractRepo = require("../repositories/loan-contract-repo");
const documentRepo = require("../repositories/document");
const aadService = require("../utils/aadService");
const { STATUS } = require("../const/caseStatus.js");
const { roleCode } = require("../const/definition");

async function resubmitDoc(req, res) {
  try {
    const poolWrite = req.poolWrite;
    // const poolRead = req.poolRead;
    const contractNumber = req.body.contractNumber;
    const docList = req.body.listDocResubmit;
    let response;

    const contractData = await loanContractRepo.getLoanContract(contractNumber);

    // const docNameList = docList.map((doc) => doc.docName);
    // let uploadedDocNameList;
    // const uploadedList = await documentModel.getDocList(poolRead, contractNumber);

    // if (!uploadedList) {
    //   uploadedDocNameList = [];
    // }

    const bundleInfo = await productService.getBundle(global.config, contractData.product_code);
    const newDocList = productService.mapBundleGroupKOV(docList, bundleInfo.data);

    await documentRepo.resubmitDoc(contractNumber, newDocList);
    await aadService.completedTaskByRole(contractNumber, "SS");

    const resubmitRole = await loanContractRepo.getLastStepResubmit(contractNumber);
    if (!resubmitRole) {
      common.log(`INVALID SALE SUPPORT RESUBMIT `, contractNumber);
    }
    let status;
    switch (resubmitRole) {
      case roleCode.CP:
        status = STATUS.IN_CP_QUEUE;
        break;
      case roleCode.CE:
        status = STATUS.IN_CE_QUEUE;
        break;
      default:
        status = `IN_${resubmitRole}_QUEUE`;
        break;
    }
    await aadService.pushTaskMcV2(resubmitRole, contractNumber, contractData.contract_type, status);
    await loanContractRepo.updateContractStatus(status, contractNumber);
    response = {
      statusCode: "200",
      body: {
        code: "SUCCESS",
        message: "Documents are uploaded successfully",
        data: {
          contractNumber: contractNumber,
          listDocCollecting: docList.map((doc) => {
            return {
              docName: doc.docName,
              docId: doc.docId,
            };
          }),
        },
      },
    };
    saveRequest(poolWrite, req.body, response, contractNumber);
    res.status(200).send(response);
  } catch (error) {
    console.log(error);
    res.status(500).send({ code: "SERVER_ERROR", message: "server error." });
  }
}

module.exports = {
  resubmitDoc,
};
