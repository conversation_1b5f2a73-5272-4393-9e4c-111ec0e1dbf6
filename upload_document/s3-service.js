const AWS = require("aws-sdk");
const fs = require("fs");
const moment = require("moment-timezone");
moment().tz("Asia/Ho_Chi_Minh").format();
const uuid = require("uuid");
const { SuccessResponse, ServerErrorResponse } = require("../base/response");
const { PARTNER_CODE, LIST_PARTNER_CODE } = require("../const/definition");
const { findByDocID } = require("../repositories/document");
const { ALLOW_FILE_EXTENSION, MIME_TYPE } = require("../const/variables-const");

const upload = (config, fileName, buffer, prefixDocIdPath) => {
  return new Promise((resolve, reject) => {
    try {
      AWS.config.update(config.aws);
      let bucketName = config.awsS3.bucketName;
      let s3 = new AWS.S3({ region: config.awsS3.region });
      let acl = "public-read";
      const param = { Bucket: bucketName, ACL: acl };
      param.Key = prefixDocIdPath.replace("/", "") + "/" + moment().format("yyyyMMDD") + "/" + fileName;
      param.Body = buffer;
      s3.upload(param, {}, async (err, data) => {
        if (err) {
          reject(err);
        }
        resolve(data);
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const uploadV2 = (config, fileName, buffer, prefixDocIdPath, acl = "public-read") => {
  return new Promise((resolve, reject) => {
    try {
      AWS.config.update(config.aws);
      let bucketName = config.awsS3.bucketName;
      let s3 = new AWS.S3({ region: config.awsS3.region });
      const param = { Bucket: bucketName };
      param.Key = prefixDocIdPath.replace("/", "") + "/" + moment().format("yyyyMMDD") + "/" + fileName;
      param.Body = buffer;
      param.ACL = acl;
      s3.upload(param, {}, async (err, data) => {
        if (err) {
          reject(err);
        }
        resolve(data);
      });
    } catch (error) {
      console.log(error);
    }
  });
};

const uploadV3 = (req, res) => {
  const config = global.config.data;

  AWS.config.update(config.aws);
  let bucketName = config.awsS3.bucketName;
  let s3 = new AWS.S3({ region: config.awsS3.region });
  if (req.files == undefined) {
    return res.status(400).json({});
  }
  let files = req.files[0];

  const fileKey = req.query.fileKey;
  // const fieldName = files.fieldname;
  // const originalFileName = files.originalname;
  const param = { Bucket: bucketName };
  param.Key = fileKey;
  param.Body = files.buffer;

  s3.upload(param, {}, async (err, data) => {
    if (err) {
      return res.status(500).json({});
    }
    return res.status(200).json({});
  });
};

const download = (config) => {
  return new Promise((resolve, reject) => {
    AWS.config.update(config.aws);
    let bucketName = config.awsS3.bucketName;
    let s3 = new AWS.S3({ region: config.awsS3.region });
    // let acl = "public-read";
    const param = { Bucket: bucketName, Key: config.awsS3.template_key };
    try {
      s3.getObject(param, (err, data) => {
        if (err) {
          console.log(err);
        } else {
          fs.writeFileSync("./static_file/contract-template.docx", data.Body);
        }
      });
    } catch (error) {
      console.log("cannot get file from: ", bucketName, error);
      return reject("");
    }
  });
};

const downloadFile = (config, key) => {
  return new Promise((resolve, reject) => {
    AWS.config.update(config.aws);
    let bucketName = config.awsS3.bucketName;
    let s3 = new AWS.S3({ region: config.awsS3.region });
    // let acl = "public-read";
    const param = { Bucket: bucketName, Key: key };
    try {
      s3.getObject(param, (err, data) => {
        if (err) {
          console.log(err);
          reject(err);
        }
        resolve(data);
      });
    } catch (error) {
      console.log("cannot get file from: ", bucketName, error);
      return reject("");
    }
  });
};

async function genPresignedUrl(req, res) {
  let { partnerCode, fileName, partner_code, file_name } = req.body;
  if (partnerCode == undefined && partner_code != undefined) {
    partnerCode = partner_code;
  }
  if (fileName == undefined && file_name != undefined) {
    fileName = file_name;
  }
  const userName = req.userName;
  const config = req.config.data;
  const poolWrite = req.poolWrite;
  const id = uuid.v4();

  if (!LIST_PARTNER_CODE.includes(partnerCode)) {
    return res.status(400).json({
      code: -1,
      msg: "partnerCode không hợp lệ.",
    });
  }
  const fileKey = genFileKey(fileName, partnerCode);
  const fileExtend = fileKey.split(".").pop().toLowerCase();
  if (fileExtend == "xlsx") {
    saveUploadFile(poolWrite, id, fileName, fileKey, userName);
    // if (partnerCode == "VSK") {
    //   url = "https://uatapis.easycredit.vn/los-mc-credit/v1/external-doc/upload-doc?fileKey=";
    // } else {
    //   url = "https://uatapis.easycredit.vn/los-mc-credit/v1/external-doc/upload-doc?fileKey=";
    // }
    const url = "https://uatapis.easycredit.vn/los-mc-credit/v1/external-doc/upload-doc?fileKey=";
    return res.status(200).json({
      code: 1,
      msg: "get Presigned URL successfully.",
      data: {
        url: url + `${fileKey}`,
        id,
      },
    });
  }

  let awsConfig;
  let s3Config;
  let S3BucketName;

  awsConfig = config.aws;
  s3Config = config.awsS3;
  S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  let s3Params = {
    Bucket: S3BucketName,
    Key: fileKey,
    Expires: 60 * 8,
  };
  if (fileExtend == "xlsx") {
    s3Params.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  }
  const url = await getPresignUrlPromiseFunction(s3, s3Params);
  saveUploadFile(poolWrite, id, fileName, fileKey, userName);
  return res.status(200).json({
    code: 1,
    msg: "get Presigned URL successfully.",
    data: {
      url,
      id,
    },
  });
}

async function genPresignedUrlV2(req, res) {
  let { partnerCode, fileName, partner_code, file_name } = req.body;
  if (partnerCode == undefined && partner_code != undefined) {
    partnerCode = partner_code;
  }
  if (fileName == undefined && file_name != undefined) {
    fileName = file_name;
  }
  const userName = req.userName;
  const config = req.config.data;
  const poolWrite = req.poolWrite;
  const id = uuid.v4();

  if (partnerCode !== "KOV" && partnerCode != "VPL" && partnerCode != "VTP" && partnerCode != "VSK") {
    return res.status(400).json({
      code: -1,
      msg: "partnerCode không hợp lệ.",
    });
  }
  const fileKey = genFileKeyV2(fileName, partnerCode);
  const fileExtend = fileKey.split(".").pop().toLowerCase();
  if (fileExtend == "xlsx") {
    saveUploadFile(poolWrite, id, fileName, fileKey, userName);
    return res.status(200).json({
      code: 1,
      msg: "get Presigned URL successfully.",
      data: {
        url: `https://uatapis.easycredit.vn/los-mc-credit/v1/external-doc/upload-doc?fileKey=${fileKey}`,
        id,
      },
    });
  }

  let awsConfig;
  let s3Config;
  let S3BucketName;

  awsConfig = config.aws;
  s3Config = config.awsS3;
  S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });
  let s3Params = {
    Bucket: S3BucketName,
    Key: fileKey,
    Expires: 60 * 8,
  };
  if (fileExtend == "xlsx") {
    s3Params.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  }
  const url = await getPresignUrlPromiseFunction(s3, s3Params);
  saveUploadFile(poolWrite, id, fileName, fileKey, userName);
  return res.status(200).json({
    code: 1,
    msg: "get Presigned URL successfully.",
    data: {
      url,
      id,
    },
  });
}

async function genPresignedUrlForSme(req, res) {
  let { partnerCode, fileName, partner_code, file_name } = req.query;
  if (partnerCode == undefined && partner_code != undefined) {
    partnerCode = partner_code;
  }
  if (fileName == undefined && file_name != undefined) {
    fileName = file_name;
  }
  const userName = req.userName;
  const config = req.config.data;
  const poolWrite = req.poolWrite;
  const id = uuid.v4();

  if (![PARTNER_CODE.KOV, PARTNER_CODE.VPL, PARTNER_CODE.VTP, PARTNER_CODE.VSK, PARTNER_CODE.MISA].includes(partnerCode)) {
    return res.status(400).json({
      code: -1,
      msg: "partnerCode không hợp lệ.",
    });
  }

  const fileKey = genFileKey(fileName, partnerCode);
  const fileExtend = fileKey.split(".").pop().toLowerCase();
  if (fileExtend == "xlsx") {
    let url;
    saveUploadFile(poolWrite, id, fileName, fileKey, userName);
    url = "https://uatapis.easycredit.vn/los-mc-credit/v1/external-doc/upload-doc?fileKey=";
    return res.status(200).json({
      code: 1,
      msg: "get Presigned URL successfully.",
      data: {
        url: url + `${fileKey}`,
        id,
      },
    });
  }

  let awsConfig;
  let s3Config;
  let S3BucketName;

  awsConfig = config.aws;
  s3Config = config.awsS3;
  S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  let s3Params = {
    Bucket: S3BucketName,
    Key: fileKey,
    Expires: 60 * 8,
  };
  if (fileExtend == "xlsx") {
    s3Params.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  }
  // if(['flv','f4v','f4p','f4a','f4b','webm','mkv','vob','avi','wmv','rm','amv','mp4','m4p','m4v','mpg','mpg2','mpeg','mpe','mpv'].includes(fileExtend)) {
  //     s3Params.ContentType = "application/octet-stream"
  // }
  const url = await getPresignUrlPromiseFunction(s3, s3Params);
  saveUploadFile(poolWrite, id, fileName, fileKey, userName);
  return res.status(200).json({
    code: 1,
    msg: "get Presigned URL successfully.",
    data: { url, id },
  });
}

async function genMultiplePresigned(files) {
  const config = global.config.data;

  let awsConfig = config.aws;
  let s3Config = config.awsS3;
  let S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  if (!Array.isArray(files) || files.length === 0) {
    return [];
  }

  try {
    const urls = await Promise.all(
      files.map(({ file_key, file_name, doc_type }) => {
        const s3Params = {
          Bucket: S3BucketName,
          Key: file_key,
          Expires: s3Config?.presignDuration || 60 * 10,
          ResponseContentDisposition: `attachment; filename="${file_name || file_key}"`,
        };
        return new Promise((resolve, reject) => {
          s3.getSignedUrl("getObject", s3Params, (err, url) => {
            if (err) return resolve({ file_key, error: err.message });
            resolve({ docType: doc_type, fileName: file_name, presignedUrl: url });
          });
        });
      })
    );
    return urls;
  } catch (error) {
    return [];
  }
}

async function genPresignedDownloadUrlForSme(req, res) {
  let { file_key, doc_id } = req.body;

  const config = req.config.data;

  let awsConfig = config.aws;
  let s3Config = config.awsS3;
  let S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  let s3Params = {
    Bucket: S3BucketName,
    Key: file_key,
    Expires: s3Config?.presignDuration || 60 * 10, // 10 minutes
    ResponseContentDisposition: `attachment; filename="${file_key}"`, // Only for getObject
  };

  try {
    // Use 'getObject' for download
    const url = await new Promise((resolve, reject) => {
      s3.getSignedUrl("getObject", s3Params, (err, data) => {
        if (err) return reject(err);
        resolve(data);
      });
    });

    return new SuccessResponse({ url, doc_id }, "get Presigned Download URL successfully.");
  } catch (error) {
    return new ServerErrorResponse([], "Failed to generate presigned download URL", error);
  }
}

async function genPresignedUploadUrlForSme(req, res) {
  let { file_name, partner_code, doc_type } = req.body;

  const userName = req.userName;
  const config = req.config.data;
  const poolWrite = req.poolWrite;

  const id = uuid.v4();
  const file_key = genFileKey(file_name, partner_code);

  let awsConfig = config.aws;
  let s3Config = config.awsS3;
  let S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  const fileExtend = file_name.split(".").pop().toLowerCase();
  console.log("fileExtend", fileExtend);
  const ContentType = MIME_TYPE[fileExtend] ?? "application/octet-stream";

  let s3Params = {
    ContentType,
    Bucket: S3BucketName,
    Key: file_key,
    Expires: s3Config?.presignDuration || 60 * 10, // 10 minutes
    // ContentDisposition: `attachment; filename="${file_name}"`, // Optional
  };

  try {
    const url = await getPresignUrlPromiseFunction(s3, s3Params);
    const publicUrl = await genPublicUrl(file_key);
    saveUploadPresignFile(poolWrite, id, file_name, file_key, doc_type, userName, publicUrl);

    return new SuccessResponse({ url, id }, "generate presigned upload URL successfully.");
  } catch (error) {
    throw new ServerErrorResponse([], "Failed to generate presigned upload URL", error);
  }
}

async function genMultiplePresignedDownloadUrlForSme(files) {
  const config = global.config.data;

  let awsConfig = config.aws;
  let s3Config = config.awsS3;
  let S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  if (!Array.isArray(files) || files.length === 0) {
    return [];
  }

  try {
    const urls = await Promise.all(
      files.map(({ file_key, file_name, doc_id }) => {
        const s3Params = {
          Bucket: S3BucketName,
          Key: file_key,
          Expires: s3Config?.presignDuration || 60 * 10,
          ResponseContentDisposition: `attachment; filename="${file_name || file_key}"`,
        };
        return new Promise((resolve, reject) => {
          s3.getSignedUrl("getObject", s3Params, (err, url) => {
            if (err) return resolve({ file_key, error: err.message });
            resolve({ file_name, url, doc_id });
          });
        });
      })
    );
    return urls;
  } catch (error) {
    return [];
  }
}

function saveUploadFile(poolWrite, docId, fileName, fileKey, uploadedBy) {
  const sql = "insert into loan_contract_document (doc_id,file_name,file_key,created_by) values ($1,$2,$3,$4)";
  return poolWrite.query(sql, [docId, fileName, fileKey, uploadedBy]);
}

function saveUploadPresignFile(poolWrite, docId, fileName, fileKey, docType, uploadedBy, url) {
  const sql = "insert into loan_contract_document (doc_id,file_name,file_key,doc_type,created_by,url) values ($1,$2,$3,$4,$5,$6)";
  return poolWrite.query(sql, [docId, fileName, fileKey, docType, uploadedBy, url]);
}

function getPresignUrlPromiseFunction(s3, s3Params) {
  return new Promise((resolve, reject) => {
    s3.getSignedUrl("putObject", s3Params, function (err, data) {
      if (err) {
        return reject(err);
      }
      resolve(data);
    });
  });
}

function genFileKey(fileName, partnerCode) {
  const date = Date.now();
  const newFIleName = date + "_" + fileName;
  const curDate = moment().format("yyyyMMDD");
  return `${partnerCode}/${curDate}/${newFIleName}`;
}

function genFileKeyV2(fileName, partnerCode) {
  const date = Date.now();
  const newFIleName = date + "_" + fileName;
  const curDate = moment().format("yyyyMMDD");
  return `${partnerCode}/afterDisbursement/${curDate}/${newFIleName}`;
}

const downloadFileVer2 = async (config, key) => {
  try {
    AWS.config.update(config.aws);
    let bucketName = config.awsS3.bucketName;
    let s3 = new AWS.S3({ region: config.awsS3.region });
    // let acl = "public-read";
    const param = { Bucket: bucketName, Key: decodeURIComponent(key) };
    const buffer = await s3.getObject(param).promise();
    return buffer;
  } catch (e) {
    console.error(`Cannot get file from key: ${key} with error: `, e);
    return undefined;
  }
};

function validateFileExtension(fileName) {
  const allowedExtensions = ALLOW_FILE_EXTENSION;
  const fileExtension = fileName.split(".").pop().toUpperCase();
  return allowedExtensions.includes(fileExtension.toLowerCase());
}

async function genUploadPresignedUrl(req, res) {
  const { fileName, docType } = req.body;
  const config = global.config.data;

  if (!fileName) {
    return res.status(400).json({
      code: -1,
      msg: "fileName is required.",
    });
  }

  const isValidExtension = validateFileExtension(fileName);
  if (!isValidExtension) {
    return res.status(400).json({
      code: -1,
      msg: "Invalid file type.",
    });
  }

  let awsConfig = config.aws;
  let s3Config = config.awsS3;
  let S3BucketName = s3Config.bucketName;

  AWS.config.update({
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
  });

  const s3 = new AWS.S3({
    region: s3Config.region,
    signatureVersion: "v4",
  });

  const fileKey = `mc-los/uploads/${moment().format("yyyyMMDD")}/${uuid.v4()}_${fileName}`;
  const fileExtend = fileName.split(".").pop().toLowerCase();
  const timeExpired = s3Config?.presignDuration || 60 * 10; // Default to 10 minutes
  let s3Params = {
    Bucket: S3BucketName,
    Key: fileKey,
    Expires: timeExpired,
    ACL: "private",
  };
  const contentType = MIME_TYPE[fileExtend] ?? "application/octet-stream";
  s3Params.ContentType = contentType;
  const docId = uuid.v4();
  try {
    const url = await getPresignUrlPromiseFunction(s3, s3Params);
    if(docType) {
      //save to loan_contract_document
      saveUploadPresignFile(global.poolWrite, docId, fileName, fileKey, docType, req?.username, url);
    }
    return res.status(200).json({
      code: 1,
      msg: "get Presigned URL successfully.",
      data: { url, fileKey, timeExpired, contentType: s3Params.ContentType, docId },
    });
  } catch (error) {
    return res.status(500).json({
      code: -1,
      msg: "Failed to generate presigned upload URL.",
      error: error.message,
    });
  }
}

async function checkS3FileExists(fileKey) {
  const config = global.config.data;
  AWS.config.update(config.aws);
  const bucketName = config.awsS3.bucketName;
  const s3 = new AWS.S3({ region: config.awsS3.region });
  const params = { Bucket: bucketName, Key: fileKey };
  try {
    await s3.headObject(params).promise();
    return true;
  } catch (err) {
    if (err.code === "NotFound") {
      return false;
    }
    throw err;
  }
}

async function checkS3FilesExist(files) {
  if (!Array.isArray(files) || files.length === 0) {
    return false;
  }

  for (const fileKey of files) {
    try {
      let fileExists = await checkS3FileExists(fileKey);
      if (!fileExists) {
        return fileKey;
      }
    } catch (err) {
      if (err.code === "NotFound") {
        return fileKey;
      }
      throw err;
    }
  }
  return true;
}

async function genViewOrDownloadPresignedUrl(req, res) {
  const { fileKey, docId } = req.query;
  if (!fileKey && !docId) {
    return res.status(400).json({
      code: 1,
      msg: "fileKey or docId is required.",
    });
  }
  let fileKeyForDownload = fileKey;
  if (docId) {
    const document = await findByDocID(docId);
    if (document) {
      fileKeyForDownload = document.file_key;
    }
  }

  const config = global.config.data;
  AWS.config.update(config.aws);
  const bucketName = config.awsS3.bucketName;
  const s3 = new AWS.S3({ region: config.awsS3.region, signatureVersion: "v4" });
  const timeExpired = config.awsS3?.presignDuration || 60 * 10; // 10 minutes

  const s3Params = {
    Bucket: bucketName,
    Key: fileKeyForDownload,
    Expires: timeExpired,
  };

  try {
    const url = await new Promise((resolve, reject) => {
      s3.getSignedUrl("getObject", s3Params, (err, data) => {
        if (err) return reject(err);
        resolve(data);
      });
    });
    return res.status(200).json({
      code: 0,
      msg: "get Presigned URL successfully.",
      data: { url },
    });
  } catch (error) {
    return res.status(500).json({
      code: 1,
      msg: "Failed to generate presigned view/download URL.",
      error: error.message,
    });
  }
}

async function genPublicUrl(fileKey) {
  const config = global.config.data;

  if (!fileKey) {
    throw new Error("fileKey is required");
  }

  let s3Config = config.awsS3;
  let S3BucketName = s3Config.bucketName;
  let S3Region = s3Config.region;

  const publicUrl = `https://${S3BucketName}.s3.${S3Region}.amazonaws.com/${fileKey}`;

  return publicUrl;
}

const parseS3Url = (url) => {
	const u = new URL(url);
	const hostParts = u.hostname.split(".");
	let bucket, key;
  
	if (hostParts[0] === "s3") {
	  const parts = u.pathname.slice(1).split("/");
	  bucket = parts.shift();
	  key = parts.join("/");
	} else {
	  bucket = hostParts[0];
	  key = u.pathname.slice(1);
	}
	return { bucket, key };
}

async function genPresignedDownloadUrl(fileUrl) {
  const { bucket, key } = parseS3Url(fileUrl);

  const config = global.config.data;

  let awsConfig = config.aws;
  let s3Config = config.awsS3;

  const s3 = new AWS.S3({
    region: s3Config.region,
    accessKeyId: awsConfig.accessKeyId,
    secretAccessKey: awsConfig.secretAccessKey,
    signatureVersion: "v4",
  });

  const params = {
    Bucket: bucket,
    Key: key,
    Expires: 60 * 10 
  };

  return s3.getSignedUrlPromise("getObject", params);
}

module.exports = {
  upload,
  uploadV2,
  download,
  downloadFile,
  genPresignedUrl,
  genPresignedUrlV2,
  uploadV3,
  genPresignedUrlForSme,
  genPresignedDownloadUrlForSme,
  genPresignedUploadUrlForSme,
  genMultiplePresignedDownloadUrlForSme,
  downloadFileVer2,
  genUploadPresignedUrl,
  checkS3FileExists,
  checkS3FilesExist,
  genViewOrDownloadPresignedUrl,
  genMultiplePresigned,
  genPublicUrl,
  validateFileExtension,
  genPresignedDownloadUrl
};
