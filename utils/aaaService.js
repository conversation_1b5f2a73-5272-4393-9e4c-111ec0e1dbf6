const common = require('./common');
const axios = require('axios')


function authenticate(request, res, next) {
    try {
        const isEnableAuthen = request.config.data.authen.enable
        if (isEnableAuthen === 'false') {
            return next()
        }

        const url = request.config.basic.aaa[request.config.env] + request.config.basic.aaa.service + "/author/services";
        const { uiid, token } = request.headers;
        //console.log('headers',request.headers)
        if (!(uiid && token)) {
            common.log('Authentication is empty');
            res.status(401).json({ code: 401, message: 'Request unauthorized' });
        }
        else {
            const service = request.route.path
            return common.authorApi(url, token, uiid, service)
                .then(response => {
                    if (response.data.status !== 1) {
                        res.status(403).json({ code: 403, message: 'Authorize error.' });
                        return;
                    }
                    else {
                        request.role = response.data.role.code
                        request.userName = response.data.user.username
                    }
                    return next();
                })
                .catch(error => {
                    console.log(error.message)
                    res.status(401).json({ code: 401, message: 'Request unauthorized.' });
                })
        }
    } catch (err) {
        common.log('Authorization failed ' + err);
        res.status(401).json({ code: 401, message: 'Authorize error' });
        return;
    }
}

function getKey(config) {
    return new Promise(function (resolve, reject) {
        //;
        let data = JSON.stringify({
            "username": global.config.data.lms.lmsUsername,
            "password": global.config.data.lms.lmsPassword
        });

        const url = config.basic.aaa[config.env] + config.basic.aaa.service + "/authen/signin";

        let configPost = {
            method: 'post',
            url: url,
            headers: {
                'Content-Type': 'application/json'
            },
            data: data
        };

        axios(configPost)
            .then(function (response) {
                resolve(response.data)
            })
            .catch(function (error) {
                reject(error)
            });
    })
}

function getLosKey(config) {
    return new Promise(function (resolve, reject) {
        //;
        let data = JSON.stringify({
            "username": config.data.losAccount.user,
            "password": config.data.losAccount.password
        });

        const url = config.basic.aaa[config.env] + config.basic.aaa.service + "/authen/signin";

        let configPost = {
            method: 'post',
            url: url,
            headers: {
                'Content-Type': 'application/json'
            },
            data: data
        };

        axios(configPost)
            .then(function (response) {
                resolve(response.data)
            })
            .catch(function (error) {
                reject(error)
            });
    })
}

async function authenticate_oauth2(req, res, next) {
    try {
        const config = req.config;
        let url = config.basic.aaa[config.env] + config.basic.aaa.service + "/oauth2/authenticate";
        const authorization = req.headers.authorization;
        if (!authorization || authorization.indexOf('Bearer ') === -1) {
            return res.status(401).json({ message: 'Missing Authorization Header' });
        }
        let headers = {
            "authorization": authorization
        }
        const authorRs = await common.getApiV2(url, headers)
        if (authorRs.status === 401) {
            common.serviceLog('AAA','',authorRs)
            res.status(403).json({message: 'Forbidden request'})
            return;
        }
        return next();
    } catch(err) {
        common.log('Authorization failed ' + err.message);
        res.status(401).json({message: 'Unauthorize request'});
        return;
    }
 }

 function authenticateRouter(request, res, next) {
    try {
        const isEnableAuthen = request.config.data.authen.enable
        if (isEnableAuthen === 'false') {
            return next()
        }

        const url = request.config.basic.aaa[request.config.env] + request.config.basic.aaa.service + "/author/services";
        const { uiid, token } = request.headers;

        if (!(uiid && token)) {
            common.log('Authentication is empty');
            res.status(401).json({ code: 401, message: 'Request unauthorized' });
        }
        else {
            const service = request._parsedUrl.pathname
            return common.authorApi(url, token, uiid, service)
                .then(response => {
                    if (response.data.status !== 1) {
                        res.status(403).json({ code: 403, message: 'Authorize error.' });
                        return;
                    }
                    else {
                        request.role = response.data.role.code
                        request.userName = response.data.user.username
                    }
                    return next();
                })
                .catch(error => {
                    console.log(error.message)
                    res.status(401).json({ code: 401, message: 'Request unauthorized.' });
                })
        }
    } catch (err) {
        common.log('Authorization failed ' + err);
        res.status(401).json({ code: 401, message: 'Authorize error' });
        return;
    }
}

const authenticate_oauth2_rule = async function (req, res, next) {
    const isStopped = 1;

    if (isStopped == 1) {
        return res.status(401).json({ message: 'Forbidden request. Please try again later.' });
    }
    return await authenticate_oauth2(req, res, next);
}

const getPartnerCode = (req) => {
    let partnerCode = req?.body?.partner_code ?? req?.body?.partnercode ?? req?.headers?.partnercode ?? req?.headers?.partner_code;
    if (partnerCode === undefined)
        partnerCode = req.body.partnerCode;
    if (partnerCode === undefined)
        partnerCode = req.query.partner_code;
    if (partnerCode === undefined)
        partnerCode = req.query.partnerCode;
    if (partnerCode === undefined) {
        partnerCode = req.headers['partner-code'];
    }
    return partnerCode;
}

const authenticate_oauth2_v03 = async ({ req, res, next, isCheckToken }) => {
    const config = req.config;
    let url = config.basic.aaa[config.env] + "/aaa/v1/sale/authenticate";
    let authorization = req?.headers?.authorization || null;
    if (!isCheckToken) isCheckToken = 0;
    if (isCheckToken && (!authorization || authorization.indexOf('Bearer ') === -1)) return res.status(401).json({ message: 'Missing Authorization Header' });
    try {
        let headers = {
            authorization: authorization,
            check_token: isCheckToken,
            service: 'LOS',
            partner_code: getPartnerCode(req) || 'MCC'
        }
        let responseAaa = await common.getApiTimeoutV2({ url, headers });
        let data = { ...responseAaa?.data };
        if (data?.status === 401 || data?.code === 401) {
            return res.status(403).json({ message: data?.message || 'Forbidden request' });
        }
        return next();
    } catch (err) {
        common.log('Authorization failed ' + err.message);
        console.error(err);
        return res.status(401).json({ message: 'Unauthorize request' });
    }
}

const authenticate_oauth2_v04 = async ({ req, res, next, isCheckToken }) => {
    const config = req.config;
    let url = config.basic.aaa[config.env] + "/aaa/v3/sale/authenticate";
    let authorization = req?.headers?.authorization || null;
    if (!isCheckToken) isCheckToken = 0;
    if (isCheckToken && (!authorization || authorization.indexOf('Bearer ') === -1)) return res.status(401).json({ message: 'Missing Authorization Header' });
    try {
        let partner_code = getPartnerCode(req)
        if (!partner_code) {
            return res.status(403).json({ message: 'partner_code is required' });
        }

        let headers = {
            authorization: authorization,
            check_token: isCheckToken,
            service: 'LOS',
            partner_code
        }
        let responseAaa = await common.getApiTimeoutV2({ url, headers });
        let data = { ...responseAaa?.data };
        if (data?.status === 401 || data?.code === 401) {
            return res.status(403).json({ message: data?.message || 'Forbidden request' });
        }
        return next();
    } catch (err) {
        common.log('Authorization failed ' + err.message);
        console.error(err);
        return res.status(401).json({ message: 'Unauthorize request' });
    }
}

const authenticateOauth2V03WithNoToken = async (req, res, next) => {
    await authenticate_oauth2_v03({ req, res, next, isCheckToken: 0 });
}

const authenticateOauth2V03WithToken = async (req, res, next) => {
    await authenticate_oauth2_v03({ req, res, next, isCheckToken: 1 });
}

const authenticateOauth2V04WithToken = async (req, res, next) => {
    await authenticate_oauth2_v04({ req, res, next, isCheckToken: 1 });
}

module.exports = {
    authenticate,
    getKey,
    getLosKey,
    authenticate_oauth2,
    authenticateRouter,
    authenticate_oauth2_rule,
    authenticateOauth2V03WithNoToken,
    authenticateOauth2V03WithToken,
    authenticateOauth2V04WithToken,
}