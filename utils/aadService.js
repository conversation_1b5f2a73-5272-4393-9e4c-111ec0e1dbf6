const common = require("./common")
const {SERVICE_NAME,roleCode,CONTRACT_TYPE, PARTNER_CODE} = require("../const/definition")
const {STATUS, CALLBACK_STAUS} = require("../const/caseStatus")
const loggingRepo = require("../repositories/logging-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const kunnRepo = require("../repositories/kunn-repo")
const callbackService = require("../services/callback-service")

async function pushTaskMc(role,contractNumber,contractType=CONTRACT_TYPE.CASH_LOAN,config,isEsigned=false,inCpBeforeSign=false,taskType) {
    if(config == undefined) {
        config = global.config
    }
    const lb = config.basic.aad[config.env]
    const aadUri = config.data.aadService.pushTask
    const aadUrl =  lb + aadUri
    const body = {
        role,
        contractNumber,
        contractType,
        taskStatus : taskType
    }
    const rs = await common.postApiV2(aadUrl,body)
    let step;
    let status;
    if(role == roleCode.CP) {
            if(isEsigned||inCpBeforeSign){
                status = STATUS.IN_CP_POST_QUEUE
            } 
            else{
                status = STATUS.IN_CP_QUEUE
            }
        await loanContractRepo.updateContractStatus(status,contractNumber)
    }
    else if(role == roleCode.CE) {
        step = SERVICE_NAME.PUSH_TASK_CE
        status = STATUS.IN_CE_QUEUE
        await loanContractRepo.updateContractStatus(status,contractNumber)
    }
    else if(role == roleCode.SS){
        step = `PUSH_TASK_${role}`
    }
    else if(role == roleCode.DE) {
        step = SERVICE_NAME.PUSH_TASK_DE
        status = STATUS.IN_DE_QUEUE
        await loanContractRepo.updateContractStatus(status,contractNumber)
    }
    else {
        step = `PUSH_TASK_${role}`
        status = `IN_${role}_QUEUE`
        await loanContractRepo.updateContractStatus(status,contractNumber)
    }
    loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.AAD,step,body,rs)
    return rs
}

async function pushTaskKU(role,contractNumber,contractType=CONTRACT_TYPE.CREDIT_LINE,inCpBeforeSign=false,isDisbursement=false) {
    if(config == undefined) {
        config = global.config
    }
    const lb = config.basic.aad[config.env]
    const aadUri = config.data.aadService.pushTask
    const aadUrl =  lb + aadUri
    const body = {
        role,
        contractNumber,
        contractType
    }
    const rs = await common.postApiV2(aadUrl,body)
    let step;
    let status;
    if(role == roleCode.CP) {
        step = SERVICE_NAME.PUSH_TASK_CP
        status = STATUS.IN_CP_QUEUE
        if(inCpBeforeSign){
            step = SERVICE_NAME.PUSH_TASK_CP
            status = STATUS.IN_CP_BEFORE_SIGN_QUEUE
        }
        if(isDisbursement){
            step = SERVICE_NAME.PUSH_TASK_CP
            status = STATUS.IN_CP_DIS_QUEUE
        }
        await loanContractRepo.updateKUStatus(status,contractNumber)
    }
    else if(role == roleCode.CE) {
        step = SERVICE_NAME.PUSH_TASK_CE
        status = STATUS.IN_CE_QUEUE
        await loanContractRepo.updateKUStatus(status,contractNumber)
    }
    else if(role == roleCode.SS){
        step = `PUSH_TASK_${role}`
    }
    else if(role == roleCode.DE) {
        step = SERVICE_NAME.PUSH_TASK_DE
        status = STATUS.IN_DE_QUEUE
        await loanContractRepo.updateKUStatus(status,contractNumber)
    }
    else {
        step = `PUSH_TASK_${role}`
        status = `IN_${role}_QUEUE`
        await loanContractRepo.updateKUStatus(status,contractNumber)
    }
    loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.AAD,step,body,rs)
    return rs
}

async function completedTask(contractNumber) {
    
}

async function completedTaskByRole(contractNumber,role) {
    const lb = config.basic.aad[config.env]
    const aadUri = '/aad-mc-credit/v1/updateTaskStatus/completeTaskByRole'
    const aadUrl =  lb + aadUri
    const body = {
        role,
        contractNumber
    }
    const rs = await common.postApiV2(aadUrl,body)
    const step = 'COMPLETE_SS'
    loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.AAD,step,body,rs)
    return rs
}

async function completedTaskByTaskId(contractNumber,taskId) {
    const lb = config.basic.aad[config.env]
    const aadUri = '/aad-mc-credit/v1/updateTaskStatus/complete'
    const aadUrl =  lb + aadUri
    const body = {
        taskId
    }
    const rs = await common.postApiV2(aadUrl,body)
    return rs
}

async function cancelTaskByContract(contractNumber) {
    try {
        const url = config.basic.aad[config.env] + '/aad-mc-credit/v1/updateTaskStatus/cancel'
        const body = {
            contractNumber
        }
        return await common.putAPIV2(url,body)
    }
    catch(err) {
        common.log(`cancel contract task error : ${err.message}`,contractNumber)
        return false
    }
}

async function pushTaskMcV2(role,contractNumber,contractType,taskType,isKycCheck=false) {
    try {
        let isSuperApp = false
        let isKunn = await kunnRepo.checkIsKU(contractNumber)
        let data
        if (isKunn)
            data = await kunnRepo.getKunnData(contractNumber)
        else
            data = await loanContractRepo.getLoanContract(contractNumber)
        isSuperApp = data.partner_code == PARTNER_CODE.SMA || data.partner_code == PARTNER_CODE.SMASYNC ? true : false
        const aadUrl = global.config.basic.aad[config.env] + global.config.data.aadService.pushTask
        let body = {
            role,
            contractNumber,
            contractType,
            taskStatus : taskType
        }
        if (isKycCheck) body.taskType = 2
        const rs = await common.postApiV2(aadUrl,body)
        if (isSuperApp) callbackService.callbackPartner(contractNumber, data.partner_code, CALLBACK_STAUS.IN_SS_QUEUE)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.AAD,'PUSH_TASK_MC',body,rs)
        return rs
    }
    catch(err) {
        common.log(`push contract task error : ${err.message}`,contractNumber)
        return false
    }
}

module.exports = {
    pushTaskMc,
    pushTaskMcV2,
    completedTask,
    completedTaskByRole,
    completedTaskByTaskId,
    cancelTaskByContract,
    pushTaskKU
}