const commom = require("./common")
const utils = require("./helper")
const {saveCallBack} = require("./loggingService")
const {callbackVds} = require("../services/vds-service")
const {PARTNER_CODE} = require("../const/definition")
const uuid = require("uuid")
const loanContractRepo = require("../repositories/loan-contract-repo")
const offerRepo = require("../repositories/offer")

async function callbackPartner(poolWrite,contractNumber,config,partnerCode,body) {
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    if(partnerCode == PARTNER_CODE.VPL) {
        callbackVds(body)
        return;
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
    const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackAprroved(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const contractData = await utils.getAllContractData(poolWrite,contractNumber)
    const startDate = contractData.approval_date || new Date()
    const endDate = new Date(startDate.setMonth(startDate.getMonth() + 36))
    const data = {
        partnerCode : contractData.partner_code,
        customerName : contractData.cust_full_name,
        identityCardId : contractData.id_number,
        phoneNumber : contractData.phone_number1,
        productCode : contractData.product_code,
        creditLimitAmount : contractData.approval_amt,
        intRate : contractData.approval_int_rate,
        startDate : startDate,
        endDate : endDate
    }
    let body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        code : "APPROVED",
        targetType : targetType,
        msg : "To be signed ( Signing process to be called)",
        data : data
    }

    if(targetType == 'disbursement-request') {
        body = {
            requestID : createRequestID(partnerCode),
            contractNumber : contractNumber,
            withdrawId : withdrawId,
            code : "APPROVED",
            targetType : targetType,
            msg : "The request is approved, eSign  process is called"
        }
    }


    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackCancel(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "CANCELLED",
        targetType : targetType,
        msg : "Cancelled"
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackReject(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "REJECTED",
        targetType : targetType,
        msg : "Not meet policy requirement",
        data : {
            partnerCode : partnerCode,
            rejectReason : "REJ_POLICY"
        }
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackOffer(poolWrite,config,contractNumber) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }

    const offerRs = await offerRepo.getSelectedOffer(contractNumber)
    const offerList = []
    offerList.push({
        offerId : offerRs.id,
        offerAmount : offerRs.offer_amt,
        offerTenor : offerRs.tenor,
        offerIr : offerRs.int_rate  
    })

    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        code : "ALT_OFFER",
        targetType : "credit-creation",
        msg : "The offer is not matched with the request, customer need to select new offer",
        offer : offerList
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackEsign(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "ESIGN",
        targetType : targetType,
        msg : "Signing in progress"
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackSigned(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "SIGNED",
        targetType : targetType,
        msg : "Signed"
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackActive(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "ACTIVE",
        targetType : targetType,
        msg : "Activated"
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackDeactive(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "INACTIVE",
        targetType : targetType,
        msg : "Deactivated"
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackTerminated(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "TERMINATED",
        targetType : targetType,
        msg : "Terminated"
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackRecieved(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "RECEIVED",
        targetType : targetType,
        msg : "Recieved documents,the contract is checking."
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

async function callbackError(poolWrite,config,contractNumber,targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }
    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "Error",
        targetType : targetType,
        msg : "An error occurred during validation processing."
    }
    const isEnable = config.data.partnerCallback.enable
    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

function createRequestID(partnerCode) {
    return partnerCode + new Date().getTime()
}

async function callbackUpdateAf(poolWrite,config,contractNumber,updateAF,  targetType='credit-creation',withdrawId=null) {
    const partnerCode = await utils.getPartnerCode(poolWrite,contractNumber)
    const url = config.data.partnerCallback[partnerCode]
    const token = config.data.partnerToken[partnerCode]
    const headers = {
        "Authorization" : "Bearer " +  token
    }

    const body = {
        requestID : createRequestID(partnerCode),
        contractNumber : contractNumber,
        withdrawId : withdrawId,
        code : "UPDATE_AF",
        targetType : targetType,
        msg : "update af form.",
        updateAF
    }

    if(isEnable === 'true') {
        const callbackRs = await commom.postApiV2(url,body,headers)
        saveCallBack(poolWrite,body,callbackRs,contractNumber)
    }
}

function bodyCallbackReject(contractNumber,partnerCode) {
    const request_id = "EC" + uuid.v4()
    const callbackBody = {
        request_id,
        contract_number : contractNumber,
        application_status : 'REJECTED',
        message : 'FAIL_POLICY'
    }
    return callbackBody
}

function bodyCallbackCancel(contractNumber,partnerCode) {
    const request_id = "EC" + uuid.v4()
    const callbackBody = {
        request_id,
        contract_number : contractNumber,
        application_status : 'CANCELLED',
        message : "The application is cancelled"
    }
    return callbackBody
}

async function bodyCallbackApproved(contractNumber,partnerCode) {
    const request_id = "EC" + uuid.v4()
    const contractData = await loanContractRepo.getLoanContract(contractNumber)
    const callbackBody = {
        "request_id": request_id,
        "contract_number": contractNumber,
        "application_status": "APPROVED",
        "message": "To be signed (Signing process to be called)",
        "reject_reason": "",
        "data": {
          "channel": contractData.channel,
          "partner_code": contractData.partnerCode,
          "customer_name": contractData.cust_full_name,
          "identity_card_id": contractData.id_number,
          "phone_number": contractData.phone_number1,
        }
    }
    return callbackBody
}

function bodyCallBackResubmit(contractNumber,resubmit) {
    const request_id = 'EC' + uuid.v4()
    const callbackBody = {
        request_id,
        contract_number : contractNumber,
        application_status : 'RESUBMIT',
        message : "The documents are required to resubmit",
        "reject_reason" : "",
        list_doc_resubmit : resubmit
    }
    return callbackBody
}

async function bodyCallbackOffer(contractNumber) {
    const offer = await offerRepo.getSelectedOffer(contractNumber)

    const request_id = 'EC' + uuid.v4()
    const callbackBody = {
        request_id,
        "contract_number": contractNumber,
        "application_status": "ALT_OFFER",
        "message": "The offer is not matched with the request, customer need to select new offer",
        "reject_reason": "",
        "offer_list" : [
            {
                "offer_id" : offer.id,
                "offer_amount" : parseInt(offer.offer_amt),
                "interest_rate" : parseFloat(parseInt(offer.int_rate * 1000 / 12) / 10),
                "monthly_installment" : parseFloat(offer.instal_amt),
                "offer_tenor" : offer.tenor,
                "offer_type" : "N"
            }
        ]
    }   
    return callbackBody
}

module.exports = {
    callbackPartner,
    callbackCancel,
    callbackReject,
    callbackOffer,
    callbackEsign,
    callbackSigned,
    callbackActive,
    callbackDeactive,
    callbackTerminated,
    callbackAprroved,
    callbackRecieved,
    callbackError,
    callbackUpdateAf,
    bodyCallbackReject,
    bodyCallbackCancel,
    bodyCallbackApproved,
    bodyCallBackResubmit,
    bodyCallbackOffer
}