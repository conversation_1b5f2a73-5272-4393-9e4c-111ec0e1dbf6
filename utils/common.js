const axios = require('axios');
const sizeof = require('object-sizeof');
const { logOut } = require('./loggingService');

function saveErrorLog(params,errorLogs) {
    try {
        const poolWrite = global.poolWrite
        const sql = 'insert into error_log (param,error_logs) values ($1,$2)'
        poolWrite.query(sql,[params,errorLogs])
    }
    catch(err) {
        console.log(`save error log error : ${err.message}`)
    }
}

function log(message,params,level=3) {
    let content = '' ;
    if(level == 1) {
        content += 'INFO|'
    }
    else if(level == 2) {
        content += 'DEBUG|'
    }
    else if(level == 3) {
        saveErrorLog(params,message)
        content += 'ERROR|'
    }
    if(params == '' || params == undefined) {
        content += message
    }
    else {
        content += (params + ' ' + message)
    }
    console.log(content)
}

function serviceLog(service,contractNumber,result) {
    // console.log("RESPONSE|" + service + "|" + contractNumber + "|" + JSON.stringify(result))
}

function bodyLog(service,contractNumber,params) {
    // console.log("BODY|" + service + "|" + contractNumber + "|" + JSON.stringify(params))
}

const getAPI = function (link, header, params = {}) {
    return new Promise(function (resolve, reject) {
        try {
            let reqObj = {
                url: link,
                method: 'get',
                headers: header,
                params: params
            };

            reqObj.startTime = Date.now();
            axios(reqObj)
                .then(function (response) {
                    logOut(reqObj, response);
                    resolve(response.data)
                })
                .catch((error) => {
                    if (error.response) {
                        logOut(reqObj, error?.response);
                        resolve(error.response.data)
                    } else {
                        reject(error)
                    }
                })
        } catch (e) {
            reject(e);
        }
    }
    )
}

async function getApiV2(url,headers = {}, params={}) {
    let data = '';
    let config = {
      method: 'get',
      url: url,
      headers: headers,
      data : data,
      params
    };
    config.startTime = Date.now();
    const rs = await axios(config)
    .then(function (response) {
        logOut(config, response);
      const status = response.status
      const data = response.data
      return {status,data}
    })
    .catch(function (error) {
      if (error.response) {
        logOut(config, error?.response);
        const status = error.response.status;
        const data = error.response.data;
        
        return {status,data}
      }
      console.log("config: ", error);
    });
    return rs
  }

function authorApi(url,token,uiid,service) {
    return new Promise(function(resolve,reject) {
        let config = {
          method: 'get',
          url: url,
          headers: { 
            'token': token, 
            'uiid': uiid, 
            'service': service
          }
        };

        axios(config)
        .then(function (response) {
            resolve(response)
        })
        .catch(function (error) {
            reject(error);
        });
    })
}

const postAPI = function (url, body, header = {'Content-Type': 'application/json'}) {
    return new Promise(function (resolve, reject) {
        try {
           
        let config = {
          method: 'post',
          url: url,
          headers: header,
          data : body
        };
        config.startTime = Date.now();

        axios(config)
        .then(function (response) {
            logOut(config, response);
            let res = JSON.parse(JSON.stringify(response.data));
            resolve(res);
        })
        .catch(function (error) {
            if (error?.response) {
                logOut(config, error?.response);
            }
            reject(error);
        });

        } catch (e) {
            reject(e);
        }
    })
}

const putAPI = function (url,headers={},data={}) {
    return new Promise(function (resolve,reject) {
        let config = {
            method: 'put',
            url: url,
            headers: { },
            data
        };
        config.startTime = Date.now();

        axios(config)
        .then(function (response) {
        console.log(JSON.stringify(response.data));
        })
        .catch(function (error) {
        console.log(error);
        });
    })
}


const updateAPI = function (link, body, header) {
    return new Promise(function (resolve, reject) {
        try {
            try {
                let reqObj = {
                    url: link,
                    method: 'put',
                    headers: header,
                    body: body
                };
                reqObj.startTime = Date.now();

                axios(reqObj)
                    .then(function (response) {
                        logOut(reqObj, response);
                        resolve(response.data)
                    })
                    .catch((error) => {
                        if (error.response) {
                            logOut(reqObj, error?.response);
                            resolve(error.response.data)
                        } else {
                            reject(error)
                        }
                    })
            } catch (e) {

                reject(e);
            }


        } catch (e) {

            reject(e);
        }
    }
    )
}
const deleteAPI = function (link, body, header) {
    return new Promise(function (resolve, reject) {
        try {
            let reqObj = {
                url: link,
                method: 'delete',
                headers: header,
                json: body
            };
            reqObj.startTime = Date.now();

            axios(reqObj)
                .then(function (response) {
                    logOut(reqObj, response);
                    resolve(response.data)
                })
                .catch((error) => {
                    if (error.response) {
                        logOut(reqObj, error?.response);
                        resolve(error.response.data)
                    } else {
                        reject(error)
                    }
                })
        } catch (e) {
            reject(e);
        }
    }
    )
}

async function postApiV2(url, data, headers = {
    'Content-Type': 'application/json'
},logger = {isLog :false,requestId:'',requestType:'',contractNumber:''}) {
    const payload = data;
    let config = {
        method: 'post',
        url: url,
        headers: headers,
        data: data
    };
    config.startTime = Date.now();
    // const bodySize = sizeof(JSON.stringify(config.data))
    // let logBody = '';
    // if (bodySize <= 10000) {
    //     logBody = config.data
    // }
    const rs = await axios(config)
        .then(function (response) {
            const status = response.status
            const data = response.data
            logOut(config, response);
            if(logger.isLog)
            {
                insertLog({
                    ...logger,
                    url,
                    resBody: response?.data,
                    reqBody: payload
                })
            }
            return { status, data }
        })
        .catch(function (error) {
            if (error.response) {
                const status = error.response.status;
                const data = error.response.data;
                logOut(config, error?.response);
                if(logger.isLog)
                    {
                        insertLog({
                            ...logger,
                            url,
                            resBody: error?.response?.data,
                            reqBody: payload
                        })
                    }
                return { status, data }
            }
        });
    return rs
}

async function responseErrorPublic(res) {
    return res.status(500).json({
        code : -1,
        msg : "INTERNAL SERVER ERROR."
    })
}

async function responseErrorInternal(res,err) {
    return res.status(500).json({
        code : -1,
        msg : `ERROR : ${err.message}`
    })
}

async function putAPIV2(
    url,
    data,
    headers = {
        "Content-Type": "application/json",
    },
    requestObj = {}
    ) {
    let method = "put";

    let config = {
        method,
        url: url,
        headers: headers,
        data: data,
    };
    config.startTime = Date.now();
    // const bodySize = sizeof(JSON.stringify(config.data))
    // let logBody = '';
    // if(bodySize <= 10000 ) {
    //     logBody = config.data
    // }
    const rs = await axios(config)
    .then(function (response) {
        const status = response.status;
        const data = response.data;
        logOut(config, response);
        return { status, data };
    })
    .catch(function (error) {
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            logOut(config, error?.response);
            return { status, data };
        }
    });
        
    return rs;
}

const getApiTimeoutV2 = async function ({ url, headers, timeout }) {
    try {
        if (!timeout) timeout = 60000; //default 60s
        if (!headers) headers = { 'Content-Type': 'application/json' }
        let config = {
            method: "get",
            url: url,
            headers: headers,
            timeout: timeout
        };
        config.startTime = Date.now();
        let response = await axios(config);
        logOut(config, response);
        return {
            status: response?.status,
            data: response?.data
        }
    } catch (error) {
        // console.error(error);
        logOut(config, error?.response);
        return {
            status: error?.response?.status,
            data: error?.response?.data
        }
    }
};
const getApiNew = function (link, header) {
    return new Promise(function (resolve, reject) {
        try {
        let config = {
            method: "get",
            url: link,
            headers: header,
        };
        config.startTime = Date.now();

        axios(config)
            .then(function (response) {
                logOut(config, response);
                resolve(response.data);
            })
            .catch(function (error) {
                //console.log(error.message);
                if (error?.response) {
                    logOut(config, error?.response);
                }
                reject(error);
            });
        } catch (e) {
            reject(e);
        }
    });
};
const resSuccess = function(res, message, code = 0, data = undefined) {
    let json = { response_code: code, response_message: message }
    if(data != undefined)
        json = { response_code: code, response_message: message, data: data }
    res.status(200).json(json)
}

const isBase64 = (str) => {
    const base64Regex = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/;
    return base64Regex.test(str);
};

const isBuffer = (data) => {
    return Buffer.isBuffer(data);
};

const isBinaryString = (data) => {
    const nonPrintableChars = /[^\x20-\x7E]/; // Regex for non-printable ASCII characters
    return typeof data === 'string' && nonPrintableChars.test(data);
};

const misaResSuccess = function({res, message="success", code = 0, data = undefined}) {
    let json = { response_code: code, response_message: message }
    if(data != undefined)
        json = { response_code: code, response_message: message, data: data }
    res.status(200).json(json)
}

const insertLog = async ({ contractNumber, requestId, requestType, reqBody, resBody, url }) => {
    try {
        const poolWrite = global.poolWrite
        let params;
        let sql = "insert into request_log (contract_number,request_id,request_type,request_body,request_response,url) values ($1,$2,$3,$4,$5,$6)"
        params = [contractNumber, requestId, requestType, reqBody, resBody, url]
        await poolWrite.query(sql, params)
        return true
    } catch (err) {
        console.log(`insertLog error :${err.message} , ${contractNumber}`)
        return false
    }
}

const postApiTimeout = async function ({ url, data, headers, timeout = 30000 }) {
    if (!headers) {
        headers = { 'Content-Type': 'application/json' }
    }
    if (!timeout) timeout = 60000
    let config = {
        method: 'post',
        url: url,
        headers: headers,
        data: data,
        timeout: timeout
    };
    config.startTime = Date.now();
    try {
        const response = await axios(config);
        logOut(config, response);
        return { status: response?.status, data: response?.data }
    } catch (error) {
        console.log(`postApiTimeout error: ${error.message}`);
        console.log(error);
        return { status: error?.response?.status || null, data: error?.response?.data || null }
    }
}

const joinAddress = (address = []) => {
  if (!Array.isArray(address)) {
    return "";
  }
  return address.filter(e => e).join(", ");
};

const getFullyAddress = (detail = '', newAddressArray = [], oldAddressArray = []) => {
  if (!!newAddressArray?.[0]) {
    return joinAddress([detail, ...newAddressArray]);
  }
  return joinAddress([detail, ...oldAddressArray]);
}

module.exports = {
    resSuccess,
    getApiNew,
    deleteAPI,
    updateAPI,
    postAPI,
    postApiV2,
    getAPI,
    authorApi,
    putAPI,
    putAPIV2,
    getApiV2,
    log,
    serviceLog,
    bodyLog,
    responseErrorPublic,
    responseErrorInternal,
    getApiTimeoutV2,
    misaResSuccess,
    postApiTimeout,
    joinAddress,
    getFullyAddress
}

