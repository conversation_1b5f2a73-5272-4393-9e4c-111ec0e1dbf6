const { object } = require("underscore");
const utils = require("../helper")
const normalizer = require("./normalize")

function convertBody(body, requestType, convertCache) {
    try{
        let rs = {}
        const cache = utils.isNullOrEmpty(convertCache) ? global.convertCache : convertCache;
        if(cache.hasOwnProperty(requestType)) {
            for(let key in body) {
                let value = body[key]
                if(cache[requestType].hasOwnProperty(key)) {
                    const inputDtype = cache[requestType][key].inputDtype
                    const outputDtype = cache[requestType][key].outputDtype
                    if(!utils.isNullOrEmpty(inputDtype) && !utils.isNullOrEmpty(outputDtype)) {
                        value = normalizer.normalizeDtype(value,inputDtype,outputDtype)
                    }

                    const inputFormat = cache[requestType][key].inputFormat
                    const outputFormat = cache[requestType][key].outputFormat
                    if(!utils.isNullOrEmpty(inputFormat) && !utils.isNullOrEmpty(outputFormat)) {
                        value = normalizer.normalizeFormat(value,inputFormat,outputFormat)
                    }
                    if(typeof(body[key]) == 'object' && body[key] != null) {
                        rs[cache[requestType][key].outputVar] = []
                        for(let i in value) {
                            const tmpObject = {}
                            for (let subKey in value[i]) {
                                if(cache[requestType].hasOwnProperty(`${key}.${subKey}`)) {
                                    tmpObject[cache[requestType][`${key}.${subKey}`].outputVar] = value[i][subKey]
                                }
                            }
                            rs[cache[requestType][key].outputVar].push(tmpObject)
                        }
                    }
                    else {
                        rs[cache[requestType][key].outputVar] = value
                    }
                }
                else {
                    rs[key] = value
                }
            }
        }
        return rs
    }
    catch(err){
        console.log(`convertBody error: ${err.message}`);
        console.log(err);

        return null;
    }
}

const convertBodyVer2 = (body, requestType, convertCache) => {
    try {
        const rs = {};
        const cache = utils.isNullOrEmpty(convertCache) ? global.convertCache : convertCache;

        if (!cache.hasOwnProperty(requestType)) {
            return body; // không có config thì trả nguyên
        }

        const mapConfig = cache[requestType];

        // Lấy giá trị theo path
        function getValueByPath(obj, path) {
            return path.split('.').reduce((o, k) => (o || {})[k], obj);
        }

        // Set giá trị theo path
        function setValue(obj, path, value) {
            const keys = path.split('.');
            let temp = obj;
            for (let i = 0; i < keys.length - 1; i++) {
                if (typeof temp[keys[i]] !== 'object' || temp[keys[i]] === null) {
                    temp[keys[i]] = {};
                }
                temp = temp[keys[i]];
            }
            temp[keys[keys.length - 1]] = value;
        }

        // Hàm duyệt toàn bộ object body (để fallback giữ nguyên)
        function traverseAndKeep(obj, prefix = "") {
            for (let k in obj) {
                const fullKey = prefix ? `${prefix}.${k}` : k;
                if (!mapConfig.hasOwnProperty(fullKey)) {
                    if (typeof obj[k] === "object" && obj[k] !== null && !Array.isArray(obj[k])) {
                        traverseAndKeep(obj[k], fullKey);
                    } else {
                        setValue(rs, fullKey, obj[k]);
                    }
                }
            }
        }

        // Duyệt config trước (map theo cấu hình)
        for (let inputKey in mapConfig) {
            const cfg = mapConfig[inputKey];
            let value = getValueByPath(body, inputKey);
            if (value === undefined) continue;

            // Convert kiểu dữ liệu
            if (!utils.isNullOrEmpty(cfg.inputDtype) && !utils.isNullOrEmpty(cfg.outputDtype)) {
                value = normalizer.normalizeDtype(value, cfg.inputDtype, cfg.outputDtype);
            }

            // Convert format
            if (!utils.isNullOrEmpty(cfg.inputFormat) && !utils.isNullOrEmpty(cfg.outputFormat)) {
                value = normalizer.normalizeFormat(value, cfg.inputFormat, cfg.outputFormat);
            }

            // Parse outputVar từ DB (có thể là string hoặc array JSON)
            let outputVarParsed = safeParseOutputVar(cfg.outputVar);

            // Nếu là array object
            if (Array.isArray(value)) {
                const arrResult = [];
                for (let item of value) {
                    if (typeof item === 'object' && item !== null) {
                        const objItem = {};
                        for (let subKey in item) {
                            const nestedKey = `${inputKey}.${subKey}`;
                            if (mapConfig.hasOwnProperty(nestedKey)) {
                                objItem[mapConfig[nestedKey].outputVar] = item[subKey];
                            } else {
                                objItem[subKey] = item[subKey];
                            }
                        }
                        arrResult.push(objItem);
                    } else {
                        arrResult.push(item);
                    }
                }
                if (Array.isArray(outputVarParsed)) {
                    outputVarParsed.forEach(outVar => setValue(rs, outVar, arrResult));
                } else {
                    setValue(rs, outputVarParsed, arrResult);
                }
            } else {
                // Giá trị thường
                if (Array.isArray(outputVarParsed)) {
                    outputVarParsed.forEach(outVar => setValue(rs, outVar, value));
                } else {
                    setValue(rs, outputVarParsed, value);
                }
            }
        }

        // Fallback giữ nguyên key chưa map
        traverseAndKeep(body);

        return rs;
    } catch (err) {
        console.log(`convertBodyVer2 error: ${err.message}`);
        console.log(err);
        return null;
    }
}

function safeParseOutputVar(value) {
    if (typeof value !== 'string') return value; // không phải string thì giữ nguyên
    try {
        return JSON.parse(value);
    } catch (e) {
        return value; // nếu parse lỗi thì trả lại string ban đầu
    }
}

module.exports = {
    convertBody,
    convertBodyVer2
}