async function loadConvertCache(poolRead) {
    const sql = "select request_type,input_var,input_dtype,input_format,output_var,output_dtype,output_format from converter where is_deleted = 0"
    const queryRs = await poolRead.query(sql)
    const cacheRs = {}
    queryRs.rows.map(row => {
        if(!cacheRs.hasOwnProperty(row.request_type)) {
            cacheRs[row.request_type] = {}
        }
        if(!cacheRs[row.request_type].hasOwnProperty(row.input_var)) {
            cacheRs[row.request_type][row.input_var] = {
                inputVar : row.input_var,
                inputDtype : row.input_dtype,
                inputFormat : row.input_format,
                outputVar : row.output_var,
                outputDtype : row.output_dtype,
                outputFormat : row.output_format
            }
        }
    })   
    return cacheRs
}

module.exports = {
    loadConvertCache
}