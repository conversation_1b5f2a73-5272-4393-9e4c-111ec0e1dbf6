
function normalizeDtype(sourceValue,inputDtype,outputDtype) {
    const standardValue = srcToStandardDtype(sourceValue,inputDtype)
    const destValue = standardToDestDtype(standardValue,outputDtype)
    return destValue
}

function normalizeFormat(sourceValue,inputFormat,outputFormat) {
    const standardValue = srcToStandardFormat(sourceValue,inputFormat)
    const destValue = standardToDestFormat(standardValue,outputFormat)
    return destValue
}

function srcToStandardDtype(value,dtype) {
    try {
        switch(dtype) {
            case 'float' :
                return parseFloat(value)
            case 'int'  :
                return parseFloat(value)
            case 'string':
                return value.toString()
            default:
                return value
        }   
    }
    catch(error) {
        console.log(error.message)
        return value
    }
}

function standardToDestDtype(value,dtype) {
    try {
        switch(dtype) {
            case 'float' :
                return parseFloat(value)
            case 'int'  :
                return parseInt(value)
            case 'string':
                return value.toString()
            default:
                return value
        }   
    }
    catch(error) {
        console.log(error.message)
        return value
    }
}

function srcToStandardFormat(value,format) {
    try {
        if(format.includes('YYYY') && format.includes('MM') && format.includes('DD')) {
            return srcDateToStandard(value,format)
        }
        return value
    }
    catch(error) {
        console.log(error.message)
        return value
    }
}

function standardToDestFormat(value,format) {
    try {
        
        if(format.includes('YYYY') && format.includes('MM') && format.includes('DD')) {
            //console.log(value,format,standardDateToDest(value,format))
            return standardDateToDest(value,format)
        }
        console.log({format})
        return value
    }
    catch(error) {
        console.log(error.message)
        return value
    }
}

function srcDateToStandard(value,format) {
    const yearIdx = format.indexOf('YYYY')
    const monthIdx = format.indexOf('MM')
    const dayIdx = format.indexOf('DD')
    return [value.slice(yearIdx,yearIdx+4),value.slice(monthIdx,monthIdx+2),value.slice(dayIdx,dayIdx+2)].join("-")
}

function standardDateToDest(value,format) {
    // standard YYYY-MM-DD
    const year = value.slice(0,4)
    const month = value.slice(5,7)
    const day = value.slice(8,10)
    const yearIdx = format.indexOf('YYYY')
    const monthIdx = format.indexOf('MM')
    const dayIdx = format.indexOf('DD')
    return format.replaceAt(yearIdx,year).replaceAt(monthIdx,month).replaceAt(dayIdx,day)
}


String.prototype.replaceAt = function(index, replacement) {
    return this.substr(0, index) + replacement + this.substr(index + replacement.length);
}

function normalizeJson(obj) {
    const ordered = {};
    Object.keys(obj).sort().forEach(k => {
      ordered[k] = obj[k];
    });
    return JSON.stringify(ordered);
  }

module.exports = {
    normalizeDtype,
    normalizeFormat,
    normalizeJson
}