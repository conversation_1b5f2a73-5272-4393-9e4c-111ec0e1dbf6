const common = require("./common")
const utis = require("../utils/helper")
const dateHelper = require("../utils/dateHelper")
const {SYS_CHANEL,SERVICE_NAME} = require("../const/definition")
const {CRM_STATUS} = require("../const/caseStatus")
const loanContractRepo = require("../repositories/loan-contract-repo")
const productService = require("../utils/productService")
const aaaService = require("./aaaService")
const loggingRepo = require("../repositories/logging-repo")
const {serviceEndpoint} = require("../const/config")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const kunnRepo = require("../repositories/kunn-repo")

async function activeContract(config,contractNumber) {
    let activeCrmUrl = config.basic.crmService[config.env] + config.data.crmService.activeContract
    activeCrmUrl = activeCrmUrl.replace("contractNumber",contractNumber)
    common.putAPI(activeCrmUrl)
}

function rejectContract(config,contractNumber) {
    let rejectCrmUrl = config.basic.crmService[config.env] + config.data.crmService.rejectContract
    rejectCrmUrl = rejectCrmUrl.replace("contractNumber",contractNumber)
    return common.putAPI(rejectCrmUrl)
}

function terminateContract(config,contractNumber) {
    let terminateCrmUrl = config.basic.crmService[config.env] + config.data.crmService.terminateContract
    terminateCrmUrl = terminateCrmUrl.replace("contractNumber",contractNumber)
    common.putAPI(terminateCrmUrl)
}

function removeContract(config,contractNumber) {
    let removeCrmUrl = config.basic.crmService[config.env] + config.data.crmService.removeContract
    removeCrmUrl = removeCrmUrl.replace("contractNumber",contractNumber)
    return common.deleteAPI(removeCrmUrl)
}

function processContract(config,contractNumber, params) {
    let processCrmUrl = config.basic.crmService[config.env] + config.data.crmService.processContract
    processCrmUrl = processCrmUrl.replace("contractNumber",contractNumber)
    common.putAPI(processCrmUrl, {}, params)
}

async function checkDedup(contractNumber){
    try {
        const config = global.config
        const crmDedupUrl = config.basic.crmService[config.env] + config.data.crmService.dedupCash

        const contractData = await loanContractRepo.getLoanContract(contractNumber);
        if(!contractData) {
            common.log(`get data for check dedup error`,contractNumber)
        }

        let body = {
            "fullName": contractData.cust_full_name,
            "idNumber": contractData.id_number,
            "idIssueDt": contractData.id_issue_dt,
            "idIssuePlace": contractData.id_issue_place,
            "birthDate": moment(contractData.birth_date).format('yyyy-MM-DD'),            
            "phoneNumber": contractData.phone_number1,
            "gender": contractData.gender,
            "contractNumber": contractNumber,
            "chanel": SYS_CHANEL.MCC,
            "productType" : contractData.contract_type || "CASH",
            "partnerCode": contractData.partner_code
        }

        const headers = await aaaService.getLosKey(config)

        common.bodyLog(SERVICE_NAME.CRM_DEDUP,contractNumber,body)
        const crmResult = await common.postApiV2(crmDedupUrl,body,headers)
        common.serviceLog(SERVICE_NAME.CRM_DEDUP,contractNumber,crmResult)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.CRM,SERVICE_NAME.CRM_DEDUP,body,crmResult)
        return crmResult.data
    }
    catch(err) {
        common.log(`check dedup error ${err.message}`,contractNumber)
        return false
    }
}

async function createCustomerService(contractNumber) {
    try {
        const config = global.config
        const crmCreateServiceUrl = config.basic.crmService[config.env] + config.data.crmService.createService
       
        const contractData = await loanContractRepo.getLoanContract(contractNumber);
        let productCode = contractData.product_code

        const prdInfo = await productService.getProductInfoV2(productCode)
        
        if(!contractData || !prdInfo) {
            common.log(`get data for create customer service error`,contractNumber)
            console.log("contract data : ",JSON.stringify(contractData))
            console.log("product data : " ,JSON.stringify(prdInfo))
            return false
        }
        let body = {
            "systemType": SYS_CHANEL.MCC,
            "custId": contractData.cust_id,
            "contractNumber": contractNumber,
            "productIdCtlg": prdInfo.prdctId,
            "productName": prdInfo.prdctName,
            "channel": contractData.partner_code,
            "aprLimitAmt": contractData.request_amt,
            "tenor": contractData.request_tenor,
            "intRate": prdInfo.productVar[0].intRate,
            "rateType": prdInfo.productVar[0].intRateType,
            "creditIntRate1": prdInfo.productVar[0].creditIntRate1,
            "creditIntRate2": prdInfo.productVar[0].creditIntRate2,
            "creditIntRate3": prdInfo.productVar[0].creditIntRate3,
            "startDate": dateHelper.PG_DATE_TODAY(),
            "status": CRM_STATUS.PROCESS,
            "contractType" : contractData.contract_type
        }

        const headers = await aaaService.getLosKey(config)

        const crmCreateServiceRs = await common.postApiV2(crmCreateServiceUrl,body,headers)
        await loggingRepo.saveStepLog(
            contractNumber,
            SERVICE_NAME.CRM,
            SERVICE_NAME.CRM_CREATE_SERVICE,
            body,
            crmCreateServiceRs
        )
        await Promise.all([updateCustomerInfo(contractNumber),updateCustomerOtherAttribute(contractNumber)])
        if(crmCreateServiceRs.data.code == 1) {
            common.log("Create customer service error",contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`create customer service error ${err.message}`,contractNumber)
        return false
    }
}

async function updateCustomerInfo(contractNumber) {
    try {
        const ctr = await loanContractRepo.getLoanContract(contractNumber)
        const body = {
            custId : ctr.cust_id,
            idIssuePlace: ctr.id_issue_place,
            addressCur: ctr.address_cur,
            provinceCur: ctr.province_cur,
            districtCur: ctr.district_cur,
            wardCur: ctr.ward_cur,
            villageCur: null,
            addressPer: ctr.address_per,
            provincePer: ctr.province_per,
            districtPer: ctr.district_per,
            wardPer: ctr.ward_per,
            villagePer: null,
            marriedStatus: ctr.married_status,
            houseType: ctr.house_type || '',
            salaryMethod: ctr.salary_method || '',
            emplType: ctr.empl_type || '',
            jobType: ctr.job_type || '',
            jobTitle: ctr.job_title || '',
            emplName: ctr.empl_name || '',
            emplAddress: ctr.empl_address || '',
            emplCountry: ctr.empl_country ,
            emplProvince: ctr.empl_province,
            emplDistrict: ctr.empl_district,
            emplWard: ctr.empl_ward,
            referenceType1: ctr.reference_type_1,
            referenceName1: ctr.reference_name_1,
            referencePhone1: ctr.reference_phone_1,
            referenceType2: ctr.reference_type_2,
            referenceName2: ctr.reference_name_2,
            referencePhone2: ctr.reference_phone_2,
            bankAccount: ctr.bank_account,
            bankName: ctr.bank_name,
            bankCity: null,
            bankBranch: ctr.bank_branch,
            bankCode: ctr.bank_code,
            email: ctr.email,
        }

        let updateCustomerUrl = config.basic.crmService[config.env] + serviceEndpoint.CRM.updateCustomerInfo
        const updateRs = await common.postApiV2(updateCustomerUrl,body)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.CRM,SERVICE_NAME.CRM_UPDATE_CUSTOMER,body,updateRs)
        if(updateRs.status != 200) {
            return false
        }
        return true
    }
    catch(err) {
        common.log(`update customer service error : ${err.message}`,contractNumber)
        return false
    }
}

async function updateCustomerOtherAttribute(contractNumber) {
    try {
        const contractData = await loanContractRepo.getLoanContract(contractNumber)
        const body = {
            custId : contractData.cust_id,
            createdBy : "LOSMC",
            smeName : contractData.sme_name,
            smeTaxId : contractData.sme_tax_id,
            smePhoneNumber : contractData.sme_phone_number,
            smeRepresentationName : contractData.sme_representation_name,
            smeRepresentationDob : contractData.sme_representation_dob,
            smeRepresentationGender : contractData.sme_representation_gender,
            smeRepresentationPosition : contractData.sme_representation_position,
            smeRepresentationId : contractData.sme_representation_id,
            smeRepresentationIssueDate : contractData.sme_representation_issue_date,
            smeRepresentationIssuePlace : contractData.sme_representation_issue_place,
            smeRepresentationOtherId : contractData.sme_representation_other_id,
            smeRepresentationPhoneNumber : contractData.sme_representation_phone_number,
            smeRepresentationEmail : contractData.sme_representation_email,
            enterpriseType : contractData.enterprise_type,
            registrationNumber : contractData.registration_number,
            branchTaxId : contractData.branch_tax_id,
            authorizedName : contractData.authorized_name,
            authorizedDob : contractData.authorized_dob,
            authorizedGender : contractData.authorized_gender,
            authorizedPosition : contractData.authorized_position,
            api_input_param : contractData.authorized_id,
            authorizedIssueDate : contractData.authorized_issue_date,
            authorizedIssuePlace : contractData.authorized_issue_place,
            authorizedOtherId : contractData.authorized_id,
            authorizedPhoneNumber : contractData.authorized_phone_number,
            applicationUsed : '',
            smeEmail : contractData.sme_email,
            accountantStatus : contractData.accountant_status,
            accountantName : contractData.accountant_name,
            accountantGender : contractData.accountant_gender,
            accountantPhone : contractData.accountant_phone_number,
            accountantEmail : contractData.accountant_email,
        }
        let updateCustomerUrl = config.basic.crmService[config.env] + serviceEndpoint.CRM.updateCustomerAttribute
        const updateRs = await common.putAPIV2(updateCustomerUrl,body)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.CRM,SERVICE_NAME.CRM_UPDATE_CUSTOMER,body,updateRs)
        if(updateRs.status != 200) {
            return false
        }
        return true
    }  
    catch(err) {
        common.log(`update customer service error : ${err.message}`,contractNumber)
        return false
    }
}

async function updateActiveCrm(config,contractNumber) {
	const crmUrl = config.basic.crmService[config.env] +  config.data.crmService.updateData
    const contractData = await loanContractRepo.getLoanContract(contractNumber)
	const crmBody = {
		"contractNumber":contractNumber,
		"productCode": contractData.product_code,
		"productName": contractData.product_code,
		"channel": contractData.partner_code,
		"aprLimitAmt": contractData.approval_amt,
		"remainLimitAmt": contractData.approval_amt,
		"tenor":contractData.approval_tenor,
		"addressCur":contractData.address_cur,
		"provinceCur": contractData.province_cur,
		"districtCur": contractData.district_cur,
		"wardCur": contractData.ward_cur,
		"villageCur":contractData.village_cur || '',
		"addressPer": contractData.address_per,
		"provincePer": contractData.province_per,
		"districtPer":contractData.district_per,
		"wardPer":contractData.ward_per,
		"villagePer":contractData.village_per || ''
	}

	const crmUpdateActiveResult = await common.postApiV2(crmUrl,crmBody)
    await loggingRepo.saveStepLog(
        contractNumber,
        SERVICE_NAME.CRM,
        SERVICE_NAME.CRM_UPDATE_INFO,
        crmBody,
        JSON.stringify(crmUpdateActiveResult?.data || {})
    )
    const crmActiveResponse = await activeContractV2(config, contractNumber);
    await loggingRepo.saveStepLog(
        contractNumber,
        SERVICE_NAME.CRM,
        SERVICE_NAME.CRM_UPDATE_ACTIVE,
        '',
        JSON.stringify(crmActiveResponse || {})
    )
}

async function updateInactiveCrm(config,contractNumber) {
    const crmInActiveResponse = await removeContract(config, contractNumber);
    await loggingRepo.saveStepLog(
        contractNumber,
        SERVICE_NAME.CRM,
        SERVICE_NAME.CRM_UPDATE_INACTIVE,
        { contractNumber },
        JSON.stringify(crmInActiveResponse || {})
    )
}

async function createCustomerServiceKunn(kunnId) {
    try {
        const config = global.config
        const crmCreateServiceUrl = config.basic.crmService[config.env] + config.data.crmService.createService
       
        const kunnData = await kunnRepo.getKunnData(kunnId);
        let productCode = kunnData.kunn_code

        const contractNumber = kunnData.contract_number
        const [prdInfo, loanData] = await Promise.all([
            productService.getProductInfoV2(productCode),
            loanContractRepo.getLoanContract(contractNumber)
        ]);

        if(!kunnData || !prdInfo) {
            common.log(`get data for create customer service error`,kunnId)
            console.log("kunn data : ",JSON.stringify(kunnData))
            console.log("product data : " ,JSON.stringify(prdInfo))
            return false
        }
        let body = {
            "systemType": SYS_CHANEL.MCC,
            "custId": loanData.cust_id,
            "contractNumber": kunnId,
            "productIdCtlg": prdInfo.prdctId,
            "productName": prdInfo.prdctName,
            "channel": loanData.partner_code,
            "aprLimitAmt": loanData.request_amt,
            "tenor": loanData.request_tenor,
            "intRate": prdInfo.productVar[0].intRate,
            "rateType": prdInfo.productVar[0].intRateType,
            "creditIntRate1": prdInfo.productVar[0].creditIntRate1,
            "creditIntRate2": prdInfo.productVar[0].creditIntRate2,
            "creditIntRate3": prdInfo.productVar[0].creditIntRate3,
            "startDate": dateHelper.PG_DATE_TODAY(),
            "status": CRM_STATUS.PROCESS,
            "contractType" : loanData.contract_type
        }

        const headers = await aaaService.getLosKey(config)

        const crmCreateServiceRs = await common.postApiV2(crmCreateServiceUrl,body,headers);
        await loggingRepo.saveStepLog(kunnId,SERVICE_NAME.CRM,SERVICE_NAME.CRM_CREATE_SERVICE,body,crmCreateServiceRs)
        if(crmCreateServiceRs.data.code == 1) {
            common.log("Create customer service error",contractNumber)
            return false
        }
        return true
    }
    catch(err) {
        common.log(`create customer service error ${err.message}`,contractNumber)
        return false
    }
}

const activeContractV2 = async (config, contractNumber) => {
    let activeCrmUrl = config.basic.crmService[config.env] + config.data.crmService.activeContract
    activeCrmUrl = activeCrmUrl.replace("contractNumber",contractNumber)
    const response = await common.putAPI(activeCrmUrl)
    return response;
}

module.exports = {
    activeContract,
    rejectContract,
    terminateContract,
    removeContract,
    processContract,
    checkDedup,
    createCustomerService,
    updateCustomerInfo,
    updateCustomerOtherAttribute,
    updateActiveCrm,
    updateInactiveCrm,
    createCustomerServiceKunn
}