const moment = require('moment-timezone');
const timezone = "Asia/Ho_Chi_Minh";

//compare date with format YYYY-MM-DD
const isSameDate = (date1, date2) =>{
    return moment(date1).isSame(date2);
}

const isAfterDate = (date1, date2) =>{
    return moment(date1).isAfter(date2);
}

const isBeforeDate = (date1, date2) =>{
    return moment(date1).isBefore(date2);
}

const formatDate = (dateString, format) => {
    return moment(dateString).format(format);
}

const addHours = (dateString, hours) => {
    return moment(dateString).add(hours, 'hours');
}

const addDay = (dateString, day) => {
    return moment(dateString).add(day, 'day');
}

const addDays = (dateString, days) => {
    return moment(dateString).add(days, 'days');
}

const now = () => {
    return moment();
}

const nowUnix = () => {
    return moment().unix();
}

const nowFullUnix = () => {
    return moment().valueOf();
}

const YYYYMMDD = () => {
    return moment().format("YYYYMMDD");
}

const PG_DATE_TODAY = () => {
    return moment().format("YYYY-MM-DD");
}

const DDMMYYYY = () => {
    return moment().format("DDMMYYYY");
}

const VN_FORMAT_DATE = () => {
    return moment().format("DD/MM/YYYY");
}

const LMS_DATE = () => {
    return moment().tz(timezone).format("YYYY-MM-DD HH:mm:ss");
}

const getDate = function (){
    let date = moment().date();
    if(date < 10)
        date = "0" + date.toString();
    return date.toString();
}

const getMonth = function (){
    return moment().month() + 1;
}

const getYear = function (){
    return moment().year();
}

const LEFT = (str, chr) =>{
    return str.slice(0, chr - str.length);
}

const RIGHT = (str, chr) =>{
    return str.slice(str.length-chr, str.length);
}

const numdayOfYear = function() {
    let days = new Date().getFullYear() % 4 == 0 ? 366 : 365;
    return days;
}

function convertYMD2DMY(time) {
    if (time == "") return "";
    let y = time.substr(0,4)
    let m = time.substr(5,2)
    let d = time.substr(8,2)
    return d+'-'+m+'-'+y
}

function convertDMY2YMD(time) {
    if (time == "") return "";
    let y = time.substr(6,8)
    let m = time.substr(3,2)
    let d = time.substr(0,2)
    return y+'-'+m+'-'+d
  }

  async function dmyValid(dateStr) {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
  
    if (dateStr.match(regex) === null) {
      return false;
    }
  
    const [day, month, year] = dateStr.split('-');
  
    // 👇️ format Date string as `yyyy-mm-dd`
    const isoFormattedStr = `${day}-${month}-${year}`;
  
    const date = new Date(isoFormattedStr);
  
    const timestamp = date.getTime();
  
    if (typeof timestamp !== 'number' || Number.isNaN(timestamp)) {
      return false;
    }
  
    return date.toISOString().startsWith(isoFormattedStr);
}

/**
 * return MM
*/
const getCurrentMonth = () => {
    const now = new Date();
    const mm = String(now.getMonth() + 1).padStart(2, '0');
    return mm;
}
/**
* return DD
*/
const getCurrentDateDD = () => {
    const now = new Date();
    const dd = String(now.getDate()).padStart(2, '0');
    return dd;
}
/**
 * 
 * @param {Date} date - ex: new Date()
 */
const convertDateToDDMMYYYY = (date) => {
    const options = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    }
    const dateDDMMYYYY = date.toLocaleDateString('en-GB', options)
    return dateDDMMYYYY;
}

const toVnDateFormat = (dateStr)=>{
    if(!dateStr) return '';
    return moment(dateStr).format("DD/MM/YYYY");
}

const exportTodayFullDate = ()=>{
    const specificDate = moment();
    const dateInfo = {
        dd: specificDate.date(),
        mm: specificDate.month() + 1,
        yyyy: specificDate.year()
      };
    return dateInfo;
}
module.exports = {
    isSameDate,
    isAfterDate,
    isBeforeDate,
    formatDate,
    addHours,
    addDay,
    addDays,
    now,
    YYYYMMDD,
    PG_DATE_TODAY,
    DDMMYYYY,
    VN_FORMAT_DATE,
    nowUnix,
    nowFullUnix,
    LMS_DATE,
    getDate,
    getMonth,
    getYear,
    LEFT,RIGHT,
    numdayOfYear,
    convertYMD2DMY,
    convertDMY2YMD,
    dmyValid,
    getCurrentMonth,
    getCurrentDateDD,
    convertDateToDDMMYYYY,
    toVnDateFormat,
    exportTodayFullDate
}