const {getLoanContract} = require("../repositories/loan-contract-repo")
const {DATE_FORMAT,SERVICE_NAME,PARTNER_CODE, CONTRACT_TYPE} = require("../const/definition")
const {DE_BUSINESS_RULE_CODE} = require("../const/service-response-const")
const {serviceEndpoint} = require("../const/config")
const productService = require("./productService")
const dateHelper = require("./dateHelper")
const common = require('./common')
const loggingRepo = require("../repositories/logging-repo")
const loanContractRepo = require("../repositories/loan-contract-repo")
const { getValueCode_v3 } = require("./masterdataService")
const { getEKYCDoc } = require("../repositories/document")
const _ = require("underscore")
const { initHeaders } = require("./helper")

async function createDEBody(businessRuleCode, contractNumber){
    try{
        const ctr = await getLoanContract(contractNumber);
        if(!ctr)
            return false;
        let productCode = ctr.product_code
        let productInfo = await productService.getProductInfoV2(productCode);
        let prdctRiskClass = productInfo.prdctRiskClass

        let date_of_birth = dateHelper.formatDate(ctr.birth_date, DATE_FORMAT.DB_FORMAT);
        let issue_date = dateHelper.formatDate(ctr.id_issue_dt, DATE_FORMAT.DB_FORMAT);
        let date_birth, month_birth, year_birth,
            date_issue, month_issue, year_issue;

        if(date_of_birth !== undefined) {
            //format YYYY-MM-DD
            let dob = date_of_birth.toString();
            year_birth = dateHelper.LEFT(dob, 4) ;
            month_birth = dob.substr(5, 2);
            date_birth = dateHelper.RIGHT(dob, 2);
        }

        if(issue_date !== undefined) {
            issue_date = issue_date.toString();
            year_issue = dateHelper.LEFT(issue_date, 4) ;
            month_issue = issue_date.substr(5, 2);
            date_issue = dateHelper.RIGHT(issue_date, 2);
        }

        let bodyRequest = {
            "businessRuleCode": businessRuleCode,
            "mapId": contractNumber,
            "custId": ctr.cust_id,
            "serviceCode": 'MCC',
            "runManual" : 0,
            "variable": {
                "af0Landing": {
                    "employmentType": ctr.empl_type,
                    "productGroup": null,
                    "documentsCanProvide": null,
                    "offer": ctr.request_amt,
                    "loanTenorPrefer": ctr.request_tenor,
                    "uploadDocumentsFields": null
                },
                "af1PersonInfo": {
                    "fullName": ctr.cust_full_name,
                    "idNumber": ctr.id_number,
                    "otherIdNumber": ctr.other_id_number || null,
                    "phoneNumber": ctr.phone_number1,
                    "gender": ctr.gender,
                    "dateOfBirthOnly": date_birth,
                    "monthOfBirth": month_birth,
                    "yearOfBirth": year_birth,
                    "issuePlace": ctr.id_issue_place,
                    "dateOfIssue": date_issue,
                    "monthOfIssue": month_issue,
                    "yearOfIssue": year_issue,
                    "totalMonthlyNetIncome": ctr.monthly_income,
                    "otherIncome": ctr.other_income,
                    "totalMonthlyExpenses": ctr.m_household_expenses,
                    "02TickBoxes": null
                },
                "af2PersonInfoDetail": {
                    "currentAddress": {
                        "nation": "VN",
                        "cityOrProvince": ctr.province_cur,
                        "district": ctr.district_cur,
                        "ward": ctr.ward_cur,
                        "addressDetails": ctr.address_cur
                    },
                    "permanentAddress": {
                        "sameAsCurrentAddress": null,
                        "nation": "VN",
                        "cityOrProvince": ctr.province_per,
                        "district": ctr.district_per,
                        "ward": ctr.ward_per,
                        "addressDetails": ctr.address_per
                    },
                    "referenceInfor": {
                        "relationship1": ctr.reference_type_1,
                        "fullName1": ctr.reference_name_1,
                        "mobilePhoneNumber1": ctr.reference_phone_1,
                        "relationship2": ctr.reference_type_2,
                        "fullName2": ctr.reference_name_2,
                        "mobilePhoneNumber2": ctr.reference_phone_2
                    }
                },
                "af3JobInfo": {
                    "company": {
                        "jobType": ctr.job_type,
                        "jobTitle": ctr.job_title,
                        "companyName": null,
                        "nation": "VN",
                        "cityOrProvince": null,
                        "district": null,
                        "ward": null,
                        "addressDetails": null,
                        "currentWorkplacePhoneNumber": null
                    },
                    "moreJobInfo": {
                        "typesOfEmploymentContract": null,
                        "from": null,
                        "to": null,
                        "contractTerm": null,
                        "taxIdForSelfEmloyedCustomers": null,
                        "methodOfReceivingSalary": null,
                        "frequencyOfReceviningSalary": null,
                        "dateOfReceivingSalary": null
                    }
                },
                "af4OtherInfo": {
                    "moreInfo": {
                        "maritalStatus": ctr.married_status,
                        "houseType": ctr.house_type,
                        "yearsOfLivingAtTheCurrentAddress": null,
                        "noOfDependants": ctr.num_of_dependants,
                        "nationality": "VN"
                    },
                    "disbusementInfor": {
                        "loanPurpose": ctr.loan_purpose,
                        "methodOfDisbursement": ctr.disbursement_method == 'Transfer' ? 2 : 1,
                        "nameOfBeneficiary": ctr.bank_account_owner,
                        "beneficiaryBank": ctr.bank_code,
                        "cityOrProvince": null,
                        "bankBranch": ctr.bank_branch,
                        "bankAccountNumberOfBeneficiary": ctr.bank_account
                    },
                    "otherContact": {
                        "otherContact": ctr.other_contact_type || null,
                        "contactDetail": ctr.other_contact_value || null,
                        "mailingAddress": ctr.mailling_address || null,
                        "email": ctr.email || null,
                        "currentCity": null,
                        "provinceOfResidence": null,
                        "employment": null
                    }
                },
                "other": {
                    "saleChannel": null,
                    "submittedBy": null,
                    "partnerCode": ctr.partner_code,
                    "zaloScore": null,
                    "fizoScore": null,
                    "tsaScore": null,
                    "vegaScore": null,
                    "fizoEkycResult": null,
                    "fizoComment": null,
                    "prdctRiskClass": prdctRiskClass,
                    "sysType": 'MCC'
                }
            }
        }
        return bodyRequest;
    }
    catch(err){
        console.log(`Make request body error`)
        console.log(err)

        return null;
    }
}

async function createDeBodyV2(contractNumber){
    try {
        const ctr = await getLoanContract(contractNumber);
        if(!ctr) return false;
        const productCode = ctr?.product_code;
        const productInfo = await productService.getProductInfoV2(productCode);
        const riskGroup = productInfo?.prdctRiskClass;
        const masterData = await Promise.all([
            getValueCode_v3(ctr.ward_cur,'WARD'),
            getValueCode_v3(ctr.district_cur,'DISTRICT'),
            getValueCode_v3(ctr.province_cur,'PROVINCE'),
            getValueCode_v3(ctr.ward_per,'WARD'),
            getValueCode_v3(ctr.district_per,'DISTRICT'),
            getValueCode_v3(ctr.province_per,'PROVINCE'),
            getValueCode_v3(ctr.id_issue_place,'ISSUE_PLACE_VN'),
            getEKYCDoc(contractNumber)
        ]);
        const docs = masterData[7];
        const pid = _.where(docs, {doc_type: 'SPID'}) || _.where(docs, {doc_type: 'SNID'})
        const pic = _.where(docs, {doc_type: 'SPIC'})

        const body = {
            "partnerCode": ctr?.partner_code || null,
            "contractNumber": contractNumber,
            "custId": ctr?.cust_id || null,
            "losType": "LOS",
            "productType": ctr?.contract_type == CONTRACT_TYPE.CREDIT_LINE ? 'CREDITLINE' : 'CASH',
            "riskGroup": riskGroup || null,
            "productCode": ctr?.product_code || null,
            "customerName": ctr?.cust_full_name || null,
            "gender": ctr?.gender || 'M',
            "dateOfBirth": ctr?.birth_date || null,
            "idNumber": ctr?.id_number || null,
            "otherIdNumber": ctr?.other_id_number || null,
            "issueDate": ctr?.id_issue_dt || null,
            "issuePlace": ctr?.id_issue_place || null,
            "phoneNumber": ctr?.phone_number1 || null,
            "email": ctr?.email || null,
            "disbursementMethod": ctr?.disbursement_method == 'Transfer' ? 2 : 1,
            "accountNumber": ctr?.bank_account || null,
            "bankCode": ctr?.bank_code || null,
            "bankName": ctr?.bank_name || null,
            "beneficiaryName": ctr?.bank_account_owner || null,
            "temCountry": "VN",
            "temProvince": masterData[2],
            "temProvinceCode": ctr?.province_cur || null,
            "temDistrict": masterData[1],
            "temDistrictCode": ctr?.district_cur || null,
            "temWard": masterData[0],
            "temWardCode": ctr?.ward_cur || null,
            "temDetailAddress": ctr?.address_cur || null,
            "permanentCountry": "VN",
            "permanentProvince": masterData[5],
            "permanentProvinceCode": ctr?.province_per || null,
            "permanentDistrict": masterData[4],
            "permanentDistrictCode": ctr?.district_per || null,
            "permanentWard": masterData[3],
            "permanentWardCode": ctr?.ward_per || null,
            "permanentDetailAddress": ctr?.address_per || null,
            "nationality": "VN",
            "reference1": ctr?.reference_type_1 || null,
            "reference2": ctr?.reference_type_2 || null,
            "relativeReferenceName1": ctr?.reference_name_1 || null,
            "relativeReferenceName2": ctr?.reference_name_2 || null,
            "relativeReferencePhone1": ctr?.reference_phone_1 || null,
            "relativeReferencePhone2": ctr?.reference_phone_2 || null,
            "monthlyIncome": ctr?.monthly_income || null,
            "otherIncome": ctr?.other_income || null,
            "monthlyExpenses": ctr?.m_household_expenses || null,
            // "companyName": "********* Công Ty cổ phần than Mông Dương - Vinacomin",
            // "companyAddress": "268 Cộng Hòa",
            // "companyCountry": "VN",
            // "companyProvince": "79",
            // "companyDistrict": "766",
            // "companyWard": "26965",
            // "companyPhoneNumber": "01234567890",
            // "jobType": "Công nhân",
            // "jobTitle": "Nhân Viên/Chuyên Viên",
            "employmentType": ctr?.empl_type || null,
            // "employmentContractType": null,
            "marriedStatus": ctr?.married_status || null,
            "houseType": ctr?.house_type || null,
            "numOfDependents": ctr?.num_of_dependants || null,
            "loanPurpose": ctr?.loan_purpose || null,
            "loanAmount": ctr?.request_amt || null,
            "loanTenor": ctr?.request_tenor || null,
            "imageIdCard": pid.length > 0 ? pid[0].file_key : "",
            "imageSelfie": pic.length > 0 ? pic[0].file_key : "",
            "phoneNumber2": ctr?.phone_number2 || null,
            "phoneNumber3": ctr?.phone_number3 || null
        }

        return body;
    } catch (error) {
        console.log(`Make request body error`)
        console.log(err)

        return null;
    }
}

async function checkPreScore(contractNumber) {
    try {
        const config = global.config
        const partnerCode = await loanContractRepo.getPartnerCode(contractNumber)
        
        // const body = await createDEBody(DE_BUSINESS_RULE_CODE.PRESCORE_CODE,contractNumber)
        const body = await createDeBodyV2(contractNumber);
        let url = config.basic.decisionsV02[config.env] + config.data.deService.checkDeCashV2;
        const serviceName = 'DECISION';
        const flow = serviceName;
        const headers = initHeaders(serviceName, flow);
        if(partnerCode == PARTNER_CODE.VTP) {
            url = config.basic.decisionsV02[config.env] + serviceEndpoint.VTP.af1
        }
        const deResult = await common.postApiV2(url, body, headers)
        common.serviceLog(SERVICE_NAME.DE_PRESCORE,contractNumber,deResult)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.DE,SERVICE_NAME.DE_PRESCORE,body,deResult)
        return deResult.data
    }
    catch(err) {
        common.log(`call check de prescore error : ${err.message}`,contractNumber)
        return false
    }
}

async function checkInternalScore(contractNumber) {
    try {
        const config = global.config
        const partnerCode = await loanContractRepo.getPartnerCode(contractNumber)
        const body = await createDEBody(DE_BUSINESS_RULE_CODE.SCORING_CODE,contractNumber)
        common.bodyLog(SERVICE_NAME.DE_INTERNAL_SCORE,contractNumber,body)
        let url = config.basic.decisionsV02[config.env] + config.data.deService.checkDeCash
        const headers = initHeaders('DECISION', 'DECISION');
        if(partnerCode == PARTNER_CODE.VTP) {
            url = config.basic.decisionsV02[config.env] + serviceEndpoint.VTP.af2
        }
        const deResult = await common.postApiV2(url,body,headers)
        common.serviceLog(SERVICE_NAME.DE_INTERNAL_SCORE,contractNumber,deResult)
        loggingRepo.saveStepLog(contractNumber,SERVICE_NAME.DE,SERVICE_NAME.DE_INTERNAL_SCORE,body,deResult)
        return deResult.data
    }
    catch(err) {
        common.log(`call check de internal score error : ${err.message}`,contractNumber)
        return false
    }
}

module.exports = {
    checkPreScore,
    checkInternalScore
}