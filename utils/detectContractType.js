const detectType = async(id, req) => {
    const sqlHm = `select 1 from loan_contract where contract_number = $1 limit 1 `
    const sqlKu = `select 1 from kunn where kunn_id = $1 limit 1 `
    const HmResult = await req.poolRead.query(sqlHm, [id])
    const KuResult = await req.poolRead.query(sqlKu, [id])
    const isHm = HmResult.rows[0] || false
    const isKu = KuResult.rows[0] || false
    if (isHm && !isKu) { // Là hạn mức và không phải khế ước
        return 'HM'
    } else if (!isHm && isKu) { // Là khế ước và không phải hạn mức
        return 'KU'
    }
    else { // Không phải cả 2 hoặc là cả 2 (impossible case)
        return 'INVALID' 
    }
}

module.exports = {detectType}