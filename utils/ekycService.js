const {getLoanContract} = require("../repositories/loan-contract-repo")
const {getEKYCDoc,getEKYCSmeDoc, getEKYCKunnDoc} = require("../repositories/document")
const uuid = require("uuid")
const utils = require("./helper")
const {DATE_FORMAT} = require("../const/definition")
const {getFullAddress} = require("./masterdataService")
const _ = require("underscore")
const common = require("../utils/common")
const {SERVICE_NAME} = require("../const/definition")
const loggingRepo = require("../repositories/logging-repo")

async function checkEKYC(contract_number,partner_code,isSme=false){
    
    // const poolWrite = global.poolWrite;
    const config = global.config;
    let body;
    try{
        const data = await Promise.all([
            getLoanContract(contract_number),
            getEKYCDoc(contract_number),
            getEKYCSmeDoc(contract_number)
        ])
        const ctr = data[0]
        const docs = data[1]
        const docSme = data[2]
        let metaData = {}
        if(!ctr || (!docs&&!docSme)) {
            common.log("contract number error : ",contract_number)
            return false;
        }
        let addressRs;
        let idNumber;
        let name;
        let dob;
        let issueDate;
        let issuePlace;
        let pid;
        let pic;
        if(isSme){
            if(ctr.is_authorization_sign=='Y'){
                addressRs = await Promise.all([getFullAddress(config,ctr.authorized_province_per,ctr.authorized_district_per,ctr.authorized_ward_per),getFullAddress(config,ctr.authorized_province_cur,ctr.authorized_district_cur,ctr.authorized_ward_cur)])
                idNumber = ctr.authorized_id
                name = ctr.authorized_name.toUpperCase()
                dob = utils.formatDate(ctr.authorized_dob, DATE_FORMAT.DDMMYYYY)
                issueDate = utils.formatDate(ctr.authorized_issue_date, DATE_FORMAT.DDMMYYYY)
                issuePlace = ctr.authorized_issue_place
                pid = _.where(docSme, {doc_type: 'SPIDAR'})
                pic = _.where(docSme, {doc_type: 'SPAR'})
            }
            else{
                addressRs = await Promise.all([getFullAddress(config,ctr.sme_representation_province_per,ctr.sme_representation_district_per,ctr.sme_representation_ward_per),getFullAddress(config,ctr.sme_representation_province_cur,ctr.sme_representation_district_cur,ctr.sme_representation_ward_cur)])
                idNumber = ctr.sme_representation_id
                name = ctr.sme_representation_name.toUpperCase()
                dob = utils.formatDate(ctr.sme_representation_dob, DATE_FORMAT.DDMMYYYY)
                issueDate = utils.formatDate(ctr.sme_representation_issue_date, DATE_FORMAT.DDMMYYYY)
                issuePlace = ctr.sme_representation_issue_place
                pid = _.where(docSme, {doc_type: 'SPIDLR'})
                pic = _.where(docSme, {doc_type: 'SPLR'})
            }
        }else{
            addressRs = await Promise.all([getFullAddress(config,ctr.province_per,ctr.district_per,ctr.ward_per),getFullAddress(config,ctr.province_cur,ctr.district_cur,ctr.ward_cur)])
            idNumber = ctr.id_number
            name = ctr.cust_full_name.toUpperCase()
            dob = utils.formatDate(ctr.birth_date, DATE_FORMAT.DDMMYYYY)
            issueDate = utils.formatDate(ctr.id_issue_dt, DATE_FORMAT.DDMMYYYY)
            issuePlace = ctr.id_issue_place

            pid = _.where(docs, {doc_type: 'SPID'}) || _.where(docs, {doc_type: 'SNID'})
            if(utils.isNullOrEmpty(pid)) {
                pid = _.where(docs, {doc_type: 'SNID'})
            }
            pic = _.where(docs, {doc_type: 'SPIC'})
        }
        const perAddress = addressRs[0]
        const curAddress = addressRs[1]
        let request_id = uuid.v4();
        body = {
            "caseId": request_id,
            "contractNumber": contract_number,
            "channel": "LOS",
            "productType": "CASH",
            "partnerCode": partner_code,
            "files": {
                "selfie": pic.length > 0 ? pic[0].file_key : "",
                "idCard": pid.length > 0 ? pid[0].file_key : ""
            },
            "metadata": {
                "idNumber": idNumber,
                "name": name,
                "dob": dob,
                "nativePlace": perAddress,
                "address": curAddress,
                "issueDate": issueDate,
                "issuePlace": issuePlace
            }
        };
        let ekycUrl = config.basic.decisionsV02[config.env] + config.data.deService.eKYCUrlDeV2;
        const serviceName = 'EKYC';
        const flow = serviceName;
        const headers = utils.initHeaders(serviceName, flow);
        const result = await common.postApiV2(ekycUrl, body, headers);
        loggingRepo.saveStepLog(contract_number,SERVICE_NAME.EKYC,SERVICE_NAME.CHECK_EKYC,body,result)
        return result;
    }
    catch(err){
        common.log(`checkEKYC error: ${err.message}`);
        console.log(err);
        return null;
    }
}

async function checkEKYCKunn(kunnNumber, contractNumber, partnerCode){
    
    const config = global.config;
    let body;
    try{
        const data = await Promise.all([
            getLoanContract(contractNumber),
            getEKYCDoc(contractNumber),
            getEKYCKunnDoc(kunnNumber)
        ])
        const ctr = data[0]
        const docs = data[1]
        const kunnDocs = data[2]
        if(!ctr || (!docs&&!kunnDocs)) {
            common.log("contract number error : ",contractNumber)
            return false;
        }
        let addressRs;
        let idNumber;
        let name;
        let dob;
        let issueDate;
        let issuePlace;
        let pid;
        let pic;
        
        addressRs = await Promise.all([getFullAddress(config,ctr.province_per,ctr.district_per,ctr.ward_per),getFullAddress(config,ctr.province_cur,ctr.district_cur,ctr.ward_cur)])
        idNumber = ctr.id_number
        name = ctr.cust_full_name.toUpperCase()
        dob = utils.formatDate(ctr.birth_date, DATE_FORMAT.DDMMYYYY)
        issueDate = utils.formatDate(ctr.id_issue_dt, DATE_FORMAT.DDMMYYYY)
        issuePlace = ctr.id_issue_place

        pid = _.where(docs, {doc_type: 'SPID'}) || _.where(docs, {doc_type: 'SNID'})
        if(utils.isNullOrEmpty(pid)) {
            pid = _.where(docs, {doc_type: 'SNID'})
        }
        pic = _.where(kunnDocs, {doc_type: 'PIC'})
        
        const perAddress = addressRs[0]
        const curAddress = addressRs[1]
        let request_id = uuid.v4();
        body = {
            "caseId": request_id,
            "contractNumber": contractNumber,
            "channel": "LOS",
            "productType": "CASH",
            "partnerCode": partnerCode,
            "files": {
                "selfie": pic.length > 0 ? pic[0].file_key : "",
                "idCard": pid.length > 0 ? pid[0].file_key : ""
            },
            "metadata": {
                "idNumber": idNumber,
                "name": name,
                "dob": dob,
                "nativePlace": perAddress,
                "address": curAddress,
                "issueDate": issueDate,
                "issuePlace": issuePlace
            }
        };
        let ekycUrl = config.basic.decisionsV02[config.env] + config.data.deService.eKYCUrlDeV2;
        const serviceName = 'EKYC';
        const flow = serviceName;
        const headers = utils.initHeaders(serviceName, flow);
        const result = await common.postApiV2(ekycUrl, body, headers);
        loggingRepo.saveStepLog(kunnNumber,SERVICE_NAME.EKYC,SERVICE_NAME.CHECK_EKYC,body,result)
        return result;
    }
    catch(err){
        common.log(`checkEKYC error: ${err.message}`);
        console.log(err);
        return null;
    }
}

module.exports = {
    checkEKYC,
    checkEKYCKunn
}