const common = require('./common');
const s3Service = require('../upload_document/s3-service');
const storageContractSignedPath = "/mc-credit/signed-contract";
const documentRepo = require('../repositories/document');
const { KUNN_STATUS, STATUS } = require('../const/caseStatus');
const moment = require('moment');
const { updateData } = require('./sqlHelper');
const { SIGNED_TYPE, TABLE } = require('../const/variables-const');
const { ENDPOINT_CONST } = require('../const/endpoint-const');
const esigingRepo = require('../repositories/loan-esigning');

const signEvf = async (document, partnerCode) => {
    const { contract_number, file_key, doc_type } = document;
    const url = global.config.basic['bss-esigning-service'][global.config.env] + `/esigning/internal/${(partnerCode ?? '').toLowerCase()}/sign`;
    console.log(`Signing document: ${JSON.stringify(document)}. URL: ${url}`);
    const esignBody = { contractNumber: contract_number, filePath: file_key, type: doc_type?.replace('BTT', '') };
    const rs = await common.postApiV2(url, esignBody, {}, {});
    const { data: responseData } = rs || {};
    console.log(`[signEvf] Body: ${JSON.stringify(esignBody)} - Response: ${JSON.stringify(rs)}`);
    if (responseData.code !== 0) {
        throw new Error(`Error signing EVF: ${responseData?.msg}`);
    } else {
        let buffer = Buffer.from(responseData.data, 'base64');
        const fileName = moment().format('yyyyMMDDHHmmss') + `_${contract_number}_${doc_type}.pdf`;
        const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, storageContractSignedPath);
        document.url = s3Result.Location;
        document.file_path = s3Result.Key;
        document.file_name = fileName;
        document.signed_type = SIGNED_TYPE.EVF_SIGNED;
        await documentRepo.deleteLoanContractDocument({id: document.id});
        await documentRepo.insertLoanContractDocument(document);
    }
}

const customerSignEc = async (contract_number) => {
    const url = global.config.basic['bss-esigning-service'][global.config.env] + ENDPOINT_CONST.E_SIGNING.INTERNAL_CUSTOMER_SIGN_EC;
    const esignBody = { contractNumber: contract_number };
    const rs = await common.postApiV2(url, esignBody, {}, {});
    const { data: responseData } = rs || {};
    console.log(`[customerSignEc] Body: ${JSON.stringify(esignBody)} - Response: ${JSON.stringify(rs)}`);
    if (responseData.code !== 0) {
        throw new Error(`Error signing EVF: ${responseData?.msg}`);
    } else {
        let buffer = Buffer.from(responseData.data, 'base64');
        const fileName = `${contract_number}_signed.pdf`;
        const s3Result = await s3Service.uploadV2(global.config.data, fileName, buffer, storageContractSignedPath);
        await esigingRepo.saveSignedContract(s3Result.Location, contract_number)
    }
}

module.exports = {
    signEvf,
    customerSignEc,
}