const ExcelJS = require('exceljs');

/**
 * Export danh sách record sang file Excel (trả về buffer)
 * @param {Array<object>} records - Dữ liệu cần export
 * @returns {Promise<Buffer>} - Buffer của file Excel
 */
async function jsonToExcelBuffer(records, defaultKeys = null) {
  if ((!records || records.length === 0) && !defaultKeys) {
    throw new Error('No data to export');
  }
  records =records || [];
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Export');

  // Lấy keys từ phần tử đầu tiên làm header
  const headers = defaultKeys || Object.keys(records[0]);
  worksheet.columns = headers.map((key) => ({
    header: key,
    key: key,
    width: 20,
  }));

  // Ghi từng dòng vào worksheet
  records.forEach((row) => {
    worksheet.addRow(row);
  });

  // Trả về buffer để ghi file hoặc response
  const buffer = await workbook.xlsx.writeBuffer();
  return buffer;
}
module.exports = {
  jsonToExcelBuffer,
};