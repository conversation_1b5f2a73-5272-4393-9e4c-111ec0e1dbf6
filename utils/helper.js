const {caseStatusCode,caseStatus} = require("../const/caseStatus")
const moment = require('moment-timezone');
const timezone = "Asia/Ho_Chi_Minh";
const uuid = require("uuid");
const common = require('../utils/common')
const VARIABLE = require('../const/variables-const');
const { getKunnData } = require("../repositories/kunn-repo");
const { PARTNER_CODE } = require("../const/definition");
const { getDataByContractNumber } = require("../repositories/loan-atribute-repo");
const _ = require('lodash');
const axios = require('axios');
const mime = require('mime-types');
const fastXmlParser = require('fast-xml-parser');
const { URL } = require('url');
const numeral = require('numeral');
const { BadReqExp } = require("../exception/exception");
const { BadRequestResponse, ServerErrorResponse, SuccessResponse } = require("../base/response");
const { ERROR_CODE, MISA_ERROR_CODE } = require("../const/response-const");
const s3Service = require("../upload_document/s3-service");
numeral.register('locale', 'vi', {
	delimiters: {
	  thousands: '.',
	  decimal: ','
	},
	abbreviations: {
	  thousand: 'k',
	  million: 'm',
	  billion: 'b',
	  trillion: 't'
	},
	ordinal: function (number) {
	  return number === 1 ? 'er' : 'ème';
	},
	currency: {
	  symbol: '€'
	}
  });
numeral.locale('vi');

async function saveStatus(poolWrite,contractNumber,statusCode) {
	let statusDesc;
	if(caseStatusCode.hasOwnProperty(statusCode)) {
		statusDesc = caseStatusCode[statusCode]
	}
	
	const currentDate = new Date()
	const sql = "update loan_contract set status=$1,updated_date=$2,status_desc=$3 where contract_number = $4"
	poolWrite.query(sql,[statusCode,currentDate,statusDesc,contractNumber]).then().catch(error => common.log("INSERT - loan_contract: update status error","ERROR"))
}

async function saveStatusAsync(poolWrite,contractNumber,statusCode) {
	try {
		let statusDesc;
		if(caseStatusCode.hasOwnProperty(statusCode)) {
			statusDesc = caseStatusCode[statusCode]
		}
		const sql = "update loan_contract set status=$1,updated_date=now(),status_desc=$2 where contract_number = $3"
		await poolWrite.query(sql,[statusCode,statusDesc,contractNumber])
		return true
	}catch(err) {
		console.log(err)
		console.log(`update status error ${statusCode} : ${contractNumber}`)
		return false
	}
}

async function saveKUStatus(poolWrite,kunnNumber,statusCode) {
	const currentDate = new Date()
	const sql = "update kunn set status=$1,updated_date=$2 where kunn_id = $3"
	await poolWrite.query(sql,[statusCode,currentDate,kunnNumber]).then().catch(error => common.log("INSERT - kunn: update status error","ERROR"))
}
/*
function getPartnerCode(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select partner_code from loan_contract where contract_number = $1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			resolve(result.rows[0].partner_code)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}*/

async function getPartnerCode(poolRead,contractNumber) {
    try {
        const sql = "select partner_code  from loan_contract where contract_number = $1"
        const data = await poolRead.query(sql,[contractNumber])
        if(data.rowCount == 0) {
            return false
        }
        return data.rows[0].partner_code
    }
    catch(err) {
        common.log("get partner code and product code error ",contractNumber)
        return false
    }
}

function getProductCode(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select product_code from loan_contract where contract_number = $1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			resolve(result.rows[0].product_code)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

function getKUCode(poolRead,kunnNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select kunn_code from kunn where kunn_id = $1"
		poolRead.query(sql,[kunnNumber])
		.then(result => {
			resolve(result.rows[0].kunn_code)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

function getContractNumber(poolRead,kunnNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select contract_number from kunn where kunn_id = $1"
		poolRead.query(sql,[kunnNumber])
		.then(result => {
			resolve(result.rows[0].contract_number)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

function getKUNNNumber(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select kunn_id from kunn where contract_number = $1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			if(result.rows.length != 0) {
				resolve(result.rows.map(x => x.kunn_id))
			}
			else {
				resolve([])
			}
			
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}


const getYYYYMMDD = () => {
    const d = new Date()
    let month = d.getMonth() + 1
    let date = d.getDate()
    month = month < 10 ? '0' + month : month
    date = date < 10 ? '0' + date : date
    return '' + d.getFullYear().toString() + "-" + month + "-" + date 
}

function getPhoneNumber(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select phone_number1 from loan_contract where contract_number=$1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			resolve(result.rows[0].phone_number1)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

function getPhoneNumberSme(contractNumber) {
	const poolRead = global.poolRead
	return new Promise(function (resolve,reject) {
		const sql = "select sme_representation_phone_number from loan_contract where contract_number=$1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			resolve(result.rows[0].sme_representation_phone_number)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function getStatus(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select status from loan_contract where contract_number=$1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			resolve(result?.rows[0]?.status)
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

async function getStatusV2(poolRead,contractNumber) {
	
	const sql = "select status from loan_contract where contract_number=$1"
	const rs = await poolRead.query(sql,[contractNumber])
	if(rs.rowCount == 0) {
		return false
	}		
	return rs.rows[0].status
}

async function getKuStatus(poolRead,contractNumber) {
	const sql = "select status from kunn where contract_number=$1"
	const statusResult = await poolRead.query(sql,[contractNumber])
	return statusResult.rows
}

async function getOneKuStatus(poolRead,kunnNumber) {
	const sql = "select status from kunn where kunn_id=$1"
	const statusResult = await poolRead.query(sql,[kunnNumber])
	return statusResult.rows[0].status
}

function validContractnumber(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select count(id) from loan_contract lc where contract_number =$1;"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			//console.log(contractNumber)
			//console.log(result.rows[0].count)
			if(result.rows[0].count == 1) {
				resolve(true)
			}
			else {
				resolve(false) // system run wrong
			}
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

function nonAccentVietnamese(str) {
    str = str.toLowerCase();
	str = str.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a");
	str = str.replace(/[èéẹẻẽêềếệểễ]/g, "e");
	str = str.replace(/[ìíịỉĩ]/g, "i");
	str = str.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o");
	str = str.replace(/[ùúụủũưừứựửữ]/g, "u");
    str = str.replace(/[ỳýỵỷỹ]/g, "y");
    str = str.replace(/đ/g, "d");
    // Some system encode vietnamese combining accent as individual utf-8 characters
    str = str.replace(/[\u0300\u0301\u0303\u0309\u0323]/g, ""); // Huyền sắc hỏi ngã nặng
    str = str.replace(/[\u02C6\u0306\u031B]/g, ""); // Â, Ê, Ă, Ơ, Ư

    return str.toUpperCase();
}

async function updateOfferLoanContract(poolRead,poolWrite,contractNumber,offerId) {
	const sql1 = "select offer_amt ,int_rate,tenor from loan_offer_selection los where id=$1"
	let data = await poolRead.query(sql1,[offerId])
	data = data.rows[0]
	const approvalAmt = data.offer_amt
	const intRate = data.int_rate
	const tenor = data.tenor

	const curDate = new Date()
	const sql2 = "update loan_contract set offer_id=$1,approval_amt=$2,approval_tenor=$3,approval_int_rate=$4,updated_date=$5,approval_date=$5 where contract_number=$6"
	poolWrite.query(sql2,[offerId,approvalAmt,tenor,intRate,curDate,contractNumber])
	.then(()=> console.log("update offer in loan contract success"))
	.catch(error => console.log(error))
}

function getAllContractData(poolRead,contractNumber) {
	return new Promise(function (resolve,reject) {
		const sql = "select * from loan_contract where contract_number=$1"
		poolRead.query(sql,[contractNumber])
		.then(result => {
			//console.log(result.rows)
			resolve(result.rows[0])
		})
		.catch(error => {
			console.log(error)
			reject(false)
		})
	})
}

async function getDocName(poolRead,docID) {
	const sql = "select doc_type from loan_contract_document where doc_id = $1"
	const docRs = await poolRead.query(sql,[docID])
	if(docRs.rows.length != 0) {
		return docRs.rows[0].doc_type
	}
	else {
		return ''
	}
}

const isNullOrEmpty = function(value){
    if(value === undefined || value === null || value.toString().trim() === "" || !value)
        return true;
    return false;
}

const formatDate = (dateString, format) => {
    return moment(dateString).format(format);
}

function _calculateAge(birthday,format) { 
	let birthDate = new Date(formatDate(birthday,format));
    let ageDifMs = Date.now() - birthDate.getTime();
    let ageDate = new Date(ageDifMs); // miliseconds from epoch
    return Math.abs(ageDate.getUTCFullYear() - 1970);
}

function convertDMY2YMD(stringDate) {
    const day = stringDate.slice(0,2)
    const month = stringDate.slice(3,5)
    const year = stringDate.slice(6,10)
    return year + "-" + month + "-" + day
}

let caculateAge = function (dob) {
    // date format  yyyy-dd-mm
    let dobDate = new Date(dob);
    let dobYear = dobDate.getFullYear();
    let dobMonth = dobDate.getMonth();
    let dobDay = dobDate.getDate();
    
    let currentDate = new Date();
    let currentYear = currentDate.getFullYear();
    let currentMonth = currentDate.getMonth();
    let currentDay = currentDate.getDate();

    let age = currentYear - dobYear;
    if (currentMonth < dobMonth) {
        age -= 1;
    }
    if (currentMonth == dobMonth) {
        if (currentDay < dobDay) {
            age -= 1;
        }
    }

    return age
}

let caculateMonth = function (regist_date) {
    // date format  yyyy-dd-mm
    let registDate = new Date(regist_date);
    let registYear = registDate.getFullYear();
    let registMonth = registDate.getMonth();
    
    let currentDate = new Date();
    let currentYear = currentDate.getFullYear();
    let currentMonth = currentDate.getMonth();

    let months;
    months = (currentYear - registYear) * 12;
    months -= registMonth;
    months += currentMonth;
    return months <= 0 ? 0 : months;
}

function convertNumber(number)
{
	let strNumber = "";
	strNumber= String(number);
	return strNumber.replace(/\d(?=(?:\d{3})+(?!\d))/g, '$&,')
}

function PV(rate, nper, pmt)
{
    return pmt / rate * (1 - Math.pow(1 + rate, -nper))
}

function round_decimals(original_number, decimals) {
	let result1 = original_number * Math.pow(10, decimals)
	let result2 = Math.round(result1)
	let result3 = result2 / Math.pow(10, decimals)
	return (result3)
}  

function formatCash(str) {
	str = round_decimals(str,0)
	str = String(str)
    return str.split('').reverse().reduce((prev, next, index) => {
        return ((index % 3) ? next : (next + ',')) + prev
    })
}

function genRequestId(partnerCode){
	const currentTimestamp = new Date().getTime();
	const requestId = partnerCode + currentTimestamp;
	return requestId;
}

function getDifferencesDays(firstDate, secondDate) {

	const fromDate = moment(firstDate)
  
	const toDate = moment(secondDate)
  
	const differencesDays = fromDate.diff(toDate, 'days')
  
	return Math.abs(differencesDays)
  
}

async function sliceIntoChunks(arr, chunkSize) {
    const res = [];
    for (let i = 0; i < arr.length; i += chunkSize) {
        const chunk = arr.slice(i, i + chunkSize);
        res.push(chunk);
    }
    return res;
}

/**
 * Ham tinh ngay tiep theo chu ky
 * @param {*} after khoang cach thang nhay buoc
 * @param {*} objDate
 * @returns
 */
 const calNextCycleDate2 = function (after, objDate) {
	let current
	const month30day = [4, 6, 9, 11]
	let month = Number(objDate.split('-')[1]) // months from 1-12
	const day = Number(objDate.split('-')[2])
	let year = Number(objDate.split('-')[0])
	if (month == 12) {
		month = 1
		year = year + 1
	} else {
		month = month + 1
	}
	if (after == 1 && month30day.includes(month) && day == 31) {
		if(month.toString.length===1) month=`0${month}`;
		current = year + '-' + month + '-' + 30
	} else if (after == 1 && month == 2 && (day == 30 || day == 31 || day == 29)) {
		if(month.toString.length===1) month=`0${month}`;
		current = year % 4 == 0 ? year + '-' + month + '-' + 29 : year + '-' + month + '-' + 28
	} else {
		if(month.toString.length===1) month=`0${month}`;
		current = year + '-' + month + '-' + day
	}
	return (after == 1) ? current : calNextCycleDate2(after - 1, current)
}

const getFileNameFromUrl = function (url) {
    let urlPart = url.split('/');

    if (urlPart.length > 0) {
    	return urlPart[urlPart.length - 1]?.split('?')?.[0]
	}

	return '';
}

function initHeaders(serviceName, flow){
	const header = {
		client_id: 'los_mc',
		request_id: uuid.v4(),
		service_name: serviceName,
		flow: flow,
		'Content-Type': 'application/json'
	}
	return header;
}

const isNullOrEmptyV2 = function(value){
    if(value === undefined || value === null || value.toString().trim() === "" || !value || isNaN(value))
        return true;
    return false;
}

const isNullOrEmptyV3 = function(value){
    if(value === undefined || value === null || value.toString().trim() === "")
        return true;
    return false;
}

const isMcaKunn = function(requestId){
	const newKunnRequest = ['MCA_KOV','MCA_SPL','MCA_MCA','SMA_SMA'];
	const check = newKunnRequest.includes(requestId?.slice(0, 7));
	if(check) return true;
	else return false;
}

const isSendNotiAppMc = async function(kunnNumber){
	try {
		const kunnData = await getKunnData(kunnNumber)
		const requestKunnId = kunnData?.request_id
		const partnerCode = kunnData?.partner_code
		const _isMcaKunn = isMcaKunn(requestKunnId)
		if ( [PARTNER_CODE.MCAPP, PARTNER_CODE.SMA, PARTNER_CODE.SMASYNC].includes(partnerCode) || ([PARTNER_CODE.KOV, PARTNER_CODE.SPL].includes(partnerCode) && _isMcaKunn)) return true
		else return false
	} catch (err) {
		common.log('check send noti app mc error',kunnNumber)
		console.log(err?.message)
	}
}

const isIncreaseLimitLoanFlow = async function(contractNumber){
	const rs = await getDataByContractNumber(contractNumber)
	const limitContractNumber = rs?.limitContractNumber
	if(!isNullOrEmpty(limitContractNumber)) return true;
	else return false;
}

const detectSuperAppSchema = function(productCode){
	if(isNullOrEmpty(productCode)) return ""
	if(productCode.includes("VIP")) return "MC_VIP"
	if(productCode.includes("SUB")) return "MC_SUB"
	if(productCode.includes("PREMIUM")) return "MC_PREMIUM"
	if(productCode.includes("STANDARD")) return "MC_STANDARD"
	else return ""
}

function convertCamelToSnake(obj) {
  if (_.isArray(obj)) {
    return obj.map(item => convertCamelToSnake(item));
  } else if (_.isObject(obj) && obj !== null) {
    return _.mapValues(_.mapKeys(obj, (value, key) => _.snakeCase(key)), value => convertCamelToSnake(value));
  }
  return obj;
}

function convertSnakeToCamel(obj) {
	if (_.isArray(obj)) {
		return obj.map(item => convertSnakeToCamel(item));
	} else if (_.isObject(obj) && obj !== null) {
		return _.mapValues(_.mapKeys(obj, (value, key) => _.camelCase(key)), value => convertSnakeToCamel(value));
	}
	return obj;
}

function isValidDate(value) {
	return value instanceof Date && !isNaN(value.getTime());
  }

const snakeToCamel = (obj)=> {
	if(isValidDate(obj))
	{
		return  moment(obj).format('YYYY-MM-DD');
	}
	if (_.isArray(obj)) {
	  return obj.map(item => snakeToCamel(item));
	} else if (_.isObject(obj) && obj) {
	  return _.mapValues(_.mapKeys(obj, (value, key) => _.camelCase(key)), value => snakeToCamel(value));
	}
	return obj;
  }

const getFileSizeSync = (filePath) => {
  try {
    const stats = fs.statSync(filePath);
    return stats.size; // Size in bytes
  } catch (error) {
    console.error(`Error getting file size for ${filePath}:`, error);
    return null;
  }
};

const getFileSizeFromUrl = async (url) => {
  try {
    const response = await axios.head(url);
    const contentLength = response.headers['content-length'];
    
    if (contentLength) {
      return parseInt(contentLength, 10);
    } else {
      console.error('Content-Length header is not available');
      return null;
    }
  } catch (error) {
    console.error('Error getting file size from URL:', error);
    return null;
  }
};

function getFileExtension(contentType) {
  return mime.extension(contentType);
}

function getFileKeyFromUrl(fileUrl) {
	if (!fileUrl) {
		return fileUrl
	}
	const url = new URL(fileUrl);
	const relativePath = url.pathname.substring(1); // Loại bỏ ký tự '/' ở đầu
	return relativePath;
}

function xmlToJson(xml) {
  const parser = new fastXmlParser.XMLParser();
  const jsonObj = parser.parse(xml);
	return jsonObj;
}

function getContentTypeFromMisaUrl(url) {
	const myUrl = new URL(url);
	const contentType = myUrl.searchParams.get('contentType');
	return contentType;
}

function isNumeric(str) {
	if (typeof str != "string") return false // we only process strings!  
	return !isNaN(str) && // use type coercion to parse the _entirety_ of the string (`parseFloat` alone does not do this)...
		   !isNaN(parseFloat(str)) // ...and ensure strings of whitespace fail
  }

const formatNumberVietnamese = (input, currency = 'vnd') => {
	input = input.toString();
	if (!isNumeric(input)) return '';
	if (currency === 'vnd') return numeral(input).format();
	return numeral(input).format('0,0.00');
};


const formatRateNumber = (input) => {
	input = input.toString();
	if (!isNumeric(input)) return '';
	return numeral(input).format('0,0.00');
};
  
const throwBadReqError = (location, message, code = MISA_ERROR_CODE.E400) => {
  const errors = [
    {
		code: code.code,
		location,
		message: code.message,
		details: message
    },
  ];
  throw new BadRequestResponse(errors, "Request is invalid");
};

const throwServerError = (message, code = MISA_ERROR_CODE.E500) => {
  const errors = [
    {
      code: code.code,
      location: "",
      message: code.message,
	  details: message
    },
  ];
  throw new ServerErrorResponse(
    null,
    message || "Internal Server Error",
    errors
  );
};

const responseSuccess = (data)=>{
	return new SuccessResponse(data,"Success");
}

const toArray = (data) => {
	try {
	  if (Array.isArray(data)) {
		return data;
	  } else if (typeof data === "object" && data) {
		return [data];
	  } else return [];
	} catch (error) {}
	return [];
  }

const upperAndRemoveTrim = (str)=>{
	return str.toUpperCase().trim();
}

const removeVietnameseTones =(str) =>{
	try {
	  str = str.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a");
	  str = str.replace(/[èéẹẻẽêềếệểễ]/g, "e");
	  str = str.replace(/[ìíịỉĩ]/g, "i");
	  str = str.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o");
	  str = str.replace(/[ùúụủũưừứựửữ]/g, "u");
	  str = str.replace(/[ỳýỵỷỹ]/g, "y");
	  str = str.replace(/đ/g, "d");
	  str = str.replace(/[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]/g, "A");
	  str = str.replace(/[ÈÉẸẺẼÊỀẾỆỂỄ]/g, "E");
	  str = str.replace(/[ÌÍỊỈĨ]/g, "I");
	  str = str.replace(/[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]/g, "O");
	  str = str.replace(/[ÙÚỤỦŨƯỪỨỰỬỮ]/g, "U");
	  str = str.replace(/[ỲÝỴỶỸ]/g, "Y");
	  str = str.replace(/Đ/g, "D");
	  // Combining Diacritical Marks
	  str = str.replace(/[\u0300\u0301\u0303\u0309\u0323]/g, ""); // huyền, sắc, hỏi, ngã, nặng
	  str = str.replace(/[\u02C6\u0306\u031B]/g, ""); // mũ â (ê), mũ ă, mũ ơ (ư)
	} catch (error) {
	  return str;
	}
  
	return str;
  }

  const hasDuplicateByFields = (list, fields) => {
    const seenValues = new Set();

    for (const obj of list) {
        // Kết hợp giá trị các field thành một chuỗi duy nhất
        const combinedValue = fields.map(field => obj[field]).join('|');
        if (seenValues.has(combinedValue)) {
            return true; // Có trùng lặp
        }
        seenValues.add(combinedValue);
    }

    return false; // Không có trùng lặp
};

function getPagination(page, size) {
	const limit = size ? size : 10;
	const offset = page ? (page - 1) * limit : 0;

	return { limit, offset };
}

const formatDateIsoVN = (dateString) => {
	if(!dateString) return '';
    return moment(dateString).tz("Asia/Ho_Chi_Minh").format('YYYY-MM-DD');
}

function transformObject(obj,comments, path = "") {
    if (typeof obj !== 'object' || obj === null) {
		const paramComments = (comments.filter(x => x.paramLocation === path ||  x.param_location === path) || []).sort((a, b) => b.id - a.id);

        return { value: obj, comment: paramComments?.[0]?.comment || null, paramLocation: path }; // Nếu là giá trị cuối cùng, thêm location
    }

    if (Array.isArray(obj)) {
        return obj.map((item, index) => transformObject(item, comments,`${path}[${index}]`)); // Xử lý array với index
    }

    const result = {};
    for (const [key, value] of Object.entries(obj)) {
        result[key] = transformObject(value,comments, path ? `${path}.${key}` : key); // Tạo location theo đường dẫn
    }
    return result;
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function arraysEqualObjectsUnordered(arr1, arr2) {
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length) return false;

  const matched = new Array(arr2.length).fill(false);

  for (const obj1 of arr1) {
    let found = false;
    for (let i = 0; i < arr2.length; i++) {
      if (!matched[i] && _.isEqual(obj1, arr2[i])) {
        matched[i] = true;
        found = true;
        break;
      }
    }
    if (!found) return false;
  }

  return true;
}

const getServiceLink = function(config, service){
    try{
        let configService = config.basic[service],
            url = config.basic.bus.api;

        if(configService !== undefined){
            if(global.config.env === "internalLb") {
                url = configService.internalLb === undefined ? "" : configService.internalLb;
            }
            else{
                url = configService.localhost === undefined ? "" : configService.localhost;
            }
        }
        //console.log("CALL URL: " + url);
        return url;
    }catch(err){
        console.log(`Get ${service} link error`)
        console.log(err);
        return config.basic.bus.api;
    }
}

function generateDocumentNo() {
    const now = new Date();
    const yy = String(now.getFullYear()).slice(-2);
    const mm = String(now.getMonth() + 1).padStart(2, '0');
    const dd = String(now.getDate()).padStart(2, '0');
    const hh = String(now.getHours()).padStart(2, '0');
    const mi = String(now.getMinutes()).padStart(2, '0');
    const ss = String(now.getSeconds()).padStart(2, '0');

    const dateTime = `${yy}${mm}${dd}${hh}${mi}${ss}`;
    const random4 = Math.floor(Math.random() * 1e4).toString().padStart(4, '0');

    return `${dateTime}${random4}`;
}

/**
 * Create an object composed of the picked object properties
 * @param {Object} object
 * @param {string[]} keys
 * @returns {Object}
 */
const pickKeysFromObject = (object, keys) => {
  return keys.reduce((obj, key) => {
    if (object && Object.prototype.hasOwnProperty.call(object, key)) {
      // eslint-disable-next-line no-param-reassign
      obj[key] = object[key];
    }
    return obj;
  }, {});
};

function findImageOffset(buffer) {
  let offset = buffer.indexOf(Buffer.from([0xff, 0xd8]));
  if (offset !== -1) return { offset, format: "jpg" };

  const jp2Sig = Buffer.from([
	0x00, 0x00, 0x00, 0x0c, 0x6a, 0x50, 0x20, 0x20, 0x0d, 0x0a,
  ]);
  offset = buffer.indexOf(jp2Sig);
  if (offset !== -1) return { offset, format: "jp2" };

  return null;
}

function extractImageFromDG2(base64Str) {
  const buffer = Buffer.from(base64Str, "base64");

  const result = findImageOffset(buffer);
  if (!result) {
	throw new Error("Không tìm thấy ảnh trong DG2 (không phải JPEG/JPEG2000)");
  }
  const { offset, format: mimetype } = result;
  const imageBuffer = buffer.slice(offset);

  return { buffer: imageBuffer, mimetype };
}

const getSelfieImgFromDG2 = async (dg2, contractNumber) => {
	const { buffer, mimetype } = extractImageFromDG2(dg2);
	const fileName = `${contractNumber}_nfc_image.${
		mimetype === "jpg" ? "jpg" : "jp2"}`;
	const s3Rs = await s3Service.upload(
		global.config.data,
		fileName,
		buffer,
		'/mc-los/uploads/nfc-image'
	);
	return { s3Rs, fileName };
};


  
module.exports = {
	formatRateNumber,
	formatNumberVietnamese,
    saveStatus,
	getPartnerCode,
	getYYYYMMDD,
	getProductCode,
	getKUCode,
	getPhoneNumber,
	numberWithCommas,
	getStatus,
	getContractNumber,
	validContractnumber,
	getKUNNNumber,
	nonAccentVietnamese,
	updateOfferLoanContract,
	getAllContractData,
	saveKUStatus,
	getKuStatus,
	getOneKuStatus,
	getDocName,
	saveStatusAsync,
	isNullOrEmpty,
	getStatusV2,
	formatDate,
	convertDMY2YMD,
	caculateAge,
	convertNumber,
	caculateMonth,
	PV,
	round_decimals,
	formatCash,
	genRequestId,
	getPhoneNumberSme,
	getDifferencesDays,
	sliceIntoChunks,
	calNextCycleDate2,
    getFileNameFromUrl,
	initHeaders,
	isNullOrEmptyV2,
	isMcaKunn,
	isSendNotiAppMc,
	isIncreaseLimitLoanFlow,
	detectSuperAppSchema,
	convertCamelToSnake,
	getFileSizeSync,
	getFileSizeFromUrl,
	getFileExtension,
	getFileKeyFromUrl,
	xmlToJson,
	getContentTypeFromMisaUrl,
	snakeToCamel,
	throwBadReqError,
	toArray,
	upperAndRemoveTrim,
	throwServerError,
	responseSuccess,
	isNullOrEmptyV3,
	removeVietnameseTones,
	hasDuplicateByFields,
	getPagination,
	formatDateIsoVN,
	transformObject,
	convertSnakeToCamel,
	delay,
	arraysEqualObjectsUnordered,
	getServiceLink,
	generateDocumentNo,
  	pickKeysFromObject,
  	getSelfieImgFromDG2
}