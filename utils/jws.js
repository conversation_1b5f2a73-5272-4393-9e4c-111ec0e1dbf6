const jose = require("jose");

const signJws = async (privateKey, data, algorithm = "ES256") => {
  // Mã hóa
  privateKey = Buffer.from(privateKey, "base64").toString("utf-8");
  const ecPrivateKey = await jose.importPKCS8(privateKey, algorithm);
  const jws = await new jose.CompactSign(new TextEncoder().encode(data))
    .setProtectedHeader({ alg: "ES256" })
    .sign(ecPrivateKey);

  return jws;
};

const verifyJws = async (publicKey, encrypted, algorithm = "ES256") => {
  // Giai mã
  publicKey = Buffer.from(publicKey, "base64").toString("utf-8");
  const decoder = new TextDecoder();
  const ecPublicKey = await jose.importSPKI(publicKey, algorithm);

  const { payload, protectedHeader } = await jose.compactVerify(
    encrypted,
    ecPublicKey
  );

  const verifySigned = decoder.decode(payload);
  return {
    protectedHeader,
    verifySigned,
  };
};

const decryptDataJws = async (
    jws,
  { partnerPublicKey } = {}
) => {
  try {
    //verify sign
    if (!partnerPublicKey) {
      console.log(`[decryptDataJws] Error partner public key not found`);
      throw new Error('Partner public key not found')
    }

    const verified = await verifyJws(partnerPublicKey, jws);
    const verifySigned = verified.verifySigned;
    console.log(`data:`, verifySigned);
    return JSON.parse(verifySigned);
  } catch (error) {
    console.log(`[decryptDataJws] Error ${error}`);
    throw error;
  }
};

const signDataJws = async (
  data,
  { evnEcPrivateKey } = {}
) => {
  if (typeof data !== "string") {
    data = JSON.stringify(data);
  }

  //sign
  evnEcPrivateKey = evnEcPrivateKey ?? global.env.EVN_EC_PRIVATE_KEY;
  return await signJws(evnEcPrivateKey, data);
};

module.exports = {
  decryptDataJws,
  signDataJws,
};
