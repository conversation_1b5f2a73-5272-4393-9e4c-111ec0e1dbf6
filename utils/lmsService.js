const { serviceEndpoint } = require("../const/config")
const common = require("./common")

async function getInstallmentKunn(kunnNumber) {
    try {
        const installmentUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.getInstallment}${kunnNumber}`
        const data = await common.getApiV2(installmentUrl)
        return data?.data?.data
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function getRemainingPayment(kunnNumber) {
    try {
        const installmentUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.getInstallment}${kunnNumber}`
        const data = await common.getApiV2(installmentUrl)
        const datas = data?.data?.data
        const countRemainingPayment = datas.filter(item => item.invoiced === 0).length;
        const countTotal = datas.length;
        const rs = `${countRemainingPayment}/${countTotal}`
        return rs
    }
    catch(err) {
        console.log(err)
        return false
    }
}

async function refundMoney(data) {
    try {
        const refundUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.refundMoney}`
        const response = await common.postApiV2(refundUrl, data)
        return response?.data
    } catch (error) {
        console.error("Error in refundMoney:", error)
        return false
    }
}

async function transferCase(data) {
    try {
        const transferUrl = `${global.config.basic.lmsMc[global.config.env]}${serviceEndpoint.LMS_MC.tranferCase}`
        const response = await common.postApiV2(transferUrl, data)
        return response?.data
    } catch (error) {
        console.error("Error in transferCase:", error)
        return false
    }
}

module.exports = {
    getInstallmentKunn,
    getRemainingPayment,
    refundMoney,
    transferCase,
}