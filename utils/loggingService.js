const e = require("express")
const {caseStatus} = require("../const/caseStatus")
const {IGNORE_LOG_LIST, ENABLE_LOG_LIST} = require("../const/definition")
const _ = require("lodash")

function saveUpdateHist(poolWrite,updatedField,oldValue,newValue,updatedBy,contractNumber,count_assignment) {
    const sql = 'insert into update_log_hist (updated_field,old_value,new_value,updated_by,contract_number,count_assignment) values ($1,$2,$3,$4,$5,$6)'
    poolWrite.query(sql,[updatedField,oldValue,newValue,updatedBy,contractNumber,count_assignment])
}

function saveCallBack(poolWrite,body,response,contractNumber) {
    const sql = 'insert into callback_log (contract_number,callback_body,callback_response) values ($1,$2,$3)'
    poolWrite.query(sql,[contractNumber,body,response])
}

function saveRequest(poolWrite,body,response,contractNumber) {
    const sql = 'insert into request_log (contract_number,request_body,request_response) values ($1,$2,$3)'
    poolWrite.query(sql,[contractNumber,body,response])
}

async function saveRequestV2(poolWrite,body,response,contractNumber,requestId,partnerCode, requestType,url) {
    const sql = 'insert into request_log (contract_number,request_body,request_response,request_id,partner_code, request_type, url) values ($1,$2,$3,$4,$5,$6,$7)'
    return await poolWrite.query(sql,[contractNumber,body,response,requestId,partnerCode, requestType, url])
}

function saveServiceLog(poolWrite,contract_number,checkType,body,response) {
    const sql = "insert into service_log(contract_number,check_type,service_body,service_response) values ($1,$2,$3,$4)"
    poolWrite.query(sql,[contractNumber,checkType,body,response])
}

function logRequest(req,res,next) {
    try {
        let contractNumber;
        let requestLog;
        
        if(req.method == 'GET') {
            contractNumber = req.query.contractNumber || req.query.contract_number || req.query.contractnumber
        }
        else if(req.body != undefined) {
            contractNumber = req.body.contractNumber || req.body.contract_number || req.body.contractnumber
        }

        let requestId;
        if(req.method == 'GET') {
            requestId = req.query.requestId || req.query.requestID || req.query.request_id
        }
        else if(req.body != undefined) {
            requestId = req.body.requestId || req.body.requestID || req.body.request_id
        }

        if(contractNumber != undefined && req.body !== undefined) {
            requestLog =  `${contractNumber} | ${req.url} | ${JSON.stringify(req.body)}`
        }
        else if(requestId != undefined && req.body !== undefined) {
            requestLog = `${requestId} | ${req.url} | ${JSON.stringify(req.body)}`    
        }
        else if(req.body !== undefined) {
            requestLog = `${req.url} | ${JSON.stringify(req.body)}`
        }
        else if(req.query !== undefined) {
            requestLog = `${req.url}`
        }
        else {
            requestLog = `${req.url}`
        }

        let oldWrite = res.write,
            oldEnd = res.end;
        
        let chunks = [];
        
        res.write = function (chunk) {
            chunks.push(chunk);
        
            return oldWrite.apply(res, arguments);
        };
        
        res.end = function (chunk) {
            if(IGNORE_LOG_LIST.includes(req.url)) {
                console.log(`${requestLog}`);
            }
            else if(typeof(chunk) != "string" && (req.method != 'GET' || ENABLE_LOG_LIST.includes(req.url.split('?')?.[0]))) {
                if (chunk)
                chunks.push(chunk);
                const _url = req.url;
                let body = Buffer.concat(chunks).toString('utf8');
                if (!_url.includes("/los-mc-credit/v1/getDocument?documentid")){
                    console.log(`${requestLog} | response : ${body}`);
                } else {
                    console.log(`${requestLog}`);
                }
            }
            else {
                if (chunk)
                    chunks.push(chunk);
            }
            oldEnd.apply(res, arguments);
        };
        next();
    }catch(err) {
        console.log(err)
        return next()
    }
}


// Middleware to capture and log response body
const logIncoming = (req, res, next) => {
    try {
        req.startTime = Date.now(); // Set startTime for calculating response time
        const originalWrite = res.write;
        const originalEnd = res.end;
        const chunks = [];

        res.write = function (chunk, ...args) {
            chunks.push(Buffer.from(chunk));
            originalWrite.apply(res, [chunk, ...args]);
        };

        res.end = function (chunk, ...args) {
            if (chunk) {
                chunks.push(Buffer.from(chunk));
            }
            let maskedResponseBody = Buffer.concat(chunks).toString('utf8');

            res.setHeader('X-Response-Time', `${Date.now() - req.startTime}ms`);
            const duration = res.getHeader('X-Response-Time') || 'unknown';
            let rawRequestPayload = Object.keys(req.body || {}).length > 0
                ? req.body : Object.keys(req.query || {}).length > 0
                    ? req.query : Object.keys(req.params || {}).length > 0
                        ? req.params : null;
            let maskedRequestPayload = _.cloneDeep(rawRequestPayload);
            if (maskedResponseBody?.length > 10000) {
                maskedResponseBody = 'response body too large to log';
            }
            try {
                if (maskedResponseBody) {
                    maskedResponseBody = JSON.parse(maskedResponseBody);
                    if (maskedResponseBody && typeof maskedResponseBody === 'object') {
                        maskSensitiveData(maskedResponseBody);
                    }
                }
                if (maskedRequestPayload && typeof maskedRequestPayload === 'object') {
                    maskSensitiveData(maskedRequestPayload);
                }
            } catch (e) {
            }

            let responseMessage = maskedResponseBody?.message
                ?? maskedResponseBody?.msg
                ?? (res.statusCode >= 500
                    ? "Internal Server Error"
                    : "Request Processed");
            const logBody = {
                timestamp: new Date().toISOString(),
                url: req.originalUrl,
                method: req.method,
                level: res.statusCode >= 500 ? "ERROR" : res.statusCode >= 400 ? "WARN" : "INFO",
                service: `mc_los`,
                requestId: req.headers?.['x-request-id'] || null,
                userId: req.headers?.['x-user-id'] || null,
                ip: req.ip,
                statusCode: res.statusCode,
                message: responseMessage,
                error: res.statusCode >= 400 ? {
                    code: res.statusCode >= 500 ? "INTERNAL_ERROR" : "CLIENT_ERROR",
                    details: responseMessage || res?.locals?.errorMessage || "An unexpected error occurred",
                    stack: res?.locals?.errorStack || null
                } : undefined,
                traceId: req?.headers?.['x-trace-id'] || null,
                spanId: req?.headers?.['x-span-id'] || null,
                duration_ms: duration,
                request: maskedRequestPayload,
                response: maskedResponseBody
            };

            console.log(JSON.stringify(logBody));
            try {
                const skipLoggingEndpoints = [
                    ...IGNORE_LOG_LIST,
                    '/v1/health',
                ];
                if (!skipLoggingEndpoints.some(path => req.originalUrl.includes(path))) {
                    
                    let contractNumber = req?.body?.contractNumber ||
                        req?.body?.contract_number ||
                        req?.params?.contractNumber ||
                        req?.params?.contract_number ||
                        req.headers?.['x-contract-number'] || null;
                    let requestId = req?.body?.requestId ||
                        req?.body?.request_id ||
                        req?.params?.requestId ||
                        req?.params?.request_id ||
                        req?.headers?.['request-id'] || null;
                    saveRequestV2(
                        global.poolWrite,
                        rawRequestPayload,
                        maskedResponseBody,
                        contractNumber,
                        requestId,
                        null,
                        null,
                        req.originalUrl
                    );
                }
            } catch (error) {
                console.error(`saveRequestV2 error: ${error.message}`);
            }

            res.removeHeader('X-Response-Time'); // Clean up the header after logging
            originalEnd.apply(res, [chunk, ...args]);
        };

        next();
    } catch (e) {
        console.error('Error in logIncoming middleware:', e);
        next();
    }
};

const logOut = (req, res) => {
    try {
        const duration = Date.now() - req.startTime;
        let rawRequestPayload = req.data;
        let maskedRequestPayload = _.cloneDeep(rawRequestPayload);
        let rawResponseBody = res?.data;
        let maskedResponseBody = _.cloneDeep(rawResponseBody);
        if (maskedResponseBody?.length > 10000) {
            maskedResponseBody = 'response body too large to log';
        }
        let responseMessage = rawResponseBody?.message
            ?? rawResponseBody?.msg
            ?? (res.statusCode >= 500
                ? "Internal Server Error"
                : "Request Processed");
        if (maskedResponseBody && typeof maskedResponseBody === 'object') {
            maskSensitiveData(maskedResponseBody);
        }
        if (maskedRequestPayload && typeof maskedRequestPayload === 'object') {
            maskSensitiveData(maskedRequestPayload);
        }
        const logBody = {
            timestamp: new Date().toISOString(),
            url: res?.config?.url,
            method: res?.request?.method,
            level: res.status >= 500 ? "ERROR" : res.statusCode >= 400 ? "WARN" : "INFO",
            service: `mc_los`,
            requestId: req?.headers?.['x-request-id'] || null,
            userId: req?.headers?.['x-user-id'] || null,
            ip: req.ip,
            statusCode: res.status,
            message: responseMessage,
            error: res.status >= 400 ? {
                code: res.status >= 500 ? "INTERNAL_ERROR" : "CLIENT_ERROR",
                details: responseMessage || res?.locals?.errorMessage || "An unexpected error occurred",
                stack: res?.locals?.errorStack || null
            } : undefined,
            traceId: req?.headers?.['x-trace-id'] || null,
            spanId: req?.headers?.['x-span-id'] || null,
            duration_ms: duration,
            request: maskedRequestPayload,
            response: maskedResponseBody
        };

        console.log(JSON.stringify(logBody));
    } catch (e) {
        console.error('Error in logOut middleware:', e);
    }
};

const maskSensitiveData = (obj) => {
    if (obj && typeof obj === 'object') {
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (key.toLowerCase().includes('password') || key.toLowerCase().includes('secret')) {
                    obj[key] = '*****';
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    maskSensitiveData(obj[key]);
                }
            }
        }
    }
}

module.exports = {
    saveUpdateHist,
    saveCallBack,
    saveRequest,
    saveRequestV2,
    saveServiceLog,
    logRequest,
    logIncoming,
    logOut
}