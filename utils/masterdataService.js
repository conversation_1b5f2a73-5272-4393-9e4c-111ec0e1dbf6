const { getAPI, getApiV2, postApiV2 } = require("../utils/common");

const getPlace = async (req, id, type, partnerCode = "OTHER") => {
  let url = req.config.basic.masterData[req.config.env] + "/masterdata/v1/" + type + "?code=" + id;
  if (partnerCode == "SPO") {
    url += "&partnerCode=SAPO";
  } else {
    url += "&partnerCode=OTHER";
  }
  const result = await getAPI(url);
  if (result && result.data) {
    return result.data.valueNameVn;
  } else return null;
};

const getBankName = async (req, code) => {
  const url = req.config.basic.masterData[req.config.env] + "/masterdata/v1/bank?bankBranch=" + code;
  const result = await getAPI(url);
  if (result && result.data) {
    return result.data.bankName;
  }

  return "";
};

const getBankNameByCode = async (req, res) => {
  const codeBank = req.query.bankCode;
  const bankData = await Promise.all([getValueCode_v3(codeBank, "BANK")]);
  res.status(200).json({
    code: 0,
    message: "The request is received",
    data: bankData[0],
  });
};

const getValueByCodeType = async (req, codetype) => {
  const url = req.config.basic.masterData[req.config.env] + "/masterdata/v1/find-all?codeType=" + codetype;
  const result = await getAPI(url);
  if (result && result.code === 0) {
    return result?.data;
  }
  return null;
};

const getValueCode = async (req, code, type) => {
  const url = req.config.basic.masterData[req.config.env] + "/masterdata/v1/get-value-code-v2?code=" + code + "&type=" + type;
  const result = await getAPI(url);
  if (result && result.code === 1) {
    return result.value.nameVn || result.value.nameEn;
  }
  return null;
};

const NodeCache = require("node-cache");
const TIME_TO_LIVE = 60 * 60;
const valueCodeCached = new NodeCache({ stdTTL: TIME_TO_LIVE });

const parseValueCode = async (code, type, fallback = '') => {
  if (!code || !type) {
    return fallback;
  }
  const cacheKey = `master_value_code_${type}_${code}`;
  if (valueCodeCached.has(cacheKey)) {
    return valueCodeCached.get(cacheKey);
  }

  const url = global.config.basic.masterData[global.config.env] + "/masterdata/v1/get-value-code-v2?code=" + code + "&type=" + type;
  const result = await getAPI(url);
  if (result && result.code === 1) {
    valueCodeCached.set(cacheKey, result.value.nameVn || result.value.nameEn);
    return result.value.nameVn || result.value.nameEn;
  }
  return fallback;
};

async function getValueCode_v2(config, code, type) {
  const url = config.basic.masterData[config.env] + "/masterdata/v1/get-value-code-v2?code=" + code + "&type=" + type;
  const result = await getAPI(url);
  if (result && result.code === 1) {
    return result.value.nameVn || result.value.nameEn;
  }
  return null;
}

async function getValueCode_v3(code, type) {
  const config = global.config;
  const url = config.basic.masterData[config.env] + "/masterdata/v1/get-value-code-v2?code=" + code + "&type=" + type;
  // console.log({url})
  const result = await getAPI(encodeURI(url));
  if (result && result.code === 1) {
    return result.value.nameVn || result.value.nameEn;
  }
  return false;
}

async function getMarriedStatus(config, code) {
  const url = config.basic.masterData[config.env] + config.data.masterDataService.getMarriedStatus + `?code=${code}`;
  const data = await getAPI(url);
  return data;
}

async function getFullAddress(config, provinceCode, districtCode, wardCode, address) {
  const rs = await Promise.all([getValueCode_v2(config, provinceCode, "PROVINCE"), getValueCode_v2(config, districtCode, "DISTRICT"), getValueCode_v2(config, wardCode, "WARD")]);
  return `${rs[2]}, ${rs[1]}, ${rs[0]}`;
}

async function getFullAddressNew(provinceCode, wardCode, address) {
  const config = global.config;
  const [province, ward] = await Promise.all([getValueCode_v2(config, provinceCode, "NEW_PROVINCE"), getValueCode_v2(config, wardCode, "NEW_WARD")]);
  if(address) {
    return `${address}, ${ward}, ${province}`;
  }
  return `${ward}, ${province}`;
}

async function getValueCodePucpCore(code, value) {
  const config = global.config;
  let url = config.basic.pucpCore[config.env] + `/pucp-core/v1/easyvip/${code}`;
  if (value) url = config.basic.pucpCore[config.env] + `/pucp-core/v1/easyvip/${code}?${value}`;
  const result = await getAPI(encodeURI(url));
  if (result && result.status === 1) {
    return result.data;
  }
  return false;
}

const getValueCodeByCodeType = async (codeType) => {
  try {
    const url = global.config.basic.masterData[global.config.env] + "/masterdata/v1/lov?codeType=" + codeType;
    const result = await getApiV2(url);
    if (result && result?.data?.code === 1) {
      return result.data.data;
    }
    return null;
  } catch (error) {
    console.log(`error when getValueCodeByCodeType: ${codeType}`);
  }
};

/**
 *
 * @param {*} partnerCode
 * @param {*} convertObject - object cần convert từ code của đối tác sang evf code tương ứng
 * @returns
 */
const convertEvfLov = async ({ partnerCode, convertObject }) => {
  try {
    const url = global.config.basic.masterData[global.config.env] + `/masterdata/v1/sme/mapping-code?partnerCode=${partnerCode}`;
    const result = await postApiV2(url, convertObject);
    return result?.data?.data ?? convertObject;
  } catch (e) {
    console.error(e);
    return undefined;
  }
};

async function getValueCodeMasterdataV2(code, type) {
  const config = global.config;
  const url = config.basic.masterData[config.env] + "/masterdata/v2/getValueCode?code=" + code + "&type=" + type;
  const result = await getAPI(url);
  if (result && result.code === 1) {
    return {
      code: code,
      type: type,
      nameVn: result.value.nameVn,
      nameEn: result.value.nameEn
    };
  }
  return null;
}

const addressCached = new NodeCache({ stdTTL: TIME_TO_LIVE });

async function getNewProvinces() {
  const cachedKey = "address_provinces_cached";
  if (addressCached.has(cachedKey)) {
    return addressCached.get(cachedKey);
  }
  const config = global.config;
  const url = config.basic.masterData[config.env] + "/masterdata/v2/address/provinces";
  const result = await getAPI(url);
  if (result && result.code === 1) {
    addressCached.set(cachedKey, result.data);
    return result.data;
  }
  return null;
}


async function getNewWards(code) {
  const cachedKey = "address_wards_cached_" + code;
  if (addressCached.has(cachedKey)) {
    return addressCached.get(cachedKey);
  }
  const config = global.config;
  const url = config.basic.masterData[config.env] + "/masterdata/v2/address/wards";
  const result = await getAPI(url, undefined, { province: code });
  if (result && result.code === 1) {
    addressCached.set(cachedKey, result.data);
    return result.data;
  }
  return null;
}

const getListByCodeType = async (codeType) => {
  const key = `master_data_list_${codeType}`;
  if (valueCodeCached.has(key)) {
    return valueCodeCached.get(key);
  }
  const url = global.config.basic.masterData[global.config.env] + "/masterdata/v1/lov";
  const result = await getAPI(url, undefined, { codeType: codeType });
  if (result && result?.code === 1) {
    valueCodeCached.set(key, result.data);
    return result.data;
  }
  return null;
};

module.exports = {
  getPlace,
  getBankName,
  getBankNameByCode,
  getValueCode,
  getValueCode_v2,
  getValueCode_v3,
  getMarriedStatus,
  getFullAddress,
  getValueCodePucpCore,
  getValueCodeByCodeType,
  convertEvfLov,
  getValueByCodeType,
  parseValueCode,
  getNewProvinces,
  getNewWards,
  getListByCodeType,
  getValueCodeMasterdataV2,
  getFullAddressNew
};
