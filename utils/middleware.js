const { signJws, verifyJws, encryptJwe, decryptJwe, decryptDataMisa, encryptDataMisa, decryptDataFinv, encryptDataFinv } = require("./encrypt/encrypt.js");
const loggingService = require("../utils/loggingService");
const kunnRepo = require("../repositories/kunn-repo");
const { PARTNER_CODE } = require("../const/definition.js");
const loanContractRepo = require("../repositories/loan-contract-repo");
const FindManyDto = require("./paging.js");
const { decryptDataJws } = require("./jws.js");
const { BadRequestResponse, ServerErrorResponse ,handleResponseError, handleInternalResponseError} = require("../base/response.js");
// const { bizziService, bizziLimitService, finvService } = require("../services");
// const { bizziKunn, bizziLimitKunn, finvKunn } = require("../KUNN");
const smeService = require("../services/sme-service.js");
const bizziService = require("../services/bizzi-service.js");
const bizziLimitService = require("../services/bizzi-limit-service.js");
const finvService = require("../services/finv-service.js");
const bizziKunn = require("../KUNN/bizzi-kunn.js");
const bizziLimitKunn = require("../KUNN/bizzi-limit-kunn.js");
const finvKunn = require("../KUNN/finv-kunn.js");
const _ = require("lodash");

const decryptRequestMisa = async (req, res, next) => {
  if (["dev", "uat"].includes(req?.headers?.env) && ["dev", "uat"].includes(process.env.NODE_ENV)) { // for test
    return next();
  }

  try {
    if (req?.body && Object.keys(req?.body).length > 0) {
      const jweRaw = req.body;
      if (req?.headers?.env == "test") {
        const misaEcPublicKey = global.env.EVN_EC_PUBLIC_KEY;
        const decrypted = await decryptDataMisa(jweRaw, { misaEcPublicKey });
        req.body = decrypted;
      } else {
        const decrypted = await decryptDataMisa(jweRaw);
        req.body = decrypted;
        loggingService.saveRequestV2(global.poolWrite, decrypted, null, req?.body?.contractNumber, req?.body?.requestId, PARTNER_CODE.MISA, "", req?.url);
      }
    }
    next();
  } catch (error) {
    console.log(`[MISA-DECYPT-RESQUEST] Error decrypt data method ${req.method}, url: ${req.url}, data: ${req?.body}`);
    res.status(500).json({ message: `Error decrypt data url: ${req.url}` });
  }
};

const encryptResponseMisa = async (req, res, next) => {
  if (["dev", "uat"].includes(req?.headers?.env) && ["dev", "uat"].includes(process.env.NODE_ENV)) { // for test
    return next();
  }

  if (checkNotBypass(req)) {
    const originalSend = res.send;
    res.send = async function (body) {
      // Modify the response body
      let encrypted;
      res.setHeader("Content-Type", "text/plain");
      try {
        console.log(`Misa | Response | ${JSON.stringify(body)}`);

        encrypted = await encryptDataMisa(body);
      } catch (error) {
        res.status(500);
        encrypted = "Internal server error";
      }
      originalSend.call(this, encrypted);
    };
  }

  next();
};

const addPaging = async (req, res, next) => {
  try {
    req.paging = new FindManyDto(req.query);
  } catch (error) {
    console.log(`[MIDDLEWARE] Error add paging ${error}`);
  }

  next();
};

const handlePartnerChanel = async (req, res, next) => {
  req?.body && (req.body.createdBy = req?.username || "");

  const whitelist = ["/v1/loan-request", "/v1/kunn-request", "/v1/kunn/search", "/v1/loan-request/export"];
  if (whitelist.includes(req.path)) {
    req.instanceService = {
      lender: smeService,
      kunn: smeService,
    };
    return next();
  }

  try { 
    let partner_code = getPartnerCodeFromReq(req);
    let contract_number = req.query.contract_number || req.body.contract_number;
    let kunnId = req.query.kunnId || req.body.kunnId;

    if (!partner_code) {
      if (contract_number || kunnId) {
        const data = contract_number ? await loanContractRepo.getLoanContract(contract_number) : await kunnRepo.getKunnData(kunnId);

        if (!data) {
          return handleInternalResponseError(res,new BadRequestResponse([], contract_number ? "contract_number not found" : "KUNN not found"));
        }

        partner_code = data.partner_code;
      }
    }

    let instanceService;
    switch (partner_code) {
      case PARTNER_CODE.BIZZ:
        instanceService = {
          lender: bizziService,
          kunn: bizziKunn,
        };
        break;
      case PARTNER_CODE.BZHM:
        instanceService = {
          lender: bizziLimitService,
          kunn: bizziLimitKunn,
        };
        break;
      case PARTNER_CODE.FINV:
        instanceService = {
          lender: finvService,
          kunn: finvKunn,
        };
        break;
      default:
        return handleInternalResponseError(res,new BadRequestResponse([], `does not have any instance service for this partner_code: ${partner_code}`));
    }
    req.instanceService = instanceService;
  } catch (error) {
    console.log(`[MIDDLEWARE] Error getting partner_code: ${error}`);
  }

  next();
};

function wrapService(handler) {
  return async (req, res, next) => {
    try {
      const instanceService = req.instanceService;
      if (!instanceService) {
        throw new Error("instanceService not initialized");
      }

      return handler(instanceService)(req, res, next);
    } catch (err) {
      if (err instanceof TypeError && /is not a function/.test(err.message)) {
        console.warn("Service method not implemented for this partner");
      }
      next(err);
    }
  };
}

const decryptRequestFinv = async (req, res, next) => {
  try {
    if (req?.body && Object.keys(req?.body).length > 0 && checkNotBypass(req)) {
      const raw = req.body;
      if (req?.headers?.env == "test") {
        const decrypted = await decryptDataFinv(raw, { finvEcPublicKey: global.env.EVN_EC_PUBLIC_KEY });
        req.body = decrypted;
      } else {
        const decrypted = await decryptDataFinv(raw);
        req.body = decrypted;
        loggingService.saveRequestV2(global.poolWrite, decrypted, null, req?.body?.contractNumber, req?.body?.requestId, PARTNER_CODE.MISA, "", req?.url);
      }
    }
    next();
  } catch (error) {
    console.log(`[FINV-DECYPT-RESQUEST] Error decrypt data method ${req.method}, url: ${req.url}, data: ${req?.body}`);
    res.status(500).json({ message: `Error decrypt data url: ${req.url}` });
  }
};

const encryptResponseFinv = async (req, res, next) => {
  if (checkNotBypass(req)) {
    const originalSend = res.send;
    res.send = async function (body) {
      // Modify the response body
      let encrypted;
      res.setHeader("Content-Type", "text/plain");
      try {
        console.log(`Finv | Response | ${JSON.stringify(body)}`);

        encrypted = await encryptDataFinv(body);
      } catch (error) {
        res.status(500);
        encrypted = "Internal server error";
      }
      originalSend.call(this, encrypted);
    };
  }

  next();
};

const checkNotBypass = (req) => {
  return global.env?.NODE_ENV !== "dev";
};

const injectPartnerHeaders = (partnerCode) => (req, res, next) => {
  let origPartnerCode = getPartnerCodeFromReq(req);
  if (!origPartnerCode) {
    req.headers["partner-code"] = partnerCode;
  }
  next();
};

const verifySignedRequestFromPartner = async (req, res, next) => {
  try {
    if (req?.headers?.env == "local") {
      next();
      return;
    }

    if (req?.body && Object.keys(req?.body).length > 0 && req?.headers?.env !== "local") {
      let decryptedData = {};
      let jwsRaw = req.headers["ec-signature"];
      if (!jwsRaw) {
        jwsRaw = req?.body?.["ec_signature"] || req?.body?.["ecSignature"];
      }

      let partnerCode = getPartnerCodeFromReq(req);
      let partnerPublicKey = getPublicKeyByPartnerCode(partnerCode);

      decryptedData = await decryptDataJws(jwsRaw, { partnerPublicKey });
      
      let requestBody = _.cloneDeep(req.body);
      
      // Remove ec_signature or ecSignature from requestBody if present
      if (requestBody?.["ec_signature"] || requestBody?.["ecSignature"]) {
        ["ec_signature", "ecSignature"].forEach((key) => {
          if (key in requestBody) delete requestBody[key];
        });
      }
      
      if (JSON.stringify(decryptedData) !== JSON.stringify(requestBody)) {
        console.log(`[PARTNER-DECYPT-RESQUEST] Error decrypt data method ${req.method}, url: ${req.url}, data: ${JSON.stringify(req?.body ?? {})}`);
        return res.status(500).json(new ServerErrorResponse());
      }
    }
    next();
  } catch (error) {
    console.log(`[PARTNER-DECYPT-RESQUEST] Error decrypt data method ${req.method}, url: ${req.url}, data: ${req?.body}`);
    return res.status(500).json(new ServerErrorResponse());
  }
};

const getPartnerCodeFromReq = (req) => {
  let partnerCode = req.body.partner_code;
  if (partnerCode === undefined) {
    partnerCode = req.body.partnerCode;
  }
  if (partnerCode === undefined) {
    partnerCode = req.query.partner_code;
  }
  if (partnerCode === undefined) {
    partnerCode = req.query.partnerCode;
  }
  if (partnerCode === undefined) {
    partnerCode = req.headers["partner-code"];
  }
  return partnerCode;
};

const getPublicKeyByPartnerCode = (partnerCode) => {
  switch (partnerCode) {
    case PARTNER_CODE.BIZZ:
      return process.env.BIZZ_EC_PUBLIC_KEY;
    case PARTNER_CODE.FINV:
      return process.env.FINV_EC_PUBLIC_KEY;
  }

  return null;
};

module.exports = {
  decryptRequestMisa,
  encryptResponseMisa,
  addPaging,
  handlePartnerChanel,
  wrapService,
  decryptRequestFinv,
  encryptResponseFinv,
  verifySignedRequestFromPartner,
  injectPartnerHeaders,
};
