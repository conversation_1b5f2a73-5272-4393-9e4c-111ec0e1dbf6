const loanContractRepo = require('../repositories/loan-contract-repo')
const dateHelper = require("./dateHelper")
const VNnum2words = require('vn-num2words');
const { getValueCode_v3, getPlace, getFullAddress, getValueCode_v2, parseValueCode } = require("./masterdataService")
const utils = require("./helper")
const { SERVICE_NAME, FILE_STORAGE, PARTNER_CODE, CONTRACT_TYPE, FILE_TYPE_MISA, CIC_STEP_CHECK, BUSINESS_TYPE, TYPE_COLLECTION } = require("../const/definition")
const s3Service = require("../upload_document/s3-service")
const common = require("./common")
const fs = require("fs")
const PizZip = require('pizzip')
const docxtemplater = require('docxtemplater')
const moment = require('moment-timezone');
moment().tz('Asia/Ho_Chi_Minh').format()
const wordUtil = require("./word")
const { callbackContractFile, mappingInstallment } = require("../services/sme-misa-v2");
const { numberToString } = require('./numberToString');
const mime = require('mime');
const { encryptFileMisa, decryptFileMisa } = require('./encrypt/encrypt');
const sqlHelper = require("../utils/sqlHelper");
const uuid = require("uuid")
const loanRevenuesRepo = require("../repositories/loan-revenues-repo");
const storageUnSignPath = "/mc-credit/unsigned-bctd";
const storageSignedPath = "/mc-credit/signed-bctd";
const helper = require('../utils/helper');
const { getKunnInstallmentApi, getKunnInstallmentByKunnApi, checkAvailableAmountApi, checkRemainPrinAmountApi } = require('../apis/lms-api');
const loanContractDocumentRepo = require("../repositories/document");
const { getKunnData } = require('../repositories/kunn-repo');
const { RATE_TYPE } = require('../const/product');
const { getProductDetailByCodeApi } = require('../apis/product-api');
const { DOC_GROUP, ADDRESS_CODE_TYPE } = require('../const/variables-const');

/**
 * Gen Quyết định cho vay
 * @param {*} contractNumber 
 * @returns 
 */
const generateBctdMisa = async (contractNumber, isRefinance = false) => {
  try {
    let tmplPath;
    const fileData = await getDataBctdMisa(contractNumber, isRefinance);
    const loanRevenues = await loanRevenuesRepo.findRevenueDocuments(contractNumber);
    const loanRevenue = loanRevenues[0];
    if (loanRevenue.financial_report_type === '133_B01') {
      tmplPath = './static_file/MS.BCTD.HM_133_B01.docx';
    } else if (loanRevenue.financial_report_type === '133_B01A') {
      tmplPath = './static_file/MS.BCTD.HM_133_B01A.docx';
    } else if (loanRevenue.financial_report_type === '133_B01B') {
      tmplPath = './static_file/MS.BCTD.HM_133_B01B.docx';
    } else if (loanRevenue.financial_report_type === '200_BCTC') {
      tmplPath = './static_file/MS.BCTD.HM_200_BCTC.docx';
    }
    // return
    const fileBuffer = await wordUtil.renderWordFile({ filePath: tmplPath, data: fileData });
    const fileName = `${contractNumber}-bao-cao-tham-dinh-${moment().format("YYYYMMDDHHmmss.SSS")}.pdf`;
    const encryptFileName = `${contractNumber}-bao-cao-tham-dinh-encrypt-${moment().format("YYYYMMDDHHmmss.SSS")}.pgp`;

    const pdfBuffer = await wordUtil.convertPdf({ fileBuffer });
    const encryptedBuffer = await encryptFileMisa(pdfBuffer);

    // const unSignS3 = await wordUtil.pushFileToS3({
    //   fileName: fileName,
    //   fileBuffer: pdfBuffer,
    //   fileStoragePath: storageUnSignPath
    // })
    // const pathUnSign = unSignS3.Key;
    // const signUri = '/esigning/internal/misa/sign-contract';
    // const signUrl = config.basic['bss-esigning-service'][config.env] + signUri;
    // //call esigning
    // const signedResp = await common.postApiV2(signUrl, {
    //   contractNumber: contractNumber,
    //   filePath: pathUnSign,
    //   type: 'BCTD'
    // })
    // if (!signedResp?.data?.data) {
    //   throw new Error(`signature BCTD error ${contractNumber}:`, JSON.stringify(signedResp));
    // }
    // const signedBuffer = Buffer.from(signedResp?.data?.data, 'base64');
    // const encryptedBuffer = await encryptFileMisa(signedBuffer);

    // const [resultS3, encryptResultS3] = await Promise.all([
    //   wordUtil.pushFileToS3({
    //     fileName: fileName,
    //     fileBuffer: signedBuffer,
    //     fileStoragePath: storageSignedPath
    //   }),
    //   wordUtil.pushFileToS3({
    //     fileName: encryptFileName,
    //     fileBuffer: encryptedBuffer,
    //     fileStoragePath: storageSignedPath,
    //     acl: 'private'
    //   })
    // ])

    const [resultS3, encryptResultS3] = await Promise.all([
      wordUtil.pushFileToS3({
        fileName: fileName,
        fileBuffer: pdfBuffer,
        fileStoragePath: storageUnSignPath
      }),
      wordUtil.pushFileToS3({
        fileName: encryptFileName,
        fileBuffer: encryptedBuffer,
        fileStoragePath: storageUnSignPath,
        acl: 'private'
      })
    ])

    //callback file to misa
    const path = resultS3.Key;
    const encryptPath = encryptResultS3.Key;
    const fileLocation = resultS3.Location;
    const encryptFileLocation = encryptResultS3.Location;

    await Promise.all([
      saveUpload(global.poolWrite, uuid.v4(), fileLocation, 'SPAR', null, path, fileName, contractNumber),//bao cao tham dinh
      saveUpload(global.poolWrite, uuid.v4(), encryptFileLocation, 'SPAR_ENC', null, encryptPath, encryptFileName, contractNumber)//bao cao tham dinh - ma hoa
    ])

    console.log(`BCTD | ${contractNumber} | file normal: `, fileLocation);
    console.log(`BCTD | ${contractNumber} | file encrypt: `, encryptFileLocation);
    callbackContractFile({
      contractNumber,
      files: [
        {
          type: FILE_TYPE_MISA.BCTD,
          fileName: encryptFileName,
          contentType: mime.lookup(fileName),
          fileSize: await utils.getFileSizeFromUrl(fileLocation),
          fileUrl: encryptPath,
          contractValidDate: dateHelper.PG_DATE_TODAY()
        }
      ]
    });
    return resultS3;
  } catch (err) {
    console.log(err)
    common.log(`generateBctdMisa error : ${err.message}`, contractNumber)
    return undefined;
  }
}

const getDataBctdMisa = async (contractNumber, isRefinance = false) => {
  try {
    let data = {};
    const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);

    let businessTypeChecked = [BUSINESS_TYPE["01"], BUSINESS_TYPE["04"]];//cty 1 tv, doanh nghiep tu nhan
    if (businessTypeChecked.includes(loan.business_type)) {
      loan.loan_customer_shareholders = [{
        ...loan.loan_business_owner,
        capital_contribution_ratio: 100.0
      }]
    }
    // console.log(`loan.loan_customer_shareholders :`,loan.loan_customer_shareholders )
    let shareholdersTotalRatio = 0;
    loan?.loan_customer_shareholders?.forEach((shareholder, index) => {
      shareholder.stt = index + 1;
      shareholdersTotalRatio += +shareholder.capital_contribution_ratio;
      shareholder.capital_contribution_ratio = parseFloat(shareholder.capital_contribution_ratio).toFixed(2);
      if (shareholder.subject === 'INDIVIDUAL') {
        shareholder.individual = true;
        shareholder.isEnterprise = false;
      } else {
        shareholder.individual = false;
        shareholder.isEnterprise = true;
      }
    });
    if (loan?.id) {
      data = { ...loan }
    }
    const [
      loanScoring,
      loanRating,
      forecastCapitalNeeds,
      revenueDocuments,
      inputPartners,
      outputPartners
    ] = await Promise.all([
      sqlHelper.find({
        table: 'loan_scoring', whereCondition: {
          contract_number: contractNumber
        }
      }),
      sqlHelper.findOne({
        table: 'loan_rating',
        whereCondition: {
          contract_number: contractNumber
        }
      }),
      sqlHelper.find({
        table: 'forecast_capital_needs',
        whereCondition: {
          contract_number: contractNumber
        }
      }),
      loanRevenuesRepo.findRevenueDocuments(contractNumber),
      sqlHelper.find({
        table: 'loan_customer_partners',
        whereCondition: {
          contract_number: contractNumber,
          partner_type: 'IN'
        }
      }),
      sqlHelper.find({
        table: 'loan_customer_partners',
        whereCondition: {
          contract_number: contractNumber,
          partner_type: 'OUT'
        }
      })
    ])
    if (inputPartners?.length > 0) {
      inputPartners.forEach((p, index) => {
        p.stt = index + 1;
      });
    }
    if (outputPartners?.length > 0) {
      outputPartners.forEach((p, index) => {
        p.stt = index + 1;
      });
    }

    const revenueDocument = revenueDocuments && revenueDocuments?.length > 0 ? revenueDocuments[0] : {};
    if (!revenueDocument.evf_file_url) {
      throw new Error(`${contractNumber} | func | getDataBctdMisa | error: Không có file báo cáo tài chính`)
    }
    const revenueDocumentBuffer = await s3Service.downloadFileVer2(global.config.data, utils.getFileKeyFromUrl(revenueDocument.evf_file_url));
    if (!revenueDocumentBuffer) {
      throw new Error(`${contractNumber} | func | getDataBctdMisa | error: xảy ra lỗi khi download file báo cáo tài chính`)
    }

    const result = {};
    let totalScoreTc = 0;
    let totalScorePtc = 0;
    loanScoring.forEach((scoring) => {
      result[scoring.code] = {
        ...scoring,
        score: scoring?.score && isNumber(parseFloat(scoring.score)) ? parseFloat(scoring.score).toFixed(2) : scoring.score ?? '',
        cust_score: scoring?.cust_score && isNumber(parseFloat(scoring.cust_score)) ? parseFloat(scoring.cust_score).toFixed(2) : scoring.cust_score ?? '',
        proportion: scoring?.proportion && isNumber(parseFloat(scoring.proportion)) ? parseFloat(scoring.proportion).toFixed(2) : scoring.proportion ?? '',
        target_score: scoring?.target_score && isNumber(parseFloat(scoring.target_score)) ? parseFloat(scoring.target_score).toFixed(2) : scoring.target_score ?? ''
      };
      if (scoring.code.startsWith("PTC")) {
        totalScorePtc += +scoring.cust_score;
      } else if (scoring.code.startsWith("TC")) {
        totalScoreTc += +scoring.cust_score;
      }
    });
    // totalScoreTc = totalScoreTc * (60 / 100); //tỉ trọng 60%
    // totalScorePtc = totalScorePtc * (40 / 100); // tỉ trọng 40%

    const forecastCapitalNeedsWrap = {}
    forecastCapitalNeeds.forEach((e) => {
      const roundUpVnd = ['DTBH', 'GVHB', 'NCVLD', 'VLDR', 'VVTD', 'VCTT', 'CPBHQLDN', 'VLDVQ', 'DTT', 'VLDRBS'];
      let previous_year
      let year_n
      if (roundUpVnd.includes(e.code)) {
        previous_year = e.previous_year ? utils.numberWithCommas(parseInt(Math.round(e.previous_year))) : e.previous_year ?? ' ';
        year_n = e.year_n ? utils.numberWithCommas(parseInt(Math.round(e.year_n))) : e.year_n ?? ' ';
      } else {
        previous_year = e.previous_year ? parseFloat(e.previous_year).toFixed(2) : e.previous_year ?? ' ';
        year_n = e.year_n ? parseFloat(e.year_n).toFixed(2) : e.year_n ?? ' ';
      }
      forecastCapitalNeedsWrap[e.code] = { ...e, previous_year, year_n };
    });

    let step;
    if (isRefinance) {
      step = CIC_STEP_CHECK.TC2_DETAIL; // for MISA TC2
    } else {
      step = CIC_STEP_CHECK.AF2_DETAIL; // for MISA AF2
    }
    const debtInfo = await getDebts(contractNumber, step);
    const personsCic = debtInfo?.personsCic;
    let personsCicTotalVnd = 0;
    let personsCicTotalUsd = 0;
    if (personsCic?.length > 0) {
      personsCic.forEach(p => {
        if (p.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.totalVnd) {
          personsCicTotalVnd += p.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.totalVnd;
        }
        if (p.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.totalUsd) {
          personsCicTotalUsd += p.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.totalUsd;
        }
      })
    }

    const smeData = await Promise.all([
      getPlace({ config: global.config }, loan.sme_headquarters_ward, 'wards'),
      getPlace({ config: global.config }, loan.sme_headquarters_district, 'districts'),
      getPlace({ config: global.config }, loan.sme_headquarters_province, 'provinces'),
      getValueCode_v3(loan.sme_representation_position, 'PROFESSION'),
      getValueCode_v3(loan.loan_customer_representations[0].issue_place, 'ISSUE_PLACE_VN')
    ]);

    const [newWard, newProvince, licenseProvince, licenseWard] = await Promise.all([
      parseValueCode(loan.sme_headquarters_new_ward, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(loan.sme_headquarters_new_province, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      parseValueCode(loan.loan_customer?.new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      parseValueCode(loan.loan_customer?.new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD),
    ]);

    data.headquartersFullAddress = loan.sme_headquarters_address + ', ' + smeData[0] + ', ' + smeData[1] + ', ' + smeData[2] || '';
    data.headquartersAddress = [loan.sme_headquarters_address, newWard, newProvince].join(', ');
    data.address_on_license = loan?.loan_customer?.address_on_license || '';
    data.new_address_on_license = [loan?.loan_customer?.detail_on_license, licenseWard, licenseProvince].join(', ');
    if (!!newWard) {
      data.address = data.headquartersAddress;
    } else {
      data.address = data.headquartersFullAddress;
    }

    data.shareholders_total_ratio = parseFloat(shareholdersTotalRatio).toFixed(2);
    const revenueDocumentXml = utils.xmlToJson(revenueDocumentBuffer.Body)?.HSoThueDTu?.HSoKhaiThue ?? {};
    let revenueDocumentXmlKetQuaDoanhThu
    let revenueDocumentXmlCanDoiKeToan
    let revenueDocumentXmlKetQuaKinhDoanh
    let doanhThuNamTruoc;
    let revenueDocumentXmlKetQuaLoiNhuan;
    let loiNhuanNamTruoc;
    if (revenueDocument.financial_report_type === '133_B01') {
      revenueDocumentXmlCanDoiKeToan = revenueDocumentXml?.CTieuTKhaiChinh || { SoDauNam: {}, SoCuoiNam: {} };
      revenueDocumentXmlKetQuaKinhDoanh = revenueDocumentXml?.PLuc?.PL_KQHDXSKD || { NamTruoc: {}, NamNay: {} };
      revenueDocumentXmlKetQuaDoanhThu = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct01) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct01);
      revenueDocumentXmlKetQuaLoiNhuan = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct07) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct07);
      doanhThuNamTruoc = revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct01;
      loiNhuanNamTruoc = revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct07;
    } else if (revenueDocument.financial_report_type === '133_B01A' || revenueDocument.financial_report_type === '133_B01B') {
      revenueDocumentXmlCanDoiKeToan = revenueDocumentXml?.CTieuTKhaiChinh || { SoDauNam: {}, SoCuoiNam: {} };
      revenueDocumentXmlKetQuaKinhDoanh = revenueDocumentXml?.PLuc?.PL_KQHDSXKD || { NamTruoc: {}, NamNay: {} };
      revenueDocumentXmlKetQuaDoanhThu = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct01) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct01);
      revenueDocumentXmlKetQuaLoiNhuan = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct60) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct60);
      doanhThuNamTruoc = revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct01;
      loiNhuanNamTruoc = revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct60;
    } else if (revenueDocument.financial_report_type === '200_BCTC') {
      revenueDocumentXmlCanDoiKeToan = revenueDocumentXml?.CTieuTKhaiChinh?.CDKT_HoatDongLienTuc || { SoDauNam: {}, SoCuoiNam: {} };
      revenueDocumentXmlKetQuaKinhDoanh = revenueDocumentXml?.PLuc?.PL_KQHDSXKD || { NamTruoc: {}, NamNay: {} };
      revenueDocumentXmlKetQuaDoanhThu = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct01) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct01);
      revenueDocumentXmlKetQuaLoiNhuan = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct60) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct60);
      doanhThuNamTruoc = revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct01;
      loiNhuanNamTruoc = revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct60;
    }

    const revenueDocumentXmlDoanhThuChenhLech = Math.abs(revenueDocumentXmlKetQuaDoanhThu);
    const revenueDocumentXmlDoanhThuChenhLechVn = utils.numberWithCommas(parseInt(Math.abs(revenueDocumentXmlKetQuaDoanhThu)));
    let revenueDocumentXmlDoanhThuChenhLechTiLe = revenueDocumentXmlDoanhThuChenhLech / parseFloat(doanhThuNamTruoc);
    revenueDocumentXmlDoanhThuChenhLechTiLe = Math.abs(revenueDocumentXmlDoanhThuChenhLechTiLe);
    revenueDocumentXmlKetQuaDoanhThu = revenueDocumentXmlKetQuaDoanhThu > 0 ? 'tăng' : 'giảm';

    // let revenueDocumentXmlKetQuaLoiNhuan = parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamNay.ct60) - parseFloat(revenueDocumentXmlKetQuaKinhDoanh.NamTruoc.ct60);
    const revenueDocumentXmlKetQuaLoiNhuanChenhLech = Math.abs(revenueDocumentXmlKetQuaLoiNhuan);
    const revenueDocumentXmlKetQuaLoiNhuanChenhLechVn = utils.numberWithCommas(parseInt(Math.abs(revenueDocumentXmlKetQuaLoiNhuan)));
    let revenueDocumentXmlKetQuaLoiNhuanChenhLechTiLe = Math.abs(revenueDocumentXmlKetQuaLoiNhuanChenhLech) / parseFloat(loiNhuanNamTruoc);
    revenueDocumentXmlKetQuaLoiNhuanChenhLechTiLe = Math.abs(revenueDocumentXmlKetQuaLoiNhuanChenhLechTiLe);
    revenueDocumentXmlKetQuaLoiNhuan = revenueDocumentXmlKetQuaLoiNhuan > 0 ? 'tăng' : 'giảm';

    // let revenueDocumentXmlCanDoiKeToan = revenueDocumentXml?.CTieuTKhaiChinh?.CDKT_HoatDongLienTuc || { SoDauNam: {}, SoCuoiNam: {} };
    // let revenueDocumentXmlKetQuaKinhDoanh = revenueDocumentXml?.PLuc?.PL_KQHDSXKD;
    let revenueDocumentXmlCanDoiKeToanColumns = [
      'ct100',
      'ct110',
      'ct111',
      'ct112',
      'ct120',
      'ct121',
      'ct122',
      'ct123',
      'ct124',
      'ct130',
      'ct131',
      'ct132',
      'ct133',
      'ct134',
      'ct135',
      'ct136',
      'ct137',
      'ct139',
      'ct140',
      'ct141',
      'ct142',
      'ct149',
      'ct150',
      'ct151',
      'ct152',
      'ct153',
      'ct154',
      'ct155',
      'ct160',
      'ct161',
      'ct162',
      'ct170',
      'ct180',
      'ct181',
      'ct182',
      'ct200',
      'ct210',
      'ct211',
      'ct212',
      'ct213',
      'ct214',
      'ct215',
      'ct216',
      'ct219',
      'ct220',
      'ct221',
      'ct222',
      'ct223',
      'ct224',
      'ct225',
      'ct226',
      'ct227',
      'ct228',
      'ct229',
      'ct230',
      'ct231',
      'ct232',
      'ct240',
      'ct241',
      'ct242',
      'ct250',
      'ct251',
      'ct252',
      'ct253',
      'ct254',
      'ct255',
      'ct260',
      'ct261',
      'ct262',
      'ct263',
      'ct268',
      'ct270',
      'ct300',
      'ct310',
      'ct311',
      'ct312',
      'ct313',
      'ct314',
      'ct315',
      'ct316',
      'ct317',
      'ct318',
      'ct319',
      'ct320',
      'ct321',
      'ct322',
      'ct323',
      'ct324',
      'ct330',
      'ct331',
      'ct332',
      'ct333',
      'ct334',
      'ct335',
      'ct336',
      'ct337',
      'ct338',
      'ct339',
      'ct340',
      'ct341',
      'ct342',
      'ct343',
      'ct350',
      'ct360',
      'ct400',
      'ct410',
      'ct411',
      'ct411a',
      'ct411b',
      'ct412',
      'ct413',
      'ct414',
      'ct415',
      'ct416',
      'ct417',
      'ct418',
      'ct419',
      'ct420',
      'ct421',
      'ct421a',
      'ct421b',
      'ct422',
      'ct423',
      'ct424',
      'ct425',
      'ct426',
      'ct427',
      'ct430',
      'ct431',
      'ct432',
      'ct440',
      'ct500',
      'ct511',
      'ct512',
      'ct513',
      'ct514',
      'ct515',
      'ct516',
      'ct517',
      'ct600'
    ]
    let revenueDocumentXmlKetQuaKinhDoanhColumns = [
      'ct01',
      'ct02',
      'ct03',
      'ct04',
      'ct05',
      'ct06',
      'ct07',
      'ct10',
      'ct11',
      'ct20',
      'ct21',
      'ct22',
      'ct23',
      'ct24',
      'ct25',
      'ct26',
      'ct30',
      'ct31',
      'ct32',
      'ct40',
      'ct50',
      'ct51',
      'ct52',
      'ct60',
      'ct70',
      'ct71'
    ]
    if (revenueDocumentXmlKetQuaKinhDoanh && revenueDocumentXmlKetQuaKinhDoanh?.NamTruoc) {
      revenueDocumentXmlKetQuaKinhDoanhColumns.forEach(key => {
        if (!(key in revenueDocumentXmlKetQuaKinhDoanh.NamTruoc)) {
          revenueDocumentXmlKetQuaKinhDoanh.NamTruoc[key] = '';
        }
      });
    }
    if (revenueDocumentXmlKetQuaKinhDoanh && revenueDocumentXmlKetQuaKinhDoanh?.NamNay) {
      revenueDocumentXmlKetQuaKinhDoanhColumns.forEach(key => {
        if (!(key in revenueDocumentXmlKetQuaKinhDoanh.NamNay)) {
          revenueDocumentXmlKetQuaKinhDoanh.NamNay[key] = '';
        }
      });
    }
    if (revenueDocumentXmlCanDoiKeToan && revenueDocumentXmlCanDoiKeToan?.SoDauNam) {
      revenueDocumentXmlCanDoiKeToanColumns.forEach(key => {
        if (!(key in revenueDocumentXmlCanDoiKeToan.SoDauNam)) {
          revenueDocumentXmlCanDoiKeToan.SoDauNam[key] = '';
        }
      });
    }
    if (revenueDocumentXmlCanDoiKeToan && revenueDocumentXmlCanDoiKeToan?.SoCuoiNam) {
      revenueDocumentXmlCanDoiKeToanColumns.forEach(key => {
        if (!(key in revenueDocumentXmlCanDoiKeToan.SoCuoiNam)) {
          revenueDocumentXmlCanDoiKeToan.SoCuoiNam[key] = '';
        }
      });
    }

    data = {
      ...data,
      ...getNgayThangNamToday(),
      ...await getMasterdata(loan),
      registration_date_vn: dateHelper.convertDateToDDMMYYYY(new Date(data.loan_customer.registration_date)),
      capital_need_vn_text: data.capital_need ? numberToString(data.capital_need) : '',
      capital_need_vn: data.capital_need ? utils.numberWithCommas(parseInt(data.capital_need)) : '',
      owner_equity_vn_text: numberToString(data.owner_equity),
      owner_equity_vn: data.owner_equity ? utils.numberWithCommas(parseInt(data.owner_equity)) : '',
      other_capital_vn_text: numberToString(data.other_capital),
      other_capital_vn: data.other_capital ? utils.numberWithCommas(parseInt(data.other_capital)) : '',
      loans_other_financial_institutions_vn_text: numberToString(data.loans_other_financial_institutions),
      loans_other_financial_institutions_vn: data.loans_other_financial_institutions ? utils.numberWithCommas(parseInt(data.loans_other_financial_institutions)) : '',
      request_amt_vn_text: numberToString(data.request_amt),
      request_amt_vn: data.request_amt ? utils.numberWithCommas(parseInt(data.request_amt)) : '',
      charter_capital_vn: data.charter_capital ? utils.numberWithCommas(parseInt(data.charter_capital)) : '',
      cicDebts: debtInfo?.debts,
      TONG_VND_ALL_TCTD: debtInfo?.TONG_VND_ALL_TCTD,
      TONG_USD_ALL_TCTD: debtInfo?.TONG_USD_ALL_TCTD,
      enterpriseCicData: debtInfo?.enterpriseCicData,
      QHTDHT_DUNO_THETD_TRUE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD ? true : false,
      QHTDHT_DUNO_THETD_FALSE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD ? false : true,
      QHTDHT_DUNO_THETD_INFO: JSON.stringify(debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD),

      QHTDHT_DUNO_VAMC_TRUE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_VAMC ? true : false,
      QHTDHT_DUNO_VAMC_FALSE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_VAMC ? false : true,
      QHTDHT_DUNO_VAMC_INFO: JSON.stringify(debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_VAMC),

      QHTDHT_DUNO_TRAIPHIEU_TRUE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.TRAIPHIEU ? true : false,
      QHTDHT_DUNO_TRAIPHIEU_FALSE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.TRAIPHIEU ? false : true,
      QHTDHT_DUNO_TRAIPHIEU_INFO: JSON.stringify(debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.TRAIPHIEU),

      QHTDHT_DUNO_CAMKETNB_TRUE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.CAMKETNB ? true : false,
      QHTDHT_DUNO_CAMKETNB_FALSE: debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG.QHTDHT?.CAMKETNB ? false : true,
      QHTDHT_DUNO_CAMKETNB_INFO: JSON.stringify(debtInfo?.enterpriseCicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.CAMKETNB),
      loan_scoring: { ...result },
      loan_rating: {
        ...loanRating,
        max_limit_by_rank_vn_text: numberToString(loanRating.max_limit_by_rank),
        max_limit_by_rank_vn: loanRating.max_limit_by_rank ? utils.numberWithCommas(parseInt(loanRating.max_limit_by_rank)) : '',
        approved_interest_rate_vn: parseFloat(loanRating.approved_interest_rate).toFixed(2),
        approved_limit_vn: loanRating.approved_limit ? utils.numberWithCommas(parseInt(loanRating.approved_limit)) : '',
        approved_limit_vn_text: loanRating.approved_limit ? numberToString(loanRating.approved_limit) : ''
      },
      forecast_capital_needs_wrap: { ...forecastCapitalNeedsWrap },
      revenueDocumentXml: revenueDocumentXml,
      total_score_tc_and_ptc: parseFloat((totalScoreTc * (60 / 100)) + (totalScorePtc * (40 / 100))).toFixed(2),
      total_score_tc: parseFloat(totalScoreTc).toFixed(2),
      total_score_ptc: parseFloat(totalScorePtc).toFixed(2),
      persons_cic: debtInfo?.personsCic,
      persons_cic_total_vnd: personsCicTotalVnd,
      persons_cic_total_usd: personsCicTotalUsd,
      persons_cic_TONG_HANMUC_ALL_TCTD: debtInfo.TONG_HANMUC_ALL_TCTD,
      persons_cic_TONG_DU_NO_ALL_TCTD: debtInfo.TONG_DU_NO_ALL_TCTD,
      input_partners: inputPartners,
      output_partners: outputPartners,
      revenueDocumentXmlkyKKhai: revenueDocumentXml?.TTinChung?.TTinTKhaiThue?.TKhaiThue?.KyKKhaiThue?.kyKKhai,
      revenueDocumentXmlPrevKyKKhai: revenueDocumentXml?.TTinChung?.TTinTKhaiThue?.TKhaiThue?.KyKKhaiThue?.kyKKhai
        ? revenueDocumentXml?.TTinChung?.TTinTKhaiThue?.TKhaiThue?.KyKKhaiThue?.kyKKhai - 1 : '',
      revenueDocumentXmlKetQuaDoanhThu,
      revenueDocumentXmlDoanhThuChenhLechVn,
      revenueDocumentXmlDoanhThuChenhLechTiLe: parseFloat(revenueDocumentXmlDoanhThuChenhLechTiLe * 100).toFixed(2),
      revenueDocumentXmlKetQuaLoiNhuan,
      revenueDocumentXmlKetQuaLoiNhuanChenhLechVn,
      revenueDocumentXmlKetQuaLoiNhuanChenhLechTiLe: parseFloat(revenueDocumentXmlKetQuaLoiNhuanChenhLechTiLe * 100).toFixed(2),
      revenueDocumentXmlCanDoiKeToan: convertNumberToVnNumber(revenueDocumentXmlCanDoiKeToan),
      revenueDocumentXmlKetQuaKinhDoanh: convertNumberToVnNumber(revenueDocumentXmlKetQuaKinhDoanh),
      approval_int_rate_vn: parseFloat(+data.approval_int_rate * 100).toFixed(2),
      sme_representation_position: smeData[3] || '',
      sme_representation_issue_date_vn: dateHelper.convertDateToDDMMYYYY(new Date(data.sme_representation_issue_date)),
      sme_representation_issue_place_vn: smeData[4] || '',
      duNoTheTinDungDoanhNghiep: debtInfo.duNoTheTinDungDoanhNghiep,
      camketNgoaiBangDN: debtInfo.camketNgoaiBangDN
    }
    data = convertUndefinedToEmptyString(data);
    return data;
  } catch (e) {
    console.error(e);
    throw new Error(`${contractNumber} | func | getDataBctdMisa | error: ${e?.message}`);
  }
}

const convertUndefinedToEmptyString = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const newObj = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      if (value === undefined || value === null) {
        newObj[key] = '';
      } else if (typeof value === 'object' && value !== null) {
        newObj[key] = convertUndefinedToEmptyString(value);
      } else {
        newObj[key] = value;
      }
    }
  }

  return newObj;
}

const convertNumberToVnNumber = (obj) => {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  const newObj = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      if (value === undefined || value === null) {
        newObj[key] = '';
      } else if (typeof value === 'object' && value !== null) {
        newObj[key] = convertNumberToVnNumber(value);
      } else {

        if (isNumber(value)) {
          newObj[key] = utils.numberWithCommas(parseInt(value));
        } else {
          newObj[key] = value;
        }
      }
    }
  }

  return newObj;
}

const getMasterdata = async (loanContract) => {
  const [
    loan_purpose_vn,
    loan_customer_business_type
  ] = await Promise.all([
    getValueCode_v3(loanContract.loan_purpose, "LOAN_PURPOSE"),
    getValueCode_v3(loanContract?.loan_customer?.business_type, "BUSINESS_TYPE")
  ])
  return {
    loan_purpose_vn,
    loan_customer_business_type
  }
}

const getNgayThangNamToday = () => {
  const today = new Date()
  const dateDDMMYYYY = dateHelper.convertDateToDDMMYYYY(today)
  return {
    today: dateDDMMYYYY,
    today_full_bang_chu: `ngày ${dateHelper.getCurrentDateDD()} tháng ${dateHelper.getCurrentMonth()} năm ${today.getFullYear()}`,
    today_ngay: dateHelper.getCurrentDateDD(),
    today_thang: dateHelper.getCurrentMonth(),
    today_nam: today.getFullYear()
  }
}

const getDebts = async (contractNumber, step = CIC_STEP_CHECK.AF2_DETAIL) => {
  let debts = [];
  let duNoTheTinDungDoanhNghiep = {
    tongHanMuc: 0,
    tongSoTienPhaiTT: 0,
    tongSoTienChamTT: 0,
    soLuongTheTinDung: 0,
    tenToChucPhatHanhThe: []
  };
  let camketNgoaiBangDN = [];
  const [
    cicLog,
    loan
  ] = await Promise.all([
    sqlHelper.findOne({
      table: `loan_cic_log`,
      whereCondition: {
        contract_number: contractNumber,
        step: step
      },
      orderBy: {
        created_at: 'DESC'
      }
    }),
    loanContractRepo.findOneMisaLoanContract(contractNumber)
  ])
  if (!cicLog?.id || !loan?.loan_customer?.tax_id) {
    throw Error(`${contractNumber} | getDebts error: loan_cic_log not found`);
  }

  const responsePayload = JSON.parse(cicLog.response_payload);
  const enterprises = responsePayload?.cicData?.enterprises ?? [];
  const cicData = enterprises.filter(e => e.taxCode === loan?.loan_customer?.tax_id)?.[0]?.response;
  let TONG_VND_ALL_TCTD = 0;
  let TONG_USD_ALL_TCTD = 0;
  let TONG_HANMUC_ALL_TCTD = 0;
  let TONG_DU_NO_ALL_TCTD = 0;
  if (cicData) {
    if (cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG || cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG[0]) {
      if (Array.isArray(cicData.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG)) {
        let i = 0;
        for (const d of cicData.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.DONG) {
          let tctd = { ...d };
          if (!Array.isArray(tctd?.CTLOAIVAY?.DONG)) {
            tctd.CTLOAIVAY.DONG = [{ ...d.CTLOAIVAY.DONG }]
          }
          tctd.CTLOAIVAY.DONG = wrapCTLOAIVAY(tctd?.CTLOAIVAY?.DONG);
          debts.push({ stt: i + 1, ...tctd, NGAYSL: moment(tctd.NGAYSL, "YYYYMMDD").format("DD/MM/YYYY")});
          i++;
        }
      } else {
        let tctd = { ...cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG };
        if (!Array.isArray(tctd?.CTLOAIVAY?.DONG)) {
          tctd.CTLOAIVAY.DONG = [{ ...cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG?.CTLOAIVAY?.DONG }]
        }
        tctd.CTLOAIVAY.DONG = wrapCTLOAIVAY(tctd?.CTLOAIVAY?.DONG);
        debts.push({ stt: 1, ...tctd, NGAYSL: moment(tctd.NGAYSL, "YYYYMMDD").format("DD/MM/YYYY") });
      }
      // console.log(`DEBT: `, JSON.stringify(debts));
      debts.forEach(debt => {
        TONG_VND_ALL_TCTD += Number(debt.TONG_VND);
        TONG_USD_ALL_TCTD += Number(debt.TONG_USD);
      });
    }
    //lấy dư nợ thẻ tín dụng
    if (cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG) {
      let tongHanMuc = 0;
      let tongSoTienPhaiTT = 0;
      let tongSoTienChamTT = 0;
      let soLuongTheTinDung = 0;
      let tenToChucPhatHanhThe = [];
      if (Array.isArray(cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG)) {
        let items = cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG;
        items.forEach(e => {
          let i = 1;
          tongHanMuc = tongHanMuc + +(e.HANMUC_THETD ?? 0);
          tongSoTienPhaiTT = tongSoTienPhaiTT + +(e.SOTIEN_PHAI_TT ?? 0);
          tongSoTienChamTT = tongSoTienChamTT + +(e.SOTIEN_CHAM_TT ?? 0);
          soLuongTheTinDung = soLuongTheTinDung + +(e.SOLUONG_THETD ?? 0);
          tenToChucPhatHanhThe.push({
            stt: i,
            name: e.TENTCTD
          })
        })
      } else {
        let item = cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG
        tongHanMuc = tongHanMuc + +(item.HANMUC_THETD ?? 0);
        tongSoTienPhaiTT = tongSoTienPhaiTT + +(item.SOTIEN_PHAI_TT ?? 0);
        tongSoTienChamTT = tongSoTienChamTT + +(item.SOTIEN_CHAM_TT ?? 0);
        soLuongTheTinDung = soLuongTheTinDung + +(item.SOLUONG_THETD ?? 0);
        tenToChucPhatHanhThe.push({
          stt: 1,
          name: item.TENTCTD + '\n'
        });
      }
      duNoTheTinDungDoanhNghiep = {
        tongHanMuc,
        tongSoTienPhaiTT,
        tongSoTienChamTT,
        soLuongTheTinDung,
        tenToChucPhatHanhThe
      }
    }

    //lấy cam kết ngoại bảng
    if (cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.CAMKETNB?.DONG) {
      if (Array.isArray(cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.CAMKETNB?.DONG)) {
        let items = cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.CAMKETNB?.DONG;
        let i = 1;
        items.forEach(e => {
          let init = {
            stt: i,
            TENTCTD: e.TENTCTD,
            GIATRI: e.GIATRI,
            LOAITIEN: e.LOAITIEN,
            NHOMNO: e.NHOMNO,
            NGAYSL: moment(e.NGAYSL, "YYYYMMDD").format("DD/MM/YYYY")
          };
          camketNgoaiBangDN.push(init);
          i = i + 1;
        })
      } else {
        let e = cicData?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.CAMKETNB?.DONG;
        camketNgoaiBangDN.push({
          stt: 1,
          TENTCTD: e.TENTCTD,
          GIATRI: e.GIATRI,
          LOAITIEN: e.LOAITIEN,
          NHOMNO: e.NHOMNO,
          NGAYSL: moment(e.NGAYSL, "YYYYMMDD").format("DD/MM/YYYY")
        });
      }
    }
  }

  //Tình hình cấp tín dụng cho nhóm khách hàng liên quan 
  const loanCustomerRepresentation = loan?.loan_customer_representations[0];
  if (!loan?.loan_customer_representations?.[0]?.id) {
    throw Error(`${contractNumber} | getDebts error: loan_customer_representations not found`);
  }

  const cicDataPersons = responsePayload?.cicData?.persons;
  const representationCic = cicDataPersons.filter(e => e.idNumber === loanCustomerRepresentation.id_number)?.[0];
  let haveData = (Array.isArray(representationCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG)
    || representationCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) ? true : false;
  let noData = !haveData;
  let shareholdersCic = [{
    stt: 1,
    ...representationCic,
    companyName: undefined,
    haveData,
    noData,
    haveDataTHETD: false,
    noDataTHETD: true
  }];

  //thông tin cic vợ/chồng
  let isMarried = false;
  let businessTypeChecked = [BUSINESS_TYPE["01"], BUSINESS_TYPE["04"]];
  if (businessTypeChecked.includes(loan.business_type) && loan.loan_business_owner.married_status === 'M') {
    isMarried = true;
    let individualCic = cicDataPersons.filter(e => e.idNumber === loan.loan_business_owner.partner_id_number)?.[0];
    if (!shareholdersCic.some(e => e.idNumber === individualCic.idNumber)) {
      individualCic.companyName = undefined;
      let haveData = (Array.isArray(individualCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG)
        || individualCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) ? true : false;
      let noData = !haveData;
      shareholdersCic.push({
        stt: 2,
        ...individualCic,
        haveData,
        noData,
        haveDataTHETD: false,
        noDataTHETD: true
      });
    }
  }

  const loanCustomerShareholders = loan?.loan_customer_shareholders;
  if (loanCustomerShareholders?.length > 0) {
    let i = isMarried ? 2 : 1;
    for (const shareholder of loanCustomerShareholders) {
      if (shareholder?.subject == 'INDIVIDUAL') {
        let individualCic = cicDataPersons.filter(e => e.idNumber === shareholder.id_number)?.[0];
        if (!shareholdersCic.some(e => e.idNumber === individualCic.idNumber)) {
          individualCic.companyName = undefined;
          let haveData = (Array.isArray(individualCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) 
          || individualCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) ? true : false;
          let noData = !haveData;
          shareholdersCic.push({ 
            stt: i + 1,
            ...individualCic,
            haveData,
            noData,
            haveDataTHETD: false,
            noDataTHETD: true
          });
          i++;
        }
      } else {
        let enterpriseCic = enterprises.filter(e => e.taxCode === shareholder.tax_id)?.[0];
        if (!shareholdersCic.some(e => e.taxCode === enterpriseCic.taxCode)) {
          let haveData = (Array.isArray(enterpriseCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) 
          || enterpriseCic?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) ? true : false;
          let noData = !haveData;
          shareholdersCic.push({
            stt: i + 1,
            ...enterpriseCic,
            customerName: null,
            haveData,
            noData,
            haveDataTHETD: false,
            noDataTHETD: true
          });
          i++
        }
      }
    }
  }

  if (shareholdersCic?.length > 0) {
    for (let i = 0; i < shareholdersCic.length; i++) {
      if (Array.isArray(shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG)) {
        let totalVnd = 0;
        let totalUsd = 0;
        let nhomNoMax;
        shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG.forEach(item => {
          totalVnd += parseFloat(item.TONG_VND);
          totalUsd += parseFloat(item.TONG_USD);
          //get nhom no cao nhat
          if (Array.isArray(item?.CTLOAIVAY?.DONG)) {
            if (!nhomNoMax) nhomNoMax = +item?.CTLOAIVAY?.DONG[0].NHOMNO;
            item?.CTLOAIVAY?.DONG.forEach(ctloaivay => {
              if (nhomNoMax < ctloaivay.NHOMNO) {
                nhomNoMax = ctloaivay.NHOMNO
              }
            })
          } else if (item?.CTLOAIVAY?.DONG) {
            if (!nhomNoMax) nhomNoMax = +item?.CTLOAIVAY?.DONG?.NHOMNO;
            if (nhomNoMax < item?.CTLOAIVAY?.DONG?.NHOMNO) {
              nhomNoMax = item?.CTLOAIVAY?.DONG?.NHOMNO
            }
          }
        });
        shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.totalVnd = totalVnd;
        shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.totalUsd = totalUsd;
        shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.nhomNoMax = nhomNoMax;
      } else if (shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG) {
        //get nhom no cao nhat
        let nhomNoMax;
        if (Array.isArray(shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG?.CTLOAIVAY?.DONG)) {
          let item = shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG;
          if (!nhomNoMax) nhomNoMax = +item?.CTLOAIVAY?.DONG[0].NHOMNO;
          item?.CTLOAIVAY?.DONG.forEach(ctloaivay => {
            if (nhomNoMax < ctloaivay.NHOMNO) {
              nhomNoMax = ctloaivay.NHOMNO
            }
          })
        } else if (shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG?.CTLOAIVAY?.DONG) {
          if (!nhomNoMax) nhomNoMax = shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG?.CTLOAIVAY?.DONG?.NHOMNO;
          nhomNoMax = shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG?.CTLOAIVAY?.DONG?.NHOMNO;
        }
        shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.totalVnd = Number(shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.DONG.TONG_VND);
        shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.totalUsd = Number(shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.DONG.TONG_USD);
        shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.QHTD.nhomNoMax = nhomNoMax;
      }
    }
  }
  //lấy thông tin thẻ tín dụng
  for (let i = 0; i < shareholdersCic?.length; i++) {
    let TONG_HANMUC_THETD = 0;
    let TONG_SOTIEN_PHAI_TT = 0;
    if (shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG) {
      shareholdersCic[i].haveDataTHETD = true;
      shareholdersCic[i].noDataTHETD = false;
      if (Array.isArray(shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.DUNO_THETD.DONG)) {
        let item = shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.DUNO_THETD.DONG;
        item.forEach(dong => {
          TONG_HANMUC_THETD = TONG_HANMUC_THETD + +dong.HANMUC_THETD;
          TONG_SOTIEN_PHAI_TT = TONG_SOTIEN_PHAI_TT + +dong.SOTIEN_PHAI_TT;
        })
      } else {
        TONG_HANMUC_THETD = TONG_HANMUC_THETD + +shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.DUNO_THETD.DONG.HANMUC_THETD;
        TONG_SOTIEN_PHAI_TT = TONG_SOTIEN_PHAI_TT + +shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.DUNO_THETD.DONG.SOTIEN_PHAI_TT;
      }
    }
    if (shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG) {
      shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.DUNO_THETD.TONG_HANMUC_THETD = TONG_HANMUC_THETD;
    }
    if (shareholdersCic[i]?.response?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.DUNO_THETD?.DONG) {
      shareholdersCic[i].response.NOIDUNG_BANTLTIN.NOIDUNG.QHTDHT.DUNO_THETD.TONG_SOTIEN_PHAI_TT = TONG_SOTIEN_PHAI_TT;
    }
    TONG_HANMUC_ALL_TCTD = TONG_HANMUC_ALL_TCTD + TONG_HANMUC_THETD;
    TONG_DU_NO_ALL_TCTD = TONG_DU_NO_ALL_TCTD + TONG_SOTIEN_PHAI_TT;
  }

  const personsCic = [...shareholdersCic];

  return {
    debts,
    TONG_VND_ALL_TCTD,
    TONG_USD_ALL_TCTD,
    enterpriseCicData: cicData,
    personsCic: personsCic,
    TONG_HANMUC_ALL_TCTD,
    TONG_DU_NO_ALL_TCTD,
    duNoTheTinDungDoanhNghiep,
    camketNgoaiBangDN
  };
}

const wrapCTLOAIVAY = (data) => {
  const result = data.reduce((acc, item) => {
    const { LOAIVAY, NHOMNO, DUNO_VND, DUNO_USD } = item;

    if (!acc[LOAIVAY]) {
      acc[LOAIVAY] = {
        LOAIVAY: mappingLoaiVay(LOAIVAY),
        NHOMNO: [],
        TONG_DUNO_VND: 0,
        TONG_DUNO_USD: 0
      };
    }

    acc[LOAIVAY].NHOMNO.push({ NHOM: NHOMNO, DUNO_VND: DUNO_VND, DUNO_USD: DUNO_USD });
    acc[LOAIVAY].TONG_DUNO_VND += Number(DUNO_VND);
    acc[LOAIVAY].TONG_DUNO_USD += Number(DUNO_USD);

    return acc;
  }, {});

  const wrappedResult = Object.values(result).map(item => ({
    ...item,
    TONG_DUNO_VND: item.TONG_DUNO_VND.toString(),
    TONG_DUNO_USD: item.TONG_DUNO_USD.toString()
  }));

  // console.log(JSON.stringify(wrappedResult));
  return wrappedResult;
}

const mappingLoaiVay = (loaiVay) => {
  if (loaiVay === '01') return 'ngắn hạn';
  if (loaiVay === '02') return 'trung hạn';
  return 'dài hạn';
}

async function saveUpload(poolWrite, docId, docUrl, docType, bundleId, fileKey, fileName, contractNumber) {
  const sql = `insert into loan_contract_document(doc_id,doc_type,url,doc_group,file_key,file_name,contract_number) values ($1,$2,$3,$4,$5,$6,$7)`;
  poolWrite
    .query(sql, [docId, docType, docUrl, bundleId, fileKey, fileName, contractNumber])
    .then()
    .catch((error) => {
      common.log(error.message);
    });
}

function isNumber(value) {
  return typeof value === 'number' && !isNaN(value);
}

const exportKunnData = async (kunnData)=>{
  const _ = {};
  try {
    const contract =  helper.snakeToCamel( await loanContractRepo.getLoanContract(kunnData.contractNumber));
    //lay lich thanh toan
    let installments = await getKunnInstallmentByKunnApi(kunnData.kunnId);
    if(!installments)
    {
      const errors = [{
        code: 'installment',
        message: `cannot get installment info`
      }]
      throw new IntReqExp({data : new BadRequestResponse(errors)});
    }
    
    installments = installments.sort((a, b) => a.dueDate - b.dueDate);
    for(let i=0;i<installments.length;i++)
    {
      const e = installments[i];
      installments[i] = {
        ...e,
        index: i+1,
        dueDate: moment(e.dueDate).tz("Asia/Ho_Chi_Minh").format("DD/MM/YYYY"),
        remainIrAmount: helper.formatNumberVietnamese(e.remainIrAmount),
        capitalRefunded: helper.formatNumberVietnamese(e.capitalRefunded),
        fee: helper.formatNumberVietnamese(e.fee),
        remainPrinAmount: helper.formatNumberVietnamese(e.remainPrinAmount),
        totalPaymentAmount: helper.formatNumberVietnamese(Number(e.remainIrAmount || '0') 
                          + Number(e.capitalRefunded || '0') + Number(e.fee || '0'))
      }
    }
    _.kunnId = kunnData.kunnId;
    _.customerName = kunnData.customerName;
    _.taxId = contract.smeTaxId || '';
    _.loanPurpose = contract.loanPurpose || '';
    _.withDrawAmount = helper.formatNumberVietnamese(kunnData.withDrawAmount || '0');
    _.disbursementDate = dateHelper.toVnDateFormat(kunnData.dateApproval|| dateHelper.LMS_DATE());
    _.matDate = dateHelper.toVnDateFormat(kunnData.endDate || '');
    _.monthlyPaymentAmount = '';
    _.finalPaymentAmount = '';
    _.tenor = kunnData.tenor;
    _.ir = helper.formatRateNumber(kunnData.ir);
    _.address = contract.addressOnLicense;
    _.schedule = installments;
    _.withDrawAmountText = numberToString(Number(kunnData.withDrawAmount));
    _.productCode = contract.productCode;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][exportKunnData] kunnId ${kunnData.kunnId}, error ${error}`);
  }
  return _;
}

const exportKunnDataTTRV = async (kunnData, contract, cicData, loan_customer, { availableAmount, lastestLoanAmount } = {}) => {
  let _ = {};
  try {
    const representation = helper.snakeToCamel(await sqlHelper.findOne({
      table: `loan_customer_representations`,
      whereCondition: {
        contract_number: kunnData.contractNumber,
      },
      orderBy: {
          created_at: 'desc'
      }
    }));
    //get address
    // const contractNumber = contract.contractNumber;
    const fullAddress = await getFullAddress(
        global.config,
        contract.smeHeadquartersProvince,
        contract.smeHeadquartersDistrict,
        contract.smeHeadquartersWard
      );
    const [newWard, newProvince, customerNewWard, customerNewProvince] = await Promise.all([
      parseValueCode(contract.smeHeadquartersNewWard, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(contract.smeHeadquartersNewProvince, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      parseValueCode(loan_customer?.new_ward_code, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(loan_customer?.new_province_code, ADDRESS_CODE_TYPE.NEW_PROVINCE)
    ]);

    _.headquartersFullAddress = `${contract.smeHeadquartersAddress || ""}, ${fullAddress}`;
    _.headquartersAddress = [contract.smeHeadquartersAddress, newWard, newProvince].join(', ');
    _.address_on_license = contract.addressOnLicense || '';
    _.new_address_on_license = [loan_customer?.new_address_on_license, customerNewWard, customerNewProvince].join(', ');
    _.limitAmount = helper.formatNumberVietnamese(contract.approvalAmt || 0);

    if (!!newWard) {
      _.address = _.headquartersAddress;
    } else {
      _.address = _.headquartersFullAddress;
    }
    const loanPurpose = await getValueCode_v2(
      global.config,
      contract.loanPurpose,
      "LOAN_PURPOSE"
    );
    const productInfo = await getProductDetailByCodeApi(
      contract.productCode
    );

    const overdueIr = productInfo?.rates?.find(
        (val) => val?.rateType === RATE_TYPE.OVERDUE
      )?.intRateVal;
    
    // const avalibleAmountRs = await checkAvailableAmountApi(contractNumber);
    const remainPrincipalAmount = lastestLoanAmount;
    const remainPrincipalAmountAfter =
    remainPrincipalAmount + Number(kunnData.withDrawAmount || 0);
    // sme_representation_address_cur
    // address = `${contract.addressCur}, ${address}`;
    const issuePlace = await getValueCode_v2(
      global.config,
      representation.issuePlace,
      "ISSUE_PLACE_VN"
    );
    const today = dateHelper.exportTodayFullDate();
    _.contractNumber = contract.contractNumber;
    _.kunnId = kunnData.kunnId;
    _.customerName = kunnData.customerName;
    _.taxId = contract.taxId || "";
    _.loanPurpose = contract.loanPurpose || "";
    _.withDrawAmount = helper.formatNumberVietnamese(
      kunnData.withDrawAmount || "0"
    );
    _.disbursementDate = dateHelper.toVnDateFormat(
      kunnData.dateApproval || dateHelper.LMS_DATE()
    );
    _.matDate = dateHelper.toVnDateFormat(kunnData.endDate || "");
    _.monthlyPaymentAmount = "";
    _.finalPaymentAmount = "";
    _.tenor = kunnData.tenor;
    _.ir = helper.formatRateNumber(kunnData.ir);
    _.withDrawAmountText = numberToString(Number(kunnData.withDrawAmount));
    _.productCode = contract.productCode;
    _.loanPurpose = loanPurpose;
    _.availableAmount = helper.formatNumberVietnamese(availableAmount);
    _.remainPrincipalAmount = helper.formatNumberVietnamese(
      remainPrincipalAmount
    );
    _.remainPrincipalAmountAfter = helper.formatNumberVietnamese(
      remainPrincipalAmountAfter
    );
    _.smeRepresentationPhoneNumber = contract.smePhoneNumber || `   `;
    _.smeRepresentationEmail = contract.smeEmail || `   `;
    _.smeRepresentationFax = contract.fax || `   `;
    _.smeRepresentationName = representation.fullName || `   `;
    _.smeRepresentationId = representation.idNumber || `   `;
    _.smeRepresentationIssueDate =  dateHelper.toVnDateFormat(representation.issueDate) || `   `;
    _.smeRepresentationIssuePlace =issuePlace || `   `;
    _.smeRepresentationEmail = representation.email || `   `;;
    _.smeRepresentationFax = representation.fax || `   `;
    _.registrationNumber = contract.registrationNumber;
    _.taxCode = contract.taxId;
    _.billDay = kunnData.billDay;
    _.overdueIr = overdueIr.toString().replace('.',',');
    _.today_dd = today.dd;
    _.today_mm = today.mm;
    _.today_yyyy = today.yyyy;
    const exportedCicData = exportCicData(cicData);
    _ = {..._,...exportedCicData};
  } catch (error) {
    console.log(
      `[MISA][KUNN][V2][exportKunnData] kunnId ${kunnData.kunnId}, error ${error}`
    );
  }
  return _;
};


const exportCicData = (cicData)=>{
  const _= {};
  try {
    if(!cicData?.response) return _;
    const rs = cicData.response;
    const debts = helper.toArray (rs?.NOIDUNG_BANTLTIN?.NOIDUNG?.QHTDHT?.QHTD?.DONG);
    const shortTerm = getCicTotalDebts({debts, debtType : '01'});
    const mediumTerm = getCicTotalDebts({debts, debtType : '02'});
    const longTerm = getCicTotalDebts({debts, debtType : '03'});

    _.shortTermDebt = helper.formatNumberVietnamese(shortTerm.totalDebt);
    _.shortTermBadDebt = helper.formatNumberVietnamese(shortTerm.totalBadDebt);
    _.mediumTermDebt = helper.formatNumberVietnamese(mediumTerm.totalDebt);
    _.mediumTermBadDebt = helper.formatNumberVietnamese(mediumTerm.totalBadDebt);
    _.longTermDebt = helper.formatNumberVietnamese(longTerm.totalDebt);
    _.longTermBadDebt = helper.formatNumberVietnamese(longTerm.totalBadDebt);
  } catch (error) {
    console.log(`[exportCicData] taxCode: ${cicData?.taxCode}, error ${error}`);
  }
  return _;
}

const getCicTotalDebts = ({ debts, debtType, key = "DUNO_VND" }) => {
  let totalDebt = 0;
  let totalBadDebt = 0;
  try {
    for (const debt of debts) {
      const exportDebt = getCicTotalDebt({ debt, debtType, key });
      totalDebt += exportDebt.totalDebt;
      totalBadDebt += exportDebt.totalBadDebt;
    }
    return { totalDebt, totalBadDebt };
  } catch (error) {
    return { totalDebt, totalBadDebt };
  }
};

const getCicTotalDebt = ({debt, debtType ,key='DUNO_VND'})=>{
  let totalDebt = 0;
  let totalBadDebt = 0;
  try {
    const ratio = 1000000;
    const detailsDebt = helper.toArray(debt?.CTLOAIVAY?.DONG)?.filter(e=>e['LOAIVAY'] == debtType);
    const detailsBadDebt = detailsDebt.filter(e=>Number(e['NHOMNO']) > 1);
    totalDebt =  detailsDebt.reduce((sum, detail) => sum + Number(detail[key]), 0) * ratio;
    totalBadDebt =  detailsBadDebt.reduce((sum, detail) => sum + Number(detail[key]), 0) * ratio;
    return {totalDebt, totalBadDebt}
  } catch (error) {
    return {totalDebt, totalBadDebt}
  }
}

/**
 * Gen lich tra no khe uoc misa
 * @param {*} contractNumber 
 * @returns 
 */
const genMisaKunnPaymentSchedule = async (kunnData) => {
  const kunnId= kunnData.kunnId;
  try {
    const tmplPath = './static_file/misa_kunn_lich_thanh_toan.docx';
    const fileData = await exportKunnData(kunnData);
    // return
    return await genMisaKunnFile(fileData,`${kunnId}_lich_thanh_toan`,tmplPath);
  } catch (error) {
    console.log(`[MISA][KUNN][V2][genMisaKunnPaymentSchedule] kunnId ${kunnId} error ${error}`);
    throw error;
  }
}

/**
 * Gen to trinh rut von
 * @param {*} contractNumber 
 * @returns 
 */
const genMisaKunnTTRV = async (kunnId, { availableAmount, lastestLoanAmount } = {}) => {
  try {
    const kunnData = utils.snakeToCamel(await getKunnData(kunnId));
    const contractNumber =  kunnData.contractNumber;
    const contract = helper.snakeToCamel(
      await loanContractRepo.getLoanContract(contractNumber)
    );
    const cicLog = await sqlHelper.findOne({
      table: `loan_cic_log`,
      whereCondition: {
        contract_number: kunnData.kunnId,
        step: CIC_STEP_CHECK.KUNN
      },
      orderBy: {
        created_at: 'DESC'
      }

    })
    if (!cicLog?.id) {
      throw Error(`${kunnId} | callbackCicDetailToMisa error: loan_cic_log not found`);
    }
    //check avaible limit
    const responsePayload = JSON.parse(cicLog.response_payload ?? '{}');
    const cicData = responsePayload?.cicData?.enterprises.find(e=> e.taxCode == contract.taxId);
    const tmplPath = './static_file/MS.TTRV.docx';
    const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);
    const fileData = await exportKunnDataTTRV(kunnData, contract, cicData, loan.loan_customer, { availableAmount, lastestLoanAmount });
    // return
    const { resultS3, encryptResultS3 } = await genMisaKunnFile(fileData,`${kunnId}_to_trinh_rut_von`,tmplPath);
    const fileName = `${kunnId}_TTRV_${new Date().getTime()}.pdf`;
    const fileNameENC = `${kunnId}_TTRV_${new Date().getTime()}_ENC.pdf.pgp`;

    await Promise.all([
      loanContractDocumentRepo.insert({
        contractNumber,
        kunnContractNumber: kunnData.kunnId,
        docType: "TTRV_ENC",
        docId: uuid.v4(),
        fileKey: encryptResultS3.Key,
        fileName: fileNameENC,
        url: encryptResultS3.Location,
        typeCollection: TYPE_COLLECTION.DOC_INTERNAL,
        fileSize: encryptResultS3.fileSize,
      }),
      loanContractDocumentRepo.insert({
        contractNumber,
        kunnContractNumber: kunnData.kunnId,
        docType: "TTRV",
        docId: uuid.v4(),
        fileKey: resultS3.Key,
        fileName: fileName,
        url: resultS3.Location,
        typeCollection: TYPE_COLLECTION.DOC_INTERNAL,
        fileSize: resultS3.fileSize,
        docGroup: DOC_GROUP.MISA_HM_DISBURSAL,
      }),
    ]);
    return;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][genMisaKunnTTRV] kunnId ${kunnId} error ${error}`);
    throw error;
  }
}

/**
 * Gen lich tra no khe uoc misa
 * @param {*} contractNumber 
 * @returns 
 */
const genMisaKunnFile = async (exportData, fileNamePre, tmplPath) => {
  try {
    const fileBuffer = await wordUtil.renderWordFile({ filePath: tmplPath, data: exportData });
    const fileName = `${fileNamePre}_${moment().format("YYYYMMDDHHmmss.SSS")}.pdf`;
    const encryptFileName = `${fileNamePre}_${moment().format("YYYYMMDDHHmmss.SSS")}.pgp`;

    const pdfBuffer = await wordUtil.convertPdf({ fileBuffer });
    const encryptedBuffer = await encryptFileMisa(pdfBuffer);
    // await fs.writeFileSync('/home/<USER>/Documents/ltt.pdf',pdfBuffer );
    // return {resultS3: {},encryptResultS3 :{}}
    const [resultS3, encryptResultS3] = await Promise.all([
      wordUtil.pushFileToS3({
        fileName: fileName,
        fileBuffer: pdfBuffer,
        fileStoragePath: storageUnSignPath
      }),
      wordUtil.pushFileToS3({
        fileName: encryptFileName,
        fileBuffer: encryptedBuffer,
        fileStoragePath: storageUnSignPath,
        acl: 'private'
      })
    ])
    //callback file to misa
    resultS3.fileSize = pdfBuffer.length;
    encryptResultS3.fileSize = encryptedBuffer.length;
    return {
      resultS3,
      encryptResultS3
    };
  } catch (error) {
    console.log(`[MISA][KUNN][genMisaKunnFile] fileName ${fileNamePre} error ${error}`);
    throw error;
  }
}

module.exports = {
  generateBctdMisa,
  getDataBctdMisa,
  genMisaKunnPaymentSchedule,
  genMisaKunnTTRV
}