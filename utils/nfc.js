function parseDG13(str) {
  const buffer = Buffer.from(str, "base64");
  const data = [...buffer];
  const lines = [[]];
  // for (let i = 0; i < data.length; i++) {
  //   const element = data[i];
  //   if (element == 2 && data[i + 1] == 1) {
  //     lines.push([]);
  //     i += 3;
  //   } else {
  //     lines[lines.length - 1].push(element);
  //   }
  // }
  let i = 0;
  while (i < data.length) {
    const element = data[i];

    if (element === 2 && data[i + 1] === 1) {
      lines.push([]);
      i += 4; 
    } else {
      lines[lines.length - 1].push(element);
      i++;
    }
  }
  const info = {};
  try {
    info.cid_number = getString(lines[2]);
    info.name = getString(lines[3]);
    info.dob = getString(lines[4]);
    info.gender = getString(lines[5]);
    info.nationality = getString(lines[6]);
    info.ethnic = getString(lines[7]);
    info.religion = getString(lines[8]);
    info.country = getString(lines[9]);
    info.address = getString(lines[10]);
    info.features = getString(lines[11]);
    info.issueDate = getString(lines[12]);
    info.expired_date = getString(lines[13]);
    const parentsName = splitNames(lines[14]);
    info.father_name = parentsName[0];
    info.mother_name = parentsName[1];
    const partnerNames = splitNames(lines[15]);
    if (partnerNames[0]) {
      info.partner_name = partnerNames[0];
    }
    info.id_number = getString(lines[16]);
    info.scan_type = "NFC";
  } catch (err) {
    console.log(err);
  }
  return info;
}

function splitNames(data) {
  const output = [];
  let index = data.indexOf(12);
  while (index > 0) {
    const len = data[index + 1];
    if (len == 0) {
      break;
    }
    output.push(Buffer.from(data.slice(index + 2, index + 2 + len)).toString());
    index = data.indexOf(12, index + len);
  }
  return output;
}

function getString(data) {
  const len = data[0];
  return Buffer.from(data.slice(1, len + 1)).toString();
}

module.exports = {
  parseDG13,
};
