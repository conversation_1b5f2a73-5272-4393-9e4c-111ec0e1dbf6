const _ = require('lodash');
const { convertCamelToSnake, isNullOrEmptyV3 } = require('./helper');
class FindManyDto {
  constructor(filter = {}) {
    filter.keyword = filter.keyword?.trim() || "";
    filter.page = Number(filter.page || 1);
    filter.take = Number(filter.take || 10);
    this.keyword = filter.keyword;
    this.page = filter.page;
    this.take = filter.take;
    this.skip = (this.page - 1) * this.take;
    this.filter = filter;
    this.allowFilter = [];
    this.allowSearch = [];
  }

  getKeyword() {
    this.keyword = this.keyword.trim();
    return this.keyword;
  }

  getPage() {
    return Number(this.page || 1);
  }

  getTake() {
    return Number(this.take || 10);
  }

  getSkip() {
    return Number((this.page - 1) * this.getTake());
  }

  addFilter(data) {
    let f = this.getFilter();

    f = { ...(f || {}), ...data };

    this.filter = f;
  }

  setDateFilterKey(key) {
    this.dateFilterKey = key;
  }

  toMetaData() {
    return {
      page: this.getPage(),
      take: this.getTake(),
      keyword: this.keyword,
      filter: this.getFilter(),
    };
  }

  getFilter() {
    return _.pickBy(_.pick( convertCamelToSnake(this.filter), this.allowFilter),value => !isNullOrEmptyV3(value));
  }

  getFilterSnake() {
    return convertCamelToSnake(this.getFilter());
  }

  setAllowFilter(allowFilter) {
    this.allowFilter = allowFilter || [];
  }

  setAllowSearch(allowSearch) {
    this.allowSearch = allowSearch || [];
  }
  getAllowSearch() {
    return this.allowSearch;
  }
  fromReq(req) {
    const query = req.query || {};
    return FindManyDto(query);
  }
}

module.exports = FindManyDto;
