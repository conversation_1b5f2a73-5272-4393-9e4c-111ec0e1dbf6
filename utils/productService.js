const { config } = require("aws-sdk")
const common = require("./common")

async function getProductLimitInfo(config,productCode) {
    const url = config.basic.product[config.env] + config.data.productService.productInfo + `?prdctCode=${productCode}`
    const productInfo = await common.getAPI(url)
    if(productInfo.status == 200) {
        return productInfo.data
    }
    return null
}

async function getBundle(config,productCode,stage=undefined,isKunn=false) {

    let url = config.basic.product[config.env] + config.data.productService.getBundleV2 + `?productcode=${productCode}`
    if(isKunn){
        url = config.basic.product[config.env] + config.data.productService.getBundleV4 + `?productcode=${productCode}`
    }
    if(stage != undefined) {
        url = config.basic.product[config.env] + config.data.productService.getBundleV2 + `?productcode=${productCode}&stage=${stage}`
    }
    const bundleInfo = await common.getApiTimeoutV2({ url })
    if(bundleInfo.data.code != 1) {
        return false
    }
    return bundleInfo.data
}

async function getBundleV3(config,productCode,stage=undefined) {

    let url = config.basic.product[config.env] + config.data.productService.getBundleV3 + `?productcode=${productCode}`
    if(stage != undefined) {
        url = config.basic.product[config.env] + config.data.productService.getBundleV3 + `?productcode=${productCode}&stage=${stage}`
    }
    const bundleInfo = await common.getApiV2(url)
    if(bundleInfo.data.code != 1) {
        return false
    }
    return bundleInfo.data
}

async function getBundleV4(config,productCode) {

    let url = config.basic.product[config.env] + `/product/v1/product-bundle-v4?productcode=${productCode}`
    // if(stage != undefined) {
    //     url = config.basic.product[config.env] + `/product/v1/product-bundle-v4?productcode=${productCode}&stage=${stage}`
    // }
    const bundleInfo = await common.getApiV2(url)
    if(bundleInfo.data.code != 1) {
        return false
    }
    return bundleInfo?.data?.data
}

function mapBundleGroup(docList,bundleInfo) {
    const docDict = {}
    bundleInfo.map(bundle => {
        let tmpDocList = bundle.docList
        tmpDocList.forEach(doc => {
            docDict[doc.docType] = bundle.bundleName
            docDict[doc.bundleNameVi] = bundle.bundleNameVi
        })
    })
    docList.map(doc => {
        doc.docGroup = doc.hasOwnProperty('doc_type') ? docDict[doc.doc_type]  : (doc.hasOwnProperty('docName') ? docDict[doc.docName]  : docDict[doc.docType])
        doc.bundleNameVi = docDict[doc.bundleNameVi]
    })
    
    return docList
}


function mapBundleGroupV2(docList,bundleInfo) {
    for(const doc of docList) {
        const bundle = bundleInfo.find(bundle => bundle.docList.find(docItem => docItem.docType == doc.docType));
        if(bundle) {
            doc.bundleName = bundle.bundleName;
            doc.bundleNameVi = bundle.bundleNameVi;
            doc.docGroup = bundle.bundleName;
        }
    }
    return docList
}

function mapBundleGroupKOV(docList,bundleInfo) {
    const docDict = {}
    bundleInfo.map(bundle => {
        let tmpDocList = bundle.docList
        tmpDocList.forEach(doc => {
            docDict[doc.docType] = bundle.bundleName
        })
    })    
    docList.map(doc => {
        doc.docGroup = docDict[doc.docName]  
    })
    return docList
}

function mapBundleGroupBizz(docList,bundleInfo) {
    const docDict = {}
    bundleInfo.map(bundle => {
        let tmpDocList = bundle.docList
        tmpDocList.forEach(doc => {
            docDict[doc.docType] = bundle.bundleName
        })
    })    
    docList.map(doc => {
        doc.doc_group = docDict[doc.doc_type]
        // doc.docNameVn = bundleInfo
    })
    return docList
}

async function getProductInfo(config,productCode) {
    const url = config.basic.product[config.env] + `/product/v1/productInfo/?prdctCode=${productCode}`
    const productInfo = await common.getApiV2(url)
    if(productInfo.data.status != 200) {
        return false
    }
    return productInfo.data.data
}

async function getProductInfoV2(productCode) {
    const config = global.config
    const url = config.basic.product[config.env] + `/product/v1/productInfo/?prdctCode=${productCode}`
    const productInfo = await common.getApiV2(url)
    if(productInfo.data.status != 200) {
        return false
    }
    return productInfo.data.data
}

async function getProductInfoV3(productCode) {
    const config = global.config
    const url = config.basic.product[config.env] + `/product/v1/product-detail-smca?productCode=${productCode}`
    const productInfo = await common.getApiV2(url)
    if(productInfo.data.status != 0) {
        return false
    }
    return productInfo.data.data
}

const getFees = (prdInfo) => {
    let fee = [];
    if(prdInfo !== undefined){
        let fees = prdInfo.fee;
        if(fees !== undefined){
            fees.every(item => {
                let feeDetail = item.feeDetail[0];
                let feeType = feeDetail.feeType;
                if(feeType === 1){
                    let calDetail = feeDetail.calculaDetail[0];
                    let detail = {
                        "feeId": feeDetail.feId,
                        "feeName": feeDetail.feeName,
                        "feeType": "M",
                        "feeAmt": calDetail.fixAmt,
                    };
                    fee.push(detail);
                    //return false;
                }
                return true;
            })
        }
    }
    return fee;
}

const getInRates = (prd) => {
    try{
        let intRates = [];
        if(prd.productVar !== undefined){
            let prdVar = prd.productVar[0];

            let rateType = prdVar.intRateType;
            let rateVal = parseFloat(prdVar.intRate);

            intRates.push(
                {
                    "intRateName": "",
                    "rateType": rateType,
                    "intRateVal": rateVal
                }
            )

            let creditIntRate1 = prdVar.creditIntRate1,
                creditIntRate1Type = prdVar.creditIntRate1Type;
            
            if(creditIntRate1 !== undefined && creditIntRate1 !== null){
                intRates.push(
                    {
                        "intRateName": "",
                        "rateType": creditIntRate1Type,
                        "intRateVal": parseFloat(creditIntRate1)
                    }
                )
            }
            
        }
        return intRates;
    }
    catch(err){
        console.log(err);
        return [];
    }
}

const getInsurance = async function (req, contract_number) {
    let insuranceRS = [];
    try{
        const insurance = await getContractSelectedInsurance_V2(req, contract_number);
        if(insurance !== null){
            insuranceRS.push({
                insurId: insurance.insur_id,
                insurName: insurance.service_name,
                insurType: "M",
                insurAmt: insurance.insurance_amount
            });
        }
        return insuranceRS;
    }
    catch(err){
        console.log(`getInsurance error: ${err.message}`);
        console.log(err);
        return insuranceRS;
    }
}

async function getCICConfig() {
    const config = global.config
    const productUri = config.data.productService.getCICTable
    const productUrl = config.basic.product[config.env] + productUri
    const scoreList = await common.getAPI(productUrl)
    
    let scoreDict = {}
	scoreList.data.forEach(element => {
		if(!(element.code in scoreDict)) {
			scoreDict[element.code] = {}
		}
		scoreDict[element.code].termType = element.term_type 
		scoreDict[element.code].loanType = element.loan_type
	})
    return scoreDict
}

async function getMonthlyInstallment(amount,rate,tenor) {
    const config = global.config
    const body = {
        amount,
        tenor,
        rate
    }
    const url = config.basic.product[config.env] +  '/product/v1/offer/annuity'
    const data = await common.postAPI(url,body) 
    return data.data
}

async function getProductByPartner(partnerCode,custType=undefined) {
    const config = global.config
    let url = config.basic.product[config.env] + `/product/v1/productInfo/mainInfo?partnerCode=${partnerCode}`
    if(custType != undefined) {
        url = config.basic.product[config.env] + `/product/v1/productInfo/mainInfo?partnerCode=${partnerCode}&custType=${custType}`
    }
    const response = await common.getApiV2(url)
    if(response.data.code == 'SUCCESS') {
        return response.data.data[0]    
    }
    return false
}

async function getListProductCodeByChannel(channel) {
    const config = global.config
    const url = config.basic.product[config.env] + `/product/v1/list-product-by-channel?channel=${channel}`
    const productInfo = await common.getApiV2(url)
    if(productInfo.status != 200) {
        return false
    }
    return productInfo.data.data
}

module.exports = {
    getProductLimitInfo,
    getBundle,
    getBundleV3,
    mapBundleGroup,
    mapBundleGroupKOV,
    getProductInfo,
    getProductInfoV2,
    getFees,
    getInRates,
    getInsurance,
    getCICConfig,
    getMonthlyInstallment,
    getProductByPartner,
    getProductInfoV3,
    getListProductCodeByChannel,
    getBundleV4,
    mapBundleGroupBizz,
    mapBundleGroupV2
}