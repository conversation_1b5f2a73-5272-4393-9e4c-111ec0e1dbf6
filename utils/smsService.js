const common = require("./common")
const {SERVICE_NAME,CONTRACT_TYPE, PARTNER_CODE} = require("../const/definition")
const { getPartnerCode } = require("../repositories/loan-contract-repo")

async function sendSMS(contractNumber,content,url,dest,isSme=false) {
    const body = { 
        "source": isSme?"EASYSME.VN":"EASY CREDIT", 
        "dest": dest,
        "content":content,
        "channel": isSme?"SME":null     
    } 
    url = config.data.smsService.sendSMS
    const sendSmsResult = await common.postApiV2(url,body)
    return sendSmsResult
}

async function sendEsign(contractNumber,phoneNumber,contractType) {
    const config = global.config
    const smsUrl = config.data.smsService.sendSMS
    let msg = config.data.smsService.esigningHMMsg
    const partnerCode = await getPartnerCode(contractNumber)
    if (partnerCode == PARTNER_CODE.MCAPP || partnerCode == PARTNER_CODE.SMA || partnerCode == PARTNER_CODE.SMASYNC) msg = config.data.smsService.esigningHMMcaMsg
    if(contractType == CONTRACT_TYPE.CASH_LOAN) {
        msg = config.data.smsService.esigningCashMsg
        if (partnerCode == PARTNER_CODE.MCAPP) msg = config.data.smsService.esigningCashMcaMsg
    }
    msg = msg.replace("contractNumber", contractNumber)
    sendSMS(contractNumber,msg,smsUrl,phoneNumber)
}

async function sendResubmit(contractNumber,phoneNumber) {
    const smsString = `Ho so cua QK tai Easy Credit chua day du, vui long truy cap https://ppbhd.easycredit.vn/econtract de bo sung chung tu theo yeu cau. Chi tiet LH 19001066`
    const body = { 
        "source":"EASY CREDIT", 
        "dest": phoneNumber, 
        "content":smsString     
    } 
    const smsUrl = config.data.smsService.sendSMS
    const sendSmsResult = await common.postApiV2(smsUrl,body)
    return sendSmsResult
}

module.exports = {
    sendSMS,
    sendEsign,
    sendResubmit
}