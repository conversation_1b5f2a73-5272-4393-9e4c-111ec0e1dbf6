const insertData = async (table, columns, values) => {
  try {
    const columnNames = columns.join(", ");
    const placeholders = values.map((_, i) => `$${i + 1}`).join(", ");
    const query = `
      INSERT INTO ${table} (${columnNames})
      VALUES (${placeholders})
      RETURNING *;
    `;
    const res = await global.poolWrite.query(query, values);
    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`INSERT - ${table} error:`, e);
    return undefined;
  }
};

const updateData = async ({ table, columns, values, conditions }) => {
  try {
    const setClauses = columns.map((column, i) => `${column} = $${i + 1}`).join(", ");
    const conditionClauses = Object.keys(conditions)
      .map((key, i) => `${key} = $${columns.length + i + 1}`)
      .join(" AND ");
    const query = `
      UPDATE ${table}
      SET ${setClauses}
      WHERE ${conditionClauses}
      RETURNING *;
    `;
    const conditionValues = Object.values(conditions);
    const res = await global.poolWrite.query(query, [...values, ...conditionValues]);
    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`UPDATE - ${table} error:`, e);
    return undefined;
  }
};

const generateValues = (object, columns) => {
  return columns.map((column) => object[column]);
};

const patchUpdate = async ({ table, columns, values, conditions }) => {
  try {
    const filteredColumns = columns.filter((_, i) => values[i] !== undefined);
    const filteredValues = values.filter((value) => value !== undefined);

    if (filteredColumns.length === 0) {
      throw new Error("No columns to update");
    }

    const setClauses = filteredColumns.map((column, i) => `${column} = $${i + 1}`).join(", ");

    const conditionClauses = Object.keys(conditions)
      .map((key, i) => `${key} = $${filteredColumns.length + i + 1}`)
      .join(" AND ");

    const query = `
      UPDATE ${table}
      SET ${setClauses}
      WHERE ${conditionClauses}
      RETURNING *;
    `;

    const conditionValues = Object.values(conditions);
    const res = await global.poolWrite.query(query, [...filteredValues, ...conditionValues]);

    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`UPDATE - ${table} error:`, e);
    return undefined;
  }
};

const findOne = async ({ table, whereCondition, orderBy = {}, select = [] }) => {
  try {
    const keys = Object.keys(whereCondition);
    const values = Object.values(whereCondition);

    const whereClauses = keys.map((key, i) => `${key} = $${i + 1}`).join(" AND ");

    const orderKeys = Object.keys(orderBy);
    const orderClauses = orderKeys.length > 0 ? "ORDER BY " + orderKeys.map((key) => `${key} ${orderBy[key]}`).join(", ") : "";

    const query = `
      SELECT ${select.length > 0 ? select.join(", ") : "*"}
      FROM ${table}
      WHERE ${whereClauses}
      ${orderClauses}
      LIMIT 1;
    `;

    const res = await global.poolWrite.query(query, values);
    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`FIND_ONE - ${table} error:`, e);
    return undefined;
  }
};

const find = async ({ table, whereCondition, orderBy = {}, select = [] }) => {
  try {
    const keys = Object.keys(whereCondition);
    const values = Object.values(whereCondition);

    const whereClauses = keys.map((key, i) => `${key} = $${i + 1}`).join(" AND ");

    const orderKeys = Object.keys(orderBy);
    const orderClauses = orderKeys.length > 0 ? "ORDER BY " + orderKeys.map((key) => `${key} ${orderBy[key]}`).join(", ") : "";

    const query = `
      SELECT ${select.length > 0 ? select.join(", ") : "*"}
      FROM ${table}
      WHERE ${whereClauses}
      ${orderClauses};
    `;

    const res = await global.poolWrite.query(query, values);
    return res?.rows ?? [];
  } catch (e) {
    console.error(`FIND - ${table} error:`, e);
    return undefined;
  }
};

async function getData({ table, select = ['*'], where = {}, order = '' } = {}) {
  let sql = '';
  try {
      const poolRead = global.poolRead;
      const columns = Array.isArray(select) && select.length > 0 ? select.join(', ') : '*';
      sql = `SELECT ${columns} FROM ${table}`;
      const params = [];
      let idx = 1;
      const whereClauses = [];

      for (const key in where) {
        // If value is an object with type and value, handle special types
        if (
          where[key] &&
          typeof where[key] === "object" &&
          where[key].type &&
          where[key].type === "date" &&
          where[key].value
        ) {
          whereClauses.push(`${key}::date = $${idx}`);
          params.push(where[key].value);
        } else if (
          where[key] &&
          typeof where[key] === "object" &&
          where[key].type === "date" &&
          where[key].fromDate &&
          where[key].toDate
        ) {
          whereClauses.push(`${key}::date BETWEEN $${idx} AND $${idx + 1}`);
          params.push(where[key].fromDate, where[key].toDate);
          idx += 1;
        } else if (Array.isArray(where[key])) {
          whereClauses.push(`${key} = ANY($${idx})`);
          values.push(where[key]);
        } else {
          whereClauses.push(`${key} = $${idx}`);
          params.push(where[key]);
        }
        idx++;
      }
      if (whereClauses.length > 0) {
        sql += ' WHERE ' + whereClauses.join(' AND ');
      }
      if (order) {
        sql += ` ORDER BY ${order}`;
      }
      if(whereClauses.length === 0) {
        //add limit to avoid returning too many rows
        sql += ' LIMIT 10';
      }
      console.log(`[getData] Executing SQL: ${sql} with params: ${JSON.stringify(params)}`);
      const rs = await poolRead.query(sql, params);
      return rs.rows;
  } catch (err) {
      console.log(`[getData] sql error: ${sql}`);
      console.log('Error in getData:', err);
      console.log(err);
      return [];
  }
}

const updateDataAny = async ({ table, columns, values, conditions }) => {
  try {
    const setClauses = columns.map((column, i) => `${column} = $${i + 1}`).join(", ");
    let conditionValues = [];
    const conditionKeys = Object.keys(conditions);
    const modifiedConditionClauses = conditionKeys.map((key, i) => {
      const value = conditions[key];
      if (Array.isArray(value)) {
        conditionValues.push(value);
        return `${key} = ANY($${columns.length + i + 1})`;
      }
      if(value === null) {
        return `${key} IS NULL`;
      }
      if (typeof value === 'object' && value.type === 'date' && value.value) {
        conditionValues.push(value.value);
        return `${key}::date = $${columns.length + i + 1}`;
      }

      if (typeof value === 'object' && value.type === 'date' && value.fromDate && value.toDate) {
        conditionValues.push(value.fromDate, value.toDate);
        return `${key}::date BETWEEN $${columns.length + i + 1} AND $${columns.length + i + 2}`;
      }

      if (typeof value === 'object' && value.type === 'date' && value.fromDate) {
        conditionValues.push(value.fromDate);
        return `${key}::date >= $${columns.length + i + 1}`;
      }

      if (typeof value === 'object' && value.type === 'date' && value.toDate) {
        conditionValues.push(value.toDate);
        return `${key}::date <= $${columns.length + i + 1}`;
      }
      conditionValues.push(value);
      return `${key} = $${columns.length + i + 1}`;
    }).join(" AND ");
    
    const query = `
      UPDATE ${table}
      SET ${setClauses}
      WHERE ${modifiedConditionClauses}
      RETURNING *;
    `;

    console.log(`[updateDataAny] Executing SQL: ${query} with params: ${JSON.stringify([...values, ...conditionValues])}`);
    const res = await global.poolWrite.query(query, [...values, ...conditionValues]);
    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`UPDATE - ${table} error:`, e);
    return undefined;
  }
};

async function getDataV2({ table, select = ['*'], where = {}, order = '' } = {}) {
  let sql = '';
  try {
      const poolRead = global.poolRead;
      const columns = Array.isArray(select) && select.length > 0 ? select.join(', ') : '*';
      sql = `SELECT ${columns} FROM ${table}`;
      const params = [];
      let idx = 1;
      const whereClauses = [];

      for (const key in where) {
        // If value is an object with type and value, handle special types
        if (
          where[key] &&
          typeof where[key] === "object" &&
          where[key].type &&
          where[key].type === "date" &&
          where[key].value
        ) {
          whereClauses.push(`${key}::date = $${idx}`);
          params.push(where[key].value);
        } else if (
          where[key] &&
          typeof where[key] === "object" &&
          where[key].type === "date" &&
          where[key].fromDate &&
          where[key].toDate
        ) {
          whereClauses.push(`${key}::date BETWEEN $${idx} AND $${idx + 1}`);
          params.push(where[key].fromDate, where[key].toDate);
          idx += 1;
        } else if (key === "is_delete") {
          whereClauses.push(`(is_delete = 0 OR is_delete IS NULL)`);
        } else if (Array.isArray(where[key])) {
          whereClauses.push(`${key} = ANY($${idx})`);
          values.push(where[key]);
        } else {
          whereClauses.push(`${key} = $${idx}`);
          params.push(where[key]);
        }
        idx++;
      }
      if (whereClauses.length > 0) {
        sql += ' WHERE ' + whereClauses.join(' AND ');
      }
      if (order) {
        sql += ` ORDER BY ${order}`;
      }
      if(whereClauses.length === 0) {
        //add limit to avoid returning too many rows
        sql += ' LIMIT 10';
      }
      console.log(`[getDataV2] Executing SQL: ${sql} with params: ${JSON.stringify(params)}`);
      const rs = await poolRead.query(sql, params);
      return rs.rows;
  } catch (err) {
      console.log(`[getDataV2] sql error: ${sql}`);
      console.log('Error in getData:', err);
      console.log(err);
      return [];
  }
}

const updateDataAnyV2 = async ({ table, columns, values, conditions }) => {
  try {
    const setClauses = columns.map((column, i) => `${column} = $${i + 1}`).join(", ");

    const conditionValues = [];
    let paramIndex = columns.length + 1; // start after SET params

    const conditionKeys = Object.keys(conditions);
    const modifiedConditionClauses = conditionKeys.map((key) => {
      const value = conditions[key];

      if (Array.isArray(value)) {
        conditionValues.push(value);
        return `${key} = ANY($${paramIndex++})`;
      }

      if (value === null) {
        return `${key} IS NULL`;
      }

      if (typeof value === "object" && value.type === "date") {
        if (value.value) {
          conditionValues.push(value.value);
          return `${key}::date = $${paramIndex++}`;
        }
        if (value.fromDate && value.toDate) {
          conditionValues.push(value.fromDate, value.toDate);
          const clause = `${key}::date BETWEEN $${paramIndex} AND $${paramIndex + 1}`;
          paramIndex += 2;
          return clause;
        }
        if (value.fromDate) {
          conditionValues.push(value.fromDate);
          return `${key}::date >= $${paramIndex++}`;
        }
        if (value.toDate) {
          conditionValues.push(value.toDate);
          return `${key}::date <= $${paramIndex++}`;
        }
      }

      conditionValues.push(value);
      return `${key} = $${paramIndex++}`;
    }).join(" AND ");

    const query = `
      UPDATE ${table}
      SET ${setClauses}
      WHERE ${modifiedConditionClauses}
      RETURNING *;
    `;

    const params = [...values, ...conditionValues];
    console.log(`[updateDataAnyV2] Executing SQL: ${query} with params: ${JSON.stringify(params)}`);

    const res = await global.poolWrite.query(query, params);
    return res?.rows[0] ?? {};
  } catch (e) {
    console.error(`UPDATE - ${table} error:`, e);
    return undefined;
  }
};

module.exports = {
  insertData,
  generateValues,
  updateData,
  patchUpdate,
  findOne,
  find,
  getData,
  updateDataAny,
  getDataV2,
  updateDataAnyV2,
};
