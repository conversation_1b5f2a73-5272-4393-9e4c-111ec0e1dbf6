
const checkMissingAndEmpty = (mandatoryFields, input) => {
    let missingMessage = ''

    mandatoryFields.every(function(item){
        let value = input[item];
        missingMessage = item + ' is missing or empty';

        if(value === undefined || value.toString().trim() === '')
            return false;
        else
            missingMessage = '';
        return true;
    })

    return missingMessage;
}
module.exports = {
    checkMissingAndEmpty
}