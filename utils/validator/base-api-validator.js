const {validator,responseError} = require("./validate")
const {TURNOVER_TYPE, PARTNER_CODE} = require("../../const/definition")
const productUtil = require("../productService")
const sqlHelper = require("../../utils/sqlHelper")

const customMessages = {
    required: 'The :attribute is missing',
}

async function validLMSCallback(req,res,next) {
    const validationRule = {
        "los_type": ["required","string",{"in" : ['MCC']}],
        "contract_number" : "required|string",
        "status" : ["required","string",{"in" : ['ACT','TER','ECT','ANN']}]
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function validRequireContract(req,res,next) {
    const validationRule = {
        'contractNumber' : "required"
    }
    validator(req.query, validationRule, customMessages, (err, status) => {
        if (!status) {
            return res.status(400).send(responseError(err));
        }
        else {
            return next()
        }
    });
}

async function validRequireContractBody(req,res,next) {
    const validationRule = {
        'contractNumber' : "required"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            return res.status(400).send(responseError(err));
        }
        else {
            return next()
        }
    });
}

async function validResubmitDoc(req,res,next) {
    const validationRule = {
        "requestId" : "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string",
        "listDocResubmit" : "required|array",
        "listDocResubmit.*.docType" : ["string"],
        "listDocResubmit.*.docName" : ["string"],
        "listDocResubmit.*.docId" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });

}

async function validResubmitDocSme(req,res,next) {
    const validationRule = {
        // "requestId" : "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string",
        "listDocResubmit" : "required|array",
        "listDocResubmit.*.docType" : ["string"],
        "listDocResubmit.*.docName" : ["string"],
        "listDocResubmit.*.docId" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });

}

async function validSelectOffer(req,res,next) {
    const validationRule = {
        "requestId" : "required|string|valid_request_id",
        "contractNumber" : "required|string",
        "selectedOfferId" : "required|integer"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function validSelectOfferSME(req,res,next) {
    const validationRule = {
        "requestId" : "string|valid_request_id",
        "contractNumber" : "required|string",
        "selectedOfferId" : "required|integer"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function validAfterLoan(req,res,next) {
    const validationRule = {
        "contractNumber" : "required|string",
        "data" : "required|array",
        "data.*.month" : "required|integer",
        "data.*.monthData" : "required|array",
        "data.*.monthData.*.infoType" : ["required","string",{"in" : [TURNOVER_TYPE.PROFIT_AFTER_LOAN,TURNOVER_TYPE.TURNOVER_AFTER_LOAN]}],
        "data.*.monthData.*.value" : "required|numeric",
        "data.*.createdDate" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

/**
 * 
 * @param {object[]} docList - Documemt list
 * @param {string} docList[].docName - Name of document. Ex : SPIC,SPID
 * @param {string} docList[].docId - Uuid of document generate by EC.
 * @param {string} productCode - Product code in product catalog.
 * @param {string} stage - stage of product bundle
 */


async function validNotEnoughDocument(docList,productCode,stage,partnerCode=undefined) {
    let bundleInfo = (await productUtil.getBundleV3(global.config,productCode,stage)).data
    if(Array.isArray(bundleInfo)&&bundleInfo.length&&[PARTNER_CODE.VSK,PARTNER_CODE.SPL].includes(partnerCode)){
        let removeBundle = ['MCBAS_ALL_OTHER DOCS','MCBAS_HMTD_OTHER DOCS','MCBAS_ALL_ID CARD','MCBAS_ALL_CLIENT PHOTO','MCBAS_ALL_LOAN CONTRACT','MCBAS_ALL_DISBURSAL DOC',
                            'SAPO_CREDITLINE_OTHER DOCS','SAPO_CREDITLINE_ID CARD','SAPO_CREDITLINE_CLIENT PHOTO','SAPO_CREDITLINE_DISBURSAL DOC','SAPO_CREDITLINE_LOAN CONTRACT']
        bundleInfo = bundleInfo.filter(item => !removeBundle.includes(item.bundleName))
    }
    console.log({bundleInfo})
    // console.log({docList})
    let isEnoughFlag = true
    for (let i in bundleInfo) {
        const docListByBundle = bundleInfo[i].docList
        const intersection = docList.filter(item =>{
            const docNameList = docListByBundle.map(x => x.docType)
            return docNameList.includes(item.docName||item.doc_type)
        })
        if(intersection.length < bundleInfo[i].minDocs) isEnoughFlag = false
    }
    return isEnoughFlag
}

async function validNotEnoughDocumentKovCash(docList, productCode, stage) {
    const bundleInfo = (await productUtil.getBundleV3(global.config, productCode, stage)).data
    let isEnoughFlag = true;
    let msg = '';
    for (let i in bundleInfo) {
        const docListByBundle = bundleInfo[i].docList
        const intersection = docList.filter(item => {
            const docNameList = docListByBundle.map(x => x.docType)
            return docNameList.includes(item.docName)
        })

        if (intersection.length < bundleInfo[i].minDocs) {
            isEnoughFlag = false
            const docsRequire = Array.from(bundleInfo[i].docList, x => x.docType);
            msg = `missing documents in: ${JSON.stringify(docsRequire)}. Min documents required: ${bundleInfo[i].minDocs}`;
            break;
        }
    }
    return { isEnough: isEnoughFlag, msg: msg };
}

async function validateCancelContract(req, res, next) {
    try {
        const { contractNumber } = req.params;
        const {
            isKunn,
            kunnNumber,
            reason,
            createdBy
        } = req.body;
        if (!contractNumber) {
            return res.status(400).send({
                code: -1,
                msg: `Missing contractNumber`
            });
        }
        const loanContract = await sqlHelper.findOne({
            table: 'loan_contract',
            whereCondition: {
                contract_number: contractNumber
            }
        });
        if (!loanContract?.id) {
            return res.status(400).send({
                code: -1,
                msg: `Không tìm thấy hợp đồng tương ứng`
            });
        }

        if (!reason || !createdBy) {
            return res.status(400).send({
                code: -1,
                msg: `Thiếu lý do hoặc thông tin người thực hiện`
            });
        }
        const hmStatus = loanContract.status;
        const partnerCode = loanContract.partner_code;
        if (isKunn === 1) {
            //cancel kunn
            const kunn = await sqlHelper.findOne({
                table: 'kunn',
                whereCondition: {
                    contract_number: contractNumber,
                    kunn_id: kunnNumber
                }
            })
            if (!kunn?.kunn_id) {
                return res.status(400).send({
                    code: -1,
                    msg: `Không tìm thấy KUNN tương ứng`
                });
            }
            const { status } = kunn;
            let cancelableStatuses = [];
            if (partnerCode === PARTNER_CODE.MISA) {
                cancelableStatuses = global?.config?.data?.cancelableStatuses?.misaKunn;
            }
            if (partnerCode === PARTNER_CODE.FINV) {
                cancelableStatuses = global?.config?.data?.cancelableStatuses?.finvKunn;
            }
            if (!cancelableStatuses.includes(status)) {
                return res.status(400).send({
                    code: -1,
                    msg: `Trạng thái KUNN ${status} không được phép cancel`
                });
            }
            return next();
        }

        //cancel hạn mức
        let cancelableStatuses = [];
        if (partnerCode === PARTNER_CODE.MISA) {
            cancelableStatuses = global?.config?.data?.cancelableStatuses?.misaHm;
        }
        if (!cancelableStatuses.includes(hmStatus)) {
            return res.status(400).send({
                code: -1,
                msg: `Trạng thái hồ sơ ${hmStatus} không được phép cancel`
            });
        }
        return next();
    } catch (e) {
        return res.status(500).send({
            code: -1,
            msg: `Có lỗi xảy ra: ${e.message}`
        });
    }
}

module.exports = {
    validLMSCallback,
    validRequireContract,
    validRequireContractBody,
    validResubmitDoc,
    validSelectOffer,
    validSelectOfferSME,
    validAfterLoan,
    validResubmitDocSme,
    validNotEnoughDocument,
    validNotEnoughDocumentKovCash,
    validateCancelContract
}