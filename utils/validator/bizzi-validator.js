const { presignDocSchema, presignKUUNDocSchema } = require("../../KUNN/schema/bizzi-schema");
const { HTTP_STATUS } = require("../../const/variables-const");
const common = require("../common");
const _ = require("lodash");

const { BadRequestResponse, BadRequestResponseV2 } = require("../../base/response");

function validate(req, res, validateSchema, next) {
  //   common.bodyLog("BIZZI_GET_PRESIGNED", contractNumber, req.body);
  const httpMethod = req.method;
  let payload = req.body;
  if (httpMethod?.toUpperCase() === "GET") {
    payload = req.query;
  }
  const { error } = validateSchema.validate(payload, { abortEarly: false });
  if (error) {
    const errors = error.details.map((detail) => detail.message.replace(/"/g, ""));
    const responseBody = new BadRequestResponse(errors, "Invalid request body");

    // common.serviceLog("BIZZI_PRESIGNED", req.body, responseBody);
    return res.status(HTTP_STATUS.BAD_REQUEST).send(responseBody);
  }

  return next();
}

function validateGetPresignDocumentRequest(req, res, next) {
  const contractNumber = req.params.contract_number;

  let requestBody = _.cloneDeep(req.body);
  requestBody.contract_number = contractNumber;

  common.bodyLog("BIZZI_GET_PRESIGNED", contractNumber, req.body);
  const { error } = presignDocSchema.validate(requestBody, { abortEarly: false });
  if (error) {
    const errors = error.details.map((detail) => detail.message.replace(/"/g, ""));
    const responseBody = new BadRequestResponse(errors, "Invalid request body");

    common.serviceLog("BIZZI_PRESIGNED", req.body, responseBody);
    return res.status(HTTP_STATUS.BAD_REQUEST).send(responseBody);
  }

  return next();
}

function validateGetKUUNPresignDocumentRequest(req, res, next) {
  let { contract_number, debt_contract_number } = req.params;

  let requestBody = _.cloneDeep(req.body);

  requestBody.contract_number = contract_number;
  requestBody.debt_contract_number = debt_contract_number;

  common.bodyLog("BIZZI_GET_KUUNP_PRESIGNED", contract_number, req.body);
  const { error } = presignKUUNDocSchema.validate(requestBody, { abortEarly: false });
  if (error) {
    const errors = error.details.map((detail) => detail.message.replace(/"/g, ""));
    const responseBody = new BadRequestResponse(errors, "Invalid request body");

    common.serviceLog("BIZZI_KUUNP_PRESIGNED", req.body, responseBody);
    return res.status(HTTP_STATUS.BAD_REQUEST).send(responseBody);
  }

  return next();
}

module.exports = {
  validate,
  validateGetPresignDocumentRequest,
  validateGetKUUNPresignDocumentRequest,
};
