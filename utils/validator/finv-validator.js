const { validator, responseError } = require("./validate");
const {
  getLoanContract,
  getLoanContractByIdNumber,
  getProcessingFinvLoans
} = require("../../repositories/loan-contract-repo");
const nfcDataRepo = require("../../repositories/nfc-data-repo");
const resConst = require("../../const/response-const");
const moment = require("moment-timezone");
moment().tz("Asia/Ho_Chi_Minh").format();
const Joi = require("joi");
const {
  ResponseBaseFinv,
  CustomError,
  FinVietBadRequestResponse,
  FinVietServerErrorResponse, handleResponse,
} = require("../../entity/response-base-finv");
const { HTTP_STATUS, TERMINATION_TYPE } = require("../../const/variables-const");
const {
  ERROR_CODE,
  DOC_TYPE,
  PARTNER_CODE,
} = require("../../const/definition");
const { STATUS } = require("../../const/caseStatus");
const { getProductByCodeApi } = require("../../apis/product-api");
const loanContractDocumentRepo = require("../../repositories/document");
const { getFileKeyFromUrl, pickKeysFromObject } = require("../../utils/helper");
const { BadRequestResponse } = require("../../base/response");
const sqlHelper = require("../../utils/sqlHelper");
const { get } = require("lodash");

const customMessages = {
  required: "The: attribute is missing",
};

function validateStatusAf2(status) {
  if (!status) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `contractNumber không tồn tại `,
        path: ["contractNumber"],
        type: "any.exist",
        context: { value: status },
      },
    ]);
  } else if (status != STATUS.PASSED_REVIEW_A1) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `Trạng thái hợp đồng ${status} không hợp lệ `,
        path: ["contractNumber"],
        type: "any.exist",
        context: { value: status },
      },
    ]);
  }
  return status;
}

function validateStatusAf3(status) {
  if (!status) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `contractNumber không tồn tại `,
        path: ["contractNumber"],
        type: "any.exist",
        context: { value: status },
      },
    ]);
  } else if (status != STATUS.SIGNED) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `Trạng thái hợp đồng ${status} không hợp lệ `,
        path: ["contractNumber"],
        type: "any.exist",
        context: { value: status },
      },
    ]);
  }
  return status;
}

async function FinvFullloanValidate(req, res, next) {
  req.body.partnerCode = PARTNER_CODE.FINV;
  const loan = await getLoanContract(req.body.contractNumber);
  const isAuthorizationSign = loan.is_authorization_sign;
  req.body.isAuthorizationSign = isAuthorizationSign;
  req.body.status = loan?.status;
  req.body.productCode = loan?.product_code;
  const schema = Joi.object({
    requestId: Joi.string().required(),
    status: Joi.string().external(validateStatusAf2),
    // productCode: Joi.string().required(),
    contractNumber: Joi.string().required(),
    registrationNumber: Joi.string().required(),
    // smeTaxId: Joi.string().required(),
    partnerCode: Joi.string().required(),
    companyEmail:  Joi.string().email().allow(null).optional(),
    companyWebsite: Joi.string().allow(null).optional(),
    loanPurpose: Joi.string().required(),
    capitalNeed: Joi.string().required(),
    loanAmount: Joi.string().required(),
    ownerEquity: Joi.string().required(),
    tenor: Joi.string().required(),
    otherCapital: Joi.string().required(),
    monthlyInterestPaymentDate: Joi.number().required(),

    companyName: Joi.string().required(),
    billDay: Joi.number().required(),
    workplaceAddress: Joi.object({
      provinceCode: Joi.string().required(),
      wardCode: Joi.string().required(),
      detailAddress: Joi.string().required(),
    }).optional(),

    registrationAddress : Joi.object({
      provinceCode: Joi.string().required(),
      wardCode: Joi.string().required(),
      detailAddress: Joi.string().required(),
    }).required(),

    repaymentMethod: Joi.string().required(),
    repaymentSources: Joi.string().required(),

    businessType: Joi.string().required(),
    businessIndustry: Joi.string().required(),
    conditionalBusinessIndustry: Joi.number().required(),
    companyPhoneNumber: Joi.string().required(),

    customerInfo: Joi.object({
      fullname: Joi.string().required(),
      marriedStatus: Joi.string().allow(null).optional(),
      identityCard: Joi.string().required(),
      dateOfBirth: Joi.string().required(),
      issueDate: Joi.string().required(),
      issuePlace: Joi.string().required(),
      phoneNumber: Joi.string().required(),
      email: Joi.string().allow(null).optional(),
      perAddress: Joi.object({
        provinceCode: Joi.string().required(),
        wardCode: Joi.string().required(),
        detailAddress: Joi.string().required(),
        typeOfOwnership: Joi.string().allow(null).optional(),
      }).required(),

      curAddress: Joi.object({
        provinceCode: Joi.string().required(),
        wardCode: Joi.string().required(),
        detailAddress: Joi.string().required(),
        typeOfOwnership: Joi.string().allow(null).optional(),
      }).allow(null).optional(),
    }).required(),

    refPerson1: Joi.object({
      relationType: Joi.string().required(),
      fullname: Joi.string().required(),
      phoneNumber: Joi.string().required(),
      identityCard: Joi.string().required(),
    }).required(),

    refPerson2: Joi.object({
      relationType: Joi.string().required(),
      fullname: Joi.string().required(),
      phoneNumber: Joi.string().required(),
      identityCard: Joi.string().required(),
    }).required(),

    businessData: Joi.object({
      timeDuration: Joi.number().required(),
      turnoverAmount: Joi.number().required(),
      businessCost: Joi.number().required(),
      avgTurnover: Joi.number().required(),
      turnover3M: Joi.number().required(),
      turnover6M: Joi.number().required(),
      totalTurnOverNextYear: Joi.number().required(),
      totalCostOverNextYear: Joi.number().required(),
    }).required(),
    partnerInfo: Joi.when("customerInfo.marriedStatus", {
      is: "M",
      then: Joi.object({
        fullname: Joi.string().required(),
        identityCard: Joi.string().required(),
        phoneNumber: Joi.string().required(),
        perAddress: Joi.object({
          provinceCode: Joi.string().required(),
          wardCode: Joi.string().required(),
          detailAddress: Joi.string().required(),
          typeOfOwnership: Joi.string().allow(null).optional(),
        }).required(),
        docs: Joi.array()
        .items(
          Joi.object({
            docId: Joi.string().required(),
            docType: Joi.string().required(),
          })
        ),
      }),
    }),
    isAuthorizationSign: Joi.allow(null).optional(),
    // docs: Joi.array()
    //   .items(
    //     Joi.object({
    //       docId: Joi.string().required(),
    //       docType: Joi.string().required(),
    //     })
    //   )
    //   .external(checkDocsAf2),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });

  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

async function checkRequestIdExistsAf1(requestId) {
  const nfcData = await nfcDataRepo.findByRequestId(requestId);
  if (nfcData) {
    //   throw new Error(`requestId "${requestId}" đã tồn tại`);
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `requestId ${requestId} đã tồn tại`,
        path: ["requestId"],
        type: "any.exist",
        context: { value: requestId },
      },
    ]);
  }
  return requestId;
}

async function checkRequestIdExistsAf2(requestId) {
  const nfcData = await nfcDataRepo.findByRequestId(requestId);
  if (!nfcData) {
    //   throw new Error(`requestId "${requestId}" đã tồn tại`);
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `requestId ${requestId} không khớp af1`,
        path: ["requestId"],
        type: "any.not_match",
        context: { value: requestId },
      },
    ]);
  }
  return requestId;
}

const checkDocsAf1 = async (docs) => {
  docs = docs || [];
  const requiredDocTypes = [DOC_TYPE.ID_CARD.FRONT, DOC_TYPE.ID_CARD.BACK, DOC_TYPE.SELFIE];
  const errDocs = docs.reduce((o, doc) => {
    o.delete(doc.docType);
    return o;
  }, new Set(requiredDocTypes));
  if (errDocs.size) {
    const errMessage = `docType ${[...errDocs].join(", ")} ${errDocs.size > 1 ? 'are': 'is'} missing`;
    throw new Joi.ValidationError("Validation Error", [
      {
        message: errMessage,
        path: ["docs.docType"],
        type: "missing",
        context: { value: docs },
      },
    ]);
  }
  return checkUploadedDocs(docs);
};

const checkUploadedDocs = async (docs) => {
  // docs: { docId, docType }[]
  const docIds = docs.map((e) => e.docId);
  const uploadedDocs = await loanContractDocumentRepo.findPresignUploadedByDocIds(
    docIds || []
  );
  if (uploadedDocs.length < docIds.length) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `Có docType chưa được upload`,
        path: ["docs.docId"],
        type: "missing",
        context: { value: docs },
      },
    ]);
  }
  const checkSet = new Set();
  for (const doc of docs) {
    if (checkSet.has(doc.docType)) {
      throw new Joi.ValidationError("Validation Error", [
        {
          message: `duplicate fileUrl ${doc.docType}`,
          path: ["docs.docType"],
          type: "missing",
          context: { value: docs },
        },
      ]);
    }
    checkSet.add(doc.docType);
  }
  return docs;
};

const checkDocsAf2 = (docs) => {
  docs = docs || [];
  const requiredDocTypes = {
    [DOC_TYPE.ID_CARD.FRONT]: 1,
    [DOC_TYPE.ID_CARD.BACK]: 1,
    [DOC_TYPE.SELFIE]: 1,
    [DOC_TYPE.SPIDOS]: 2,
    [DOC_TYPE.SBIZ]: 2,
    [DOC_TYPE.STAX]: 5,
    [DOC_TYPE.SPCB]: 5,
    [DOC_TYPE.SVATSTA]: 5,
    [DOC_TYPE.PTC]: 1,
    [DOC_TYPE.SLOH]: 10,
    [DOC_TYPE.SOLH]: 10,
    [DOC_TYPE.SBLSTA]: 3,
    [DOC_TYPE.SCPS]: 3,
    [DOC_TYPE.RIOD]: 5,
    [DOC_TYPE.VIDEO]: 1,
  };

  const actualCounts = docs.reduce((acc, doc) => {
    if (doc.docType in requiredDocTypes) {
      acc[doc.docType] = (acc[doc.docType] || 0) + 1;
    }
    return acc;
  }, {});

  const missingOrInsufficient = Object.entries(requiredDocTypes)
    .filter(([type, requiredCount]) => {
      const actual = actualCounts[type] || 0;
      return actual < requiredCount;
    })
    .map(([type, requiredCount]) => {
      const actual = actualCounts[type] || 0;
      return `${type} (required: ${requiredCount}, found: ${actual})`;
    });

  if (missingOrInsufficient.length > 0) {
    const errMessage = `docType${missingOrInsufficient.length > 1 ? 's' : ''} ${missingOrInsufficient.join(', ')} ${missingOrInsufficient.length > 1 ? 'are' : 'is'} missing`;
    
    throw new Joi.ValidationError("Validation Error", [
      {
        message: errMessage,
        path: ["docs.docType"],
        type: "missing",
        context: { value: docs },
      },
    ]);
  }
  return checkUploadedDocs(docs);
};

const checkDocsAf3 = (docs) => {
  docs = docs || [];
  const video = docs.find((e) => e.docType == DOC_TYPE.VIDEO);
  if (!video) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `Thiếu docType ${DOC_TYPE.VIDEO}`,
        path: ["docs.docType"],
        type: "missing",
        context: { value: docs },
      },
    ]);
  }
  return checkUploadedDocs;
};

async function checkInProgress(identityCard) {
  const loan = await getLoanContractByIdNumber(identityCard);
  if (
    loan?.status &&
    ![
      STATUS.ACTIVATED,
      STATUS.CANCELLED,
      STATUS.TERMINATED,
      STATUS.REFUSED,
      STATUS.NOT_ELIGIBLE,
      STATUS.NOT_ELIGIBLE2,
    ].includes(loan.status)
  ) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `identityCard ${identityCard} có yêu cầu vay đang xử lý`,
        path: ["customerInfo.identityCard"],
        type: "INVALID",
        context: { value: identityCard },
      },
    ]);
  }
  return identityCard;
}

const getProductInfo = async (productCode) => {
  let errors;
  const productInfo = await getProductByCodeApi(productCode);
  if (!productInfo) {
    errors = [
      new CustomError(resConst.ERROR_CODE.INVALID, "productCode invalid"),
    ]
  }
  return { productInfo, errors };
};

async function FinvAf1Validate(req, res, next) {
  const schema = Joi.object({
    requestId: Joi.string()
      .pattern(/^FINV.*/i)
      .required()
      .external(checkRequestIdExistsAf1),
    companyName: Joi.string().required(),
    customerInfo: Joi.object({
      fullname: Joi.string().required(),
      identityCard: Joi.string()
        // Chuỗi chỉ chứa số, dài 9 hoặc 12 ký tự
        .required()
        .external(checkInProgress),
      phoneNumber: Joi.string()
        .pattern(/^\d{10,11}$/)
        .required()
    }).required().unknown(true),
    productCode: Joi.string().required(),
    nfcData: Joi.object({
      dg1: Joi.string().required(),
      dg2: Joi.string().required(),
      dg3: Joi.string().allow(null).allow(""),
      dg4: Joi.string().allow(null).allow(""),
      dg5: Joi.string().allow(null).allow(""),
      dg6: Joi.string().allow(null).allow(""),
      dg7: Joi.string().allow(null).allow(""),
      dg8: Joi.string().allow(null).allow(""),
      dg9: Joi.string().allow(null).allow(""),
      dg10: Joi.string().allow(null).allow(""),
      dg11: Joi.string().allow(null).allow(""),
      dg12: Joi.string().allow(null).allow(""),
      dg13: Joi.string().required(),
      dg14: Joi.string().allow(null).allow(""),
      dg15: Joi.string().allow(null).allow(""),
      dg16: Joi.string().allow(null).allow(""),
      sod: Joi.string().required(),
      challenge: Joi.string().allow(null).allow(""),
      aAResult: Joi.string().allow(null).allow(""),
      eACCAResult: Joi.string().allow(null).allow(""),
    }).required(),
    // docs: Joi.array()
    //   .items(
    //     Joi.object({
    //       docId: Joi.string().required(),
    //       docType: Joi.string().required(),
    //     })
    //   )
    //   .length(3)
    //   .external(checkDocsAf1),
  })
    .messages({
      'any.required': '{#label} is missing'
    })
    .unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => {
            if (e.message.includes('missing')) {
              return new CustomError(resConst.ERROR_CODE.MISSING, e.message)
            } else {
              return new CustomError(resConst.ERROR_CODE.INVALID, e.message)
            }
          }
        ),
      };
    });

  if (!isValid) {
    const response = new FinVietBadRequestResponse(
      "bad request",
      {},
      errors
    );
    return handleResponse(res, response);
  }
  // try {
  //   const { productInfo, errors: getProductErrors } = await getProductInfo(req.body.productCode);
  //   if (getProductErrors) {
  //     const response = new FinVietBadRequestResponse(
  //       "productCode is invalid",
  //       {},
  //       getProductErrors
  //     );
  //     return handleResponse(res, response);
  //   }
  //   req.body.partnerCode = PARTNER_CODE.FINV;
  //   req.body.productInfo = productInfo;
  // } catch (e) {
  //   const response = new FinVietServerErrorResponse();
  //   return handleResponse(res, response);
  // }

  const endStatusList = [
    STATUS.ACTIVATED,
    STATUS.CANCELLED,
    STATUS.TERMINATED,
    STATUS.REFUSED,
    STATUS.NOT_ELIGIBLE,
    STATUS.EXPIRED
  ];
  const [
    loansByRequestId,
    processingLoansByIdNumber
  ] = await Promise.all([
    sqlHelper.find({
      table: 'loan_contract',
      whereCondition: {
        request_id: req.body.requestId
      }
    }),
    getProcessingFinvLoans({
      idNumber: req.body.customerInfo.identityCard,
      status: endStatusList
    })
  ])
  if (loansByRequestId.length > 0) {
    let message = `requestId ${req.body.requestId} already exists`;
    const response = new FinVietBadRequestResponse(
      resConst.ERROR_MSG.BAD_REQUEST,
      {},
      [new CustomError('requestId', message)]
    );
    return handleResponse(res, response);
  }
  if (processingLoansByIdNumber.length > 0) {
    let message = `identityCard ${req.body.customerInfo.identityCard} has in-progress loan request`;
    const response = new FinVietBadRequestResponse(
      resConst.ERROR_MSG.BAD_REQUEST,
      {},
      [new CustomError('customerInfo.identityCard', message)]
    );
    return handleResponse(res, response);
  }

  next();
}

async function finvA3Validate(req, res, next) {
  // req.body.partnerCode = PARTNER_CODE.FINV;
  const loan = await getLoanContract(req.body.contractNumber);
  req.body.status = loan?.status;
  req.body.productCode = loan?.product_code;
  const schema = Joi.object({
    requestId: Joi.string().required(),
    contractNumber: Joi.string().required(),
    agreeInsurance: Joi.boolean().required(),
    status: Joi.string().external(validateStatusAf3),
    partnerCode: Joi.string().required(),
    docs: Joi.array()
      .items(
        Joi.object({
          docId: Joi.string().required(),
          docType: Joi.string().required(),
        })
      )
      .external(checkDocsAf3),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });

  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

function validateStatusResubmitAf3(status) {
  if (!status) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `Status không tồn tại `,
        path: ["status"],
        type: "any.exist",
        context: { value: status },
      },
    ]);
  } else if (status != STATUS.RESUBMIT_A3) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `Trạng thái hợp đồng ${status} không hợp lệ `,
        path: ["contractNumber"],
        type: "any.exist",
        context: { value: status },
      },
    ]);
  }
  return status;
}

async function finvA3ResubmitValidate(req, res, next) {
  req.body.partnerCode = PARTNER_CODE.FINV;
  const loan = await getLoanContract(req.body.contractNumber);
  req.body.status = loan?.status;
  req.body.productCode = loan?.product_code;
  const schema = Joi.object({
    requestId: Joi.string().required(),
    contractNumber: Joi.string().required(),
    agreeInsurance: Joi.boolean().required(),
    status: Joi.string().external(validateStatusResubmitAf3),
    partnerCode: Joi.string().required(),
    docs: Joi.array()
      .items(
        Joi.object({
          docId: Joi.string().required(),
          docType: Joi.string().required(),
        })
      )
      .external(checkDocsAf3),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });


  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

async function finvFetWithdrawalValidate(req, res, next) {
  const schema = Joi.object({
    requestId: Joi.string().required(),
    partnerCode: Joi.string().required(),
    debtContractNumber: Joi.string().required(),
    terminationType: Joi.string().valid(...Object.values(TERMINATION_TYPE)).required(),
    terminationPartially: Joi.optional(),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });


  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

async function finvGetInstallmentValidate(req, res, next) {
  const schema = Joi.object({
    partnerCode: Joi.string().required(),
    contractNumber: Joi.string().required(),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });


  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

async function finvGetKunnStatusValidate(req, res, next) {
  const schema = Joi.object({
    requestId: Joi.string().required(),
    partnerCode: Joi.string().required(),
    ecSignature: Joi.string().optional(),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });


  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

async function cancelCreditLimitSigningInProgress(req, res, next) {
  req.body.partnerCode = PARTNER_CODE.FINV;
  const schema = Joi.object({
    days: Joi.number().required(),
    comment: Joi.string().optional(),
  }).unknown(true);

  const { isValid, errors } = await schema
    .validateAsync(req.body)
    .then(() => ({ isValid: true, errors: [] }))
    .catch((error) => {
      console.log(`error: `, JSON.stringify(error?.details));
      return {
        isValid: false,
        errors: error.details?.map(
          (e) => new CustomError(resConst.ERROR_CODE.INVALID, e.message)
        ),
      };
    });


  //   return { isValid, errors };
  const response = new ResponseBaseFinv(
    200,
    ERROR_CODE.SUCCESS,
    "sucess",
    {},
    []
  );
  //   const { isValid, errors } = await validateAf1(req.body);
  if (!isValid) {
    response.statusCode = HTTP_STATUS.BAD_REQUEST;
    response.body.code = ERROR_CODE.INVALID_REQUEST;
    response.body.message = "bad request";
    response.body.errors = errors;
    return res.status(response.statusCode).json(response.body);
  } else {
    next();
  }
}

const validateProductInfo = async (productInfo) => {
  if (!productInfo?.prdctCode) {
    throw new Joi.ValidationError("Validation Error", [
      {
        message: `productCode invalid `,
        path: ["productCode"],
        type: "any.exist",
        context: { value: "" },
      },
    ]);
  }
  return productInfo;
};

function validate(req, res, validateSchema, next) {
  //   common.bodyLog("BIZZI_GET_PRESIGNED", contractNumber, req.body);
  const httpMethod = req.method;
  let payload = req.body;
  if (httpMethod?.toUpperCase() === "GET") {
    payload = req.query;
  }
  const { error } = validateSchema.validate(payload, { abortEarly: false });
  if (error) {
    const errors = error.details.map((detail) => detail.message.replace(/"/g, ""));
    const responseBody = new BadRequestResponse(errors, "Invalid request body");

    // common.serviceLog("BIZZI_PRESIGNED", req.body, responseBody);
    return res.status(HTTP_STATUS.BAD_REQUEST).send(responseBody);
  }

  return next();
}

const addCustomMessagesToSchema = (schema, keys = ['params', 'query', 'body']) => {
  const result = {};
  
  keys.forEach(key => {
    if (schema[key]) {
      result[key] = schema[key].messages({
        'any.required': '{#label} is missing'
      });
    }
  });
  
  return result;
};

const validateRequest = (req, res, schema, next) => {
  const schemaWithCustomMessages = addCustomMessagesToSchema(schema);
  
  const validSchema = pickKeysFromObject(schemaWithCustomMessages, ['params', 'query', 'body']);
  const object = pickKeysFromObject(req, Object.keys(validSchema));const { value, error } = Joi.compile(validSchema)
    .prefs({ errors: { label: 'key' }, abortEarly: false })
    .validate(object);

  if (error) {
    const errors = error.details.map((detail) => {
      detail.message = detail.message.replace(/"/g, "");
      if (detail.message.includes('missing')) {
        return new CustomError(resConst.ERROR_CODE.MISSING, detail.message);
      } else {
        return new CustomError(resConst.ERROR_CODE.INVALID, detail.message);
      }
    })
    const response = new FinVietBadRequestResponse(
      "Invalid request body",
      {},
      errors
    );
    return handleResponse(res, response);
  }
  Object.assign(req, value);
  return next();
};

const validateGetContractStatus = (req, res, next) => {
  const schema = {
    body: Joi.object({
      requestId: Joi.string().required(),
      partnerCode: Joi.string().required()
    }).unknown(true)
  };
  return validateRequest(req, res, schema, next);
};

const validateGetPresignedFiles = (req, res, next) => {
  const schema = {
    query: Joi.object({
      requestId: Joi.string().required(),
      docType: Joi.string().required(),
    }),
    params: Joi.object({
      contract_number: Joi.string().required(),
    }),
  };
  return validateRequest(req, res, schema, next);
};

const validateGenFileResult = (req, res, next) => {
  const schema = {
    body: Joi.object({
      contractNumber: Joi.string().required(),
      fileName: Joi.string().required(),
      docType: Joi.string().required(),
      fileKey: Joi.string().required(),
      fileUrl: Joi.string().required(),
    }),
  };
  return validateRequest(req, res, schema, next);
};

module.exports = {
  FinvAf1Validate,
  FinvFullloanValidate,
  finvA3Validate,
  validate,
  validateGetContractStatus,
  validateGetPresignedFiles,
  validateGenFileResult,
  finvA3ResubmitValidate,
  cancelCreditLimitSigningInProgress,
  finvFetWithdrawalValidate,
  finvGetInstallmentValidate,
  finvGetKunnStatusValidate
};
