const {validator,responseError} = require("./validate")
const common = require("../common");
const { authenticateOauth2V03WithNoToken } = require("../aaaService");

const customMessages = {
    required: 'The :attribute is missing',
}

async function KOVBasicValidate(req, res, next) {

    const validationRule = {
        // "timeUsingAsset": "required|integer|min:3",
        "turnover": "required|array|valid_kov_turnover",
        "transaction": "required|array|valid_kov_transaction",
        "fundingFromEc": "required|integer"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function KOVFullLoanValidate(req,res,next) {
    
    const validationRule = {
        "contractNumber" : "required|string|valid_eligible_fullLoan",
        // "channel" : ["required","string",{"in" : ['PLAT-KOV']}],
        "temDistrict" : "required|string|valid_district",
        "temWard" : "required|string|valid_ward",
        "temAddress" : "required|string|min:1|max:100",
        "yearsOfStay" : "required|integer",
        "permanentProvince" : "required|string|valid_province",
        "permanentDistrict" : "required|string|valid_district",
        "permanentWard" : "required|string|valid_ward",
        "permanentAddress" : "required|string|min:1|max:100",
        "houseType" : "string|valid_house_type",
        "loanAmount" : "required|integer|min:0|max:************",
        "tenor" : "required|integer",
        "typeTrading": ["required","string",{"in" : ['ON','OF','BO']}],
        "accountTrading" : "array",
        "accountTrading.*.platformName" : "string",
        "accountTrading.*.storeName" : "string",
        "merchantContractNumber" : "required|string",
        "representatiMerchantContract" : "required|string",
        "startDateOnMerchant" : "required|string|format_date_ymd",
        "endDateOnMerchant" : "required|string|format_date_ymd",
        "sectorIndustry" : "required|string",
        "businessType" : "required|string",
        "capitalNeed" : "required|integer",
        "selfFinancing" : "required|integer",
        "otherCapital" : "required|integer",
        "fundingFromEc" : "required|integer",
        "repaymentSources" : "required|string",
        "branchAddress" : "required|array",
        "branchAddress.*.name" : "required|string",
        "branchAddress.*.province" : "required|string|valid_province",
        "branchAddress.*.district" : "required|string|valid_district",
        "branchAddress.*.ward" : "required|string|valid_ward",
        "branchAddress.*.detail" : "required|string",
        "fixedAsset": "required|string",
        "planAsset": "required|string",
        "timeUsingAsset": "required|integer|min:3",
        "numberBusinessAsset": "required|integer",
        "latestTaxPayment" : "required|integer",
        "loanPurpose" : "required|string|valid_loan_purpose",
        "relation1" : "required|string|valid_relation",
        "relation1Name" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "relation1PhoneNumber" : "required|string|size:10|valid_phone_number",
        "relation2" : "required|string|valid_relation",
        "relation2Name" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "relation2PhoneNumber" : "required|string|size:10|valid_phone_number",
        "ownerOfAccount": "required|string",
        "bankCode" : "required|string|valid_bank_code",
        "bankBranchCode" : "required|string",
        "bankAccount" : "required|string|max:50",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "externalDocument" : "required|array",
        "externalDocument.*.docName" : "required|string",
        "externalDocument.*.docId" : "required|string"

    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function KOVReviewHmValidate(req, res,next ){
    const validationRule = {
        "contractNumber": "required|string",
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "identityCardId" : "required|string|valid_id_card",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "externalDocument" : "required|array",
        "externalDocument.*.docName" : "required|string",
        "externalDocument.*.docId" : "required|string",
        "branchAddress" : "array",
        "branchAddress.*.name" : "string",
        "branchAddress.*.province" : "string|valid_province",
        "branchAddress.*.district" : "string|valid_district",
        "branchAddress.*.ward" : "string|valid_ward",
        "branchAddress.*.detail" : "string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

module.exports = {
    KOVBasicValidate,
    KOVFullLoanValidate,
    KOVReviewHmValidate
}