const common = require("../common")

function isValidResubmit(waitingResubmitList,resubmitList) {
    try {
        if(waitingResubmitList.length > resubmitList.length ) {
            return false
        }
        const resubmitObject = {}
        resubmitList.forEach(item => {
            resubmitObject[item.doc_type] = item.doc_id
        })
    
        waitingResubmitList.forEach(item => {
            if(!resubmitObject.hasOwnProperty(item.doc_type)) {
                return false
            }
        })
        return true
    }
    catch(err) {
        common.log(`valid resubmit error : ${err.message}`)
        return false
    }
}

module.exports = {
    isValidResubmit
}