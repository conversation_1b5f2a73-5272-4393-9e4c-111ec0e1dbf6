const {validator,responseError} = require("./validate")
const common = require("../common")
const { getLoanContract } = require("../../repositories/loan-contract-repo")
const { authenticateOauth2V03WithNoToken } = require("../aaaService")
const { BadRequestResponse } = require("../../base/response");
const { MISA_ERROR_CODE } = require("../../const/response-const");

const customMessages = {
    required: 'The: attribute is missing',
}

async function MisaGetPresignedValidate(req,res,next) {
    common.bodyLog('MISA_PRESIGNED',req.body.request_id,req.body)
    const validationRule = {
        "requestId": "string|min:15|max:40|valid_request_id",
        "partnerCode": ["string",{"in" : ['MIS']}],
        "fileName" : "required|string|min:1|max:100|valid_file_format"
    }
    validator(req.query, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('MISA_PRESIGNED',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function MisaBasicValidate(req, res,next ){
    const validationRule = {
        // "requestId": "required|string|min:15|max:40|valid_request_id",
        "partnerCode" : ["string",{"in" : ['MIS']}],
        "channel" : ["string",{"in" : ['MC']}],
        "contractType" : ["required","string", {"in":['CREDITLINE','CASHLOAN']}],
        "enterpriseType" : "required|string|valid_enterprise_type",
        "smeName": "required|string|min:1|max:100",
        "smeTaxId": "required|string|min:10|max:50",
        "registrationNumber": "string|min:10|max:50",
        "firstRegistrationDate": "required|string|format_date_ymd",
        "smePhoneNumber" : "required|string|size:10|valid_phone_number",
        "sectorIndustry" : "required|string",
        "sectorIndustry1" : "string",
        "sectorIndustry2" : "string",
        "sectorIndustry3" : "string",
        "smeRepresentationName" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "smeRepresentationDob": "required|string|format_date_ymd|valid_age",
        "smeRepresentationGender": ["required","string", {"in":['F','M']} ],
        "smeRepresentationPosition": "required|string|valid_profession",
        "smeRepresentationId": "required|string|valid_id_card",
        "smeRepresentationissueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "smeRepresentationissuePlace": "required|string|valid_issue_place",
        "smeRepresentationOtherId": "string|valid_id_card",
        "smeRepresentationPhoneNumber" : "required|string|size:10|valid_phone_number",
        "authorizedName": [{"required_if" : ['isAuthorizationSign','Y']},"string","valid_cust_name_1","valid_cust_name_2",{"max":80}],
        "authorizedDob": [{"required_if" : ['isAuthorizationSign','Y']},"string","format_date_ymd","valid_age"],
        "authorizedGender": [{"required_if" : ['isAuthorizationSign','Y']},"string", {"in":['F','M']} ],
        "authorizedPosition": [{"required_if" : ['isAuthorizationSign','Y']},"string","valid_profession"],
        "authorizedId": [{"required_if" : ['isAuthorizationSign','Y']},"string","valid_id_card"],
        "authorizedIssueDate": [{"required_if" : ['isAuthorizationSign','Y']},"string","format_date_ymd","valid_issue_date_1","valid_issue_date_2"],
        "authorizedIssuePlace": [{"required_if" : ['isAuthorizationSign','Y']},"string","valid_issue_place"],
        "authorizedOtherId": [{"required_if" : ['isAuthorizationSign','Y']},"string","valid_id_card"],
        "authorizedPhoneNumber" : [{"required_if" : ['isAuthorizationSign','Y']},"string",{"size":10},"valid_phone_number"],
        "timeDuration" : "required|integer",
        "businessDuration": "required|integer|valid_sme_business_duration",
        "nameOfApp": ["required","string", {"in":['SME.Net','Meinvoice']}],
        "socalInsuranceStaffNumber": "required|string",
        "profitAfterTax": "numeric",
        "totalCapitalLastYear" : "required|integer|min:0|max:************",
        "totalTurnoverNextYear" : "required|integer|min:0|max:************",
        "averageDepreciation" : "required|numeric|min:0|max:************",
        "turnover" : "required|array",
        "turnover.*.month" : "required|integer",
        "turnover.*.amount" : "required|numeric|min:0|max:************",
        "loanAmount" : "required|integer|min:0|max:************",
        "tenor" : "required|integer",
        "tenorKunn" : "integer",
        "isAuthorizationSign" : ["required","string", {"in":['Y','N']}],
        "contractCreatorName":"required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "contractCreatorPosition":"string|valid_profession",
        "contractCreatorPhoneNumber":"required|string|size:10|valid_phone_number",
        "contractCreatorEmail":"string|email|min:1|max:100",
        "contractCreatorProvince":"string|valid_province",
        "contractCreatorDistrict":"string|valid_district",
        "contractCreatorWard":"string|valid_ward",
        "contractCreatorAddress":"string|min:1|max:300",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function MisaFullloanValidate(req, res,next ){
    const isAuthorizationSign = (await getLoanContract(req.body.contractNumber))?.is_authorization_sign
    req.body.isAuthorizationSign = isAuthorizationSign
    const validationRule = {
        // "requestId": "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string|valid_eligible_fullLoan",
        "disbursementMethod" : ["string",{"in" : ['Transfer']}],
        "smeEmail": "string|email|min:1|max:250",
        "branchTaxId": "string|min:1|max:50",
        "smeRepresentationProvinceCur" : "required|string|valid_province",
        "smeRepresentationDistrictCur" : "required|string|valid_district",
        "smeRepresentationWardCur" : "required|string|valid_ward",
        "smeRepresentationAddressCur" : "required|string|min:1|max:100",
        "smeRepresentationProvincePer" : "required|string|valid_province",
        "smeRepresentationDistrictPer" : "required|string|valid_district",
        "smeRepresentationWardPer" : "required|string|valid_ward",
        "smeRepresentationAddressPer" : "required|string|min:1|max:100",
        "wareHouseProvince" : "string|valid_province",
        "wareHouseDistrict" : "string|valid_district",
        "wareHouseWard" : "string|valid_ward",
        "wareHouseAddress" : "string|min:1|max:100",
        "smeRepresentationEmail": "string|email|min:1|max:250",
        "accountantStatus" : ["string",{"in" : ['Y','N']}],
        "accountantName" : "string|valid_cust_name_1|valid_cust_name_2|max:80",
        "accountantGender": ["string", {"in":['F','M']} ],
        "accountantPhoneNumber" : "string|size:10|valid_phone_number",
        "accountantEmail": "email|min:1|max:250",
        "authorizedProvinceCur" : "string|valid_province",
        "authorizedDistrictCur" : "string|valid_district",
        "authorizedWardCur" : "string|valid_ward",
        "authorizedAddressCur" : "string|min:1|max:100",
        "smeHeadquartersProvince" : "required|string|valid_province",
        "smeHeadquartersDistrict" : "required|string|valid_district",
        "smeHeadquartersWard" : "required|string|valid_ward",
        "smeHeadquartersAddress" : "required|string|min:1|max:100",
        "authorizedProvincePer" : "string|valid_province",
        "authorizedDistrictPer" : "string|valid_district",
        "authorizedWardPer" : "string|valid_ward",
        "authorizedAddressPer" : "string|min:1|max:100",
        "authorizationLetterNumber" : [{"required_if" : ['isAuthorizationSign','Y']},"string"],
        "authorizationLetterSingedDate" : [{"required_if" : ['isAuthorizationSign','Y']},"string","format_date_ymd"],
        "loanPurpose" : "required|string|valid_loan_purpose",
        "ownerOfAccount": "required|string",
        "bankCode" : "required|string|valid_bank_code",
        "bankBranchCode" : "required|string",
        "bankAccount" : "required|string|max:50",
        "ownerOfAccount2": "string",
        "bankCode2" : "string|valid_bank_code",
        "bankBranchCode2" : "string",
        "bankAccount2" : "string|max:50",
        "profit" : "array",
        "profit.*.month" : "integer",
        "profit.*.amount" : "integer|min:0|max:************",
        "branchAddress" : "array",
        "branchAddress.*.name" : "string",
        "branchAddress.*.province" : "string|valid_province",
        "branchAddress.*.district" : "string|valid_district",
        "branchAddress.*.ward" : "string|valid_ward",
        "branchAddress.*.detail" : "string",
        // "branchAddress.*.statusOwned" : "required|string",
        // "branchAddress.*.numOfStaff" : "required|integer",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : ["required","string",{"in" : ['SAF','SBIZ','SCR','STCRC','SDALR','SPALR','SDACA','SMRCO','SNIDLR','SPIDLR','SPPTLR','SNIDAR',
        'SPIDAR','SPPTAR','SNIDCO','SPIDCO','SPPTCO','SSL','SPC','SBC','SWC','SFSTD','SIFS','STRP','SEC','SPLR','SPAR','SPCHQ','SPBW']}],
        "listDocCollecting.*.docId" : "required|string",
        "smeEmploymentType1Code" : "required|string|valid_sme_employment_type1_code",
        "smeEmploymentType4Code" : "required|string|valid_sme_employment_type4_code",
        // "externalDocument" : "required|array",
        // "externalDocument.*.docName" : "required|string",
        // "externalDocument.*.docId" : "required|string"
    }
   
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function MisaResubmitDoc(req,res,next) {
    common.bodyLog('MISA_RESUBMIT_DOC',req.body.request_id,req.body)
    const validationRule = {
        "requestId" : "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string",
        "listDocResubmit" : "required|array",
        "listDocResubmit.*.docType" : ["required","string",{"in" : ['SAF','SBIZ','SCR','STCRC','SDALR','SPALR','SDACA','SMRCO','SNIDLR','SPIDLR','SPPTLR','SNIDAR',
        'SPIDAR','SPPTAR','SNIDCO','SPIDCO','SPPTCO','SSL','SPC','SBC','SWC','SFSTD','SIFS','STRP','SEC','SPLR','SPAR','SPCHQ','SPBW']}],
        "listDocResubmit.*.docId" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('MISA_RESUBMIT_DOC',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}
//valid_bank_code
async function MisaCreateKunnValidate(req, res,next ){
    const validationRule = {
        // "requestId": "string|min:15|max:40|valid_request_id",
        "partnerCode": ["string",{"in" : ['MIS']}],
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "withdrawPurpose": "required|string|valid_loan_purpose",
        "bankCode" : "string|valid_bank_code",
        "bankCode2" : "string|valid_bank_code",
        "bankCode3" : "string|valid_bank_code",
        "bankCode4" : "string|valid_bank_code",
        "bankBranchCode" : "string",
        "bankAccount" : "string|max:50",
        "bankBranchCode2" : "string",
        "bankAccount2" : "string|max:50",
        "bankBranchCode3" : "string",
        "bankAccount3" : "string|max:50",
        "bankBranchCode4" : "string",
        "bankAccount4" : "string|max:50",
        "acronymSmeName" : "required|string|max:49",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function MisaReceiveInfoValidate(req, res,next ){
    const poolWrite = global.poolWrite
    const sql = `select contract_number from loan_contract lc where partner_code = 'MIS' and registration_number = $1 and sme_name = $2`;
    const sqlRs = await poolWrite.query(sql,[req.body.smeRegistrationNumber,req.body.smeName]);
    if(sqlRs.rowCount!=1) req.body.contractNumber = undefined;
        else req.body.contractNumber = sqlRs.rows[0]?.contract_number;
    const appName = (await getLoanContract(req.body.contractNumber))?.name_of_app;
    req.body.appName = appName
    const validationRule = {
        "smeName" : "required|string",
        "smeRegistrationNumber" : "required|string",
        "turnover" : "required|array",
        "turnover.*.month_of_info" : "required|string",
        "turnover.*.value_of_month" : "required|numeric",
        // "profit" : [{"required_if" : ['appName','SME.Net']},"array"],
        "profit" : ["array"],
        "profit.*.month_of_info" : "string",
        "profit.*.value_of_month" : "numeric",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

const docsSupplementationValidation = async (req, res,next ) => {
    const validationRule = {
        "partnerCode": ["string",{"in" : ['MIS']}],
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "kunnNumber" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

const addSignedFileValidation = async (req, res,next ) => {
    const validationRule = {
        "kunnNumber" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function misaCreateKunnValidateV2(req, res,next ){
    console.log(`[MISA][KUNN] validate create kunn payload :${JSON.stringify(req.body || {})}`);
    const validationRule = {
        // "requestId": "string|min:15|max:40|valid_request_id",
        "requestId": 'string',
        "companyName" : "required|string",
        "taxId": "required|string",
        "contractNumber" : "required|string",
        "misaLoanCode" : "required|string",
        "addressOnLicense" : "required|string",
        // "address.provinceCode" : "required|string",
        // "address.districtCode" : "required|string",
        // "address.wardCode" : "required|string",
        "address.newProvinceCode" : "required|string",
        "address.newWardCode" : "required|string",
        "address.detailAddress" : "required|string",
        "representations" : "required|array",
        "representations.*.idNumber" : "required|string|min:9|max:12",
        "representations.*.otherIdNumber" : "string|min:9|max:12",
        "representations.*.fullName" : "required|string",
        "representations.*.issueDate" : "required|valid_issue_date_2",
        "monthlyIntPaymentDate" : "string|min_str:1|max:2",
        "totalWithdrawAmt" : "numeric|min_numeric:1000",
        "loanApplicationFile.fileUrl":"required|string",
        "loanApplicationFile.fileType":"required|string",
        //validate disbursement info
        
        "disbursementInfos":"required|array",
        "disbursementInfos.*.accountName":"required|string",
        "disbursementInfos.*.bankAccount":"required|string",
        "disbursementInfos.*.bankCode":"required|string",
        "disbursementInfos.*.amount":"required|numeric",
        "disbursementInfos.*.transferContent":"required|string",
        "disbursementInfos.*.collectingDocs":"required|array",
        "disbursementInfos.*.scanDocs":"required|array",
        //validate documents
        "disbursementInfos.*.collectingDocs.*.docCode":"required|string",
        "disbursementInfos.*.collectingDocs.*.docNumber":"required|string",
        "disbursementInfos.*.collectingDocs.*.totalPaymentAmount":"required|numeric|min_numeric:1000",
        "disbursementInfos.*.collectingDocs.*.file.fileUrl":"required|string",
        "disbursementInfos.*.collectingDocs.*.file.fileType":"required|string",

        //validate scandocs 
        "disbursementInfos.*.scanDocs.*.fileUrl":"required|string",
        "disbursementInfos.*.scanDocs.*.fileType":"required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = new BadRequestResponse(mappingError(err),"Request is invalid")
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

const misaSignKunnValidate = (req, res, next)=>{
    const validationRule = {
        // "requestId": "string|min:15|max:40|valid_request_id",
        "requestId": 'string',
        "debtContractNumber" : "required|string",
        "fileUrl" : "required|string",
        "fileType" : "required|string",
        "fileName" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = new BadRequestResponse(mappingError(err),"Request is invalid")
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

const misaCancelKunnValidate = (req, res, next)=>{
    const validationRule = {
        // "requestId": "string|min:15|max:40|valid_request_id",
        "requestId": 'string',
        "debtContractNumber" : "required|string",
        "reason" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = new BadRequestResponse(mappingError(err),"Request is invalid")
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

const mappingError = (err)=>{
    return Object.keys(err.errors).map(item => {
        const msg = err.errors[item][0]
        const errorMessage = {
            code: MISA_ERROR_CODE.E400.code,
            location : item,
            message: MISA_ERROR_CODE.E400.message,
            details: msg
        }
        return errorMessage
    })
}

const misaGetBankInfoValidate = (req, res, next)=>{
    const validationRule = {
        // "requestId": "string|min:15|max:40|valid_request_id",
        "accountNumber": "required|string",
        "bankCode" : "required|string"    }
    validator(req.query, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = new BadRequestResponse(mappingError(err),"Request is invalid")
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

const misaGetFinancialReportValidate = (req, res, next) => {
  const validationRule = {
    // "requestId": "string|min:15|max:40|valid_request_id",
    "contractNumber": "required|string",
    "revenues":"required|array",
        "revenues.*.year":"required|string",
        "revenues.*.netRevenue":"required|string",
        "revenues.*.totalAssets":"required|string",
        "revenues.*.financialReportDocs":"required|array",
        "revenues.*.financialReportType":"required|string",
        "revenues.*.financialReportDocs.*.fileName":"required|string",
        "revenues.*.financialReportDocs.*.fileUrl":"required|string",
        "revenues.*.financialReportDocs.*.docType":"required|string",

  };
  validator(req.body, validationRule, customMessages, (err, status) => {
    if (!status) {
      const responseBody = new BadRequestResponse(
        mappingError(err),
        "Request is invalid"
      );
      return res.status(400).send(responseBody);
    } else {
      return next();
    }
  });
};

const misaGetDocumentReportValidate = (req, res, next) => {
    const validationRule = {
        // "requestId": "string|min:15|max:40|valid_request_id",
        "contractNumber": "required|string",
        "period":"required|string",
        "financialReportType":"required|string",
        "files":"required|array",
        "files.*.fileName":"required|string",
        "files.*.fileType":"required|string",
        "files.*.fileUrl":"required|string",
        "files.*.docType":"required|string",
    };
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = new BadRequestResponse(
                mappingError(err),
                "Request is invalid"
            );
            return res.status(400).send(responseBody);
        } else {
            return next();
        }
    });
};

const misaUploadFinancialReportInternalValidate = (req, res, next) => {
    const validationRule = {
      // "requestId": "string|min:15|max:40|valid_request_id",
      "contractNumber": "required|string",
      "revenues":"required|array",
          "revenues.*.year":"required|string",
          "revenues.*.netRevenue":"required|string",
          "revenues.*.totalAssets":"required|string",
          "revenues.*.financialReportDocs":"required|array",
          "revenues.*.financialReportType":"required|string",
          "revenues.*.financialReportDocs.*.fileName":"required|string",
          "revenues.*.financialReportDocs.*.fileData":"required|string",
          "revenues.*.financialReportDocs.*.docType":"required|string",
  
    };
    validator(req.body, validationRule, customMessages, (err, status) => {
      if (!status) {
        const responseBody = new BadRequestResponse(
          mappingError(err),
          "Request is invalid"
        );
        return res.status(400).send(responseBody);
      } else {
        return next();
      }
    });
  };
  

module.exports = {
    MisaBasicValidate,
    MisaFullloanValidate,
    MisaGetPresignedValidate,
    MisaResubmitDoc,
    MisaCreateKunnValidate,
    MisaReceiveInfoValidate,
    docsSupplementationValidation,
    addSignedFileValidation,
    misaCreateKunnValidateV2,
    misaSignKunnValidate,
    misaCancelKunnValidate,
    misaGetBankInfoValidate,
    misaGetFinancialReportValidate,
    misaGetDocumentReportValidate,
    misaUploadFinancialReportInternalValidate
}