const {validator,responseError} = require("./validate")
const common = require("../common");
const { authenticateOauth2V03WithNoToken } = require("../aaaService");

const customMessages = {
    required: 'The :attribute is missing',
}

async function splBasicValidate(req,res,next) {
    
    const validationRule = {
        "partnerCode" : ["required","string",{"in" : ['SPL']}],
        "customerName" : "required|string",
        "gender" : ["required","string", {"in":['F','M']} ],
        "dateOfBirth" : "required|string|valid_age",
        "identityCardId" : "required|string|valid_id_card",
        "issueDate" : "required|string|valid_issue_date_1|valid_issue_date_2",
        "issuePlace" : "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "otherIssueDate" : "string|valid_issue_date_1|valid_issue_date_2",
        "otherIssuePlace" : "string|valid_issue_place",
        "productCode" : ["required","string",{"in" : ['SAPO_CREDIT_LINE','HMTD_SAPO']}], 
        "timeUsingAsset": "required|integer|min:3",
        "turnover": "required|array|valid_kov_turnover",
        "transaction" : "required|array|valid_kov_transaction",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

module.exports = {
    splBasicValidate
}