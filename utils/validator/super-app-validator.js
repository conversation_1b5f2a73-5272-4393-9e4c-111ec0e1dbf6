const {validator,responseError} = require("./validate")
const common = require("../common")
const { getLoanContract } = require("../../repositories/loan-contract-repo")
const { authenticateOauth2V03WithNoToken } = require("../aaaService")
const { LIST_SUPER_APP_DOCS, PRODUCT_CODE, SMA_PAYMENT_METHOD } = require("../../const/definition")

const customMessages = {
    required: 'The: attribute is missing',
}

async function SmaGetPresignedValidate(req,res,next) {
    common.bodyLog('SMA_PRESIGNED',req.body.request_id,req.body)
    const validationRule = {
        "requestId": "string|min:15|max:40|valid_request_id",
        "partnerCode": ["string",{"in" : ['SMA']}],
        "fileName" : "required|string|min:1|max:100|valid_file_format"
    }
    validator(req.query, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function SubmitLoanLimitValidate(req, res,next ){
    const validationRule = {
        "productCode" : ["required","string", {"in":PRODUCT_CODE.SUPER_APP.LIMIT} ],
        "identityCardId" : "required|string|valid_id_card",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "gender" : ["required","string", {"in":['F','M']} ],
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "email": "string|email|min:1|max:250",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "isWithdrawRequest": ["required","string", {"in":["T","F"]} ],
        "carInfo": "required",
        "carInfo.carBrand": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string","valid_car_brand"],
        "carInfo.carModel": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string"],
        "carInfo.carKmTraveled": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"numeric"],
        "carInfo.carFuel": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string","valid_car_fuel"],
        "carInfo.carManufactureOrigin": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string","valid_car_origin"],
        "carInfo.carManufactureYear": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"integer"],
        "taxId": [{"required_if" : ['productCode','SMA_MCTAX_HMTD']},"string"],
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function SubmitIncreaseLoanLimitValidate(req, res,next ){
    const validationRule = {
        "limitContractNumber": "required|string|valid_limit_contract_number",
        "productCode" : "required|string|valid_product_code_sma",
        "identityCardId" : "required|string|valid_id_card",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "gender" : ["required","string", {"in":['F','M']} ],
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "email": "string|email|min:1|max:250",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        // "isWithdrawRequest": ["required","string", {"in":["T","F"]} ],
        "carInfo": "required",
        "carInfo.carBrand": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string","valid_car_brand"],
        "carInfo.carModel": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string"],
        "carInfo.carKmTraveled": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"numeric"],
        "carInfo.carFuel": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string","valid_car_fuel"],
        "carInfo.carManufactureOrigin": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"string","valid_car_origin"],
        "carInfo.carManufactureYear": [{"required_if" : ['productCode','SMA_MC_CAR_HMTD']},"integer"],
        "taxId": [{"required_if" : ['productCode','SMA_MCTAX_HMTD']},"string"]
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function FullloanValidate(req, res,next ){
    const validationRule = {
        "loanAmount" : "required|integer|min:0|max:************",
        "productCode": "required|string|valid_product_code_sma",
        "ownerOfAccount": "required|string",
        "bankCode" : "required|string|valid_bank_code",
        "bankAccount" : "required|string|max:50",
        "temProvince" : "required|string|valid_province",
        "temDistrict" : "required|string|valid_district",
        "temWard" : "required|string|valid_ward",
        "temAddress" : "required|string|min:1|max:100",
        "permanentProvince" : "required|string|valid_province",
        "permanentDistrict" : "required|string|valid_district",
        "permanentWard" : "required|string|valid_ward",
        "permanentAddress" : "required|string|min:1|max:100",
        "marriedStatus" : "required|string|valid_married_status",
        "marriageMateId" : "string|valid_id_card",
        "relation1" : "required|string|valid_relation",
        "relation1Name" : "required|string|max:80",
        "relation1PhoneNumber" : "required|string|size:10|valid_phone_number",
        "relation2" : "required|string|valid_relation",
        "relation2Name" : "required|string|max:80",
        "relation2PhoneNumber" : "required|string|size:10|valid_phone_number",
        "branchAddress" : "required|array",
        "branchAddress.*.name" : "string",
        "branchAddress.*.province" : "required|string|valid_province",
        "branchAddress.*.district" : "required|string|valid_district",
        "branchAddress.*.ward" : "required|string|valid_ward",
        "branchAddress.*.detail" : "required|string",
        "branchAddress.*.statusOwned" : "string",
        "branchAddress.*.numOfStaff" : "integer",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "taxId" : "string",
        "carInfo": [{"required_if" : ['assetsType','CAR']}],
        "carInfo.carBrand": [{"required_if" : ['assetsType','CAR']},"string","valid_car_brand"],
        "carInfo.carModel": [{"required_if" : ['assetsType','CAR']},"string"],
        "carInfo.carKmTraveled": [{"required_if" : ['assetsType','CAR']},"numeric"],
        "carInfo.carFuel": [{"required_if" : ['assetsType','CAR']},"string","valid_car_fuel"],
        "carInfo.carManufactureOrigin": [{"required_if" : ['assetsType','CAR']},"string","valid_car_origin"],
        "carInfo.carManufactureYear": [{"required_if" : ['assetsType','CAR']},"integer"],
        "identityCardId" : "required|string|valid_id_card",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "gender" : ["required","string", {"in":['F','M']} ],
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "email": "string|email|min:1|max:250",
        "turnover.*.month": "integer",
        "turnover.*.amount": "numeric|min:0|max:************",
        "expenses.*.month": "integer",
        "expenses.*.amount": "numeric|min:0|max:************",
        // "propertyInfo": [{"required_if" : ['assetsType','PROPERTY']}],
        // "propertyInfo.area": [{"required_if" : ['assetsType','PROPERTY']},"numeric","min:0","max:************"],
        // "propertyInfo.floorNumber": [{"required_if" : ['assetsType','PROPERTY']},"integer"],
        // "propertyInfo.province" : [{"required_if" : ['assetsType','PROPERTY']},"string","valid_province"],
        // "propertyInfo.district" : [{"required_if" : ['assetsType','PROPERTY']},"string","valid_district"],
        // "propertyInfo.ward" : [{"required_if" : ['assetsType','PROPERTY']},"string","valid_ward"],
        // "propertyInfo.detail" : [{"required_if" : ['assetsType','PROPERTY']},"string"],
        "assetsType": "string|valid_assets_type",
        // "propertyType": [{"required_if" : ['assetsType','PROPERTY']},"string","valid_property_type"],
        "numberOfDependents" : "required|integer",
        "monthlyExpenses" : "required|numeric",
        "capitalNeed" : "numeric",
        "selfFinancing" : "numeric",
        "otherCapital" : "numeric",
        "fundingFromEc" : "numeric",
        "repaymentSources" : "string",
    }
    req.body.relation1Name = req.body?.relation1Name?.trim() || req.body.relation1Name;
    req.body.relation2Name = req.body?.relation2Name?.trim() || req.body.relation2Name;
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function ResubmitDoc(req,res,next) {
    common.bodyLog('SUPERAPP_RESUBMIT_DOC',req.body.request_id,req.body)
    const validationRule = {
        "requestId" : "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string",
        "listDocResubmit" : "required|array",
        "listDocResubmit.*.docType" : ["required","string",{"in" : LIST_SUPER_APP_DOCS}],
        "listDocResubmit.*.docId" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VSK_RESUBMIT_DOC',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function CreateKunnValidate(req, res,next ){
    const validationRule = {
        "paymentMethod":  ["required","string", {"in":SMA_PAYMENT_METHOD} ],
        "kunnCode" : "required|string",
        "contractNumber" : "required|string",
        "withdrawAmount" : "required|numeric",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "billDay" : "required|integer",
        "beneficiaryName" : "required|string",
        "bankCode" : "required|string|valid_bank_code",
        "bankAccount" : "required|string|max:50",
        "ir" : "required|numeric",
        "tenor" : "required|integer",
        "capitalNeed" : "numeric",
        "selfFinancing" : "numeric",
        "otherCapital" : "numeric",
        "fundingFromEc" : "numeric",
        "repaymentSources" : "string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function createWithDrawRequestCheck(req, res,next ){
    const validationRule = {
        "paymentMethod":  ["required","string", {"in":SMA_PAYMENT_METHOD} ],
        "kunnCode" : "required|string",
        "contractNumber" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

module.exports = {
    SmaGetPresignedValidate,
    SubmitIncreaseLoanLimitValidate,
    FullloanValidate,
    ResubmitDoc,
    SubmitLoanLimitValidate,
    CreateKunnValidate,
    createWithDrawRequestCheck
}