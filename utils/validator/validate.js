const Validator = require('validatorjs');
const moment = require('moment');
const {getValueCode_v3, getValueCodePucpCore} = require("../masterdataService")
const {getProductInfoV2, getListProductCodeByChannel} = require("../../utils/productService")
const loanContractRepo = require("../../repositories/loan-contract-repo")
const documentRepo = require("../../repositories/document")
const {validRequestId} = require("../../repositories/logging-repo")
const {STATUS} = require("../../const/caseStatus")
const utils = require("../helper");
const { RES_STT_CODE, RESPONSE_CODE } = require('../../const/response-const');
const { CHANNEL } = require('../../const/definition');

const validator = (body, rules, customMessages, callback) => {
    const validation = new Validator(body, rules, customMessages);
    validation.fails(() => callback(validation.errors, false));
    validation.passes(() => callback(null,true))
};

function responseError(err,extend = "") {
    const responseBody = {
        code: 'INVALID_REQUEST',
        message: 'Request is invalid',
        errors: Object.keys(err.errors).map(item => {
            const msg = err.errors[item][0]
            const errorMessage = {
                code: msg.search('missing') > 0 ? 'MISSING' : "INVALID",
                location : `${extend}${extend?'.':''}${item}`,
                message: msg,
            }
            return errorMessage
        })
    }
    return responseBody
}

function validatePhoneNumber(fieldName, value) {
    const PATTERN = /^0(\d{9,10})$/;
    if (!PATTERN.test(value)) {
        return {
            isValid: false,
            field: fieldName,
            message: `Must be in pattern ${PATTERN}`
        }
    }
    return {
        isValid: true,
        field: fieldName,
        message: `${fieldName} valid`
    };
}

Validator.setAttributeFormatter(function(attribute) {
    return attribute.replace(/_/g, '_');
  });

Validator.registerAsync('valid_province',async function(provinceCode,attribute,req,passes) {
    const rs = await getValueCode_v3(provinceCode,'PROVINCE')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_district',async function(districtCode,attribute,req,passes) {
    const rs = await getValueCode_v3(districtCode,'DISTRICT')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_ward',async function(wardCode,attribute,req,passes) {
    const rs = await getValueCode_v3(wardCode,'WARD')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_issue_place',async function(issuePlaceCode,attribute,req,passes) {
    const rs = await getValueCode_v3(issuePlaceCode,'ISSUE_PLACE_VN')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_loan_purpose',async function(loanPurpose,attribute,req,passes) {
    const rs = await getValueCode_v3(loanPurpose,'LOAN_PURPOSE')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_married_status',async function(marriedStatus,attribute,req,passes) {
    const rs = await getValueCode_v3(marriedStatus,'MARRIED_STATUS')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_relation',async function(relationCode,attribute,req,passes) {
    const rs = await getValueCode_v3(relationCode,'FONCTION_INTERLOCUTEUR')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_bank_code',async function(bankCode,attribute,req,passes) {
    const rs = await getValueCode_v3(bankCode,'BANK')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_house_type',async function(houseType,attribute,req,passes) {
    const rs = await getValueCode_v3(houseType,'HABITAT')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.register('format_date_dmy',function(date,requrirement,attribute) {
    const format = 'DD-MM-YYYY';
    const check = moment(date, format, true).isValid();
    return check
},"The :attribute must be format dd-mm-yyyy.")

Validator.register('format_date_ymd',function(date,requrirement,attribute) {
    const format = 'YYYY-MM-DD';
    const check = moment(date, format, true).isValid();
    return check
},"The :attribute must be format yyyy-mm-dd.")

Validator.register('valid_age',function(dob,requrirement,attribute) {
    dob = utils.convertDMY2YMD(dob)
    let age = utils.caculateAge(dob)
    if(age < 20 || age > 60) return false
    return true
},"Birthday of client that must be older than 20 and younger than 60")

Validator.register('valid_employment_type',function(employmentType,requrirement,attribute) {
    if(!['E','FE','RP','SE'].includes(employmentType)) return false
    return true
},"The :attribute does not exists.")

Validator.registerAsync('valid_loan_amount',async function(loanAmount,attribute,req,passes) {
    const productCode = 'VIETTELPAY_PRO'
    const productData = await getProductInfoV2(productCode)
    let minAmount = parseInt(productData.productVar[0].minAmt)
    let maxAmount = parseInt(productData.productVar[0].maxAmt)
    if(loanAmount < minAmount || loanAmount > maxAmount) passes(false)
    passes()
},"The :attribute must in product loan amount range.")

Validator.registerAsync('valid_loan_tenor',async function(loanTenor,attribute,req,passes) {
    const productCode = 'VIETTELPAY_PRO'
    const productData = await getProductInfoV2(productCode)
    const minTenor = parseInt(productData.productVar[0].tenorFrm)
    const maxTenor = parseInt(productData.productVar[0].tenorTo)
    if(loanTenor < minTenor || loanTenor > maxTenor) passes(false)
    passes()
},"The :attribute must in product loan tenor range.")

Validator.registerAsync('valid_eligible_fullLoan',async function(contractNumber,attribute,req,passes) {
    const contractStatus = await loanContractRepo.getContractStaus(contractNumber)
    if(contractStatus != STATUS.ELIGIBLE) passes(false)
    passes()
},"The :attribute is not ELIGIBLE.")

Validator.registerAsync('valid_doc_id',async function(docId,attribute,req,passes) {
    const validDoc = await documentRepo.validUsedDocId(docId)
    if(!validDoc) passes(false)
    passes()
},"The :attribute is INVALID.")

Validator.register('valid_file_format',function(fileName,requrirement,attribute) {
    const fileFormat = fileName.split(".").pop()
    // console.log('fileFormat.toLowerCase()',fileFormat.toLowerCase())
    if(!['pdf','xlsx','csv'.includes(fileFormat.toLowerCase())]) return false
    return true
},"The :attribute must be pdf format.")

Validator.register('valid_phone_number',function(phoneNumber,requrirement,attribute) {
    const regex = /0[35789](\d{8})\b/g;
    if(phoneNumber.match(regex))
        return true;
    else
        return false;
},"The :attribute is Invalid.")

Validator.register('valid_id_card',function(idCardNumber,requrirement,attribute) {
    const regrex1 = /^\d{9}$/
	const regrex2 = /^\d{12}$/
	if(regrex1.test(idCardNumber) || regrex2.test(idCardNumber)) {
		return true
	}
    return false
},"The :attribute must be 9-12 digits.")

Validator.register('valid_cust_name_1',function(customerName,requrirement,attribute) {
    //console.log(customerName)
    const VIETNAMESE_CHARACTERS = "a-zA-ZÁÀẢÃẠÂẤẦẨẪẬĂẮẰẲẴẶÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴĐáàảãạâấầẩẫậăắằẳẵặéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵđ";
    const VIETNAMESE_CHARACTERS_PATTERN = "^[" + VIETNAMESE_CHARACTERS + "]" + "+([\\s']" + "+[" + VIETNAMESE_CHARACTERS + "]+)*$";
    let patternFieldName = new RegExp(VIETNAMESE_CHARACTERS_PATTERN,"ui");
    customerName = customerName.normalize('NFC');
    if(!patternFieldName.test(customerName)){
        return false;
    }
    return true;
},"The :attribute must contain only Vietnamese characters.")

Validator.register('valid_cust_name_2',function(customerName,requrirement,attribute) {
    let splitName = customerName.split(" ")
    //console.log(splitName)
	if(splitName.includes("")) {
		return false;
	}
    return true;
},"The :attribute must not contain multiple consecutive.")


Validator.register('valid_issue_date_1',function(issueDate,requrirement,attribute) {
    issueDate = utils.convertDMY2YMD(issueDate)
    issueDate = new Date(issueDate)
    const issueYear = issueDate.getFullYear()

    const currentDate = new Date()

	if(issueYear < 1900 || (currentDate - issueDate) < 0) {
		return false
	}
    return true
},"The :attribute must between 1990 and now")

Validator.register('valid_issue_date_2',function(issueDate,requrirement,attribute) {
    issueDate = utils.convertDMY2YMD(issueDate)
    issueDate = new Date(issueDate)
	
    let numYear = utils.caculateAge(issueDate)

	if(numYear > 20) {
		return false
	}
    return true
},"The :attribute must be in 20 years before current date")

Validator.registerAsync('valid_request_id',async function(requestId,attribute,req,passes) {
    const isValidRequestId = await validRequestId(requestId)
    if(!isValidRequestId) passes(false)
    passes()
},"The :attribute is Duplicated.")

Validator.registerAsync('valid_merchant_account',async function(merchantAccount,attribute,req,passes) {
    const isValidRequestId = await loanContractRepo.isValidMerchantAccountVTP(merchantAccount)
    if(!isValidRequestId) passes(false)
    passes()
},"The :attribute have contract in process.")

Validator.registerAsync('valid_lms_callback',async function(contractNumber,attribute,req,passes) {
    const isValidRequestId = await loanContractRepo.isValidLmsCallback(contractNumber)
    if(!isValidRequestId) passes(false)
    passes()
},"The :attribute is Invalid.")

Validator.register('valid_kov_turnover',function(turnover,requrirement,attribute) {
    for(let i in turnover) {
        let monthTurnover = turnover[i]
        if (monthTurnover.month >= 1 && monthTurnover.month <= 3) {
            if(monthTurnover.amount < ******** ) {
                return false
            }
        }
    }
    return true
},"Doanh thu bình quân 3 tháng liên tục gần nhất trên KIOT VIỆT >= 10 triệu và không có tháng nào <= 10 triệu.")

Validator.register('valid_kov_transaction',function(transaction,requrirement,attribute) {
    for(let i in transaction){
        let monthTransaction = transaction[i]
        if (monthTransaction.month >= 1 && monthTransaction.month <= 3) {
            if(monthTransaction.amount < 10 ) {
                return false
            }
        }
    }
    return true
},"Số lượng đơn hàng/giao dịch bán hàng thành công BQ 03 tháng gần nhất ghi nhận trên KIOT Việt >= 10  đơn hàng và không có tháng nào < 10 đơn hàng.")

Validator.registerAsync('valid_enterprise_type',async function(enterpriseType,attribute,req,passes) {
    const rs = await getValueCode_v3(enterpriseType,'ENTERPRISE_TYPE')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_sme_business_duration',async function(businessDuration,attribute,req,passes) {
    if(parseInt(businessDuration)<parseInt(24)){
        passes(false)
    }
    passes()
},"Thời gian hoạt động của SME phải ≥ 24 tháng.")

Validator.registerAsync('valid_sme_turnover',function(turnover,attribute,req,passes) {
    const turnOverRule = 0.0;
    let maxMonth = 1;
    let amount;
    turnover?.forEach(element => {
        if(parseInt(element?.month) >= maxMonth){
            maxMonth = element?.month;
            amount = parseFloat(element?.amount);
        } 
    });
    if(amount <= turnOverRule){
        passes(false)
    }
    passes()
},"Doanh thu trên BCKQKD trong Quý/Tháng gần nhất trên MISA SME.NET phải ≥ 100tr VND.")

Validator.registerAsync('valid_total_cp_vpl',function(turnover,attribute,req,passes) {
    const totalCpRule = parseInt(100000);
    const totalAmountRule = parseInt(500000);
    let amount = 0;
    turnover?.forEach(element => {
        if(element?.TOTAL_CP<totalCpRule){
            passes(false)
        }
        amount += element?.TOTAL_CP
    });
    if(amount < totalAmountRule){
        passes(false)
    }
    passes()
},"Thu nhập bình quân 6 tháng từ ViettelPay Pro > 500.000 đồng và mỗi tháng > 100.000 đồng.")

Validator.registerAsync('valid_profession',async function(enterpriseType,attribute,req,passes) {
    const rs = await getValueCode_v3(enterpriseType,'PROFESSION')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_sme_employment_type1_code',async function(smeEmploymentType1Code,attribute,req,passes) {
    const rs = await getValueCode_v3(smeEmploymentType1Code,'SME_EMPLOYMENT_TYPE')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")

Validator.registerAsync('valid_sme_employment_type4_code',async function(smeEmploymentType4Code,attribute,req,passes) {
    const rs = await getValueCode_v3(smeEmploymentType4Code,'SME_EMPLOYMENT_TYPE_4')
    if(!rs) passes(false)
    else passes()
},"The :attribute does not exists.")
Validator.registerAsync('valid_car_brand',async function(value,attribute,req,passes) {
    const rs = await getValueCodePucpCore("branch")
    if(Array.isArray(rs) && rs.length > 0) passes()
    else passes(false) 
},"The :attribute does not exists.")

Validator.registerAsync('valid_car_fuel',async function(value,attribute,req,passes) {
    const rs = await getValueCodePucpCore("fuel")
    if(Array.isArray(rs) && rs.length > 0) passes()
    else passes(false) 
},"The :attribute does not exists.")

Validator.registerAsync('valid_car_origin',async function(value,attribute,req,passes) {
    const rs = await getValueCodePucpCore("origin")
    if(Array.isArray(rs) && rs.length > 0) passes()
    else passes(false) 
},"The :attribute does not exists.")

Validator.registerAsync('valid_limit_contract_number',async function(limitContractNumber,attribute,req,passes) {
    const sql = 'select * from loan_contract lc where contract_number = $1'
    const rs = await global.poolRead.query(sql,[limitContractNumber])
    if(rs.rowCount > 0){
        passes()
    }
    passes(false)
},"limit contract number invalid.")

Validator.registerAsync('valid_product_code_sma',async function(value,attribute,req,passes) {
    const rs = await getListProductCodeByChannel("SMCA")
    if(rs){
        let listProductCode = []
        rs.map(x => {
            listProductCode.push(x?.prdctCode)
        })
        if(!listProductCode.includes(value))
            passes(false)
        else
        passes()
    }
},"The: attribute does not exists.")

Validator.registerAsync('valid_assets_type',async function(value,attribute,req,passes) {
    const rs = await getValueCode_v3(value,'ASSETS_TYPE')
    if(!rs) passes(false)
    else passes()
},"The: attribute does not exists.")

Validator.registerAsync('valid_property_type',async function(value,attribute,req,passes) {
    const rs = await getValueCode_v3(value,'PROPERTY_TYPE')
    if(!rs) passes(false)
    else passes()
},"The: attribute does not exists.")


Validator.registerAsync('min_numeric',async function(value,attribute,req,passes) {
    if(Number(value) < Number(attribute)) passes(false,`Value length must >= ${Number(attribute)}`)
    else passes()
},`Value invalid`)


Validator.registerAsync('min_str',async function(value,attribute,req,passes) {
    console.log(`min : value :${value}, attribute :${attribute}`)
    if(value?.length < Number(attribute)) passes(false,`Value length must >= ${Number(attribute)}`)
    else passes()
},`'value invalid`)

Validator.registerAsync('number',async function(value,attribute,req,passes) {
    if (!isNaN(value) && !isNaN(parseFloat(value))) {
        passes()
    } else {
        passes(false)
    }
},"The :attribute must be a number.")

module.exports = {
    validator,
    responseError,
    validatePhoneNumber
}
