const {validator,responseError} = require("./validate")
const common = require("../common")

const customMessages = {
    required: 'The :attribute is missing',
}

async function VPLGetPresignedValidate(req,res,next) {
    common.bodyLog('VPL_PRESIGNED',req.body.request_id,req.body)
    const validationRule = {
        "request_id": "required|string|min:15|max:40|valid_request_id",
        "partner_code": ["required","string",{"in" : ['VPL']}],
        "file_name" : "required|string|min:1|max:100|valid_file_format"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VPL_PRESIGNED',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function VPLEligibleValidate(req, res,next ){
    common.bodyLog('VPL_ELIGIBLE',req.body.request_id,req.body)
    const validationRule = {
        "request_id": "required|string|min:15|max:40|valid_request_id",
        "partner_code": ["required","string",{"in" : ['VPL']}],
        "gender" : ["required","string", {"in":['F','M']} ],
        "customer_name": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "date_of_birth": "required|string|format_date_dmy|valid_age",
        "identity_card_id" : "required|string|valid_id_card",
        "issue_date": "required|string|format_date_dmy|valid_issue_date_1|valid_issue_date_2",
        "issue_place": "required|string|valid_issue_place",
        "phone_number" : "required|string|size:10|valid_phone_number",
        "email": "string|email|min:1|max:250",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VPL_ELIGIBLE',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function VPLFullloanValidate(req, res,next ){
    common.bodyLog('VPL_FULLLOAN',req.body.request_id,req.body)
    const validationRule = {
        "request_id": "required|string|min:15|max:40|valid_request_id",
        "channel" : ["required","string",{"in" : ['MC']}],
        "contract_number" : "required|string|valid_eligible_fullLoan",
        "identity_card_id_2" : "string|valid_id_card",
        "phone_number_2" : "string|size:10|valid_phone_number",
        "employment_type" : "required|string|valid_employment_type",
        "loan_amount" : "required|integer|valid_loan_amount",
        "loan_tenor" : "required|integer|valid_loan_tenor",
        "tem_province" : "required|string|valid_province",
        "tem_district" : "required|string|valid_district",
        "tem_ward" : "required|string|valid_ward",
        "tem_address" : "required|string|min:1|max:100",
        "permanent_province" : "required|string|valid_province",
        "permanent_district" : "required|string|valid_district",
        "permanent_ward" : "required|string|valid_ward",
        "permanent_address" : "required|string|min:1|max:100",
        "monthly_income" : "required|integer|min:0|max:999999999999",
        "monthly_expense" : "required|integer|min:0|max:999999999999",
        "workplace_name" : "required|string",
        "workplace_province" : "required|string|valid_province",
        "workplace_district" : "required|string|valid_district",
        "workplace_ward" : "required|string|valid_ward",
        "workplace_address" : "required|string|min:1|max:100",
        "married_status" : "required|string|valid_married_status",
        "number_of_dependents" : "required|integer",
        "loan_purpose" : "required|string|valid_loan_purpose",
        "disbursement_method" : "required|string",
        "bank_code" : "required|string|valid_bank_code",
        "bank_account" : "required|string|max:50",
        "relation_1" : "required|string|valid_relation",
        "relation_1_name" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "relation_1_phone_number" : "required|string|size:10|valid_phone_number",
        "relation_2" : "required|string|valid_relation",
        "relation_2_name" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "relation_2_phone_number" : "required|string|size:10|valid_phone_number",
        "time_duration" : "required|integer",
        "sector_industry" : "required|string",
        "business_type" : "required|string",
        "capital_need" : "required|integer",
        "self_financing" : "required|integer",
        "turnover" : "required|array|valid_total_cp_vpl",
        "turnover.*.month" : "required|integer",
        "turnover.*.transactions" : "required|integer",
        "turnover.*.DT_PINCODE_VT_N" : "required|integer",
        "turnover.*.DT_TT_VT_N" : "required|integer",
        "turnover.*.DT_GD_VINA_N" : "required|integer",
        "turnover.*.DT_GD_MOBI_N" : "required|integer",
        "turnover.*.DT_GD_BEELINE_N" : "required|integer",
        "turnover.*.DT_GD_VNMOBILE_N" : "required|integer",
        "turnover.*.DT_GAME_RECHARGE_N" : "required|integer",
        "turnover.*.DT_GAME_KHAC_N" : "required|integer",
        "turnover.*.TOTAL_CP" : "required|integer",
        "month_fee" : "required|integer",
        "doc_collecting_list" : "required|array",
        "doc_collecting_list.*.doc_type" : ["required","string",{"in" : ['SPID','SPIC','SNID','STCA','STRC','SHK9','SKT3','SFRB','SWAI','SEB','SLHC','SBIZ','SPBL','SPCG']}],
        "doc_collecting_list.*.doc_id" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VPL_FULLLOAN',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function VPLSelectOfferValidate(req,res,next) {
    common.bodyLog('VPL_SELECT_OFFER',req.body.request_id,req.body)
    const validationRule = {
        "request_id" : "required|string|min:15|max:40|valid_request_id",
        "contract_number" : "required|string",
        "selected_offer_id" : "required",
        "selected_offer_amount" : "required",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VPL_SELECT_OFFER',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });

}

async function VPLResubmitDoc(req,res,next) {
    common.bodyLog('VPL_RESUBMIT_DOC',req.body.request_id,req.body)
    const validationRule = {
        "request_id" : "required|string|min:15|max:40|valid_request_id",
        "contract_number" : "required|string",
        "list_doc_resubmit" : "required|array",
        "list_doc_resubmit.*.doc_type" : ["required","string",{"in" : ['SPID','SPIC','SNID','STCA','STRC','SHK9','SKT3','SFRB','SWAI','SEB','SLHC','SBIZ','SPBL','SPCG']}],
        "list_doc_resubmit.*.doc_id" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VPL_RESUBMIT_DOC',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });

}

module.exports = {
    VPLEligibleValidate,
    VPLFullloanValidate,
    VPLGetPresignedValidate,
    VPLSelectOfferValidate,
    VPLResubmitDoc
};

