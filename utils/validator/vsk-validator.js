const {validator,responseError} = require("./validate")
const common = require("../common")
const { authenticateOauth2V03WithNoToken } = require("../aaaService")

const customMessages = {
    required: 'The: attribute is missing',
}

async function VSKGetPresignedValidate(req,res,next) {
    common.bodyLog('VSK_PRESIGNED',req.body.request_id,req.body)
    const validationRule = {
        "requestId": "required|string|min:15|max:40|valid_request_id",
        "partnerCode": ["required","string",{"in" : ['VSK']}],
        "fileName" : "required|string|min:1|max:100|valid_file_format"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VSK_PRESIGNED',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next();
        }
    });
}

async function VSKBasicValidate(req, res,next ){
    const validationRule = {
        "requestId": "required|string|min:15|max:40|valid_request_id",
        "channel" : ["required","string",{"in" : ['MC']}],
        "gender" : ["required","string", {"in":['F','M']} ],
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "identityCardId" : "required|string|valid_id_card",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "employmentType" : "required|string|valid_employment_type",
        "email": "string|email|min:1|max:250",
        "phoneNumberZalo" : "string|size:10|valid_phone_number",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "loanAmount" : "required|integer|min:0|max:************",
        "tenor" : "required|integer",
        "contractType" : ["required","string", {"in":['CREDITLINE','CASHLOAN']} ],
        "nameOfTool": "string",
        "nameOfApp": "string",
        "ecommerce": "string",
        "ecommerceDuration": "integer|min:0",
        "nameOfToolDuration": "integer|min:0",
        "nameOfAppDuration": "integer|min:0",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function VSKFullloanValidate(req, res,next ){
    // common.bodyLog('VSK_FULLLOAN',req.body.request_id,req.body)
    const validationRule = {
        "requestId": "required|string|min:15|max:40|valid_request_id",
        "channel" : ["required","string",{"in" : ['MC']}],
        "contractNumber" : "required|string|valid_eligible_fullLoan",
        "disbursementMethod" : ["string",{"in" : ['TRS']}],
        // "ownerOfAccount": "required|string",
        // "bankCode" : "required|string|valid_bank_code",
        // "bankBranchCode" : "required|string",
        // "bankAccount" : "required|string|max:50",
        "temProvince" : "required|string|valid_province",
        "temDistrict" : "required|string|valid_district",
        "temWard" : "required|string|valid_ward",
        "temAddress" : "required|string|min:1|max:100",
        "permanentProvince" : "required|string|valid_province",
        "permanentDistrict" : "required|string|valid_district",
        "permanentWard" : "required|string|valid_ward",
        "permanentAddress" : "required|string|min:1|max:100",
        "yearsOfStay" : "required|integer",
        "houseType" : "string|valid_house_type",
        "marriedStatus" : "required|string|valid_married_status",
        "marriageMateId" : "string|valid_id_card",
        "numberOfDependents" : "required|integer",
        "cusTurnover" : "required|array",
        "cusTurnover.*.month" : "required|integer",
        "cusTurnover.*.amount" : "required|integer|min:0|max:************",
        "monthlyIncome" : "required|integer|min:0|max:************",
        "monthlyIncomeOther" : "required|integer|min:0|max:************",
        "monthlyExpense" : "required|integer|min:0|max:************",
        "relation1" : "required|string|valid_relation",
        "relation1Name" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "relation1PhoneNumber" : "required|string|size:10|valid_phone_number",
        "relation2" : "required|string|valid_relation",
        "relation2Name" : "required|string|valid_cust_name_1|valid_cust_name_2|max:80",
        "relation2PhoneNumber" : "required|string|size:10|valid_phone_number",
        "loanPurpose" : "required|string|valid_loan_purpose",
        // "businessDuration": "required|integer",
        "firstRegistrationDate": "required|string|format_date_ymd",
        "sectorIndustry" : "required|string",
        "businessType" : "required|string",
        "businessLegal": ["required","string",{"in" : ['Y','N']}],
        "typeTrading": ["required","string",{"in" : ['ON','OF','BO']}],
        "capitalNeed" : "required|integer",
        "selfFinancing" : "required|integer",
        "otherCapital" : "required|integer",
        "fundingFromEc" : "required|integer",
        "repaymentSources" : "required|string",
        "branchAddress" : "required|array",
        "branchAddress.*.name" : "required|string",
        "branchAddress.*.province" : "required|string|valid_province",
        "branchAddress.*.district" : "required|string|valid_district",
        "branchAddress.*.ward" : "required|string|valid_ward",
        "branchAddress.*.detail" : "required|string",
        "branchAddress.*.statusOwned" : "required|string",
        "branchAddress.*.numOfStaff" : "required|integer",
        "accountTrading" : "array",
        "accountTrading.*.platformName" : "string",
        "accountTrading.*.storeName" : "string",
        "latestTaxPayment" : "required|integer",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string"
    }
   
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function VSKResubmitDoc(req,res,next) {
    common.bodyLog('VSK_RESUBMIT_DOC',req.body.request_id,req.body)
    const validationRule = {
        "requestId" : "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string",
        "listDocResubmit" : "required|array",
        "listDocResubmit.*.docType" : ["required","string",{"in" : ['SPID','SPIC','SNID','STCA','STRC','SHK9','SKT3','SFRB','SPBL','SNHC','SUNHC','SUBCP','SCPHD','SFBMC','SOSS','SCPS','SBAS','SIBE','SSSR','SAD1','SAD2','SAD3','SAD4','SAD5']}],
        "listDocResubmit.*.docId" : "required|string",
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VSK_RESUBMIT_DOC',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function VSKCreateKunnValidate(req, res,next ){
    const validationRule = {
        "requestId": "required|string|min:15|max:40|valid_request_id",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function VSKReviewHmValidate(req, res,next ){
    const validationRule = {
        "contractNumber": "required|string",
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "identityCardId" : "required|string|valid_id_card",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "branchAddress" : "array",
        "branchAddress.*.name" : "string",
        "branchAddress.*.province" : "string|valid_province",
        "branchAddress.*.district" : "string|valid_district",
        "branchAddress.*.ward" : "string|valid_ward",
        "branchAddress.*.detail" : "string"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

module.exports = {
    VSKBasicValidate,
    VSKFullloanValidate,
    VSKGetPresignedValidate,
    VSKResubmitDoc,
    VSKCreateKunnValidate,
    VSKReviewHmValidate
}