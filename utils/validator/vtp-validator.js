const {validator,responseError} = require("./validate")
const common = require("../common")
const { authenticateOauth2V03WithNoToken } = require("../aaaService")

const customMessages = {
    required: 'The :attribute is missing',
}

async function VTPGetPresignedValidate(req,res,next) {
    common.bodyLog('VPL_PRESIGNED',req.body.request_id,req.body)
    const validationRule = {
        "requestId": "required|string|min:15|max:40|valid_request_id",
        "partnerCode": ["required","string",{"in" : ['VTP']}],
        "fileName" : "required|string|min:1|max:100|valid_file_format"
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            common.serviceLog('VPL_PRESIGNED',req.body.request_id,responseBody)
            return res.status(400).send(responseBody);
        }
        else {
            return next()
        }
    });
}

async function VTPBasicValidate(req, res,next ){
    const validationRule = {
        "requestId": "required|string|min:15|max:40|valid_request_id",
        "channel" : "required|string",
        "partnerCode": ["required","string",{"in" : ['VTP']}],
        "gender" : ["required","string", {"in":['F','M']} ],
        "customerID" : "required|string",
        "customerName": "required|string|min:1|max:80|valid_cust_name_1|valid_cust_name_2",
        "dateOfBirth": "required|string|format_date_ymd|valid_age",
        "identityCardId" : "required|string|valid_id_card",
        "issueDate": "required|string|format_date_ymd|valid_issue_date_1|valid_issue_date_2",
        "issuePlace": "required|string|valid_issue_place",
        "otherIdentityCardId" : "string|valid_id_card",
        "phoneNumber" : "required|string|size:10|valid_phone_number",
        "employmentType" : "required|string|valid_employment_type",
        "email": "string|email|min:1|max:250",
        "phoneNumberZalo" : "string|size:10|valid_phone_number",
        "merchantAccount" : "required|string|valid_merchant_account",
        "marriedStatus" : "required|string|valid_married_status",
        "marriageMateId" : "string|valid_id_card",
        "typeTrading" : "required|string",
        "businessLegal" : ["required","string", {"in":['Y','N']} ],
        "firstRegistrationDate" : "required|string|format_date_ymd" 
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

async function VTPFullloanValidate(req,res,next) {
    const validationRule = {
        "requestId" : "required|string|min:15|max:40|valid_request_id",
        "contractNumber" : "required|string",
        "customerID" : "required|string",
        "partnerCode" : ["required","string",{"in" : ['VTP']}],
        "disbursementMethod" : ["required","string",{"in" : ['TRANSFER','CASH']}],
        "bankCode" : "required|string|valid_bank_code",
        "bankBranchCode" : "required|string",
        "bankAccount" : "required|string|max:50",
        "temProvince" : "required|string|valid_province",
        "temDistrict" : "required|string|valid_district",
        "temWard" : "required|string|valid_ward",
        "temAddress" : "required|string|min:1|max:100",
        "permanentProvince" : "required|string|valid_province",
        "permanentDistrict" : "required|string|valid_district",
        "permanentWard" : "required|string|valid_ward",
        "permanentAddress" : "required|string|min:1|max:100",
        "numberOfDependents" : "required|integer",
        "yearsOfStay" : "integer",
        "houseType" : "string|valid_house_type",
        "monthlyIncome" : "integer|min:0|max:************",
        "monthlyExpenses" : "required|integer",
        "timeDuration" : "integer",
        "relation1" : "required|string|valid_relation",
        "relation1Name" : "required|string|min:1|max:80",
        "relation1PhoneNumber" : "required|string|size:10|valid_phone_number",
        "relation2" : "required|string|valid_relation",
        "relation2Name" : "required|string|min:1|max:80",
        "relation2PhoneNumber" : "required|string|size:10|valid_phone_number",
        "listDocCollecting" : "required|array",
        "listDocCollecting.*.docName" : "required|string",
        "listDocCollecting.*.docId" : "required|string",
        "loanPurpose" : "required|string|valid_loan_purpose",
        "loanAmount" : "required|integer",
        "tenor" : "required|integer",
        "accountTrading" : "array",
        "accountTrading.*.platformName" : "string",
        "accountTrading.*.storeName" : "string",
        "businessDuration" : "required|integer",
        "sectorIndustry" : "required|string",
        "businessType" : "required|string",
        "capitalNeed" : "required|integer",
        "selfFinancing" : "required|integer",
        "otherCapital" : "required|integer",
        "fundingFromEc" : "required|integer",
        "repaymentSources" : "required|string",
        "fixedAsset" : "string",
        "planAsset" : "required|string",
        "numberBusinessAsset" : "integer",
        "timeUsingAsset" : "required|integer",
        "branchAddress" : "required|array",
        "branchAddress.*.name" : "required|string",
        "branchAddress.*.province" : "required|string|valid_province",
        "branchAddress.*.district" : "required|string|valid_district",
        "branchAddress.*.ward" : "required|string|valid_ward",
        "branchAddress.*.detail" : "required|string",
        "latestTaxPayment" : "required|integer" 
    }
    validator(req.body, validationRule, customMessages, (err, status) => {
        if (!status) {
            const responseBody = responseError(err)
            return res.status(400).send(responseBody);
        }
        else {
            return authenticateOauth2V03WithNoToken(req, res, next);
        }
    });
}

module.exports = {
    VTPBasicValidate,
    VTPGetPresignedValidate,
    VTPFullloanValidate
}