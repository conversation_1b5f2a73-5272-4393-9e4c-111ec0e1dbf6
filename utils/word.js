const s3Service = require("../upload_document/s3-service")
const fs = require("fs")
const PizZip = require('pizzip')
const docxtemplater = require('docxtemplater')
const moment = require('moment-timezone');
moment().tz('Asia/Ho_Chi_Minh').format()
const libre = require('libreoffice-convert')

const renderWordFile = async ({ filePath, data = {} }) => {
  try {
    let fileBuffer = fs.readFileSync(filePath, 'binary');
    let zip = new PizZip(fileBuffer);
    let doc = new docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
      nullGetter() {
        return '';
      }
  });
    doc.render(data);
    let buffer = doc.getZip().generate({ type: 'nodebuffer' });
    return buffer;
  } catch (e) {
    console.error(e)
    return null
  }
}

const pushFileToS3 = async ({ fileName, fileBuffer, fileStoragePath, acl }) => {
  try {
    return await s3Service.uploadV2(global.config.data, fileName, fileBuffer, fileStoragePath, acl);
  } catch (e) {
    console.error(e);
    return null
  }
}

const convertPdf = ({ fileBuffer }) => {
  return new Promise(function (resolve, reject) {
    libre.convert(fileBuffer, ".pdf", undefined, (err, data) => {
      if (err) {
        reject(err)
        console.log('Error converting file:err');
      }
      else {
        resolve(data);
      }
    })
  })
}

module.exports = {
  renderWordFile,
  pushFileToS3,
  convertPdf
}